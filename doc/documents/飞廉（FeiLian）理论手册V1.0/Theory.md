---
mainfont: Microsoft YaHei

---

<br/>





<p style="line-height: 1.5;">
<center><strong><font color=black size=10>飞廉(FeiLian)理论手册</font ></>
</p>

<br/>

<center><strong><font color=black size=5> &lt Version 1.0 &gt </font ></>

<div align="center">
<img src="./media/d4e727ee348e1116e3403fa906a89eb0.png" alt="描述文字" height="500"></div>

<br/>

<center><strong><font color=black size=4>中国航空工业空气动力研究院</font ></>
<br/>

<center><strong><font color=black size=4>2021-11-15</font ></><!-- pagebreak -->
<div STYLE="page-break-after: always;"></div>

<br/>



<br/>

​        本资料系统描述了ARI-CFD系列软件之一的多面体网格二阶精度流场计算软件（简称：飞廉，FeiLian）的理论原理。本资料可作为该软件的程序开发人员、高级用户的参考资料，供迅速和详细掌握算法原理。
​        本资料由飞廉软件研发团队编写及校对。

<div STYLE="page-break-after: always;"></div>
# 控制方程

## 基本物理量说明

对于描述流体运动的物理量，基本符号表示如下：
密度$\rho$，压强$p$，速度$\vec{U}=\left( u,v,w \right)$，速度的模，温度$T$，声速$c$；单位质量的内能$e$,单位体积流体的内能$\rho e$；
单位体积的总内能:
$$
\rho E=\rho e + \rho {U^2}/2
\tag{1.1}
$$
单位体积的公式热焓:
$$
\rho h=\rho e+p
\tag{1.2}
$$
单位体积的总热焓:
$$
\rho H=\rho E+p
\tag{1.3}
$$

对于流动介质为空气的情况，气体常数$R$：
$$
R={{C}_{p}}-{{C}_{v}}
\tag{1.4}
$$

其中,${{C}_{p}}$为定压比热,${{C}_{v}}$为定容比热。
定义两者的比值为比热比$\gamma $（对于空气，其值为1.4）：
$$
\gamma ={{C}_{p}}/{{C}_{v}}
\tag{1.5}
\label{eq:比热比}
$$

## 控制方程的表达形式
本软件离散方法采用的是密度基方法，因此描述流体运动的控制方程为Favre密度加权平均的可压缩流体NS方程（简称RANS方程），其张量表达的微分形式如下：
$$
\left\{\begin{aligned}
\frac{\partial \rho}{\partial t}+\frac{\partial \rho u_{j}}{\partial x_{j}}&=0 \\
\frac{\partial \rho u_{i}}{\partial t}+\frac{\partial}{\partial x_{j}}\left(\rho u_{i} u_{j}+p \delta_{i j}\right)&=\frac{\partial}{\partial x_{j}}\left(\tau_{i j}^{L}+\tau_{i j}^{T}\right)+\rho f_{i} \\ \frac{\partial \rho E}{\partial t}+\frac{\partial}{\partial x_{j}}\left[(\rho E+p) u_{j}\right]&=\frac{\partial}{\partial x_{j}}\left[u_{i}\left(\tau_{i j}^{L}+\tau_{i j}^{T}\right)+q_{j}^{L}+q_{j}^{T}\right]+\rho f_{j} u_{j}\end{aligned}\right.
\tag{1.6}
\label{eq:Favre加权平均的RANS方程}
$$
其中，上角标$L$表示层流状态，上角标$T$表示在时均化过程中引入的雷诺应力项，一般通过湍流模型来计算完成。$f$为彻体力源项。其中，记$\tau =\tau _{ij}^{L}+\tau _{ij}^{T}$，${{q}_{j}}=q_{j}^{L}+q_{j}^{T}$，在后续湍流模型部分再详细介绍处理方式。
本软件的求解方法采用有限体积法，上述控制方程一般采用向量和矩阵的积分形式来表达，并通过高斯定理完成体积分到面积分的转化，控制方程转化为：
$$
\frac{\partial }{\partial t}\int_{\Omega }{Wd}\Omega +\oint_{\partial \Omega }{\left( {{F}_{C}}-{{F}_{V}} \right)}\cdot d\vec{S}=\int_{\Omega }{Qd}\Omega
\tag{1.7}
\label{eq:控制方程}
$$
其中，$\Omega $为控制体，$\partial \Omega $为边界表面，$\vec{S}$表示矢量面积，其他各项含义说明如下。

### 时间项部分
向量$W$称为守恒量：
$$
W=\left[\begin{array}{l}
\rho \\ \rho u \\ \rho v \\ \rho w \\ \rho E
\end{array}\right]=\left[\begin{array}{l}
\rho \\ \rho \vec{U} \\ \rho \vec{E}
\end{array}\right]
\tag{1.8}
$$
同时，我们记原始变量为：
$$
q=\left[ \begin{array}{c}
\rho  \\  u \\  v \\  w \\  p
\end{array} \right]
=\left[ \begin{array}{c}
\rho  \\  {\vec{U}} \\  p
\end{array} \right]
\tag{1.9}
$$

### 对流项部分
$F_C$称为对流项或无粘项：
$$
{{F}_{C}}=\left[ \begin{array}{c}
\rho \vec{U} \\ 
\rho \vec{U}\vec{U}+pI \\ 
\rho H\vec{U}
\end{array} \right]
=\left[ \begin{array}{c}
\rho \vec{U} \\ 
\rho \vec{U}\vec{U}+pI \\ 
(\rho E+p)\vec{U}
\end{array} \right]
\tag{1.10}
$$
其中，$I$表示单位矩阵（张量）。

### 粘性项部分
$F_V$称为粘性项：
$$
{{F}_{V}}=\left[ \begin{array}{c}
 0 \\  \tau  \\  {\vec{\theta }}
\tag{1.11}
\end{array} \right]
$$
其中，应力张量$\tau $表达如下：
$$
\begin{aligned}
\tau =
\left( \begin{matrix}
   {{\tau }_{xx}} & {{\tau }_{xy}} & {{\tau }_{xz}}  \\
   {{\tau }_{yx}} & {{\tau }_{yy}} & {{\tau }_{yz}}  \\
   {{\tau }_{zx}} & {{\tau }_{zy}} & {{\tau }_{zz}}  \\
\end{matrix} \right)
& =\mu \left( \begin{matrix}
   2 {U_{x,x}} & {U_{x,y}} + {U_{y,x}} & {U_{x,z}}+ {U_{z,x}} \\
   {U_{x,y}}+ {U_{y,x}} & 2 {U_{y,y}} & {U_{y,z}}+ {U_{z,x}} \\
   {U_{x,z}} + {U_{z,x}} & {U_{y,z}}+ {U_{z,x}} & 2 {U_{z,z}} \\
\end{matrix} \right) \\
& + \lambda \mu \left({U_{x,x}}+ {U_{y,y}}+ {U_{z,z}} \right)
\left( \begin{matrix}
1 & 0 & 0 \\
0 & 1 & 0 \\
0 & 0 & 1\\
\end{matrix} \right)
\end{aligned}
\tag{1.12}
$$
采用矢量形式，可记为：
$$
\tau =\mu \left( \nabla \vec{U}+{{(\nabla \vec{U})}^{T}}+\lambda (\nabla \cdot \vec{U}) \right)
\tag{1.13}
\label{eq:应力张量矢量形式}
$$
其中 $\mu $ 为粘性系数，$\nabla \vec{U}$ 为速度的梯度张量，$\nabla \cdot \vec{U}$为速度的哈密度算子(也可表示为 $\nabla \vec{U}$ 的迹)，Stokes系数$\lambda =-2/3$。
对于能量方程，有：
$$
\vec{\theta }=\vec{U}\cdot \tau +\kappa \nabla T
\tag{1.14}
$$
其中，$\kappa $为热传导系数，计算如下：
$$
\kappa ={{C}_{p}}\left( \frac{{{\mu }_{L}}}{\Pr {}_{L}}+\frac{{{\mu }_{T}}}{\Pr {}_{T}} \right)
\tag{1.15}
$$
其中，${{\mu }_{L}}$为层流粘性系数，由Sutherland公式给出：
$$
{{\mu }_{L}}=\frac{1+C}{T+C}{{T}^{\tfrac{3}{2}}}
\tag{1.16}
$$
其中，$C= {110.4} / {T_{\infty }}$，$T_{\infty }$为来流温度。
${{\mu }_{T}}$为湍流粘性系数，$\Pr_L$为层流普朗特数（对于空气取$0.72$），$\Pr_T$为湍流普朗特数（对于空气取$0.90$）。

### 源项部分
$Q$称为彻体力源项，如下：
$$
Q=\left[ \begin{array}{c}
0 \\ 
\rho \vec{f} \\ 
\rho \vec{f}\cdot
 \vec{U}+\dot{q}
\end{array} \right]
\tag{1.17}
$$
其中，$\vec{f}$为彻体力源项（如电磁力、特殊考虑重力等），$\dot{q}$为热源等。

## 气体模型
理想气体模型:
$$
p=\rho RT \tag{1.18}
$$
<div STYLE="page-break-after: always;"></div>
# 网格
本软件支持任意形式的多边形或多面体网格。软件支持采用原始网格或者对偶网格两种形式来完成流场计算，用户可以根据需要选择。

## 原始网格

本软件支持任意形状的二维或者三维网格单元。常见的单元形式如下图。
<div align="center">
<img src="./media/fe2980a35642fc54038b378256842742.png" alt="二维网格字" width="250">
<img src="./media/0bb13ff29584c6f2cf3cbaa94fc54cd8.png" alt="三维网格字" height="250"> </div>
<p style="-typora-class:FigCnt;text-align:center;" >常见的二维与三维网格单元</p>
## 对偶网格

在原始网格的基础上，通过对偶化可转化为对偶网格。本软件求解器为格心格式，转化为对偶网格后，控制体的体心为原始网格点，在对偶网格上实现对格点物理量的求解。与原始网格相比，对偶网格的控制体数量会缩减（取决于网格维度和具体网格形式）。因此，对于同一网格，采用对偶网格计算的时间和精度会同步降低。
<div align="center"><img src="./media/e5e52f4ed717112513ed726b2178c196.png" alt="二维网格字" width="400"></div>
<p style="-typora-class:FigCnt;text-align:center;" >原始网格（黑线）与对偶网格（红线）</p>
<div STYLE="page-break-after: always;"></div>
# 有限体积法概述

本软件对控制方程的离散采用有限体积法，相关物理量定义在网格中心，网格单元即为控制体的格心格式，如下图所示。
<div align="center">
<img src="./media/21cf5d47ddabae74c557cb7ed837f074.png" alt="二维网格字" width="200"><img src="./media/a57385fbd1ca6ded3e6c99c18279e7c3.png" alt="二维网格字" width="300">
</div>
<p style="-typora-class:FigCnt;text-align:center;" >二维网格和三维网格的控制体示意图</p>
同时软件提供了采用对偶网格作为控制体的计算方式，详见对偶网格章节部分。

## 时空分离的半离散方法

对于二阶精度的有限体积法，我们认为物理量在控制体内的分布是均匀的。因此，对于方程$\eqref{eq:控制方程}$，在控制体内积分可得到：
$$
\frac{d\left( \Omega W \right)}{dt}=-R
\tag{3.1}
\label{eq:半离散的控制方程}
$$
其中，$R$表示空间离散后得到的残差：
$$
R=\oint_{\partial \Omega }{{{F}_{C}}}d\vec{S}-\oint_{\partial \Omega }{{{F}_{V}}}d\vec{S}-\int_{\Omega }{Qd}\Omega \tag{3.2}
$$
在软件设计中，$R$分别由以上三项组成：对流项残差、粘性项残差、源项残差。其中，对流项的计算方法种类很多，本软件提供了多种方法，详见空间离散部分。
方程$\eqref{eq:半离散的控制方程}$称为半离散的控制方程，在残差R计算完成后其为关于时间的常微分方程，可以采用各种方法来进行求解。常见的时间推进方法为显式的多步Runge-Kutta方法，本软件也提供了各类隐式计算方法，详见时间离散部分。
<div STYLE="page-break-after: always;"></div>
# 空间离散
空间离散构成了残差$R$的差。

## 对流项的离散
对流项的残值$R_C$可以表述为：
$$
{{R}_{C}}=\oint_{\partial \Omega }{{{F}_{C}}\cdot }d\vec{S}=\sum\limits_{j=1}^{{{N}_{F}}}{{{({{F}_{C}})}_{j}}\cdot {{{\vec{S}}}_{j}}}
\tag{4.1}
\label{eq:对流项残值计算}
$$
其中，$j$表示环绕控制体的面序号，$N_F$为控制体的总面数。
软件提供了多种对流离散格式，总结如下：
<p style="-typora-class:TabCnt;text-align:center;" >对流项离散方法汇总</p>
<table>
    <tr>
        <td colspan="2">分类</td>
        <td>离散格式</td> 
   </tr>
    <tr>
        <td colspan="2">中心格式</td>
        <td >Central</td>  
    </tr>
    <tr>
        <td rowspan="2">迎风格式</td> 
        <td >通量差分分裂</td>  
        <td >Roe</td>  
    </tr>
    <tr>
        <td >通量矢量分裂</td>  
        <td >VanLeer, AUSM</td>  
    </tr>
</table>

### Central格式
对于公式$\eqref{eq:对流项残值计算}$中的某个面j的通量，计算如下：
$$
{{({{F}_{C}})}_{j}}\cdot {{\vec{S}}_{j}}={{F}_{C}}({{W}_{j}})\cdot {{\vec{S}}_{j}}-{{D}_{j}}
\tag{4.2}
$$

其中，${{D}_{j}}$为人工耗散项。面心处的物理量${{W}_{j}}$计算如下：
$$
{{W}_{j}}=0.5({{W}_{L}}+{{W}_{R}})
\tag{4.3}
$$

其中，下标$L$、$R$分别表示该面的左单元值和右单元值。
以左单元L为例，面心处人工耗散项${{D}_{j}}$计算如下：
$$
{{D}_{j}}={{({{\Lambda }_{C}})}_{j}}{{\theta }_{j}}\left[ \varepsilon _{j}^{(2)}({{W}_{R}}-{{W}_{L}})-\varepsilon _{j}^{(4)}(L({{W}_{R}})-L({{W}_{L}})) \right]
\tag{4.4}
$$

其中，${{\theta }_{j}}$为几何形状因子，这里取值为1。
该单元的对流谱半径${{({{\Lambda }_{C}})}_{L}}$计算如下：
$$
{{({{\Lambda }_{C}})}_{L}}=\sum\limits_{j=1}^{{{N}_{F}}}{(|{{V}_{j}}|+{{c}_{j}})|{{S}_{j}}|}
\tag{4.5}
$$

其中，$V$为逆变速度，$c$为声速，其面心值分别采用左右单元均值计算。逆变速度计算如下：
$$
V=\vec{U}\cdot \vec{n}
\tag{4.6}
$$

其中，$\vec{n}$为面积矢量$\vec{S}$的单位法向量。
从而，面心处对流谱半径${{({{\Lambda }_{C}})}_{j}}$计算如下：
$$
{{({{\Lambda }_{C}})}_{j}}=0.5\left[ {{({{\Lambda }_{C}})}_{L}}+{{({{\Lambda }_{C}})}_{R}} \right]
\tag{4.7}
$$

$\varepsilon _{j}^{(2)}$、$\varepsilon _{j}^{(4)}$为二阶和四阶耗散系数，计算如下：
$$
\left\{ \begin{array}{l}
\varepsilon _{j}^{(2)}={{k}^{(2)}}\max ({{\gamma }_{L}},{{\gamma }_{R}}) \\ 
\varepsilon _{j}^{(4)}=\max (0,({{k}^{(4)}}-\varepsilon _{j}^{(2)})) \\ 
\end{array} \right.
\tag{4.8}
$$

其中，二阶人工粘性系数${{k}^{(2)}}=0.5$，四阶人工粘性系数${{k}^{(4)}}=1/64$。${{\gamma }_{L}}$为激波探测器，计算如下（$N_A$为与该单元相邻的单元数量）：
$$
{{\gamma }_{L}}=\frac{\left| \sum\nolimits_{1}^{{{N}_{A}}}{{{\theta }_{j}}({{p}_{R}}-{{p}_{L}})} \right|}{\sum\nolimits_{1}^{{{N}_{A}}}{({{p}_{R}}+{{p}_{L}})}}
\tag{4.9}
$$
$L({{W}_{L}})$为伪拉普拉斯算子，计算如下：
$$
L({{W}_{L}})=\sum\limits_{j=1}^{{{N}_{A}}}{{{\theta }_{j}}({{W}_{R}}-{{W}_{L}})}
\tag{4.10}
\label{eq:伪拉普拉斯算子}
$$

### Roe格式

对于公式$\eqref{eq:对流项残值计算}$中的某个面$j$的通量，计算如下：

$$
{{({{F}_{C}})}_{j}}\cdot {{\vec{S}}_{j}}={{({{F}_{C}})}_{j}}\cdot {{\vec{S}}_{j}}
\tag{4.11}
$$

$$
{{({{F}_{C}})}_{j}}=0.5\left[ {{F}_{C}}({{W}_{L}})+{{F}_{C}}({{W}_{R}})-|{{A}_{Roe}}{{|}_{j}}({{W}_{R}}-{{W}_{L}}) \right]
\tag{4.12}
$$

其中，前半部分称为平均项，后半部分称为人工耗散项。不同于中心格式，这里的下标$L$、$R$分别表示该面的左值和右值，对于一阶精度格式可取左右单元的体心值，对于二阶精度格式需要采用梯度和限制器进行重构。
对于Roe格式，面心值（用~表示）采用左值和右值的Roe平均计算得到。其中，密度计算如下：

$$
\tilde{\rho }=\sqrt{{{\rho }_{L}}{{\rho }_{R}}}
\tag{4.13}
$$

计算左右权重如下：

$$
\left\{ \begin{array}{c}
{{\phi }_{L}}={\sqrt{{{\rho }_{L}}}}/{(\sqrt{{{\rho }_{L}}}+\sqrt{{{\rho }_{R}}})}\; \\ 
{{\phi }_{R}}={\sqrt{{{\rho }_{R}}}}/{(\sqrt{{{\rho }_{L}}}+\sqrt{{{\rho }_{R}}})}\; 
\end{array} \right.
\tag{4.14}
$$

其他物理量计算如下：

$$
\left\{ \begin{array}{l}
\tilde{U}={{{\vec{U}}}_{L}}{{\phi }_{L}}+{{{\vec{U}}}_{R}}{{\phi }_{R}} \\ 
\tilde{H}={{H}_{L}}{{\phi }_{L}}+{{H}_{R}}{{\phi }_{R}} \\ 
\tilde{c}=\sqrt{(\gamma -1)(\tilde{H}-0.5{{{\tilde{q}}}^{2}})} \\ 
\tilde{V}=\tilde{u}{{n}_{x}}+\tilde{v}{{n}_{y}}+\tilde{w}{{n}_{z}} \\ 
 {{{\tilde{q}}}^{2}}={{{\tilde{u}}}^{2}}+{{{\tilde{v}}}^{2}}+{{{\tilde{w}}}^{2}}
\end{array} \right.
\tag{4.15}
$$

人工耗散项部分按照波数分解，计算如下：

$$
|{{A}_{Roe}}{{|}_{j}}({{W}_{R}}-{{W}_{L}})=|\Delta {{F}_{1}}|+|\Delta {{F}_{234}}|+|\Delta {{F}_{5}}|
\tag{4.16}
$$

其中：

$$
|\Delta {{F}_{1}}|=|\tilde{V}-\tilde{c}|\left( \frac{\Delta p-\tilde{\rho }\tilde{c}\Delta V}{2{{{\tilde{c}}}^{2}}} \right)\left[ \begin{matrix}
  1 \\ 
  \tilde{U}-\tilde{c}\vec{n} \\ 
  \tilde{H}-\tilde{c}\tilde{V} \\ 
\end{matrix} \right]
\tag{4.17}
$$

$$
|\Delta {{F}_{234}}|=|\tilde{V}|\left( \Delta \rho -\frac{\Delta p}{{{{\tilde{c}}}^{2}}} \right)\left[ \begin{matrix}
  1 \\ 
  {\tilde{U}} \\ 
  0.5{{{\tilde{q}}}^{2}} \\ 
\end{matrix} \right]+|\tilde{V}|\tilde{\rho }\left[ \begin{matrix}
  0 \\ 
  \Delta \vec{U}-\Delta \vec{n} \\ 
  \tilde{U}\cdot \Delta \vec{U}-\tilde{V}\Delta V \\ 
\end{matrix} \right]
\tag{4.18}
$$

$$
|\Delta {{F}_{5}}|=|\tilde{V}+\tilde{c}|\left( \frac{\Delta p+\tilde{\rho }\tilde{c}\Delta V}{2{{{\tilde{c}}}^{2}}} \right)\left[ \begin{matrix}
  1 \\ 
  \tilde{U}+\tilde{c}\vec{n} \\ 
  \tilde{H}+\tilde{c}\tilde{V} \\ 
\end{matrix} \right]
\tag{4.19}
$$

其中，差量符号$\Delta $表示$\Delta (\bullet )={{(\bullet )}_{R}}-{{(\bullet )}_{L}}$。
为了防止红玉现象，对特征值$|{{\tilde{\Lambda }}_{C}}|=|\tilde{V}\pm \tilde{c}||\tilde{V}|$，采用Harten熵修正：
$$
|{{\tilde{\Lambda }}_{C}}|=\left\{ \begin{matrix}
   |{{{\tilde{\Lambda }}}_{C}}| & if|{{{\tilde{\Lambda }}}_{C}}|>\delta   \\
   \frac{\tilde{\Lambda }_{C}^{2}+{{\delta }^{2}}}{2\delta } & if|{{{\tilde{\Lambda }}}_{C}}|\le \delta   \\
\end{matrix} \right.
\tag{4.20}
$$

其中，$\delta =0.05\tilde{c}$。

### VanLeer格式

对于公式$\eqref{eq:对流项残值计算}$中的某个面$j$的通量计算同公式$\eqref{eq:伪拉普拉斯算子}$，${{({{F}_{C}})}_{j}}\cdot {{\vec{S}}_{j}}={{({{F}_{C}})}_{j}}\cdot {{\vec{S}}_{j}}$，其中：
$$
{{({{F}_{C}})}_{j}}=F_{C}^{+}+F_{C}^{-}
\tag{4.21}
$$

对于基本物理量，采用下标$L$、$R$分别表示该面的左值和右值。则面心马赫数计算如下：
$$
{{M}_{j}}=M_{L}^{+}+M_{R}^{-}
\tag{4.22}
$$

其中，马赫数分裂计算如下：
$$
M_{L}^{+}=\left\{ \begin{matrix}
   {{M}_{L}} & \begin{matrix}
   if & {{M}_{L}}\ge +1  \\
\end{matrix}  \\
   0.25{{({{M}_{L}}+1)}^{2}} & \begin{matrix}
   if & |{{M}_{L}}|<1  \\
\end{matrix}  \\
   0 & \begin{matrix}
   if & {{M}_{L}}\le -1  \\
\end{matrix}  \\
\end{matrix} \right.
\tag{4.23}
$$

$$
M_{R}^{-}=\left\{ \begin{matrix}
   0 & \begin{matrix}
   if & {{M}_{R}}\ge +1  \\
\end{matrix}  \\
   0.25{{({{M}_{R}}-1)}^{2}} & \begin{matrix}
   if & |{{M}_{R}}|<1  \\
\end{matrix}  \\
   {{M}_{R}} & \begin{matrix}
   if & {{M}_{R}}\le -1  \\
\end{matrix}  \\
\end{matrix} \right.
\tag{4.24}
$$

其中，${{M}_{L}}$、${{M}_{R}}$分别利用逆变速度及音速的左值和右值计算。
当亚音速流动（$|{{M}_{j}}|<1$）时，$F_{C}^{\pm }$计算如下：
$$
F_{C}^{\pm }=\left[ \begin{matrix}
  f_{mass}^{\pm } \\ 
  f_{mass}^{\pm }\left[ \vec{n}(-V\pm 2c)/\gamma +\vec{U} \right] \\ 
  f_{energy}^{\pm } \\ 
\end{matrix} \right]
\tag{4.25}
$$

其中，质量和能量部分计算如下：
$$
\begin{array}{*{35}{l}}
   f_{mass}^{+}=+0.25{{\rho }_{L}}{{c}_{L}}{{({{M}_{L}}+1)}^{2}}  \\
   f_{mass}^{-}=-0.25{{\rho }_{R}}{{c}_{R}}{{({{M}_{R}}-1)}^{2}}  \\
   f_{energy}^{\pm }=f_{mass}^{\pm }{{\left\{ \frac{{{\left[ (\gamma -1)V\pm 2c \right]}^{2}}}{2({{\gamma }^{2}}-1)}+\frac{{{{\vec{U}}}^{2}}-{{V}^{2}}}{2} \right\}}_{L/R}}  \\
\end{array}
\tag{4.26}
$$

对于超音速流动：
$$
\left\{ \begin{array}{l}
{{({{F}_{C}})}_{j}}=F_{C}^{+}\begin{matrix}
   {} & \begin{matrix}
   if & M_{j}^{{}}\ge +1  \\
\end{matrix}  \\
\end{matrix} \\ 
{{({{F}_{C}})}_{j}}=F_{C}^{-}\begin{matrix}
   {} & \begin{matrix}
   if & M_{j}^{{}}\le -1  \\
\end{matrix}  \\
\end{matrix} \\ 
\end{array} \right.
\tag{4.27}
$$

## 梯度与限制器
对于二阶精度的有限体积法，需要计算各种物理量的梯度。同时，在对流项离散计算左值和右值时，为了防止局部震荡经常采用限制器。

### 梯度
本软件提供了两种梯度计算方法，Green-Gauss方法和最小二乘法。这里仅介绍Green-Gauss方法。对于任意物理量$\phi $，其单元的梯度计算如下：
$$
\nabla \phi =\frac{1}{\Omega }\int\limits_{\partial \Omega }{\phi d\vec{S}}=\frac{1}{\Omega }\sum\limits_{j=1}^{{{N}_{F}}}{0.5({{\phi }_{L}}+{{\phi }_{R}}){{{\vec{S}}}_{j}}}
\tag{4.28}
$$

其中，$L$，$R$表示左右单元的体心值，$\Omega $为单元的体积。

### 重构与限制器
以左值为例，对于任意物理量$\phi $，其二阶精度的重构左值如下：
$$
{{\phi }_{L}}={{\phi }_{i}}+{{\Psi }_{i}}(\nabla {{\phi }_{i}}\cdot {{\vec{r}}_{L}})
\tag{4.29}
$$

其中，下标$i$表示左值所在的单元编号，${{\vec{r}}_{L}}$为体心到面心的距离，$\Psi $为限制器。
记参变量$r$如下：
$$
r=\frac{\Delta \phi }{{{\Delta }_{2}}}=\left\{ \begin{matrix}
   \frac{{{\phi }_{\max }}-{{\phi }_{i}}}{{{\Delta }_{2}}} & if\begin{matrix}
   {} & {{\Delta }_{2}}>0  \\
\end{matrix}  \\
   \frac{{{\phi }_{\min }}-{{\phi }_{i}}}{{{\Delta }_{2}}} & if\begin{matrix}
   {} & {{\Delta }_{2}}<0  \\
\end{matrix}  \\
   1 & if\begin{matrix}
   {} & {{\Delta }_{2}}=0  \\
\end{matrix}  \\
\end{matrix} \right.
\tag{4.30}
$$

其中:
$$
\begin{aligned}
{{\phi }_{\max }} & =\max ({{\phi }_{i}},{{\max }_{j}}{{\phi }_{j}}) \\ 
{{\phi }_{\min }} & =\min ({{\phi }_{i}},{{\min }_{j}}{{\phi }_{j}}) \\ 
{{\Delta }_{2}} & =sign({{\Delta }_{2}}')(\left| {{\Delta }_{2}}' \right|+\varepsilon ) \\ 
{{\Delta }_{2}}' & =\vec{r}\cdot \nabla {{U}_{i}} \\ 
\end{aligned}
\tag{4.31}
$$

单元的限制器取面限制器的最小值：
$$
{{\Psi }_{i}}={{\min }_{j}}{{\Psi }_{j}}
\tag{4.3}
$$

<p style="-typora-class:TabCnt;text-align:center;" >限制器方法汇总</p>
| 序号 | 名称            | 计算公式${\Psi _j}$                      |
| ---- | --------------- | ------------------------------------------ |
| 1    | MinMod          | ${\Psi} = min \left( r, 1\right)$        |
| 2    | Venkatakrishnan | ${\Psi} = \frac{r^2 + 2 r}{r^2 + r + 2}$ |
| 3    | Van Leer        | ${\Psi} = \frac{r + |r|} {r + 1}$        |
| 4    | Van Albadal     | ${\Psi} = \frac{2 r} {r^2 + 1}$          |

## 粘性项的离散

粘性项的残值$R_V$可以表述为：
$$
{{R}_{C}}=-\oint_{\partial \Omega }{{{F}_{V}}}\cdot d\vec{S}=-\sum\limits_{j=1}^{{{N}_{F}}}{{{({{F}_{V}})}_{j}}\cdot {{{\vec{S}}}_{j}}}
\tag{4.33}
$$

其中，$j$表示环绕控制体的面序号，$N_F$为控制体的总面数。
粘性项通量的质量方程为零。动量方程的${{({{F}_{V}})}_{\text{momentum}}}$计算如下：
$$
{{({{F}_{V}})}_{\text{momentum}}}=\tau =\mu \left( \nabla \vec{U}+{{(\nabla \vec{U})}^{T}}+\lambda (\nabla \cdot \vec{U}) \right)
\tag{4.34}
$$

能量方程的${{({{F}_{V}})}_{\text{energy}}}$计算如下：
$$
{{({{F}_{V}})}_{\text{energy}}}=\vec{U}\cdot \tau +\kappa \nabla T
\tag{4.35}
$$

可见，粘性项的计算需要面心处的梯度值。粘性项采用中心格式进行离散，为了应对网格质量较差的情况，软件提供了两种计算方式。

### 直接计算法向梯度的方式

针对薄层N-S方程假设，舍弃切向速度的变化，直接利用法向的速度变化替代面心处的速度梯度（温度梯度同样如此），可得动量方程的某个面的粘性通量计算如下：
$$
\begin{aligned}
{{({{F}_{V}})}_{\text{momentum}}}\cdot \vec{S} & =\mu \left( \nabla \vec{U}+{{(\nabla \vec{U})}^{T}}+\lambda (\nabla \cdot \vec{U}) \right)\cdot \vec{S} \\ 
& ={{\mu }_{j}}|{{{\vec{S}}}_{j}}|\left( \frac{\Delta \vec{U}}{d}+\frac{1}{3}\left( \frac{\Delta \vec{U}}{d}\cdot \vec{n} \right)\vec{n} \right)
\end{aligned}
\tag{4.36}
$$

其中，$\Delta \vec{U}={{\vec{U}}_{R}}-{{\vec{U}}_{L}}$（$R$, $L$表示为左右单元的体心值），$d=({{\vec{r}}_{R}}-{{\vec{r}}_{L}})\cdot \vec{n}$。
同理，能量方程的粘性通量计算如下：
$$
\begin{aligned}
{{({{F}_{V}})}_{\text{energy}}}\cdot \vec{S} & =\left( \vec{U}\cdot \tau +\kappa \nabla T \right)\cdot \vec{S} \\ 
& =\left[ {{({{F}_{V}})}_{\text{momentum}}}\cdot \vec{S} \right]\cdot {{{\vec{U}}}_{j}}+{{\kappa }_{j}}|{{{\vec{S}}}_{j}}|\left( \frac{\Delta T}{d} \right) 
\end{aligned}
\tag{4.37}
$$

### 基于面心梯度的方式

首先计算面心处的速度梯度：
$$
{{(\nabla \vec{U})}_{\text{j}}}=0.5\left[ {{(\nabla \vec{U})}_{\text{L}}}+{{(\nabla \vec{U})}_{\text{R}}} \right]
\tag{4.38}
$$

然后通过公式$\eqref{eq:应力张量矢量形式}$得到面心处的应力张量$\tau $，而从得到粘性通量：
$$
{{({{F}_{V}})}_{\text{momentum}}}\cdot \vec{S}={{\left( \tau  \right)}_{j}}\cdot {{\vec{S}}_{j}}
\tag{4.39}
$$

$$
{{({{F}_{V}})}_{\text{energy}}}\cdot \vec{S}=\left[ {{\left( \tau  \right)}_{j}}\cdot {{{\vec{S}}}_{j}} \right]\cdot {{\vec{U}}_{j}}+{{\kappa }_{j}}{{\left( \nabla T \right)}_{j}}\cdot {{\vec{S}}_{j}}
\tag{4.40}
$$

## 源项的离散

略。

<div STYLE="page-break-after: always;"></div>
# 时间离散

在空间离散后，对于公式$\eqref{eq:半离散的控制方程}$每个控制体的时间离散可统一如下（略去下角标）：
$$
\frac{\Omega }{\Delta t}\Delta {{W}^{n}}=-\frac{\beta }{1+\omega }{{R}^{n+1}}-\frac{1-\beta }{1+\omega }{{R}^{n}}+\frac{\omega }{1+\omega }\frac{\Omega }{\Delta t}\Delta {{W}^{n-1}}
\tag{5.1}
\label{eq:时间离散方程}
$$

其中：
$$
\Delta {{W}^{n}}={{W}^{n+1}}-{{W}^{n}}
\tag{5.2}
$$

$\Delta t$为时间步长，参数$\beta $和$\omega $决定了时间离散的方法（显/隐式、精度）。

## 显式RK推进方法

令参数$\beta =0$和$\omega =0$得到显示时间推进方法如下：
$$
\Delta {{W}^{n}}=-\frac{\Delta t}{\Omega }{{R}^{n}}
\tag{5.3}
\label{eq:显式时间推进半离散方程}
$$

对于如上的显式推进方法最常用的为多步Runge-Kutta方法（简称RK），计算过程如下：
$$
\left\{ \begin{array}{l}
{{W}^{(0)}}={{W}^{n}} \\ 
{{W}^{(1)}}={{W}^{(0)}}-{{\alpha }_{1}}\frac{\Delta t}{\Omega }{{R}^{(0)}} \\ 
{{W}^{(2)}}={{W}^{(0)}}-{{\alpha }_{2}}\frac{\Delta t}{\Omega }{{R}^{(1)}} \\ 
\cdots  \\ 
{{W}^{n+1}}={{W}^{(m)}}={{W}^{(0)}}-{{\alpha }_{m}}\frac{\Delta t}{\Omega }{{R}^{(m-1)}} \\ 
\end{array} \right.
\tag{5.4}
\label{eq:Runge-Kutta计算过程}
$$

其中，${{\alpha }_{1}}\cdots {{\alpha }_{m}}$为相应步骤的系数，${{R}^{(k)}}$为由${{W}^{(k)}}$计算得到的残差。
通过对${{\alpha }_{1}}\cdots {{\alpha }_{m}}$的设定可以使得格式达到二阶精度，下表给出了本软件采用的系数。

<p style="-typora-class:TabCnt; text-align:center;" >RK方法的迭代系数汇总</p>
| 精度   | 一阶精度 | 一阶精度 | 一阶精度 | 二阶精度 | 二阶精度 | 二阶精度 |
| ------ | -------- | -------- | -------- | -------- | -------- | -------- |
| 总步数 | 3        | 4        | 5        | 3        | 4        | 5        |
| CFL数  | 1.5      | 2.0      | 2.5      | 0.69     | 0.92     | 1.15     |
| $\alpha_1$ | 0.1481   | 0.0833   | 0.0533   | 0.1918   | 0.1084   | 0.0695   |
| $\alpha_2$ | 0.4000   | 0.2069   | 0.1263   | 0.4929   | 0.2602   | 0.1602   |
| $\alpha_3$ | 1.0000   | 0.4265   | 0.2375   | 1.0000   | 0.5052   | 0.2898   |
| $\alpha_4$ |          | 1.0000   | 0.4414   |          | 1.0000   | 0.5060   |
| $\alpha_5$ |          |          | 1.0000   |          |          | 1.0000   |

为了节省计算时间，本软件提供了混合RK方法。将所有残值分解成平均项和耗散项，如下：
$$
R={{R}_{avg}}+{{R}_{diff}}
\tag{5.5}
$$

其中，${{R}_{avg}}$包含对流项的平均部分和源项，${{R}_{diff}}$包含对流项的人工粘性部分和粘性项。在RK的第$0$步计算时将$R_{diff}^{(0)}$保存下来，以后的每步迭代直接采用$R_{diff}^{(0)}$而不再重新计算。
对于公式$\eqref{eq:显式时间推进半离散方程}$中的时间步长$\Delta t$计算如下：
$$
\Delta t=CFL\frac{\Omega }{{{\Lambda }_{C}}+C{{\Lambda }_{V}}}
\tag{5.6}
\label{eq:时间步长计算方法}
$$

其中，系数$C=4$，对流项谱半径${{\Lambda }_{C}}$的计算方法见（4.5）式，粘性项谱半径${{\Lambda }_{V}}$的计算方法如下：
$$
{{\Lambda }_{V}}=\frac{1}{\Omega }\sum\limits_{j=1}^{{{N}_{F}}}{\left[ \max \left( \frac{4}{3{{\rho }_{j}}},\frac{{{\gamma }_{j}}}{{{\rho }_{j}}} \right){{\left( \frac{{{\mu }_{L}}}{{{\Pr }_{L}}}+\frac{{{\mu }_{T}}}{{{\Pr }_{T}}} \right)}_{j}}|{{{\vec{S}}}_{j}}{{|}^{2}} \right]}
\tag{5.7}
$$

其中，下角标$j$表示面心值。

## 隐式LU-SGS推进方法

针对公式$\eqref{eq:时间离散方程}$，令参数$\beta \ne 0$和$\omega =0$得到一种隐式推进方法如下：
$$
\frac{\Omega }{\Delta t}\Delta {{W}^{n}}=-\beta {{R}^{n+1}}-(1-\beta ){{R}^{n}}
\tag{5.8}
\label{eq:通用隐式离散方程}
$$

对$n+1$步的残差进行如下线化处理：
$$
{{R}^{n+1}}\approx {{R}^{n}}+\left( \frac{\partial R}{\partial W} \right)\Delta {{W}^{n}}
\tag{5.9}
$$

带入公式$\eqref{eq:通用隐式离散方程}$，可得隐式方法如下：
$$
\left[ \frac{\Omega }{\Delta t}+\beta \left( \frac{\partial R}{\partial W} \right) \right]\Delta {{W}^{n}}=-{{R}^{n}}
\tag{5.10}
\label{eq:隐式离散方程}
$$

隐式上下三角对称高斯赛德尔格式（简称LU-SGS）是计算流体力学中常用的隐式推进方法。针对公式$\eqref{eq:隐式离散方程}$，该方法的隐式操作符分解成三个部分，推进格式如下：
$$
(D+L){{D}^{-1}}(D+U)\Delta {{W}^{n}}=-{{R}^{n}}
\tag{5.11}
$$

其中，$L$为下三角阵，$U$为上三角阵，$D$为对角阵。上述推进格式可以分解成两个推进过程：
$$
\left\{ \begin{aligned}
(D+L)\Delta {{W}^{*}}&=-{{R}^{n}} \\ 
(D+U)\Delta {{W}^{n}}&=D\Delta {{W}^{*}}
\end{aligned} \right.
\tag{5.12}
$$

引入大量的近似算法，上式一般写成如下推进形式：
$$
\left\{ \begin{aligned}
D\Delta {{W}^{*}}&=-{{R}^{n}}-L\Delta {{W}^{*}} \\ 
D\Delta {{W}^{n}}&=D\Delta {{W}^{*}}-U\Delta {{W}^{n}} \\ 
\end{aligned} \right.
\tag{5.13}
$$

通过近似算法，以单元$i$为例，上式具体写为：
$$
\left\{ \begin{aligned}
D\Delta W_{i}^{*}&=-R_{i}^{n}-\sum\limits_{j\in L(i)}{\tfrac{1}{2}\left[ {{(\Delta F_{C}^{*})}_{j}}{{S}_{ij}}+{{(r_{A}^{*})}_{j}}I\Delta W_{j}^{*} \right]} \\ 
D\Delta W_{i}^{n}&=D\Delta {{W}^{*}}-\sum\limits_{j\in U(i)}{\tfrac{1}{2}\left[ {{(\Delta F_{C}^{n})}_{j}}{{S}_{ij}}-{{(r_{A}^{*})}_{j}}I\Delta W_{j}^{n} \right]} \\ 
\end{aligned} \right.
\tag{5.14}
$$

其中，$L(i)$、$U(i)$表示与单元$i$相邻的归属下三角阵或上三角阵的单元集合，$S_{ij}$为单元$i$与$j$邻接面的面积，$\Delta {{F}_{C}}$表示相邻时间层对流项残值的差量，系数${{(r_{A}^{*})}_{j}}$计算如下：
$$
{{(r_{A}^{*})}_{j}}=\omega \left( {{{\vec{U}}}_{j}}\cdot {{{\vec{n}}}_{j}}+{{c}_{j}} \right){{S}_{ij}}+\frac{{{S}_{ij}}}{|{{{\vec{r}}}_{j}}-{{{\vec{r}}}_{i}}|}\left[ \max (\frac{4}{3{{\rho }_{j}}}+\frac{{{\gamma }_{j}}}{{{\rho }_{j}}}){{(\frac{{{\mu }_{L}}}{{{\Pr }_{L}}}+\frac{{{\mu }_{T}}}{{{\Pr }_{T}}})}_{j}} \right]
\tag{5.15}
$$

其中，$\omega $为松弛因子（可取2.0）。对角阵$D$计算如下：
$$
D=\frac{\Omega }{\Delta t}+\frac{\omega }{2}{{\Lambda }_{C}}+{{\Lambda }_{V}}-\frac{\partial (\Omega Q)}{\partial W}
\tag{5.16}
$$

其中，${{\Lambda }_{C}}$、${{\Lambda }_{V}}$分别为对流项和粘性项谱半径，$Q$为源项。
对于LU-SGS时间推进，时间步长可参照显式推进方法（要舍弃粘性项的贡献），对于定常计算$CFL$数可取${{10}^{4}}$至${{10}^{6}}$。

## 其他隐式方法

空间离散构成了残差$R$的差。

## 双时间推进方法

空间离散构成了残差$R$的差。

<div STYLE="page-break-after: always;"></div>
# 初始条件

本软件提供了多种流场初始化方法，常见的为采用来流参数进行初始化，其他还可选择从静止流场、从文件续算等方式。

<div STYLE="page-break-after: always;"></div>
# 边界条件

本软件提供了多种类型的边界条件，以满足各种计算需求。当物理量定义在网格点时（格点格式），直接按照边界类型确定格点值。当物理量定义在网格中心时（格心格式），本软件用虚拟网格处理边界（如图7.1所示，用脚标1代表邻接物面的空间网格，脚标0代表边界，脚标-1代表邻接物面的虚拟网格)。

<div align="center">
<img src="media/image-20240521092808770.png" alt="虚拟单元示意图" width="300">
</div>
<p style="-typora-class:FigCnt;text-align:center;" >虚拟单元示意图</p>
采用虚单元时，对于所有的边界可得物理量$\phi $的关系如下：
$$
{{\phi }_{0}}=0.5({{\phi }_{1}}+{{\phi }_{-1}})
\tag{7.1}
$$

## 物面边界

### 有滑移物面边界
对于Euler方程的物面边界，采用表面无穿透、绝热、法向零压梯度的有滑移边界条件。
速度在物体表面无穿透，即$U_{0}^{n}=0$，可得$U_{-1}^{n}=-U_{1}^{n}$，因此该边界速度关系如下：
$$
{{\vec{U}}_{-1}}={{\vec{U}}_{1}}-2U_{1}^{n}\vec{n}
\tag{7.2}
$$

其他物理量直接采用相邻单元值：
$$
\left\{ \begin{array}{l}
{{\rho }_{-1}}={{\rho }_{1}} \\ 
{{p}_{-1}}={{p}_{1}} \\ 
\end{array} \right.
\tag{7.3}
$$

### 无滑移物面边界

对于NS方程的物面边界，，采用表面无穿透、绝热、法向零压梯度的无滑移边界条件。速度在物体表面为零，即${{\vec{U}}_{0}}=0$。因此，该边界速度关系如下：
$$
\left\{ \begin{array}{l}
{{{\vec{U}}}_{-1}}=-{{{\vec{U}}}_{1}} \\ 
{{\rho }_{-1}}={{\rho }_{1}} \\ 
{{p}_{-1}}={{p}_{1}} \\ 
\end{array} \right.
\tag{7.4}
$$

## 对称面边界

对称面边界同Euler方程的物面边界物理性质一样，采用的边界条件方法也一样，详见7.1.1节。

## 远场边界

为了模拟物体绕流的真实状态，远场边界条件要求由物面发出的扰动波在有限远的边界上不反射，就好像这样的远场边界不存在一样。对于超音速流动，流入值取自由来流值，流出值由场内外插；对于亚音速及跨音速流动，采用如下的Riemann不变量处理（脚标$\infty $代表边界处的流场远场值，脚标$0$代表边界值，脚标$inner$代表边界处的与边界相邻的网格单元值）。
首先，计算不变量如下：
$$
\left\{ \begin{array}{l}
{{R}_{\infty }}={{{\vec{U}}}_{\infty }}\cdot \vec{n}-\frac{2{{c}_{\infty }}}{\gamma -1} \\ 
{{R}_{inner}}={{{\vec{U}}}_{inner}}\cdot \vec{n}+\frac{2{{c}_{inner}}}{\gamma -1} \\ 
\end{array} \right.
\tag{7.5}
$$

可得边界法向速度和音速如下：
$$
\left\{ \begin{array}{l}
{{{\vec{U}}}_{0}}\cdot \vec{n}=\frac{1}{2}({{R}_{\infty }}+{{R}_{inner}}) \\ 
{{c}_{0}}=\frac{\gamma -1}{4}({{R}_{inner}}-{{R}_{\infty }}) \\ 
\end{array} \right.
\tag{7.6}
$$

根据出入流条件计算切向速度和墒值：
$$
\left\{ \begin{array}{l}
{{{\vec{V}}}_{0}}\cdot \vec{n}<0(){{S}_{0}}=\frac{\rho _{\infty }^{\gamma }}{{{p}_{\infty }}},\vec{U}_{0}^{\tau }=\vec{U}_{\infty }^{\tau } \\ 
{{{\vec{V}}}_{0}}\cdot \vec{n}>0(){{S}_{0}}=\frac{\rho _{inner}^{\gamma }}{{{p}_{inner}}},\vec{U}_{0}^{\tau }=\vec{U}_{inner}^{\tau } \\ 
\end{array} \right.
\tag{7.7}
$$

可得边界处其他物理量如下：
$$
\left\{ \begin{array}{l}
\vec{U}_{0}^{{}}=\vec{U}_{0}^{\tau }+\vec{U}_{0}^{n} \\ 
{{\rho }_{0}}={{\left( \frac{c_{0}^{2}{{S}_{0}}}{\gamma } \right)}^{\frac{1}{\gamma -1}}} \\ 
{{p}_{0}}=\frac{c_{0}^{2}{{\rho }_{0}}}{\gamma } \\ 
\end{array} \right.
\tag{7.8}
$$

## 其他边界

无。

<div STYLE="page-break-after: always;"></div>
# 湍流模型
对于Favre加权平均的RANS方程$\eqref{eq:Favre加权平均的RANS方程}$，湍流脉动引起的雷诺应力和湍流热通量表达如下：
$$
\eqalign{
\tau _{ij}^T & =  - \bar \rho \overline {{{u'}_i}{{u'}_j}} \\ 
q_j^T & =  - \bar \rho \overline {{{u'}_i}T'}  \cr}
\tag{8.1}
$$

不同湍流模型对于这两项的处理方法不同，下具体介绍。本软件的主要湍流列表见附录X，下面介绍几种常见的湍流模型。
所有的关于湍流量X的微分方程都可以写成如下的统一形式：
$$
\frac{\partial \rho X}{\partial t}+\frac{\partial \rho {{u}_{j}}X}{\partial {{x}_{j}}}={{S}_{P}}+{{S}_{D}}+D
\tag{8.2}
\label{eq:通用湍流量输运方程}
$$

其中，$S_P$表示“产生”源项，$S_D$表示“破坏”源项，$S_P$和$S_D$有时候会显式或者隐式的一起出现。$D$表示扩散项，其形式为$\frac{\partial }{\partial {{x}_{j}}}\left[ \left( {} \right)\cdot \frac{\partial X}{\partial {{x}_{j}}} \right]$，有的方程中$D$还包含交叉扩散项，我们这里将$D$分解成主扩散项和交叉扩散项：
$$
D={{D}_{main}}+{{D}_{cross}}=\frac{\partial }{\partial {{x}_{j}}}\left[ \left( {} \right)\cdot \frac{\partial {{X}_{1}}}{\partial {{x}_{j}}} \right]+\frac{\partial {{X}_{1}}_{\grave{\ }}}{\partial {{x}_{j}}}\frac{\partial {{X}_{2}}}{\partial {{x}_{j}}}
\tag{8.3}
$$

其中，湍流模型计算时常用到的应变率张量$S_{ij}$和旋度率张量$\Omega_{ij}$如下：
$$
{{S}_{ij}}=\frac{1}{2}\left( \frac{\partial {{u}_{i}}}{\partial {{x}_{j}}}+\frac{\partial {{u}_{j}}}{\partial {{x}_{i}}} \right)
\tag{8.4}
\label{eq:应变率张量}
$$

$$
{{\Omega }_{ij}}=\frac{1}{2}\left( \frac{\partial {{u}_{i}}}{\partial {{x}_{j}}}-\frac{\partial {{u}_{j}}}{\partial {{x}_{i}}} \right) \tag{8.5}
$$

## Spalart-Allmaras模型

Spalart-Allmaras模型是从经验和量纲分析出发，针对简单流动再逐渐补充发展而适用于带有层流流动的固壁湍流的一方程模式，在航空领域计算中被大量采用。该方程求解的湍流量为动粘性系数$\hat{\nu }$，方程形式参见$\eqref{eq:通用湍流量输运方程}$式，具体如下：
$$
\frac{\partial \rho \hat{\nu }}{\partial t}+\frac{\partial \rho {{u}_{j}}\hat{\nu }}{\partial {{x}_{j}}}={{S}_{P}}+{{S}_{D}}+D
\tag{8.6}
\label{eq:SA模型输运方程}
$$

湍流涡粘性系数计算如下：
$$
{{\mu }_{T}}=\rho \hat{\nu }{{f}_{v1}}
\tag{8.7}
$$

其中：
$$
\left\{
\begin{aligned}
 & {{f}_{v1}}=\frac{{{\chi }^{3}}}{{{\chi }^{3}}+C_{v1}^{3}} \\ 
 & \chi =\frac{{\hat{\nu }}}{{{\nu }_{L}}} \\ 
\end{aligned} \right.
\tag{8.8}
$$

$ \eqref{eq:SA模型输运方程} $式的右端各项计算如下：
$$
\left\{ \begin{aligned}
  & {{S}_{P}}={{C}_{b1}}(1-{{f}_{t2}})\hat{S}\rho \hat{\nu } \\ 
 & {{S}_{D}}=\rho \left[ \frac{{{C}_{b1}}}{{{\kappa }^{2}}}{{f}_{t2}}-{{C}_{w1}}{{f}_{w}} \right]{{\left( \frac{{\hat{\nu }}}{d} \right)}^{2}} \\ 
 & D=\frac{1}{\sigma }\frac{\partial }{\partial {{x}_{j}}}\left[ \left( \rho {{\nu }_{L}}+\rho \hat{\nu } \right)\frac{\partial \hat{\nu }}{\partial {{x}_{j}}} \right]+\frac{\rho {{C}_{b2}}}{\sigma }\frac{\partial \hat{\nu }}{\partial x_{j}^{{}}}\frac{\partial \hat{\nu }}{\partial x_{j}^{{}}} \\ 
\end{aligned} \right.
\tag{8.9}
$$

其中，相关项计算如下：
$$
\left\{ \begin{aligned}
  & {{f}_{v2}}=1-\frac{\chi }{1+\chi {{f}_{v1}}} \\ 
 & \hat{S}=\Omega +\frac{\hat{\nu }{{f}_{v2}}}{{{\kappa }^{2}}{{d}^{2}}} \\ 
 & {{f}_{w}}=g{{\left( \frac{1+C_{w3}^{6}}{{{g}^{6}}+C_{w3}^{6}} \right)}^{\frac{1}{6}}} \\ 
 & g=r+{{C}_{w2}}({{r}^{6}}-r) \\ 
 & r=\frac{{\hat{\nu }}}{\hat{S}{{\kappa }^{2}}{{d}^{2}}} \\ 
\end{aligned} \right.
\tag{8.10}
$$

其中，$\Omega$为旋度张量的模，计算如下：
$$
\Omega =\sqrt{2{{\Omega }_{ij}}{{\Omega }_{ij}}}
\tag{8.11}
$$

该模型的相关系数如下：
$$
\begin{array}{*{35}{l}}
   {{C}_{b1}}=0.1355 & {{C}_{b2}}=0.622 & {} & {}  \\
   {{C}_{w1}}=\frac{{{C}_{b1}}}{{{\kappa }^{2}}}+\frac{(1+{{C}_{b2}})}{\sigma } & {{C}_{w2}}=0.3 & {{C}_{w3}}=2.0 & {}  \\
   {{C}_{v1}}=7.1 & \sigma =\tfrac{2}{3} & \kappa =0.41 & {}  \\
\end{array}
\tag{8.12}
$$

## Menter SST模型

该模型求解两个湍流量：湍动能$k$和耗散率$\omega$。其输运方程如下：
$$
\left\{ \begin{matrix}
   \frac{\partial \rho k}{\partial t}+\frac{\partial \rho {{u}_{j}}k}{\partial {{x}_{j}}}=S_{P}^{k}+S_{D}^{k}+D_{main}^{k}  \\
   \frac{\partial \rho \omega }{\partial t}+\frac{\partial \rho {{u}_{j}}\omega }{\partial {{x}_{j}}}=S_{P}^{\omega }+S_{D}^{\omega }+D_{main}^{\omega }+D_{cross}^{\omega }  \\
\end{matrix} \right.
\tag{8.13}
$$

该模型的的涡粘性系数计算如下：
$$
{{\mu }_{T}}=\frac{{{a}_{1}}\rho k}{\max \left\{ {{a}_{1}}\omega ,{{f}_{2}}\Omega  \right\}}
\tag{8.14}
$$

该模型的右端各项计算如下：
$$
\left\{ \begin{aligned}
S_{P}^{k} & =\tau _{ij}^{F}{{S}_{ij}} \\ 
S_{D}^{k} & =-{{\beta }^{*}}\rho \omega k \\ 
D_{main}^{k} & =\frac{\partial }{\partial {{x}_{j}}}\left( ({{\mu }_{L}}+{{\sigma }_{k}}{{\mu }_{T}})\frac{\partial k}{\partial {{x}_{j}}} \right) \\ 
S_{P}^{\omega } & =\frac{{{C}_{\omega }}\rho }{{{\mu }_{T}}}S_{P}^{k} \\ 
S_{D}^{\omega } & =-\beta \rho {{\omega }^{2}} \\ 
D_{main}^{\omega } & =\frac{\partial }{\partial {{x}_{j}}}\left( ({{\mu }_{L}}+{{\sigma }_{\omega }}{{\mu }_{T}})\frac{\partial \omega }{\partial {{x}_{j}}} \right) \\ 
D_{cross}^{\omega } & =2(1-{{f}_{1}})\frac{\rho {{\sigma }_{\omega 2}}}{\omega }\frac{\partial k}{\partial {{x}_{j}}}\frac{\partial \omega }{\partial {{x}_{j}}} \\ 
\end{aligned} \right.
\tag{8.15}
$$

模型参数用表示，并用和分别表示原始模式系数和转化后模式系数，计算关系如下：
$$
\varphi ={{f}_{1}}{{\varphi }_{1}}+(1-{{f}_{1}}){{\varphi }_{2}}
\tag{8.16}
$$

内层模式系数为：
$$
{{\sigma }_{k1}}=0.85\begin{matrix}
   {} & {{\sigma }_{\omega 1}}=0.5\begin{matrix}
   {} & {{\beta }_{1}}=0.075\begin{matrix}
   {} & {{C}_{\omega 1}}=0.533  \\
\end{matrix}  \\
\end{matrix}  \\
\end{matrix}
\tag{8.17}
$$

外层模式系数为：
$$
{{\sigma }_{k2}}=1.0\begin{matrix}
   {} & {{\sigma }_{\omega 2}}=0.856\begin{matrix}
   {} & {{\beta }_{2}}=0.0828\begin{matrix}
   {} & {{C}_{\omega 2}}=0.440  \\
\end{matrix}  \\
\end{matrix}  \\
\end{matrix}
\tag{8.18}
$$

调和函数计算如下：
$$
\left\{ \begin{aligned}
{{f}_{1}}  & =\tanh ({{\Gamma }^{4}}) \\ 
\Gamma & =\min \left[ \max \left( {{\Gamma }_{1}},{{\Gamma }_{2}} \right),\frac{4\rho {{\sigma }_{\omega 2}}k}{C{{D}_{k\omega }}{{d}^{2}}} \right] \\ 
{{\Gamma }_{1}} & =\frac{\sqrt{k}}{0.09\omega d} \\ 
{{\Gamma }_{2}} & =\frac{500{{\mu }_{L}}}{\rho \omega {{d}^{2}}} \\ 
C{{D}_{k\omega }} & =\max \left( \frac{2\rho {{\sigma }_{\omega 2}}}{\omega }\frac{\partial k}{\partial {{x}_{j}}}\frac{\partial \omega }{\partial {{x}_{j}}},{{10}^{-20}} \right) \\ 
\end{aligned} \right.
\tag{8.19}
$$

调和函数计算如下：
$$
\left\{ \begin{aligned}
{{f}_{2}}  & =\tanh ({{\Pi }^{2}}) \\ 
\Pi & = \max \left( 2{{\Gamma }_{1}},{{\Gamma }_{2}} \right) \\ 
\end{aligned} \right.
\tag{8.20}
$$

其它相关系数有：
$$
{{\beta }^{*}}=0.09\begin{matrix}
   {} & {{a}_{1}}=0.31\begin{matrix}
   {} & {}  \\
\end{matrix}  \\
\end{matrix}
\tag{8.21}
$$

## 湍流模型的离散方法

湍流方程的扩散项离散方式与主流的粘性项离散方式相同，时间推进方法与主流相同并同步进行。下面介绍湍流方程的对流项和源项的处理方式。

### 湍流方程的对流项离散方法

湍流方程的对流项离散采用简单的迎风格式。任意湍流量X的对流项$\frac{\partial \rho {{u}_{j}}X}{\partial {{x}_{j}}}$，对于某个面j其对流项计算如下：
$$
F_{X}^{j}={{(\rho \overset{\scriptscriptstyle\rightharpoonup}{U}X)}_{j}}\cdot {{\overset{\scriptscriptstyle\rightharpoonup}{S}}_{j}}
\tag{8.22}
$$

计算速度的面通量：
$$
{{F}_{U}}={{(\overset{\scriptscriptstyle\rightharpoonup}{U})}_{j}}\cdot {{\overset{\scriptscriptstyle\rightharpoonup}{S}}_{j}}=0.5({{\overset{\scriptscriptstyle\rightharpoonup}{U}}_{L}}+{{\overset{\scriptscriptstyle\rightharpoonup}{U}}_{R}})\cdot {{\overset{\scriptscriptstyle\rightharpoonup}{S}}_{j}}
\tag{8.23}
$$

则湍流对流通量计算如下：
$$
F_{X}^{j}=0.5\left[ {{(\rho X)}_{L}}({{F}_{U}}+|{{F}_{U}}|)+{{(\rho X)}_{R}}({{F}_{U}}-|{{F}_{U}}|) \right]
\tag{8.24}
$$

其中，左右面值可以根据精度采用一阶或者二阶重构，方法同主流的对流项。

### 湍流方程的源项处理方法

湍流方程的源项直接采用体积积分。为了保障格式的稳定性和加速收敛，对湍流方程中的负源项采用线隐式加速方法，即对负源项求导数后加入方程的左端增加方程的对角占优特性。

## 湍流模型的边界条件与初始条件

### 湍流量的物面边界
对于Spalart-Allmaras模型的动粘性系数$\hat{\nu }$，在物面处：
$$
{{\hat{\nu }}_{w}}=0
\tag{8.25}
$$

对于湍动能$k$，在物面处：
$$
{{k}_{w}}=0
\tag{8.26}
$$

对于耗散率$\omega$，在物面处：
$$
{{\omega }_{w}}=\frac{60{{\mu }_{L}}}{0.075\rho d_{1}^{2}}
\tag{827}
$$

其中，${{d}_{1}}$表示第一层网格中心到物面的距离。

### 湍流量的远场边界

对于Spalart-Allmaras模型的动粘性系数$\hat{\nu }$，给定涡粘性比$R=10$，则在远场处：
$$
{{\hat{\nu }}_{\infty }}=10{{\nu }_{L\infty }}=10\frac{{{\mu }_{L\infty }}}{{{\rho }_{\infty }}}
\tag{8.28}
$$

对于湍动能$k$，在远场处给定来流速度和湍流度，计算脉动速度：
$$
U{{'}_{\infty }}={{U}_{\infty }}T{{u}_{\infty }}
\tag{8.29}
$$

则远场的湍动能计算如下：
$$
{{k}_{\infty }}=1.5 {U'}_{\infty }^{2}
\tag{8.30}
$$

对于耗散率$ \omega $，给定远场涡粘性比$R=1$，则在远场处湍流粘性系数：
$$
{{\mu }_{T\infty }}={{\mu }_{L\infty }}
\tag{8.31}
$$

则远场的耗散率计算如下：
$$
{{\omega }_{\infty }}=\frac{{{\rho }_{\infty }}{{k}_{\infty }}}{{{\mu }_{T\infty }}}
\tag{8.32}
$$

### 湍流量的初始条件

所有湍流量的初始条件取远场值。

<div STYLE="page-break-after: always;"></div>
# 加速技术与提高稳定性的措施

## 当地时间步长
对于定常计算，通过采用$CFL$数约束下的当地时间步长来加速收敛，$\eqref{eq:时间步长计算方法}$式给出了非结构网格的当地时间步长计算方式。在采用当地时间步长时，每一个时刻的瞬态解就没有实际的物理意义了。

## 残值光顺

残值光顺对于显式格式能提供隐式特性来加速收敛，因此能显著提升$CFL$数的最大值。同时，残值光顺能提高对于高频误差的衰减作用，在多重网格技术中也有重要的应用。本软件主要采用了隐式残值光顺技术，所有的物理量（如多重网格中的修正量）及其残值都采用了相同的残值光顺技术。
对于任意单元$I$，其原始残值为${{R}_{I}}$及光顺后的残值${{\hat{R}}_{I}}$，有：
$$
{{\hat{R}}_{I}}={{R}_{I}}+\varepsilon \sum\limits_{j=1}^{{{N}_{A}}}{({{w}_{j}}{{{\hat{R}}}_{j}}-{{{\hat{R}}}_{I}})}
\tag{9.1}
$$

其中，$\varepsilon $为光顺系数（可取$0.8$），$N_A$为与单元I相邻的单元数量，${{w}_{j}}$为权重系数。上式可以改写为：
$$
(1+{{N}_{A}}\varepsilon ){{\hat{R}}_{I}}-\varepsilon \sum\limits_{j=1}^{{{N}_{A}}}{{{w}_{j}}{{{\hat{R}}}_{j}}}={{R}_{I}}
\tag{9.2}
$$

对于非结构网格，上式形成一个大型的对角占优稀疏矩阵，可采用两次Jacobi迭代计算完成：
$$
\hat{R}_{I}^{n+1}=\frac{R_{I}^{0}+\varepsilon \sum\limits_{j=1}^{{{N}_{A}}}{{{w}_{j}}\hat{R}_{j}^{n}}}{1+{{N}_{A}}\varepsilon }
\tag{9.3}
$$

对于权重系数${{w}_{j}}$可以取$1$。当网格拉伸比较大时，考虑不同方向的残值贡献可采用距离与面积权重方式：
$$
\left\{ \begin{aligned}
 & {{w}_{j}}={{{N}_{A}}{{\varphi }_{j}}}/{\sum\limits_{j=1}^{{{N}_{A}}}{{{\varphi }_{j}}}}\; \\ 
 & {{\varphi }_{j}}={S_{Ij}^{2}}/{\left| {{{\overset{\scriptscriptstyle\rightharpoonup}{r}}}_{j}}-{{{\overset{\scriptscriptstyle\rightharpoonup}{r}}}_{I}} \right|}\; \\ 
\end{aligned} \right.
\tag{9.4}
$$

其中，$S_{Ij}^{{}}$表示单元$I$和单元$j$交界面的面积。

## 多重网格技术

多重网格技术是加快流场收敛速度的重要措施，可用于所有的时间推进方法。多重网格技术分为代数多重网格技术和几何多重网格技术，本软件采用几何多重网格技术。多重网格技术针对粗细不同的网格序列，通过在细网格上消除高频残差、在粗网格上消除低频残差来达到加速收敛的目的。

### 网格序列与粗网格

首先，多重网格技术要建立一个网格序列${{G}_{0}},{{G}_{1}},\cdots ,{{G}_{N}}$，其中${{G}_{0}}$表示最细的网格层次，${{G}_{1}},\cdots ,{{G}_{N}}$为通过网格控制体聚合得到的逐渐粗化的网格。网格聚合在前处理阶段完成，具体聚合方法可参考前处理部分。下图给出了网格聚合得到的网格序列示意图。
<div align="center">
<img src="./media/07e7f039cf305f7a29df5f2ef762d966.png" alt="描述文字" height="200">
<img src="./media/071abf611327d24714df87b6ea6a4150.png" alt="描述文字" height="200">
<img src="./media/850eae7a2d883e6733e8dd03f9f46a08.png" alt="描述文字" height="200">
</div>
<p style="-typora-class:FigCnt;text-align:center;" >网格聚合形成的网格序列(G0、G1、G2)</p>
### 多重网格循环的基本计算方法

多重网格循环主要由三个基本操作构成：本层网格的流场求解、从细网格到粗网格的限制、从粗网格到细网格的延拓。下面使用角标$h$表示细网格物理量、角标$2h$表示粗网格物理量来说明具体操作方法。
在细网格$h$上有如下离散方程：
$$
\frac{d}{dt}{{W}_{h}}=-\frac{1}{{{\Omega }_{h}}}{{R}_{h}}
\tag{9.4}
$$

其上一步的解为$W_{h}^{n}$，通过求解以上方程得到细网格上的解$W_{h}^{n+1}$后，通过残值的重新计算可以得到一个新残值$R_{h}^{n+1}$，下面通过多重网格技术来修正$W_{h}^{n+1}$获得$(W_{h}^{n+1})^*$使得其加速收敛。
首先，将细网格上的解和残值限制（插值）到粗网格：
$$
\left\{ \begin{aligned}
W_{2h}^{(0)} & =I_{h}^{2h}W_{h}^{n+1} \\ 
\tilde{R}_{2h}^{(0)} & =I_{h}^{2h}R_{h}^{n+1} \\ 
\end{aligned} \right.
\tag{9.5}
$$

其中，$I_{h}^{2h}$表示细网格到粗网格的限制插值算子。残值限制到粗网格保证了其遗传了细网格的精度。
然后，利用$W_{2h}^{(0)}$计算残值得到粗网格的初始残值$R_{2h}^{(0)}$，得到粗网格上的力源项：
$$
{{({{Q}_{F}})}_{2h}}=\tilde{R}_{2h}^{(0)}-R_{2h}^{(0)}
\tag{9.6}
$$

再次，完成粗网格流场的计算。在粗网格的残值计算中，力源项需要累加到残值中。以显式RK推进方法为例，对于$\eqref{eq:Runge-Kutta计算过程}$式将残值加上力源项即可：
$$
\left\{ \begin{aligned}
W_{2h}^{(0)} & =I_{h}^{2h}W_{h}^{n+1} \\ 
W_{2h}^{(1)} & =W_{2h}^{(0)}-{{\alpha }_{1}}\frac{\Delta t}{{{\Omega }_{2h}}}\left[ R_{2h}^{(0)}+{{({{Q}_{F}})}_{2h}} \right] \\ 
 & \cdots \\ 
W_{2h}^{n+1} & =W_{2h}^{(m)}=W_{2h}^{(0)}-{{\alpha }_{m}}\frac{\Delta t}{{{\Omega }_{2h}}}\left[ R_{2h}^{(m-1)}+{{({{Q}_{F}})}_{2h}} \right] \\ 
\end{aligned} \right.
\tag{9.7}
$$
可以看到，在第0步时粗网格上的残值完全来自细网格，保证了来自细网格的格式精度，因此粗网格上空间离散时可以采取低阶精度以节省计算时间和提升鲁棒性。

最后，将粗网格解的变化量延拓到细网格来修正细网格的解。在粗网格上可得如下变化量：
$$
 \delta {{W}_{2h}}=W_{2h}^{n+1}-W_{2h}^{(0)}
 \tag{9.8}
$$

将$\delta {{W}_{2h}}$延拓到细网格：
$$
\delta {{W}_{h}}=I_{2h}^{h}\delta {{W}_{2h}}
\tag{9.9}
$$

其中，$I_{2h}^{h}$为粗网格到细网格的延拓算子。
因此，得到细网格解的修正值：
$$
(W_{h}^{n+1})*=W_{h}^{n+1}+\delta {{W}_{h}}
\tag{9.10}
$$

在延拓的过程中，对于 $\delta {{W}_{2h}}$和$\delta {{W}_{h}}$等修正量可以采用残值光顺技术来处理来提升鲁棒性。

### 多重网格循环类型

前面给出了只存在一层粗网格的基本多重网格循环过程，可用下图来描述。

<div align="center"><img src="media/image-20240521082813694.png" alt="两个网格序列的多重网格循环" width="400"></div>
<p style="-typora-class:FigCnt;text-align:center;" >两个网格序列的多重网格循环</p>
其中，↘表示限制过程，↗表示延拓过程，●表示限制之前在本层网格需要推进迭代的次数，○表示延拓之后再本层网格需要推进迭代的次数。对于显式RK推进，可取（●,○）=（1,0），对于LU-SGS等隐式方法，可取（●,○）=（2,1）。

随着粗网格层数的增加，多重网格循环路径的形式多样。本软件提供了V、W两种循环形式，其典型循环路径如下：

<div align="center"><img src="media/image-20240521084743345.png" alt="多层网格序列的多重网格循环" width="600"></div>
<p style="-typora-class:FigCnt;text-align:center;" >多层网格序列的多重网格循环</p>
### 限制算子

对于流场物理量$W$，细网格到粗网格的限制算子$I_{h}^{2h}$如下：
$$
 I_{h}^{2h}{{W}_{h}}=\frac{\sum{{{\Omega }_{h}}{{W}_{h}}}}{\sum{{{\Omega }_{h}}}}
\tag{9.11}
$$

其中，$\Sigma $表示构成粗网格的所有细网格单元求和。
对于残值R，细网格到粗网格的限制算子$I_{h}^{2h}$如下：
$$
I_{h}^{2h}{{R}_{h}}=\sum{{{R}_{h}}}
\tag{9.12}
$$

上述限制算子采用的方法称为注入方法，具有一阶精度。

### 延拓算子
将粗网格变化量$\delta {{W}_{2h}}$延拓到细网格的算子$I_{2h}^{h}$如下：
$$
I_{2h}^{h}\delta {{W}_{2h}}=\delta {{W}_{2h}}
\tag{9.13}
$$

即细网格的变化量等于对应粗网格的变化量。
上述延拓算子采用的方法也称为注入方法，具有一阶精度。

### 全多重网格技术

前面的多重网格技术都是从细网格层次开始流场计算的。因多重网格聚合时粗网格单元数下降很快，可以从粗网格开始流场计算很快得到预估解，将其采用延拓算子延拓到细网格上作为初始流场，这样可以提升细网格的收敛速度。这种从粗网格开始流场计算并采用多重网格的计算方法称为全多重网格技术。其中，粗网格流场迭代次数可以大幅降低，不用完全收敛仅得到一个较粗糙的预估解就可以了。

## 点隐式技术（负源项的处理）

当采用显式方法时间推进时，如果源项变化量很大时（如湍流方程、化学反应方程等），流场计算为了保持稳定性需要降低$CFL$数，有时还可能导致计算发散。为了提升计算的稳定性，可对负源项采用隐式方法来处理，提升方程的对角占优特性，增强稳定性和加速收敛。
对于负源项，我们进行如下处理：
$$
{{Q}^{n+1}}\approx {{Q}^{n}}+\frac{\partial Q}{\partial W}\Delta {{W}^{n}}
\tag{9.14}
$$

针对$ \eqref{eq:显式时间推进半离散方程} $式，将${{Q}^{n}}$保留在方程右端，将$\frac{\partial Q}{\partial W}\Delta {{W}^{n}}$移到方程左端，可得：
$$
\left[ \frac{I}{\Delta t}-\frac{\partial Q}{\partial W} \right]\Delta {{W}^{n}}=-\frac{1}{\Omega }\left[ {{R}^{n}}({{\text{Q}}^{\text{n}}}) \right]
\tag{9.15}
$$

对于负源项，$\frac{\partial Q}{\partial W}<0$增加了方程的对角占优特性。本软件的湍流方程求解采用了这种技术。

## 低马赫数预处理

当来流速度较低（$Ma<0.2$）时，空气的压缩性已经可以忽略，对流项导致方程呈现刚性特征，计算稳定性大幅下降。除了转为不可压缩方程采用压力基方法进行求解之外，我们可以对密度基方程采用低马赫数预处理提升方程的稳定性，从而得到收敛解。
对于$ \eqref{eq:控制方程} $式积分方程进行如下处理：
$$
\frac{\partial }{\partial t}\int_{\Omega }{Wd}\Omega +P{{\Gamma }^{-1}}\oint_{\partial \Omega }{\left( {{F}_{C}}-{{F}_{V}} \right)}\cdot d\overset{\scriptscriptstyle\rightharpoonup}{S}=P{{\Gamma }^{-1}}\int_{\Omega }{Qd}\Omega
\tag{9.16}
\label{eq:预处理控制方程}
$$

其中，$P$和$\Gamma $为预处理矩阵。上述两个方程的解相同，但是该方程的特征值已经不同于原始方程。与特征值相关的对流谱半径、粘性谱半径、当地时间步长、迎风格式、远场边界条件等都要进行相应处理。
预处理矩阵$P$及其逆阵如下：
$$
 P=\left[ \begin{matrix}
  {\rho }/{p}\; & 0 & 0 & 0 & {-\rho }/{T}\; \\
  {\rho u}/{p}\; & \rho & 0 & 0 & {-\rho u}/{T}\; \\
  {\rho v}/{p}\; & 0 & \rho & 0 & {-\rho v}/{T}\; \\
  {\rho w}/{p}\; & 0 & 0 & \rho & {-\rho w}/{T}\; \\
  {\rho E}/{p}\; & \rho u & \rho v & \rho w & {-\rho {{q}^{2}}}/{(2T)}\; \\
\end{matrix} \right]
\tag{9.17}
$$

$$
{{P}^{-1}}=\left[ \begin{matrix}
  (\gamma -1)\tfrac{{{q}^{2}}}{2} & (1-\gamma )u & (1-\gamma )v & (1-\gamma )w & \gamma -1 \\
  {-u}/{\rho }\; & {1}/{\rho }\; & 0 & 0 & 0 \\
  {-v}/{\rho }\; & 0 & {1}/{\rho }\; & 0 & 0 \\
  {-w}/{\rho }\; & 0 & 0 & {1}/{\rho }\; & 0 \\
  \tfrac{1}{\rho }(\tfrac{\gamma {{q}^{2}}}{2{{c}_{p}}}-T) & -\tfrac{\gamma u}{\rho {{c}_{p}}} & -\tfrac{\gamma v}{\rho {{c}_{p}}} & -\tfrac{\gamma w}{\rho {{c}_{p}}} & \tfrac{\gamma }{\rho {{c}_{p}}} \\
\end{matrix} \right]
\tag{9.18}
$$

预处理矩阵$\Gamma $及其逆阵如下：
$$
\Gamma =\left[ \begin{matrix}
  \theta & 0 & 0 & 0 & {-\rho }/{T}\; \\
  \theta u & \rho & 0 & 0 & {-\rho u}/{T}\; \\
  \theta v & 0 & \rho & 0 & {-\rho v}/{T}\; \\
  \theta w & 0 & 0 & \rho & {-\rho w}/{T}\; \\
  \theta H-1 & \rho u & \rho v & \rho w & {-\rho {{q}^{2}}}/{(2T)}\; \\
\end{matrix} \right]
\tag{9.19}
$$

$$
{{\Gamma }^{-1}}=\left[ \begin{matrix}
  {{a}_{2}}\left[ \tfrac{{{c}^{2}}}{\gamma -1}-(H-{{q}^{2}}) \right] & -{{a}_{2}}u & -{{a}_{2}}v & -{{a}_{2}}w & {{a}_{2}} \\
  {-u}/{\rho }\; & {1}/{\rho }\; & 0 & 0 & 0 \\
  {-v}/{\rho }\; & 0 & {1}/{\rho }\; & 0 & 0 \\
  {-w}/{\rho }\; & 0 & 0 & {1}/{\rho }\; & 0 \\
  {{a}_{3}}\left[ 1-\theta (H-{{q}^{2}}) \right] & -\theta {{a}_{3}}u & -\theta {{a}_{3}}v & -\theta {{a}_{3}}w & \theta {{a}_{3}} \\
\end{matrix} \right]
\tag{9.20}
$$

其中，相关参数如下：
$$
\left\{ \begin{aligned}
{{a}_{2}} & =(\gamma -1)\phi \\ 
{{a}_{3}} & =\frac{(\gamma -1)\phi T}{\rho } \\ 
\phi & =\frac{1}{\theta {{c}^{2}}-(\gamma -1)} \\ 
\theta & ={1}/{(\beta {{c}^{2}})}\; \\ 
\beta & =\frac{M_{r}^{2}}{1+(\gamma -1)M_{r}^{2}} \\ 
M_{r}^{2} & =\max \{\min \{{{M}^{2}},1\},M_{\min }^{2}\} \\ 
\end{aligned} \right.
\tag{9.21}
$$

其中，$M$表示当地马赫数，$M_{\min }^{2}=KM_{\infty }^{2}$（$K=3$）.
式$ \eqref{eq:预处理控制方程} $的特征值如下：
$$
{{\Lambda }_{C}}=\left[ \begin{matrix}
  V & 0 & 0 & 0 & 0 \\
  0 & V & 0 & 0 & 0 \\
  0 & 0 & V & 0 & 0 \\
  0 & 0 & 0 & \frac{{{a}_{4}}+1}{2}V+c' & 0 \\
  0 & 0 & 0 & 0 & \frac{{{a}_{4}}+1}{2}V-c' \\
\end{matrix} \right]
\tag{9.22}
$$

其中，$V$为原始的逆变速度。修正后音速计算如下：
$$
c'=\tfrac{1}{2}\sqrt{{{V}^{2}}{{({{a}_{4}}-1)}^{2}}+4{{a}_{5}}}
\tag{9.23}
$$

其中：${{a}_{4}}=\phi $，${{a}_{5}}=\phi {{c}^{2}}$。

<div STYLE="page-break-after: always;"></div>
# 前处理技术

<div STYLE="page-break-after: always;"></div>
# 附录一. XXXX