﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Ensight.h
//! <AUTHOR>
//! @brief 用于生成Ensight后处理格式文件的类
//! @date 2021-04-13
//
//------------------------------修改日志----------------------------------------
// 2021-04-13 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _basic_postTools_Ensight_
#define _basic_postTools_Ensight_

#include "basic/postTools/BasePost.h"

/**
 * @brief 后处理命名空间
 * 
 */
namespace Post
{

/**
 * @brief Ensight格式文件输出类
 * 
 */
class  Ensight: public BasePost
{
public:    
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 网格指针
     * @param[in] dualFlag_ 对偶标识
     * @param[in] outputPosition_ 物理量输出位置
     * @param[in] caseName_ 模型名称
     */
    Ensight(Mesh *mesh_,
            const bool dualFlag_ = false,
			const Position outputPosition_ = Position::CELL_CENTER,
			const bool exportInteriorFlag_ = false,
            const std::string caseName_ = "");

    /**
     * @brief 生成Ensight格式文件
     * 根据是否包含场生成纯网格文件或包含场的文件
     * 
     */
    void WriteFile();    

protected:
    /**
     * @brief 生成.case文件
     * 
     */
    void WriteCase();

    /**
     * @brief 生成.geo文件
     * 
     */
    void WriteGeo();

    /**
     * @brief 生成标量场的相关文件
     * 
     */
    void WriteScalarField();

    /**
     * @brief 生成矢量场的相关文件
     * 
     */
    void WriteVectorField();

    /**
     * @brief 生成张量场的相关文件
     * 
     */
    void WriteTensorField();

private:
    /**
     * @brief 写体网格的坐标
     * 
     * @param[in] file 文件对象
     */
    void WriteVolumeCoordinate(std::fstream &file);

    /**
     * @brief 写体网格的单元构成关系
     * 
     * @param[in] file 文件对象
     */
    void WriteVolumeElement(std::fstream &file);

    /**
     * @brief 写边界网格
     * 
     * @param[in] patchID 边界编号
     * @param[in] file 文件对象
     */
    void WriteBoundaryMesh(const int &patchID, std::fstream &file);

private:
    /**
     * @brief 字符串写入文件
     * 
     * @param[in] file 文件对象
     * @param[in] stringTemp 待输出字符串
     */
    void WriteString(std::fstream &file, const std::string &stringTemp);

    /**
     * @brief 获取体单元类型
     * 
     * @return std::string 
     */
    std::string GetVolumeElementType();

    /**
     * @brief 获取边界单元类型
     * 
     * @return std::string 
     */
    std::string GetBoundaryElementType();

private:
    /// Ensight二进制文件要求每行80个字符
    const int numChar;

    /// 边界点容器
    std::vector<std::vector<int>> boundaryNodeID;
};

} // namespace Post
#endif 