﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BasePost.h
//! <AUTHOR> 乔龙
//! @brief 用于后处理的基类
//! @date 2022-05-27
//
//------------------------------修改日志----------------------------------------
// 2022-05-27 李艳亮  乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _basic_postTools_BasePost_
#define _basic_postTools_BasePost_

#include "basic/configure/ConfigureMacro.h"
#include "basic/field/ElementField.h"
#include "basic/field/NodeField.h"

/**
 * @brief 后处理命名空间
 * 
 */
namespace Post
{
/**
 * @brief 三维网格边界面单元的边 BoundaryEdge
 * 
 */
struct BoundaryEdge
{
    int begin; ///< 起始点的编号
    int end; ///< 结束点的编号
    int owner; ///< 边的左面单元编号
    int neighbor; ///< 边的右面单元编号

    BoundaryEdge(const int &begin_ = -1, const int &end_ = -1)
    {
        begin = begin_;
        end = end_;
        owner = -1;
        neighbor = -1;
    }
};

/**
 * @brief 输出文件类型
 * 
 */
enum FileType
{
    FULL = 0, ///< 网格和解数据
    GRID = 1, ///< 仅包含网格数据
    SOLUTION = 2 ///< 仅包含解数据
};

/**
 * @brief 后处理基类
 * 
 */
class  BasePost
{
public:    
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 网格指针
     * @param[in] dualFlag_ 对偶标识
     * @param[in] outputPosition_ 物理量输出位置
     * @param[in] exportInteriorFlag_ 输出内部场标识
     * @param[in] caseName_ 模型名称
     */
    BasePost(Mesh *mesh_,
             const bool dualFlag_ = false,
             const Position outputPosition_ = Position::CELL_CENTER,
			 const bool exportInteriorFlag_ = false,
             const std::string caseName_ = "noName");

    virtual ~BasePost();

    /**
     * @brief 生成后处理文件，由具体派生类实现
     * 
     */
    virtual void WriteFile() = 0;

    /**
     * @brief 重新给定caseName
     * 
     * @param[in] caseName_ 模型名称
     */
    void SetCaseName(const std::string caseName_);

    /**
     * @brief 设置计算时间，用于非定常计算
     * 
     * @param[in] solutionTime_ 计算时间
     */
    void SetFileType(const FileType &fileType_) {fileType = fileType_;}

    /**
     * @brief 设置计算时间，用于非定常计算
     * 
     * @param[in] solutionTime_ 计算时间
     */
    void SetSolutionTime(const Scalar &solutionTime_) {solutionTime = solutionTime_;}
    std::vector<int>  GetnodeListVector(int pid) {return this->nodeListVector[pid];};

    /**
     * @brief 设置时间序列编号
     * 
     * @param[in] strandID_ 时间序列编号
     */
    void SetStrandID(const int &strandID_) { strandID = strandID_;}

    /**
     * @brief 加入待输出的标量场
     * 
     * @param[in] phi 待输出标量场
     */
    void PushScalarField(void *phi);
    
    /**
     * @brief 加入待输出的矢量场
     * 
     * @param[in] phi 待输出矢量场
     */
    void PushVectorField(void *phi);

    /**
     * @brief 获取边界点编号
     * 
     * @param[in] patchID 边界编号
     */
    std::vector<int>  GetBoundaryNodeIDList(const int &patchID) {return this->nodeListVector[patchID];}
    
    /**
     * @brief 加入待输出的张量场
     * 
     * @param[in] phi 待输出张量场
     */
    void PushTensorField(void *phi);

protected:
    int GetOutputSize(const int boundaryID);

    std::string GetScalarFieldName(void *phi);
    
    std::string GetVectorFieldName(void *phi);
    
    std::string GetTensorFieldName(void *phi);
    
    Scalar GetScalarFieldValue(void *phi, const int &ID, const int boundaryID = -1);
    
    Vector GetVectorFieldValue(void *phi, const int &ID, const int boundaryID = -1);
    
    Tensor GetTensorFieldValue(void *phi, const int &ID, const int boundaryID = -1);
    
    /**
     * @brief 获取体心（面心）值
     * 
     * @tparam Type 物理量类型
     * @param[in] phi 单元场
     * @param[in] ID 单元编号（内部单元）或面编号（边界面）
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 体心（面心）值
     */
    template<class Type>
    Type GetCenterValue(const ElementField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 获取节点值
     * 
     * @tparam Type 物理量类型
     * @param[in] phi 单元场
     * @param[in] ID 节点编号
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 节点值
     */
    template<class Type>
    Type GetNodeValue(const ElementField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 获取体心（面心）值
     * 
     * @tparam Type 物理量类型
     * @param[in] phi 节点场
     * @param[in] ID 单元编号（内部单元）或面编号（边界面）
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 体心（面心）值
     */
    template<class Type>
    Type GetCenterValue(const NodeField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 获取节点值
     * 
     * @tparam Type 物理量类型
     * @param[in] phi 节点场
     * @param[in] ID 节点编号
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 节点值
     */
    template<class Type>
    Type GetNodeValue(const NodeField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 按单精度二进制写场数据
     * 后处理对象对外统一函数，根据场的类型由其私有函数具体实现
     * 
     * @tparam Type 场的类型，包括基于格点和格心的标量、矢量和张量场
     * @param[in] file 文件
     * @param[in] field 输出的场
     * @param[in] num 输出场元素的数量
     * @param[in] binary 二进制输出标识，true为二进制
     */
    template<class Type>
    void WriteField(std::fstream &file, const Type &field, const int &num, const bool binary = true);

    void WriteScalarValue(std::fstream &file, void *phi, const int boundaryID, const bool binary = true);
    
    void WriteVectorValue(std::fstream &file, void *phi, const int boundaryID, const bool binary = true);
    
    void WriteTensorValue(std::fstream &file, void *phi, const int boundaryID, const bool binary = true);
    
    void SetBoundNodeMap();

private:
    /**
     * @brief 按二进制写矢量场数据
     * 
     * @tparam Type 场的类型，包括基于格点和格心的矢量场
     * @param[in] file 文件
     * @param[in] field 输出的场
     * @param[in] num 输出场元素的数量
     * @param[in] binary 二进制输出标识，true为二进制
     */
    template<class Type>
    void WriteVectorField(std::fstream &file, const Type &field, const int &num, const bool binary = true);

    /**
     * @brief 按二进制写张量场数据
     * 
     * @tparam Type 场的类型，包括基于格点和格心的张量场
     * @param[in] file 文件
     * @param[in] field 输出的场
     * @param[in] num 输出场元素的数量
     * @param[in] binary 二进制输出标识，true为二进制
     */
    template<class Type>
    void WriteTensorField(std::fstream &file, const Type &field, const int &num, const bool binary = true);

    /**
     * @brief 根据单双精度需求输出场数据
     * 
     * @tparam Type 数据类型
     * @param[in] file 文件
     * @param[in] value 待输出数据
     * @param[in] binary 二进制输出标识，true为二进制
     */
    template<class Type>
    void WriteValue(std::fstream &file, const Type value, const bool binary = true);

    /**
     * @brief 设置点的相邻信息
     * 
     */
    void SetNodeAdjacent();

protected:    
    /// 网格或模型路径
    std::string casePath;

    /// 网格或模型名称（不含路径）
    std::string caseName;

    /// 网格或模型局部名称（不含路径）
    std::string localCaseName;

    /// 网格指针
    Mesh *mesh;

    /// 网格是否是二维,true为二维，false为三维
    bool dim2;

    /// 标量场指针容器、名称容器
    std::vector<void*> scalarField;

    /// 矢量场指针容器、名称容器
    std::vector<void*> vectorField;

    /// 张量场指针容器、名称容器
    std::vector<void*> tensorField;

    int scalarFieldSize; ///< 标量场数量
    int vectorFieldSize; ///< 矢量场数量
    int tensorFieldSize; ///< 张量场数量
    
    /// 包含边界点全局编号和局部编号的图：->first表示全局编号，->second表示局部编号
#ifdef _Supports_CXX11_
    std::vector<std::unordered_map<int, int>> nodeMapVector;
#else
    std::vector<std::map<int, int>> nodeMapVector;
#endif

    /// 包含边界点全局编号的容器：list[i]表示局部编号为i的点，全局编号为list[i]
    std::vector<std::vector<int>> nodeListVector;

    /// 网格点相邻的单元编号
    std::vector<std::vector<int>> nodeElementIDList;

    /// 边界点相邻的面编号及边界编号
	std::vector<std::vector<std::pair<int, int>>> nodeFaceIDListBC;

    /// 边界节点标识
    std::vector<bool> boundaryNodeFlag;
    
    /// 物理场存储位置，true为存储在单元中心
    bool outputAtCenter;
    
    /// 对偶标识
    bool dualFlag;

	/// 输出内部场标识
	bool exportInteriorFlag;

    /// z分量是否输出标识
    bool thirdDOutFlag;

    /// 双精度输出标识
    bool doubleFlag;

    /// 文件类型
    FileType fileType;

    /// 物理时间
    Scalar solutionTime;

    /// 时间序列编号
    int strandID;
};

} // namespace Post
#endif 