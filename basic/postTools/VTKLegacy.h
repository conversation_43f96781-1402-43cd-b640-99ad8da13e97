﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file VTKLegacy.h
//! <AUTHOR>
//! @brief 用于生成VTK后处理格式文件的类
//! @date 2022-05-02
//
//------------------------------修改日志----------------------------------------
// 2022-05-02 李子健(数峰科技)
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _basic_postTools_VTKLegacy_
#define _basic_postTools_VTKLegacy_

#include "basic/postTools/BasePost.h"

namespace Post
{
class VTKLegacy : public BasePost
{
public:
    // 构造函数
    // mesh_:[in],网格指针
    // caseName_:[in],模型名称
	VTKLegacy(Mesh *mesh_,
			  const bool dualFlag_ = false,
			  const Position outputPosition_ = Position::CELL_CENTER,
		      const bool exportInteriorFlag_ = false,
		      const std::string caseName_ = "");

    // 生成Ensight格式文件，根据是否包含场生成纯网格文件或包含场的文件
    void WriteFile();

protected:
    // 生成文件题头
    // file:[in&out],文件对象
    void WriteTitle(std::fstream &file);

    // 生成体信息
    // file:[in&out],文件对象
    void WriteVolume(std::fstream &file);

    // 生成边界信息
    // file:[in&out],文件对象
    void WriteBoundary(std::fstream &file);

    // 生成场的信息
    // file:[in&out],文件对象
    void WriteFieldData(std::fstream& file);

private:

    int GetVTKCellType(const Element::ElemShapeType &elemShapeType);

    template<class Type>
    void Write(std::fstream& file, const Type& var, const bool &binary);

    void WriteVector(std::fstream& file, const Vector& var, const bool &binary);

    template<class Type>
    void SwapEnd(Type& var);

private:
    // 场的输出格式，true为二进制格式，false为十进制格式
    bool binary;
};

} // namespace Post

#endif //_basic_postTools_VTKLegacy_