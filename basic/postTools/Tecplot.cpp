﻿#include "basic/postTools/Tecplot.h"

namespace Post
{
Tecplot::Tecplot(Mesh *mesh_, const bool dualFlag_, const Position outputPosition_, const bool exportInteriorFlag_, const std::string caseName_)
	: BasePost(mesh_, dualFlag_, outputPosition_, exportInteriorFlag_, caseName_), binary(true)
{
    doubleFlag = true;
    thirdDOutFlag = false;

	// Element::ElemShapeType::estMixed,        -- -1
	// Element::ElemShapeType::estTriangular,   --  2 FETRIANGLE
	// Element::ElemShapeType::estTetrahedral,  --  4 FETETRAHEDRON
	// Element::ElemShapeType::estQuadrilateral,--  3 FEQUADRILATERAL
	// Element::ElemShapeType::estHexahedral,   --  5 FEBRICK
	// Element::ElemShapeType::estPyramid,      --  7 FEPOLYHEDRON
	// Element::ElemShapeType::estWedge,        --  7 FEPOLYHEDRON
	// Element::ElemShapeType::estPolyhedron,   --  7 FEPOLYHEDRON
	// Element::ElemShapeType::estPolygon,      --  6 FEPOLYGON
	// Element::ElemShapeType::estLine,         --  1 FELINESEG
	// Element::ElemShapeType::estNoType,       -- -2
	zoneElementTypeMap = { -1, 2, 4, 3, 5, 7, 7, 7, 6, 1, -2 };
	
	zoneNameMap = {"ORDERED", "FELINESEG", "FETRIANGLE", "FEQUADRILATERAL",
	               "FETETRAHEDRON", "FEBRICK", "FEPOLYGON", "FEPOLYHEDRON"};
	
	cellNodeSizeMap = {0, 2, 3, 4, 4, 8, -1, -2};

    if (!dim2) this->CreatBoundaryEdge();
}

void Tecplot::WriteFile()
{
    if(caseName.size() == 0) FatalError("Tecplot::WriteFile: case name is missing!");

    // 确定场数量
    const int numValueGrid = dim2 ? 2 : 3;
    const int numValueSolution = scalarField.size() + vectorField.size() * numValueGrid;
    if (fileType == FileType::GRID)          numValue = numValueGrid;
    else if (fileType == FileType::SOLUTION) numValue = numValueSolution;
    else                                     numValue = numValueGrid + numValueSolution;
    
#if defined(_EnableTecio_)
	this->WriteFileTecIO(caseName);
#else
    // 确定文件名称与文件对象
    std::string fileName = caseName + ".plt";
    std::fstream file;
	
    // 打开文件
	if (binary) file.open(fileName, std::ios::out | std::ios::binary);
	else        file.open(fileName, std::ios::out);
	
    // 十进制输出
    if (!binary)
    {
        // 写文件
        this->WriteTitle(file);      
        this->WriteVolume(file);       
        this->WriteBoundary(file);
        

        file.close();
    }

    // 二进制输出
    else
    {
        // 写文件
        this->WriteTitleBinary(file);

        //写场的Zone
        this->WriteVolumeZoneBinary(file);

        //写边界的Zone
        for (int i = 0; i < mesh->GetBoundarySize(); i++) this->WriteBoundaryZoneBinary(i, file);

        // EOH Marker 357.0
        float eohMarker = 357.0;
        IO::Write(file, eohMarker, true);

        //写场的Data
        this->WriteVolumeDataBinary(file);

        //写边界的Data
        for (int i = 0; i < mesh->GetBoundarySize(); i++) this->WriteBoundaryDataBinary(i, file);

        file.close();
    }
#endif

    std::size_t pos = caseName.rfind("_");
    if (pos == caseName.npos) Print("\nTecplot文件路径: " + caseName);
    else                      Print("\nTecplot文件路径: " + caseName.substr(0, pos));

    return;
}

void Tecplot::WriteBoundaryFile(const std::vector<int> &boundaryID, std::vector< std::vector<Node>> *nodeVector)
{
    if(caseName.size() == 0) FatalError("Tecplot::WriteFile: case name is missing!");
    
    // 确定文件名称与文件对象
    std::string fileName = caseName + ".plt";
    std::fstream file;
	
    // 打开文件
	if (binary) file.open(fileName, std::ios::out | std::ios::binary);
	else        file.open(fileName, std::ios::out);
	
     // 十进制输出
     file << "TITLE =\"" << "ARI-Solver" << "\"" << std::endl;

     // 写变量名称(坐标) 
    if (!dim2)
    {
             file << "VARIABLES = " << "\"x\"," << "\"y\"";    
             file << ",\"z\","<< "\"id\""<<std::endl;     }
    else
    {
            file << "VARIABLES = " << "\"x\"," << "\"y\","<< "\"id\""<< std::endl;
    }      	
    
    for (int i = 0; i < boundaryID.size(); i++)
    {
    	const int &id = boundaryID[i];

        const int nodeSize = nodeListVector[id].size();
        if (nodeVector != nullptr && nodeSize != nodeVector[i].size())
        {
            FatalError("input nodeVector size isnot matched!");
        }

        //创建点列表变量
        const std::vector<BoundaryEdge> &vbe = vv_boundaryEdge[id];
        const int &faceSize = mesh->GetBoundaryFaceSize(id);

        // 写zone表头信息 
        file << "zone t=" << "\"" << "mdo body 1" << "\","
             << " i=" << nodeSize << ", j=" << faceSize << ",  f=fepoint";
        file << ", solutiontime= 0.1000000E+01, strandid=0";
        file << std::endl;

        // 写网格坐标
        for (int j = 0; j < nodeSize; j++)
        {
            const int &nodeID = nodeListVector[id][j];
            const Node &node = nodeVector == nullptr ? mesh->GetNode(nodeID) : (*nodeVector)[i][j];

            file << std::setprecision(10) << std::setiosflags(std::ios::scientific)
                 << node.X() << " " << node.Y() << " ";

            if (!dim2)
            {
                file << node.Z() << " " << j+1 << " ";
            }
            else
            {
                file << j+1 << " ";
            }
            file << std::endl;
        }

        // 写边界面的构成关系
        for (int j = 0; j < mesh->GetBoundaryFaceSize(id); j++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(id, j);

            for (int k = 0; k < mesh->GetFace(faceID).GetNodeSize(); k++)
            {
            	const int &nodeID = mesh->GetFace(faceID).GetNodeID(k);
                file << nodeMapVector[id][nodeID] + 1 << "\t";
            }
            if (this->mesh->GetFace(faceID).GetNodeSize() == 3)
            {
                // ZONETYPE = FEQUADRILATERAL 需要四点显示三角形，最后一点在写一次构成畸形四边形
                const int &nodeID = mesh->GetFace(faceID).GetNodeID(mesh->GetFace(faceID).GetNodeSize() - 1) ;
                file << nodeMapVector[id][nodeID] + 1 << "\t";
            }
            file << std::endl;
        }
    }
    
    file.close();
  
    std::size_t pos = caseName.rfind("_");
    if (pos == caseName.npos) Print("\nTecplot文件路径: " + caseName);
    else                      Print("\nTecplot文件路径: " + caseName.substr(0, pos));

    return;
}

void Tecplot::WriteBoundaryFile_AeroDynamic(std::vector<int> boundaryID,std::vector< std::vector<Node>> nodeVector, std::vector<std::vector<int>> nodeID)
{
    if(caseName.size() == 0) FatalError("Tecplot::WriteFile: case name is missing!");
    
    // 确定文件名称与文件对象
    std::string fileName = caseName + ".plt";
    std::fstream file;
	
    // 打开文件
	if (binary) file.open(fileName, std::ios::out | std::ios::binary);
	else        file.open(fileName, std::ios::out);
	
     // 十进制输出
     file << "TITLE =\"" << "ARI-Solver" << "\"" << std::endl;

     // 写变量名称(坐标)
     if (!dim2)
     {
         file << "VARIABLES = "
              << "\"x\","
              << "\"y\"";
         file << ",\"z\","
              << "\"id\"" << std::endl;
     }
     else
     {
         file << "VARIABLES = "
              << "\"x\","
              << "\"y\","
              << "\"id\"" << std::endl;
     }

     for (int i = 0; i < boundaryID.size(); i++)
     {
         int id = boundaryID[i];
         // 创建点列表变量
         std::vector<Node> &nodeList = nodeVector[i];
         std::vector<BoundaryEdge> &vbe = vv_boundaryEdge[id];
         const int &faceSize = mesh->GetBoundaryFaceSize(id);

         // 写zone表头信息
         file << "zone t="
              << "\""
              << mesh->GetBoundaryName(id)
              << "\","
              << " i=" << nodeList.size()
              << ", j=" << faceSize
              << ",  f=fepoint";
         file << ", solutiontime= 0.1000000E+01, strandid=0";
         /*
          if (dim2) file << ", ZONETYPE = FELINESEG";
          else      file << ", ZONETYPE = FEQUADRILATERAL" ;*/

         file << std::endl;

         // 写网格坐标
         for (int j = 0; j < nodeList.size(); j++)
         {
             file << std::setprecision(10) << std::setiosflags(std::ios::scientific)
                  << nodeList[j].X() << " " << nodeList[j].Y() << " ";
             if (!dim2)
             {
                 file << nodeList[j].Z() << " " << nodeID[i][j] << " ";
             }
             else
             {
                //  file << i + 1 << " ";
                // 获取网格点的ID
                file << nodeID[i][j];
             }
             file << std::endl;
         }

         // 写边界面的构成关系
         for (int j = 0; j < mesh->GetBoundaryFaceSize(id); j++)
         {
             const int &faceID = mesh->GetBoundaryFaceID(id, j);

             for (int k = 0; k < mesh->GetFace(faceID).GetNodeSize(); k++)
             {
                 const int &nodeID = mesh->GetFace(faceID).GetNodeID(k);
                 file << nodeMapVector[id][nodeID] + 1 << "\t";
             }
             if (this->mesh->GetFace(faceID).GetNodeSize() == 3)
             {
                 // ZONETYPE = FEQUADRILATERAL 需要四点显示三角形，最后一点在写一次构成畸形四边形
                 const int &nodeID = mesh->GetFace(faceID).GetNodeID(mesh->GetFace(faceID).GetNodeSize() - 1);
                 file << nodeMapVector[id][nodeID] + 1 << "\t";
             }
             file << std::endl;
         }
     }

     file.close();

     std::size_t pos = caseName.rfind("_");
     if (pos == caseName.npos)
         Print("\nTecplot文件路径: " + caseName);
     else
         Print("\nTecplot文件路径: " + caseName.substr(0, pos));

     return;
}


#if defined(_EnableTecio_)
void Tecplot::WriteFileTecIO(const std::string &caseName)
{
	INTEGER4 fileFormat = 0; // 0 == PLT, 1 == SZPLT
	INTEGER4 fileType1 = (int)fileType; //  FULL = 0, GRID = 1, SOLUTION = 2
	INTEGER4 Debug = 0, VIsDouble = 1;
	std::string fileName = caseName + ".plt";
	if (fileType == FileType::GRID) fileName = caseName + "-grid" + ".plt";
	else if (fileType == FileType::SOLUTION) fileName = caseName + "-solution" + ".plt";

	// 标题及变量
	variableNames = this->GetVariableNames();
	std::ostringstream stringStream; stringStream << variableNames[0];
	for (int i = 1; i < numValue; ++i) stringStream << ", " << variableNames[i];
	TECINI142((char*)"ARI-Solver", stringStream.str().c_str(), fileName.c_str(), (char*)".", &fileFormat, &fileType1, &Debug, &VIsDouble);

	// 空间流场输出
	if (exportInteriorFlag) this->WriteFileTecIOVolume();

	// 边界流场输出
	for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
		this->WriteFileTecIOBoundary(patchID);

	// 结束输出
	TECEND142();
}

void Tecplot::WriteFileTecIOVolume()
{
	std::string zoneName2 = "zone" + ToString(mesh->GetMeshZoneID());
	if (strandID >= 1) zoneName2 = zoneName2 + ": t = " + ToString(solutionTime);
	char const* ZoneTitle = zoneName2.c_str();

	INTEGER4 ZoneType = dim2 ? 6 : 7;
	std::vector<int> zoneSize; zoneSize.resize(8, 0);
	const int nElement = mesh->GetElementNumberReal();
	for (int elemID = 0; elemID < nElement; elemID++)
	{
		const int &elemType = (int)mesh->GetElement(elemID).GetElemShapeType();
		const int &zoneType = this->zoneElementTypeMap[elemType];
		if (zoneType >= 0)
		{
			zoneSize[zoneType]++;
			if (zoneSize[zoneType] == nElement) ZoneType = zoneType;
		}
	}

	INTEGER4 nNodes = mesh->GetNodeNumber();
	INTEGER4 nCells = mesh->GetElementNumberReal();
	INTEGER4 nFaces = mesh->GetFaceNumber();
	INTEGER4 ICellMax = 0, JCellMax = 0, KCellMax = 0, ParentZn = 0, IsBlock = 1;
	INTEGER4 NFConns = 0, FNMode = 0, nFaceNodes = 0, nBdFaces = 0, nBdConns = 0;
	INTEGER4 PassiveVarList = 0, ShrVar = 0, ShrConn = 0;
	INTEGER4 DIsDouble = 1;
	std::vector<INTEGER4> ValueLocation(variableNames.size());
	const int dimension = dim2 ? 2 : 3;
	for (int i = 0; i < dimension; ++i) ValueLocation[i] = 1;
	for (int i = dimension; i < variableNames.size(); ++i) ValueLocation[i] = outputAtCenter ? 0 : 1;
	if (ZoneType >= 6 && fileType != FileType::SOLUTION)
	{
		for (int i = 0; i < nFaces; i++)
			nFaceNodes += mesh->GetFace(i).GetNodeSize();
	}

	// ZoneTitle, ZoneType, IMxOrNumPts, JMxOrNumElements, KMxOrNumFaces, ICellMax, JCellMax, KCellMax,
	// SolutionTime, StrandID, ParentZone, IsBlock, NumFaceConnections, FaceNeighborMode,
	// TotalNumFaceNodes, NumConnectedBoundaryFaces, TotalNumBoundaryConnections,
	// PassiveVarList, ValueLocation, ShareVarFromZone, ShareConnectivityFromZone
	TECZNE142(ZoneTitle, &ZoneType, &nNodes, &nCells, &nFaces, &ICellMax, &JCellMax, &KCellMax,
		&solutionTime, &strandID, &ParentZn, &IsBlock, &NFConns, &FNMode, &nFaceNodes, &nBdFaces,
		&nBdConns, NULL, &ValueLocation[0], NULL, &ShrConn);

	std::vector<Scalar> list;
	if (fileType != FileType::SOLUTION)
	{
		list.clear(); list.resize(nNodes);
		for (int i = 0; i < nNodes; i++) list[i] = mesh->GetNode(i).X();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		for (int i = 0; i < nNodes; i++) list[i] = mesh->GetNode(i).Y();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		if (!dim2)
		{
			for (int i = 0; i < nNodes; i++) list[i] = mesh->GetNode(i).Z();
			TECDAT142(&nNodes, &list[0], &DIsDouble);
		}
	}

	const int valueSize = outputAtCenter ? nCells : nNodes;
	if (fileType != FileType::GRID)
	{
		list.clear(); list.resize(valueSize);
		for (int j = 0; j < scalarField.size(); j++)
		{
			for (int i = 0; i < valueSize; i++) list[i] = GetScalarFieldValue(scalarField[j], i);
			TECDAT142(&valueSize, &list[0], &DIsDouble);
		}

		for (int j = 0; j < vectorField.size(); j++)
		{
			for (int i = 0; i < valueSize; i++) list[i] = GetVectorFieldValue(vectorField[j], i).X();
			TECDAT142(&valueSize, &list[0], &DIsDouble);

			for (int i = 0; i < valueSize; i++) list[i] = GetVectorFieldValue(vectorField[j], i).Y();
			TECDAT142(&valueSize, &list[0], &DIsDouble);

			if (!dim2)
			{
				for (int i = 0; i < valueSize; i++) list[i] = GetVectorFieldValue(vectorField[j], i).Z();
				TECDAT142(&valueSize, &list[0], &DIsDouble);
			}
		}
	}

	std::vector<Scalar>().swap(list);

	if (fileType != FileType::SOLUTION)
	{
		if (ZoneType > 1 && ZoneType < 6)
		{
			const int connectivityCount = nCells * cellNodeSizeMap[ZoneType];
			std::vector<int> connectivity(connectivityCount);
			for (int i = 0, index = 0; i < nCells; i++)
			{
				const auto &element = mesh->GetElement(i);
				const int nodeSize = element.GetNodeSize();
				for (int j = 0; j < nodeSize; j++) connectivity[index++] = element.GetNodeID(j) + 1;
			}
			TECNODE142(&connectivityCount, &connectivity[0]);
		}
		else if (ZoneType >= 6)
		{
			std::vector<INTEGER4> faceNodeID; faceNodeID.reserve(nFaceNodes);
			std::vector<INTEGER4> faceNodeCounts(nFaces);
			std::vector<INTEGER4> leftElementID(nFaces);
			std::vector<INTEGER4> rightElementID(nFaces);

			// 面的点构成及面的左右单元编号
			for (int i = 0; i < nFaces; i++)
			{
				const int &nodeSize = mesh->GetFace(i).GetNodeSize();
				for (int j = 0; j < nodeSize; j++) faceNodeID.push_back(mesh->GetFace(i).GetNodeID(j) + 1);
				faceNodeCounts[i] = nodeSize;

				const int &ownerID = mesh->GetFace(i).GetOwnerID();
				const int &neighID = mesh->GetFace(i).GetNeighborID();
				leftElementID[i] = ownerID + 1;
				rightElementID[i] = mesh->JudgeRealElement(neighID) ? neighID + 1 : 0;
			}

			TECPOLYFACE142(&nFaces, &faceNodeCounts[0], &faceNodeID[0], &leftElementID[0], &rightElementID[0]);
		}
	}
}

void Tecplot::WriteFileTecIOBoundary(const int &patchID)
{
	const std::vector<int> &nodeIDList = nodeListVector[patchID];
	const std::vector<BoundaryEdge> &vbe = vv_boundaryEdge[patchID];

	INTEGER4 ZoneType = dim2 ? 1 : 6;
	std::vector<int> zoneSize; zoneSize.resize(8, 0);
	const int boundaryFaceSize = mesh->GetBoundaryFaceSize(patchID);
	for (int index = 0; index < boundaryFaceSize; index++)
	{
		const int &faceID = mesh->GetBoundaryFaceID(patchID, index);
		const int &nodeSize = (int)mesh->GetFace(faceID).GetNodeSize();
		if (nodeSize >= 2 && nodeSize <= 4) zoneSize[nodeSize - 1]++;
		else if (nodeSize > 4) zoneSize[6]++;
	}
	for (int zoneType = 0; zoneType < 8; zoneType++)
	{
		if (zoneSize[zoneType] == boundaryFaceSize) ZoneType = zoneType;
	}

	std::string zoneName2 = "zone" + ToString(mesh->GetMeshZoneID()) + " " + mesh->GetBoundaryName(patchID);
	if (strandID >= 1) zoneName2 = zoneName2 + ": t = " + ToString(solutionTime);
	char const* ZoneTitle = zoneName2.c_str();

	INTEGER4 nNodes = nodeIDList.size();
	INTEGER4 nCells = boundaryFaceSize;
	INTEGER4 nFaces = 1;
	INTEGER4 ICellMax = 0, JCellMax = 0, KCellMax = 0, ParentZn = 0, IsBlock = 1;
	INTEGER4 NFConns = 0, FNMode = 0, nFaceNodes = 0, nBdFaces = 0, nBdConns = 0;
	INTEGER4 PassiveVarList = 0, ShrVar = 0, ShrConn = 0;
	INTEGER4 DIsDouble = 1;
	std::vector<INTEGER4> ValueLocation(variableNames.size());
	const int dimension = dim2 ? 2 : 3;
	for (int i = 0; i < dimension; ++i) ValueLocation[i] = 1;
	for (int i = dimension; i < variableNames.size(); ++i) ValueLocation[i] = outputAtCenter ? 0 : 1;
	if (ZoneType == 6) { nFaceNodes = 2 * vbe.size(); nFaces = vbe.size(); }
	if (fileType == FileType::SOLUTION) nFaceNodes = 0;

	// ZoneTitle, ZoneType, IMxOrNumPts, JMxOrNumElements, KMxOrNumFaces, ICellMax, JCellMax, KCellMax,
	// SolutionTime, StrandID, ParentZone, IsBlock, NumFaceConnections, FaceNeighborMode,
	// TotalNumFaceNodes, NumConnectedBoundaryFaces, TotalNumBoundaryConnections,
	// PassiveVarList, ValueLocation, ShareVarFromZone, ShareConnectivityFromZone
	TECZNE142(ZoneTitle, &ZoneType, &nNodes, &nCells, &nFaces, &ICellMax, &JCellMax, &KCellMax,
		&solutionTime, &strandID, &ParentZn, &IsBlock, &NFConns, &FNMode, &nFaceNodes, &nBdFaces,
		&nBdConns, NULL, &ValueLocation[0], NULL, &ShrConn);

	std::vector<Scalar> list;

	if (fileType != FileType::SOLUTION)
	{
		list.clear(); list.resize(nNodes);
		for (int i = 0; i < nNodes; i++) list[i] = mesh->GetNode(nodeIDList[i]).X();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		for (int i = 0; i < nNodes; i++) list[i] = mesh->GetNode(nodeIDList[i]).Y();
		TECDAT142(&nNodes, &list[0], &DIsDouble);
		if (!dim2)
		{
			for (int i = 0; i < nNodes; i++) list[i] = mesh->GetNode(nodeIDList[i]).Z();
			TECDAT142(&nNodes, &list[0], &DIsDouble);
		}
	}

	const int valueSize = outputAtCenter ? nCells : nNodes;
	if (fileType != FileType::GRID)
	{
		list.clear(); list.resize(valueSize);
		for (int j = 0; j < scalarField.size(); j++)
		{
			for (int i = 0; i < valueSize; i++) list[i] = GetScalarFieldValue(scalarField[j], i, patchID);
			TECDAT142(&valueSize, &list[0], &DIsDouble);
		}

		for (int j = 0; j < vectorField.size(); j++)
		{
			for (int i = 0; i < valueSize; i++) list[i] = GetVectorFieldValue(vectorField[j], i, patchID).X();
			TECDAT142(&valueSize, &list[0], &DIsDouble);

			for (int i = 0; i < valueSize; i++) list[i] = GetVectorFieldValue(vectorField[j], i, patchID).Y();
			TECDAT142(&valueSize, &list[0], &DIsDouble);

			if (!dim2)
			{
				for (int i = 0; i < valueSize; i++) list[i] = GetVectorFieldValue(vectorField[j], i, patchID).Z();
				TECDAT142(&valueSize, &list[0], &DIsDouble);
			}
		}
	}
	std::vector<Scalar>().swap(list);

	if (fileType != FileType::SOLUTION)
	{
		if (dim2 || (ZoneType >= 1 && ZoneType <= 3))
		{
			const int &faceID0 = mesh->GetBoundaryFaceID(patchID, 0);
			const int &nodeSize = (int)mesh->GetFace(faceID0).GetNodeSize();
			const int connectivityCount = nCells * nodeSize;
			std::vector<int> connectivity(connectivityCount);
			for (int i = 0, index = 0; i < nCells; i++)
			{
				const int &faceID = mesh->GetBoundaryFaceID(patchID, i);
				for (int k = 0; k < mesh->GetFace(faceID).GetNodeSize(); k++)
				{
					const int &nodeID = mesh->GetFace(faceID).GetNodeID(k);
					connectivity[index++] = nodeMapVector[patchID][nodeID] + 1;
				}
			}
			TECNODE142(&connectivityCount, &connectivity[0]);
		}
		else
		{
			const int edgeSize = vbe.size();
			std::vector<INTEGER4> faceNodeID; faceNodeID.reserve(edgeSize * 2);
			std::vector<INTEGER4> leftElementID(edgeSize);
			std::vector<INTEGER4> rightElementID(edgeSize);

			// 写面的点构成及面的左右单元编号
			for (int i = 0; i < edgeSize; i++)
{
				faceNodeID.push_back(vbe[i].begin + 1);
				faceNodeID.push_back(vbe[i].end + 1);

				leftElementID[i] = vbe[i].owner + 1;
				rightElementID[i] = vbe[i].neighbor + 1;
}

			TECPOLYFACE142(&nFaces, NULL, &faceNodeID[0], &leftElementID[0], &rightElementID[0]);
		}
	}
}

#endif

std::vector<std::string> Tecplot::GetVariableNames()
{
	std::vector<std::string> variableNames;
	
	if (fileType != FileType::SOLUTION)
	{
	    variableNames.push_back("x [m]");
	    variableNames.push_back("y [m]");
        if (!dim2) variableNames.push_back("z [m]");
	}

	if (fileType != FileType::GRID)
	{
        for (int i = 0; i < scalarField.size(); i++)
            variableNames.push_back(GetScalarFieldName(scalarField[i]));

        for (int i = 0; i < vectorField.size(); i++)
        {
            std::string vectorName = GetScalarFieldName(vectorField[i]);
            variableNames.push_back(vectorName + ".x");
	    	variableNames.push_back(vectorName + ".y");
            if (!dim2) variableNames.push_back(vectorName + ".z");
        }
	}

    return variableNames;
}

void Tecplot::WriteStringASCII(std::string currentString, std::fstream &file)
{
    int value = 0;
    int stringSize = currentString.size();
    for (int len = 0; len < stringSize; len++)
    {
        value = int(currentString[len]);
        IO::Write(file, value, true);
    }
    value = 0;
    IO::Write(file, value, true);
}

void Tecplot::WriteTitleBinary(std::fstream &file)
{
    // 写 Magic Number, Version Number
    char magic_number[] = "#!TDV112";
    for (int i = 0; i < 8; i++)
    {
        file.write((char *)& magic_number[i], sizeof(char));
    }
    int integer_one = 1;
    IO::Write(file, integer_one, true);

    // 写标题
    int file_type = (int)fileType;
    IO::Write(file, file_type, true);
    std::string title = "ARI-Solver";
    WriteStringASCII(title, file);

    // 写变量名称(坐标)
    IO::Write(file, numValue, true);

    if (fileType != FileType::SOLUTION)
    {
        std::string var1Name = "x [m]";
        WriteStringASCII(var1Name, file);

        std::string var2Name = "y [m]";
        WriteStringASCII(var2Name, file);

        if (!dim2)
        {
            std::string var3Name = "z [m]";
            WriteStringASCII(var3Name, file);
        }
    }

    if (fileType != FileType::GRID)
    {
        // 写变量名称(标量)
        for (int i = 0; i < scalarField.size(); i++)
            WriteStringASCII(GetScalarFieldName(scalarField[i]), file);

        // 写变量名称(矢量)
        for (int i = 0; i < vectorField.size(); i++)
        {
            std::string vectorName = GetScalarFieldName(vectorField[i]);
            WriteStringASCII(vectorName + ".x", file);
            WriteStringASCII(vectorName+ ".y", file);
            if (!dim2) WriteStringASCII(vectorName + ".z", file);
        }
    }

    return;
}

void Tecplot::WriteVolumeZoneBinary(std::fstream &file)
{
    // Zone Marker 299.0
    float zoneMarker = 299.0;
    IO::Write(file, zoneMarker, true);

    // Zone Name
    std::string zoneName = "";
    int zoneType;
    if (dim2) // 二维
    {
        zoneName = "Volume:Polygon";
        zoneType = 6;
    }
    else // 三维
    {
        zoneName = "Volume:Polyhedron";
        zoneType = 7;
    }
    WriteStringASCII(zoneName, file);
    
    int parentZone = -1;
    int minusOne = -1;
    IO::Write(file, parentZone, true);
    IO::Write(file, strandID, true);
    IO::Write(file, solutionTime, true);
    IO::Write(file, minusOne, true);

	// ZoneType: 0=ORDERED, 1=FELINESEG, 2=FETRIANGLE, 3=FEQUADRILATERAL, 4=FETETRAHEDRON, 5=FEBRICK, 6=FEPOLYGON, 7=FEPOLYHEDRON
    IO::Write(file, zoneType, true);
    
	// Note:
	// BLOCK format must be used for cell-centered data and polyhedral zones
	// (FEPOLYGON/FEPOLYHEDRAL), as well as for all binary data
    // int dataPacking = 0;
    // if (outputAtCenter) dataPacking = 1;
    // IO::Write(file, dataPacking, true);
	
    int specifyVarLocation = 0;
    if (outputAtCenter) specifyVarLocation = 1;
    IO::Write(file, specifyVarLocation, true);

    if (specifyVarLocation == 1)
    {
        int varLocNode = 0;
        int varLocCellCenter = 1;
        IO::Write(file, varLocNode, true);
        IO::Write(file, varLocNode, true);
        if(!dim2) IO::Write(file, varLocNode, true);

        int remainVar;
        if (dim2) remainVar = numValue - 2;
        else remainVar = numValue - 3;
        for (int i = 0; i < remainVar; i++) IO::Write(file, varLocCellCenter, true);
    }

    int faceNeighborSupplied = 0;
    int faceNeighborConnection = 0;
    IO::Write(file, faceNeighborSupplied, true);
    IO::Write(file, faceNeighborConnection, true);

    int nodeNum = mesh->GetNodeNumber();
    int elemNum = mesh->GetElementNumberReal();
    int faceNum = mesh->GetFaceNumber();
    IO::Write(file, nodeNum, true);

    IO::Write(file, faceNum, true);
    int faceNodeNum = 0;
    for (int i = 0; i < faceNum; i++)
    {
        faceNodeNum = faceNodeNum + mesh->GetFace(i).GetNodeSize();
    }
    IO::Write(file, faceNodeNum, true);
    int boundaryFaceNum = 0;
    int boundaryConectionNum = 0;
    IO::Write(file, boundaryFaceNum, true);
    IO::Write(file, boundaryConectionNum, true);

    IO::Write(file, elemNum, true);

    int iCellDim = 0;
    int jCellDim = 0;
    int kCellDim = 0;
    IO::Write(file, iCellDim, true);
    IO::Write(file, jCellDim, true);
    IO::Write(file, kCellDim, true);

    int auxiliaryName = 0;
    IO::Write(file, auxiliaryName, true);

}

void Tecplot::WriteBoundaryZoneBinary(int boundaryID, std::fstream &file)
{
    // 获取该边界的点列表
    std::vector<int> &nodeList = nodeListVector[boundaryID];
    std::vector<BoundaryEdge> &vbe = vv_boundaryEdge[boundaryID];

    // Zone Marker
    float zoneMarker = 299.0;
    IO::Write(file, zoneMarker, true);

    std::string zoneName = "";
    int zoneType;
    if (dim2) // 二维
    {
        zoneName = "Surface:Line_";
        zoneType = 1;
    }
    else // 三维
    {
        zoneName = "Surface:Polygon_";
        zoneType = 6;
    }
    zoneName = zoneName + mesh->GetBoundaryName(boundaryID);
    WriteStringASCII(zoneName, file);

    int parentZone = -1;
    int minusOne = -1;
    IO::Write(file, parentZone, true);
    IO::Write(file, strandID, true);
    IO::Write(file, solutionTime, true);
    IO::Write(file, minusOne, true);
    
	// ZoneType: 
	// 0=ORDERED, 1=FELINESEG,
	// 2=FETRIANGLE, 3=FEQUADRILATERAL,
	// 4=FETETRAHEDRON, 5=FEBRICK,
	// 6=FEPOLYGON, 7=FEPOLYHEDRON
    IO::Write(file, zoneType, true);
    
	// Note:
	// BLOCK format must be used for cell-centered data and polyhedral zones
	// (FEPOLYGON/FEPOLYHEDRAL), as well as for all binary data
    // int dataPacking = 0;
    // if (outputAtCenter) dataPacking = 1;
    // IO::Write(file, dataPacking, true);
	
    int specifyVarLocation = 0;
    if (outputAtCenter) specifyVarLocation = 1;
    IO::Write(file, specifyVarLocation, true);

    if (specifyVarLocation == 1)
    {
        int varLocNode = 0;
        int varLocCellCenter = 1;
        IO::Write(file, varLocNode, true);
        IO::Write(file, varLocNode, true);
        if (!dim2) IO::Write(file, varLocNode, true);

        int remainVar;
        if (dim2) remainVar = numValue - 2;
        else remainVar = numValue - 3;
        for (int i = 0; i < remainVar; i++)
        {
            IO::Write(file, varLocCellCenter, true);
        }
    }

    int faceNeighborSupplied = 0;
    int faceNeighborConnection = 0;
    IO::Write(file, faceNeighborSupplied, true);
    IO::Write(file, faceNeighborConnection, true);

    if (dim2)
    {
        int nodeNum = nodeList.size();
        int elemNum = mesh->GetBoundaryFaceSize(boundaryID);
        
        IO::Write(file, nodeNum, true);
        IO::Write(file, elemNum, true);
    }
    else
    {
        int nodeNum = nodeList.size();
        int elemNum = mesh->GetBoundaryFaceSize(boundaryID);
        int faceNum = vbe.size();
        IO::Write(file, nodeNum, true);

        IO::Write(file, faceNum, true);
        int faceNodeNum = faceNum * 2;
        IO::Write(file, faceNodeNum, true);
        int boundaryFaceNum = 0;
        int boundaryConectionNum = 0;
        IO::Write(file, boundaryFaceNum, true);
        IO::Write(file, boundaryConectionNum, true);

        IO::Write(file, elemNum, true);
    }

    int iCellDim = 0;
    int jCellDim = 0;
    int kCellDim = 0;
    IO::Write(file, iCellDim, true);
    IO::Write(file, jCellDim, true);
    IO::Write(file, kCellDim, true);

    int auxiliaryName = 0;
    IO::Write(file, auxiliaryName, true);
}

template<>
void Tecplot::WriteMinMax(std::fstream &file, const Scalar &min, const Scalar &max)
{
    IO::Write(file, min, true);
    IO::Write(file, max, true);
}

template<>
void Tecplot::WriteMinMax(std::fstream &file, const Vector &min, const Vector &max)
{
    IO::Write(file, min.X(), true);
    IO::Write(file, max.X(), true);
    IO::Write(file, min.Y(), true);
    IO::Write(file, max.Y(), true);
    if (!dim2)
    {
        IO::Write(file, min.Z(), true);
        IO::Write(file, max.Z(), true);
    }
}

template void Tecplot::WriteMinMax(std::fstream &file, const ElementField<Scalar> *phi, const int boundaryID);
template void Tecplot::WriteMinMax(std::fstream &file, const ElementField<Vector> *phi, const int boundaryID);
template<class Type>
void Tecplot::WriteMinMax(std::fstream &file, const ElementField<Type> *phi, const int boundaryID)
{
    const int size = GetOutputSize(boundaryID);
    Type minValue = GetFieldValue(*phi, 0, boundaryID);
    Type maxValue = GetFieldValue(*phi, 0, boundaryID);
    for (int k = 1; k < size; k++)
    {
        const auto value = GetFieldValue(*phi, k, boundaryID);
        minValue = Min(minValue, value);
        maxValue = Max(maxValue, value);
    }
    this->WriteMinMax(file, minValue, maxValue);
}

template void Tecplot::WriteMinMax(std::fstream &file, const NodeField<Scalar> *phi, const int boundaryID);
template void Tecplot::WriteMinMax(std::fstream &file, const NodeField<Vector> *phi, const int boundaryID);
template<class Type>
void Tecplot::WriteMinMax(std::fstream &file, const NodeField<Type> *phi, const int boundaryID)
{
    const int size = GetOutputSize(boundaryID);
    Type minValue = GetFieldValue(*phi, 0, boundaryID);
    Type maxValue = GetFieldValue(*phi, 0, boundaryID);
    for (int k = 1; k < size; k++)
    {
        const auto value = GetFieldValue(*phi, k, boundaryID);
        minValue = Min(minValue, value);
        maxValue = Max(maxValue, value);
    }
    this->WriteMinMax(file, minValue, maxValue);
}

template Scalar Tecplot::GetFieldValue(const ElementField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector Tecplot::GetFieldValue(const ElementField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type Tecplot::GetFieldValue(const ElementField<Type> &phi, const int &ID, const int boundaryID)
{
    if (outputAtCenter)
    {
        return GetCenterValue(phi, ID, boundaryID);
    }
    else
    {
        const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
        return GetNodeValue(phi, nodeID, boundaryID);
    }
}

template Scalar Tecplot::GetFieldValue(const NodeField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector Tecplot::GetFieldValue(const NodeField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type Tecplot::GetFieldValue(const NodeField<Type> &phi, const int &ID, const int boundaryID)
{
    if (outputAtCenter)
    {
        return GetCenterValue(phi, ID, boundaryID);
    }
    else
    {
        const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
        return GetNodeValue(phi, nodeID, boundaryID);
    }
}

void Tecplot::WriteVolumeDataBinary(std::fstream &file)
{
    WriteZoneDataInfo(file);
    
    // 写变量最大值和最小值
    Vector minVector, maxVector;

    if (fileType != FileType::SOLUTION)
    {
        // a.坐标变量最大值最小值
        minVector = mesh->GetNode(0);
        maxVector = mesh->GetNode(0);
        for (int j = 1; j < mesh->GetNodeNumber(); j++)
        {
            minVector = Min(minVector, mesh->GetNode(j));
            maxVector = Max(maxVector, mesh->GetNode(j));
        }
        this->WriteMinMax(file, minVector, maxVector);
    }

    if (fileType != FileType::GRID)
    {
        // b.标量场最大值最小值
        for (int j = 0; j < scalarField.size(); j++)
        {
            if(dualFlag) this->WriteMinMax(file, (NodeField<Scalar>*)(scalarField[j]));
            else         this->WriteMinMax(file, (ElementField<Scalar>*)(scalarField[j]));
        }

        // c.矢量场最大值最小值
        for (int j = 0; j < vectorField.size(); j++)
        {
            if(dualFlag) this->WriteMinMax(file, (NodeField<Vector>*)(vectorField[j]));
            else         this->WriteMinMax(file, (ElementField<Vector>*)(vectorField[j]));
        }
    }
    
    // 写数据

    if (fileType != FileType::SOLUTION)
    {
        // 网格坐标
        for (int i = 0; i < mesh->GetNodeNumber(); i++) IO::Write(file, mesh->GetNode(i).X(), true);
        for (int i = 0; i < mesh->GetNodeNumber(); i++) IO::Write(file, mesh->GetNode(i).Y(), true);
        if (!dim2) for (int i = 0; i < mesh->GetNodeNumber(); i++) IO::Write(file, mesh->GetNode(i).Z(), true);
    }
    
    if (fileType != FileType::GRID)
    {
        // 标量场
        for (int j = 0; j < scalarField.size(); j++) WriteScalarValue(file, scalarField[j], -1, true);

        // 矢量场
        for (int j = 0; j < vectorField.size(); j++) WriteVectorValue(file, vectorField[j], -1, true);
    }
    
    // 物理解文件不输出网格拓扑关系
    if (fileType == FileType::SOLUTION) return;

    // 写单元构成关系
    if (!dim2)
    {
        int faceNodeArray = 0;
        IO::Write(file, faceNodeArray, true);
        for (int i = 0; i < mesh->GetFaceNumber(); i++)
        {
            int nodeNumber = mesh->GetFace(i).GetNodeSize();
            faceNodeArray = faceNodeArray + nodeNumber;
            IO::Write(file, faceNodeArray, true);
        }
    }

    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        for (int j = 0; j < mesh->GetFace(i).GetNodeSize(); j++)
        {
            int temp_nodeID = mesh->GetFace(i).GetNodeID(j);
            IO::Write(file, temp_nodeID, true);
        }
    }

    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        int temp_Left = mesh->GetFace(i).GetOwnerID();
        //if (!mesh->JudgeRealElement(temp_Left)) temp_Left = -1;
        IO::Write(file, temp_Left, true);
    }

    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        int temp_Right = mesh->GetFace(i).GetNeighborID();
        if (!mesh->JudgeRealElement(temp_Right)) temp_Right = -1;
        IO::Write(file, temp_Right, true);
    }
}

void Tecplot::WriteBoundaryDataBinary(int boundaryID, std::fstream &file)
{
    std::vector<int> &nodeList = nodeListVector[boundaryID];
    std::vector<BoundaryEdge> &vbe = vv_boundaryEdge[boundaryID];

    WriteZoneDataInfo(file);

    // 写变量最大值和最小值
    Vector minVector, maxVector;

    if (fileType != FileType::SOLUTION)
    {
        // a.坐标变量最大值最小值
        minVector = mesh->GetNode(nodeList[0]);
        maxVector = mesh->GetNode(nodeList[0]);
        for (int j = 1; j < nodeList.size(); j++)
        {
            minVector = Min(minVector, mesh->GetNode(nodeList[j]));
            maxVector = Max(maxVector, mesh->GetNode(nodeList[j]));
        }
        this->WriteMinMax(file, minVector, maxVector);
    }
    
    if (fileType != FileType::GRID)
    {
        // b.标量场最大值最小值    
        for (int j = 0; j < scalarField.size(); j++)
        {
            if(dualFlag) this->WriteMinMax(file, (NodeField<Scalar>*)(scalarField[j]), boundaryID);
            else         this->WriteMinMax(file, (ElementField<Scalar>*)(scalarField[j]), boundaryID);
        }

        // c.矢量场最大值最小值
        for (int j = 0; j < vectorField.size(); j++)
        {
            if(dualFlag) this->WriteMinMax(file, (NodeField<Vector>*)(vectorField[j]), boundaryID);
            else         this->WriteMinMax(file, (ElementField<Vector>*)(vectorField[j]), boundaryID);
        }
    }

    // 写数据

    if (fileType != FileType::SOLUTION)
    {
        // 网格坐标
        for (int j = 0; j < nodeList.size(); j++) IO::Write(file, mesh->GetNode(nodeList[j]).X(), true);
        for (int j = 0; j < nodeList.size(); j++) IO::Write(file, mesh->GetNode(nodeList[j]).Y(), true);
        if (!dim2) for (int j = 0; j < nodeList.size(); j++) IO::Write(file, mesh->GetNode(nodeList[j]).Z(), true);
    }

    if (fileType != FileType::GRID)
    {
        // 标量场
        for (int j = 0; j < scalarField.size(); j++) WriteScalarValue(file, scalarField[j], boundaryID, true);

        // 矢量场
        for (int j = 0; j < vectorField.size(); j++) WriteVectorValue(file, vectorField[j], boundaryID, true);
    }

    // 物理解文件不输出网格拓扑关系
    if (fileType == FileType::SOLUTION) return;

    // 写边界面的构成关系
    if (dim2)
    {
        const int &faceSize = mesh->GetBoundaryFaceSize(boundaryID);
        for (int j = 0; j < faceSize; j++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(boundaryID, j);
            for (int k = 0; k < mesh->GetFace(faceID).GetNodeSize(); k++)
            {
                const int &nodeID = mesh->GetFace(faceID).GetNodeID(k);
                IO::Write(file, nodeMapVector[boundaryID][nodeID], true);
            }
        }
    }
    else
    {
        for (int j = 0; j < vbe.size(); j++)
        {
            IO::Write(file, vbe[j].begin, true);
            IO::Write(file, vbe[j].end, true);
        }
        for (int j = 0; j < vbe.size(); j++) IO::Write(file, vbe[j].owner, true);
        for (int j = 0; j < vbe.size(); j++) IO::Write(file, vbe[j].neighbor, true);
    }
}

void Tecplot::WriteTitle(std::fstream &file)
{
    // 写标题
    file << "TITLE =\"" << "ARI-Solver" << "\"" << std::endl;    

    // 写变量名称(坐标)
    file << "VARIABLES = " << "\"x [m]\"," << "\"y [m]\"";
    if (!dim2) file << ",\"z [m]\"";

    // 写变量名称(标量)
    if (scalarField.size() > 0)
    {
        for (int i = 0; i < scalarField.size(); i++)
            file << ",\"" << GetScalarFieldName(scalarField[i]) << "\"";
    }

    // 写变量名称(矢量)
    if (vectorField.size() > 0)
    {
        for (int i = 0; i < vectorField.size(); i++)
        {
            std::string vectorName = GetVectorFieldName(vectorField[i]);
            file << ",\"" << vectorName << ".x\"," << "\"" << vectorName << ".y\"";
            if (!dim2) file << ",\"" << vectorName << ".z\"";
        }
    }
    file << std::endl;

    return;
}

void Tecplot::WriteVolume(std::fstream &file)
{
    // 写zone表头信息
    if (mesh->GetMeshDimension() == Mesh::md2D)
    {
        file << "ZONE T = " << "\"" << "Volume" << "\","
            << " DATAPACKING = POINT, N = " << mesh->GetNodeNumber()
            << ", E = " << mesh->GetElementNumberReal()
            << ", ZONETYPE = FEQUADRILATERAL" << std::endl;

    }
    else if (mesh->GetMeshDimension() == Mesh::md3D)
    {
        file << "ZONE T = " << "\"" << "Tet_mesh" << "\","
            << " DATAPACKING = POINT, N = " << mesh->GetNodeNumber()
            << ", E = " << mesh->GetElementNumberReal()
            << ", ZONETYPE = FEBRICK" << std::endl;
    }
    else
    {
        FatalError("Tecplot::WriteVolume: mesh shapeType is unknown!");
		return;
    }
   
    // 写网格坐标及场值
    for (int i = 0; i < mesh->GetNodeNumber(); i++)
    {
        file << std::setprecision(10) << std::setiosflags(std::ios::scientific)
            << mesh->GetNode(i).X() << " " << mesh->GetNode(i).Y() << " ";
        if (!dim2)  file << mesh->GetNode(i).Z() << " ";
      
        for (int j = 0; j < scalarField.size(); j++)
        {
            file << GetScalarFieldValue(scalarField[j], i) << " ";
        }

        for (int j = 0; j < vectorField.size(); j++)
        {
            const Vector temp = GetVectorFieldValue(vectorField[j], i);
            file << temp.X() << " " << temp.Y() << " ";
            if (!dim2)  file << temp.Z() << " ";
        }

        file << std::endl;
    }

    // 写单元构成关系
    if (mesh->GetMeshDimension() == Mesh::md2D)
    {
        for (int i = 0; i < mesh->GetElementNumberReal(); i++)
        {
            if (mesh->GetElement(i).GetElemShapeType() == Element::estTriangular)
            {
                file << mesh->GetElement(i).GetNodeID(0) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(1) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << std::endl;
            }
            else if (mesh->GetElement(i).GetElemShapeType() == Element::estQuadrilateral)
            {
                file << mesh->GetElement(i).GetNodeID(0) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(1) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << std::endl;
            }
        }
    }
    else
    {
        for (int i = 0; i < mesh->GetElementNumberReal(); i++)
        {
            if (mesh->GetElement(i).GetElemShapeType() == Element::estTetrahedral)
            {
                file << mesh->GetElement(i).GetNodeID(0) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(1) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << std::endl;
            }
            else if (mesh->GetElement(i).GetElemShapeType() == Element::estPyramid)
            {
                file << mesh->GetElement(i).GetNodeID(0) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(1) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(4) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(4) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(4) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(4) + 1 << std::endl;
            }
            else if (mesh->GetElement(i).GetElemShapeType() == Element::estWedge)
            {
                file << mesh->GetElement(i).GetNodeID(0) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(1) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(4) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(5) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(5) + 1 << std::endl;
            }
            else if (mesh->GetElement(i).GetElemShapeType() == Element::estHexahedral)
            {
                file << mesh->GetElement(i).GetNodeID(0) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(1) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(2) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(3) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(4) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(5) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(6) + 1 << " "
                    << mesh->GetElement(i).GetNodeID(7) + 1 << std::endl;
            }
        }

    }
    return;
}

void Tecplot::WriteBoundary(std::fstream &file)
{
    for (int i = 0; i < mesh->GetBoundarySize(); i++)
    {
        // 写zone表头信息 
        file << "ZONE T = " << "\"" << mesh->GetBoundaryName(i) << "\","
            << " DATAPACKING = POINT, N = " << this->mesh->GetNodeNumber()
            << ", E = " << mesh->GetBoundaryFaceSize(i);

        if (dim2) file << ", ZONETYPE = FELINESEG, " << "VARSHARELIST = ([";
        else      file << ", ZONETYPE = FEQUADRILATERAL, " << "VARSHARELIST = ([";

        for (int j = 0; j < numValue - 1; j++)    file << j + 1 << ",";
        if (dim2) file << numValue << "]=1,[1,2])" << std::endl;
        else      file << numValue << "]=1,[1,2,3])" << std::endl;

        // 写边界面的构成关系
        for (int j = 0; j < mesh->GetBoundaryFaceSize(i); j++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(i, j);

            for (int k = 0; k < mesh->GetFace(faceID).GetNodeSize(); k++)
            {
                file << this->mesh->GetFace(faceID).GetNodeID(k) + 1 << "\t";
            }
            if (this->mesh->GetFace(faceID).GetNodeSize() == 3)
            {
                // ZONETYPE = FEQUADRILATERAL 需要四点显示三角形，最后一点在写一次构成畸形四边形
                file << mesh->GetFace(faceID).GetNodeID(mesh->GetFace(faceID).GetNodeSize() - 1) + 1 << "\t";
            }
            file << std::endl;
        }
    }
    return;
}

void Tecplot::WriteZoneDataInfo(std::fstream &file)
{
    float zoneMarker = 299.0;
    int dataFormat = 2;
    int hasPassiveVar = 0;
    int hasVarSharing = 0;
    int zoneNum2Conectivity = -1;

    IO::Write(file, zoneMarker, true);
    for (int i = 0; i < numValue; i++) IO::Write(file, dataFormat, true);
    IO::Write(file, hasPassiveVar, true);
    IO::Write(file, hasVarSharing, true);
    IO::Write(file, zoneNum2Conectivity, true);
}

void Tecplot::CreatBoundaryEdge()
{
    const int boundarySize = mesh->GetBoundarySize();
    vv_boundaryEdge.resize(boundarySize);
    for (int patchID = 0; patchID < boundarySize; patchID++)
    {
        const int faceSize = mesh->GetBoundaryFaceSize(patchID);
        vv_boundaryEdge[patchID].reserve(2 * faceSize);
        for (int j = 0; j < faceSize; j++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(patchID, j);
            const Face &face = mesh->GetFace(faceID);
            const int &nodeSize = face.GetNodeSize();
            for (int k = 0; k < nodeSize; k++)
            {
                const int &globalNodeID0 = nodeMapVector[patchID][face.GetNodeID(k)];
                const int &globalNodeID1 = nodeMapVector[patchID][face.GetNodeID((k + 1) % nodeSize)];
                
                bool newFlag = true;
                const int currentEdgeSize = vv_boundaryEdge[patchID].size();
                for (int n = 0; n < currentEdgeSize; n++)
                {
                    if ((vv_boundaryEdge[patchID][n].begin == globalNodeID1)
                        && (vv_boundaryEdge[patchID][n].end == globalNodeID0))
                    {
                        vv_boundaryEdge[patchID][n].neighbor = j;
                        newFlag = false;
                        break;
                    }
                }
                if (newFlag)
                {
                    BoundaryEdge boundaryEdge(globalNodeID0, globalNodeID1);
                    boundaryEdge.owner = j;
                    vv_boundaryEdge[patchID].push_back(boundaryEdge);
                }
            }
        }
    }
}

}// namespace Post