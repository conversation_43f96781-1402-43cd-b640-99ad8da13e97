﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file CGNS.h
//! <AUTHOR>
//! @brief 用于生成CGNS格式文件的类
//! @date 2024-01-08
//
//------------------------------修改日志----------------------------------------
// 2021-04-18 乔龙
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_postTools_CGNSLegacy_
#define _basic_postTools_CGNSLegacy_

#include "basic/postTools/BasePost.h"
#include <cgnslib.h>

#if CGNS_VERSION < 3100
#define cgsize_t int
#endif

/**
 * @brief 后处理命名空间
 * 
 */
namespace Post
{
/**
 * @brief CGNS文件输出类
 * 
 */
class  CGNSLegacy :public BasePost
{
public:    
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 网格指针
     * @param[in] dualFlag_ 对偶标识
     * @param[in] outputPosition_ 物理量输出位置
     * @param[in] caseName_ 模型名称
     */
    CGNSLegacy(Mesh *mesh_,
               const bool dualFlag_ = false,
			   const Position outputPosition_ = Position::CELL_CENTER,
			   const bool exportInteriorFlag_ = false,
               const std::string caseName_ = "");

    /**
     * @brief 生成CGNSLegacy格式文件
     * 根据是否包含场生成纯网格文件或包含场的文件
     * 
     */
    void WriteFile();    

protected:
	
    /**
     * @brief 输出网格信息
     * 
     * @param[in] fileIndex CGNS文件索引
     * @param[in] baseIndex CGNS基础索引
     * @param[in] zoneIndex CGNS数据域索引
     */
	bool WriteGrid(const int &fileIndex, const int &baseIndex, const int &zoneIndex);

    /**
     * @brief 写物理场内部值
     * 
     * @param[in] fileIndex CGNS文件索引
     * @param[in] baseIndex CGNS基础索引
     * @param[in] zoneIndex CGNS数据域索引
     */
	bool WriteVolumeData(const int &fileIndex, const int &baseIndex, const int &zoneIndex);
    
    /**
     * @brief 写物理场边界值
     * 
     * @param[in] fileIndex CGNS文件索引
     * @param[in] baseIndex CGNS基础索引
     * @param[in] zoneIndex CGNS数据域索引
     * @param[in] patchID 边界编号
     */
	bool WriteBoundaryData(const int &fileIndex, const int &baseIndex, const int &zoneIndex, const int &patchID);

	/**
	* @brief 获取变量名称
	*
	*/
	std::vector<std::string> GetVariableNames();

    /**
     * @brief 获取物理场指定位置物理值
     * 
     * @tparam Type 物理场类型，标量或矢量
     * @param[in] phi 物理场
     * @param[in] ID 位置编号
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 
     */
    template<class Type>
    Type GetFieldValue(const ElementField<Type> &phi, const int &ID, const int boundaryID = -1);

    /**
     * @brief 获取物理场指定位置物理值
     * 
     * @tparam Type 物理场类型，标量或矢量
     * @param[in] phi 物理场
     * @param[in] ID 位置编号
     * @param[in] boundaryID 边界编号，取-1时为内部值
     * @return Type 
     */
    template<class Type>
    Type GetFieldValue(const NodeField<Type> &phi, const int &ID, const int boundaryID = -1);

private:

	/// 输出变量名称
	std::vector<std::string> variableNames;

	int nodeSize; ///< 节点数量
	int elemSize; ///< 单元数量
	int boundaryFaceSize; ///< 边界面数量
};

} // namespace Post
#endif 