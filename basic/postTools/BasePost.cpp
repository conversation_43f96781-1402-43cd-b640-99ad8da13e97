﻿#include "basic/common/InterpolationTools.h"
#include "basic/postTools/BasePost.h"

namespace Post
{
BasePost::BasePost(Mesh *mesh_, const bool dualFlag_, const Position outputPosition_, const bool exportInteriorFlag_, const std::string caseName_)
	: mesh(mesh_), dualFlag(dualFlag_), exportInteriorFlag(exportInteriorFlag_)
{
    if (mesh->GetMeshDimension() == Mesh::MeshDim::md2D) dim2 = true;
    else dim2 = false;

    if (caseName_.size() > 0) this->SetCaseName(caseName_);

    outputAtCenter = true;
    if (outputPosition_ == Position::CELL_VERTICE) outputAtCenter = false;

    scalarFieldSize = 0;
    vectorFieldSize = 0;
    tensorFieldSize = 0;

    fileType = FileType::FULL;
    solutionTime = 0.0;
    strandID = 0;

    // 建立该边界的点列表
    this->SetBoundNodeMap();

    if (outputPosition_ == Position::CELL_VERTICE && !dualFlag) 
        this->SetNodeAdjacent();
}

BasePost::~BasePost()
{
}

void BasePost::SetCaseName(const std::string caseName_)
{
    if (caseName_ == "" || caseName_ == "." || caseName_ == "/" || caseName_ == "./")
        FatalError("BasePost::SetCaseName: case name is wrong!");
    
    std::string casePath = "./";
    this->localCaseName = caseName_;
    std::string::size_type pos = caseName_.rfind("/");
    if (pos != caseName_.npos)
    {
        casePath = caseName_.substr(0, pos + 1);
        this->localCaseName = caseName_.substr(pos + 1, -1);
    }
    
    if (casePath.rfind("./") != 0 && casePath.find("/") != 0 && casePath.find(":") == casePath.npos)
        casePath = "./" + casePath;
    
    this->caseName = casePath + this->localCaseName;
    
    return;
}

void  BasePost::PushScalarField(void *phi)
{
    if (phi == nullptr) return;
    scalarField.push_back(phi);
    ++scalarFieldSize;
    return;
}

void  BasePost::PushVectorField(void *phi)
{
    if (phi == nullptr) return;
    vectorField.push_back(phi);
    ++vectorFieldSize;
    return;
}

void  BasePost::PushTensorField(void *phi)
{
    if (phi == nullptr) return;
    tensorField.push_back(phi);
    ++tensorFieldSize;
    return;
}

int BasePost::GetOutputSize(const int boundaryID)
{
    if (boundaryID != -1)
    {
        if (outputAtCenter) return mesh->GetBoundaryFaceSize(boundaryID);
        else                return nodeListVector[boundaryID].size();
    }
    else
    {
        if (outputAtCenter) return mesh->GetElementNumberReal();
        else                return mesh->GetNodeNumber();
    }
}

std::string BasePost::GetScalarFieldName(void *phi)
{
    if (!dualFlag) return ((ElementField<Scalar>*)phi)->GetName();
    else           return ((NodeField<Scalar>*)phi)->GetName();
}

std::string BasePost::GetVectorFieldName(void *phi)
{
    if (!dualFlag) return ((ElementField<Vector>*)phi)->GetName();
    else           return ((NodeField<Vector>*)phi)->GetName();
}

std::string BasePost::GetTensorFieldName(void *phi)
{
    if (!dualFlag) return ((ElementField<Tensor>*)phi)->GetName();
    else           return ((NodeField<Tensor>*)phi)->GetName();
}

Scalar BasePost::GetScalarFieldValue(void *phi, const int &ID, const int boundaryID)
{
    if (outputAtCenter)
    {
        if (dualFlag) return GetCenterValue(*(NodeField<Scalar> *)phi, ID, boundaryID);
        else          return GetCenterValue(*(ElementField<Scalar> *)phi, ID, boundaryID);
    }
    else
    {
        const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
        if (dualFlag) return GetNodeValue(*(NodeField<Scalar> *)phi, nodeID, boundaryID);
        else          return GetNodeValue(*(ElementField<Scalar> *)phi, nodeID, boundaryID);
    }
}

Vector BasePost::GetVectorFieldValue(void *phi, const int &ID, const int boundaryID)
{
    if (outputAtCenter)
    {
        if (dualFlag) return GetCenterValue(*(NodeField<Vector> *)phi, ID, boundaryID);
        else          return GetCenterValue(*(ElementField<Vector> *)phi, ID, boundaryID);
    }
    else
    {
        const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
        if (dualFlag) return GetNodeValue(*(NodeField<Vector> *)phi, nodeID, boundaryID);
        else          return GetNodeValue(*(ElementField<Vector> *)phi, nodeID, boundaryID);
    }
}

Tensor BasePost::GetTensorFieldValue(void *phi, const int &ID, const int boundaryID)
{
    if (outputAtCenter)
    {
        if (dualFlag) return GetCenterValue(*(NodeField<Tensor> *)phi, ID, boundaryID);
        else          return GetCenterValue(*(ElementField<Tensor> *)phi, ID, boundaryID);
    }
    else
    {
        const int nodeID = (boundaryID == -1) ? ID : nodeListVector[boundaryID][ID];
        if (dualFlag) return GetNodeValue(*(NodeField<Tensor> *)phi, nodeID, boundaryID);
        else          return GetNodeValue(*(ElementField<Tensor> *)phi, nodeID, boundaryID);
    }
}

template Scalar BasePost::GetCenterValue(const ElementField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector BasePost::GetCenterValue(const ElementField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type BasePost::GetCenterValue(const ElementField<Type> &phi, const int &ID, const int boundaryID)
{
    if (boundaryID != -1)
    {
        const int &faceID = mesh->GetBoundaryFaceID(boundaryID, ID);
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        return phi.GetValue(neighID);
    }
    else
    {
        const int &elementID = ID;
        return phi.GetValue(elementID);
    }
}

template Scalar BasePost::GetNodeValue(const ElementField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector BasePost::GetNodeValue(const ElementField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type BasePost::GetNodeValue(const ElementField<Type> &phi, const int &ID, const int boundaryID)
{
    std::vector<Type> valueList;
    std::vector<Scalar> distanceList;
    const int &nodeID = ID;
	if (boundaryID != -1 || boundaryNodeFlag[ID])
	{
		for (int i = 0; i < nodeFaceIDListBC[nodeID].size(); i++)
		{
			const int &faceID = nodeFaceIDListBC[nodeID][i].first;

		    // 按边界输出时，获取边界点的值，仅采用本边界的面进行插值
			if (boundaryID != -1 && nodeFaceIDListBC[nodeID][i].second != boundaryID) continue;
            
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();
			const Type &faceValue = phi.GetValue(neighID);
			valueList.push_back(faceValue);
			distanceList.push_back((mesh->GetFace(faceID).GetCenter() - mesh->GetNode(nodeID)).Mag());
		}
	}
    else
    {
        for (int i=0; i < nodeElementIDList[nodeID].size(); i++)
        {
            const int &elementID = nodeElementIDList[nodeID][i];
            valueList.push_back(phi.GetValue(elementID));
            distanceList.push_back((mesh->GetElement(elementID).GetCenter() - mesh->GetNode(nodeID)).Mag());
        }
    }
    return DistanceInterpolation(distanceList, valueList);
}

template Scalar BasePost::GetCenterValue(const NodeField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector BasePost::GetCenterValue(const NodeField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type BasePost::GetCenterValue(const NodeField<Type> &phi, const int &ID, const int boundaryID)
{
    Type valueSum = phi.GetValue(0) - phi.GetValue(0);
    int nodeIDSize;
    if (boundaryID != -1)
    {
        const int &faceID = mesh->GetBoundaryFaceID(boundaryID, ID);
        nodeIDSize = mesh->GetFace(faceID).GetNodeSize();
        for (int i=0; i < nodeIDSize; i++)
        {
            const int &nodeID = mesh->GetFace(faceID).GetNodeID(i);
            valueSum += phi.GetValue(nodeID);
        }
    }
    else
    {
        nodeIDSize = mesh->GetElement(ID).GetNodeSize();
        for (int i=0; i < nodeIDSize; i++)
        {
            const int &nodeID = mesh->GetElement(ID).GetNodeID(i);
            valueSum += phi.GetValue(nodeID);
        }
    }

    return valueSum / nodeIDSize;
}

template Scalar BasePost::GetNodeValue(const NodeField<Scalar> &phi, const int &ID, const int boundaryID);
template Vector BasePost::GetNodeValue(const NodeField<Vector> &phi, const int &ID, const int boundaryID);
template<class Type>
Type BasePost::GetNodeValue(const NodeField<Type> &phi, const int &ID, const int boundaryID)
{
    const int &nodeID = ID;
    return phi.GetValue(nodeID);
}

template void BasePost::WriteField(std::fstream &file, const ElementField<Scalar> &field, const int &num, const bool binary);
template void BasePost::WriteField(std::fstream &file, const NodeField<Scalar> &field, const int &num, const bool binary);
template<class Type>
void BasePost::WriteField(std::fstream &file, const Type &field, const int &num, const bool binary)
{
    for (int j = 0; j < num; ++j)        
        WriteValue(file, field.GetValue(j), binary);
}

template<>
void BasePost::WriteField(std::fstream &file, const NodeField<Vector> &field, const int &num, const bool binary)
{
    WriteVectorField(file, field, num, binary);
}

template<>
void BasePost::WriteField(std::fstream &file, const ElementField<Vector> &field, const int &num, const bool binary)
{
    WriteVectorField(file, field, num, binary);
}

template<>
void BasePost::WriteField(std::fstream &file, const NodeField<Tensor> &field, const int &num, const bool binary)
{
    WriteTensorField(file, field, num, binary);
}

template<>
void BasePost::WriteField(std::fstream &file, const ElementField<Tensor> &field, const int &num, const bool binary)
{
    WriteTensorField(file, field, num, binary);
}

template void BasePost::WriteVectorField(std::fstream &file, const ElementField<Vector> &field, const int &num, const bool binary);
template void BasePost::WriteVectorField(std::fstream &file, const NodeField<Vector> &field, const int &num, const bool binary);
template<class Type>
void BasePost::WriteVectorField(std::fstream &file, const Type &field, const int &num, const bool binary)
{
    for (int j = 0; j < num; ++j)
        WriteValue(file, field.GetValue(j).X(), binary);

    for (int j = 0; j < num; ++j)
        WriteValue(file, field.GetValue(j).Y(), binary);

    if (dim2)
    {
        for (int j = 0; j < num; ++j)
            WriteValue(file, 0.0, binary);
    }
    else
    {
        for (int j = 0; j < num; ++j)
            WriteValue(file, field.GetValue(j).Z(), binary);
    }
}

template void BasePost::WriteTensorField(std::fstream &file, const ElementField<Tensor> &field, const int &num, const bool binary);
template void BasePost::WriteTensorField(std::fstream &file, const NodeField<Tensor> &field, const int &num, const bool binary);
template<class Type>
void BasePost::WriteTensorField(std::fstream &file, const Type &field, const int &num, const bool binary)
{
    for (int j = 0; j < num; ++j)
        WriteValue(file, field.GetValue(j).XX(), binary);

    for (int j = 0; j < num; ++j)
        WriteValue(file, field.GetValue(j).YY(), binary);

    if (dim2)
    {
        for (int j = 0; j < num; ++j)
            WriteValue(file, 0.0, binary);
    }
    else
    {
        for (int j = 0; j < num; ++j)
            WriteValue(file, field.GetValue(j).ZZ(), binary);
    }

    for (int j = 0; j < num; ++j)
        WriteValue(file, field.GetValue(j).XY(), binary);

    if (dim2)
    {
        for (int j = 0; j < num; ++j)
            WriteValue(file, 0.0, binary);
        for (int j = 0; j < num; ++j)
            WriteValue(file, 0.0, binary);
    }
    else
    {
        for (int j = 0; j < num; ++j)
            WriteValue(file, field.GetValue(j).XZ(), binary);
        for (int j = 0; j < num; ++j)
            WriteValue(file, field.GetValue(j).YZ(), binary);
    }
}

void BasePost::WriteScalarValue(std::fstream &file, void *phi, const int boundaryID, const bool binary)
{
    const int size = GetOutputSize(boundaryID);
    for (int k = 0; k < size; k++) WriteValue(file, GetScalarFieldValue(phi, k, boundaryID), binary);
}

void BasePost::WriteVectorValue(std::fstream &file, void *phi, const int boundaryID, const bool binary)
{
    const int size = GetOutputSize(boundaryID);
    for (int k = 0; k < size; k++) WriteValue(file, GetVectorFieldValue(phi, k, boundaryID).X(), true);
    for (int k = 0; k < size; k++) WriteValue(file, GetVectorFieldValue(phi, k, boundaryID).Y(), true);
    if (!dim2) for (int k = 0; k < size; k++) WriteValue(file, GetVectorFieldValue(phi, k, boundaryID).Z(), true);
    else if (thirdDOutFlag) for (int k = 0; k < size; k++) WriteValue(file, 0.0, true);
}

void BasePost::WriteTensorValue(std::fstream &file, void *phi, const int boundaryID, const bool binary)
{
    const int size = GetOutputSize(boundaryID);
    for (int j = 0; j < size; ++j) WriteValue(file, GetTensorFieldValue(phi, j, boundaryID).XX(), binary);
    for (int j = 0; j < size; ++j) WriteValue(file, GetTensorFieldValue(phi, j, boundaryID).YY(), binary);
    if (dim2) for (int j = 0; j < size; ++j) WriteValue(file, 0.0, binary);
    else      for (int j = 0; j < size; ++j) WriteValue(file, GetTensorFieldValue(phi, j, boundaryID).ZZ(), binary);

    for (int j = 0; j < size; ++j) WriteValue(file, GetTensorFieldValue(phi, j, boundaryID).XY(), binary);

    if (!dim2)
    {
        for (int j = 0; j < size; ++j) WriteValue(file, GetTensorFieldValue(phi, j, boundaryID).XZ(), binary);
        for (int j = 0; j < size; ++j) WriteValue(file, GetTensorFieldValue(phi, j, boundaryID).YZ(), binary);
    }
    else if (thirdDOutFlag)
    {
        for (int j = 0; j < size; ++j) WriteValue(file, 0.0, binary);
        for (int j = 0; j < size; ++j) WriteValue(file, 0.0, binary);
    }
}

template void BasePost::WriteValue(std::fstream &file, const int value, const bool binary);
template void BasePost::WriteValue(std::fstream &file, const float value, const bool binary);
template void BasePost::WriteValue(std::fstream &file, const double value, const bool binary);
template<class Type>
void BasePost::WriteValue(std::fstream &file, const Type value, const bool binary)
{
    if (doubleFlag) IO::Write(file, (double)value, binary);
    else            IO::Write(file, (float)value, binary);
}

void BasePost::SetBoundNodeMap()
{
    const int boundarySize = mesh->GetBoundarySize();
    nodeMapVector.resize(boundarySize);
    nodeListVector.resize(boundarySize);
    boundaryNodeFlag.clear();
    boundaryNodeFlag.resize(mesh->GetNodeNumber(), false);
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
    {
        mesh->PopulateBoundaryNodeID(patchID, nodeListVector[patchID]);
#ifdef _Supports_CXX11_
        nodeMapVector[patchID].reserve(nodeListVector[patchID].size());
#endif
        for (int i = 0; i < nodeListVector[patchID].size(); ++i)
            nodeMapVector[patchID].insert(std::make_pair(nodeListVector[patchID][i], i));
        
        for (int i = 0; i < nodeListVector[patchID].size(); ++i)
            boundaryNodeFlag[nodeListVector[patchID][i]] = true;
    }
}

void BasePost::SetNodeAdjacent()
{
    nodeElementIDList.resize(mesh->GetNodeNumber());
    for (int elementID = 0; elementID < mesh->GetElementNumberReal(); ++elementID)
    {
        const Element &element = mesh->GetElement(elementID);
        for (int index = 0; index < element.GetNodeSize(); ++index)
        {
            const int &nodeID = element.GetNodeID(index);
            nodeElementIDList[nodeID].push_back(elementID);
        }
    }

	nodeFaceIDListBC.resize(mesh->GetNodeNumber());
	for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
	{
		const int faceSize = mesh->GetBoundaryFaceSize(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			const int &faceID = mesh->GetBoundaryFaceID(patchID, index);
			const Face &face = mesh->GetFace(faceID);
			for (int index1 = 0; index1 < face.GetNodeSize(); ++index1)
			{
				const int &nodeID = face.GetNodeID(index1);
				nodeFaceIDListBC[nodeID].push_back(std::make_pair(faceID, patchID));
			}
		}
	}
}

} // namespace Post
