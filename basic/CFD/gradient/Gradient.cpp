﻿#include "basic/CFD/gradient/Gradient.h"
#include <unordered_set>

namespace Gradient
{

Gradient::Gradient(Mesh *mesh_, const FieldManipulation::GradientScheme method_, const bool nodeCenter_)
    : mesh(mesh_), method(method_), nodeCenter(nodeCenter_)
{
    if (method == FieldManipulation::GradientScheme::LEAST_SQUARE
     || method == FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX)
    {
        LSVertexFlag = (method == FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX);
        // if (nodeCenter_) LSVertexFlag = false;
        LSVertexFlag = false;
        if (LSVertexFlag) this->SearchNodeAdjacentElements();
        this->CalculateLSWeight();
    }
}

Gradient::~Gradient()
{

}

template void Gradient::Calculate(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void Gradient::Calculate(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void Gradient::Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{

    if (method == FieldManipulation::GradientScheme::GREEN_GAUSS)
    {
        GreenGauss(phi, gradPhi);
    }
    else if (method == FieldManipulation::GradientScheme::LEAST_SQUARE ||
             method == FieldManipulation::GradientScheme::LEAST_SQUARE_VERTEX)
    {
        LeastSquare(phi, gradPhi);
    }
    else if (method == FieldManipulation::GradientScheme::GREEN_GAUSS_M1 )
    {
        GreenGaussCrossCorrected0(phi, gradPhi);
    }
    else if (method == FieldManipulation::GradientScheme::GREEN_GAUSS_M2 )
    {
        GreenGaussCrossCorrected1(phi, gradPhi);
    }
    else if (method == FieldManipulation::GradientScheme::GREEN_GAUSS_M3 )
    {
        GreenGaussWeighted(phi, gradPhi);
    }

    return;
}

template void Gradient::GreenGauss(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void Gradient::GreenGauss(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void Gradient::GreenGauss(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    // 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
		    // 得到面相关信息
		    const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();
            const Vector faceArea = face.GetArea() * face.GetNormal();

            //计算面心通量并累加（系数0.5放在最后处理）
            const TypeGradient phiFlux = faceArea * (phi.GetValue(ownerID) + phi.GetValue(nodeCenter ? ownerID : neighID));
            gradPhi.AddValue(ownerID, phiFlux);
        }
    }
    
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Vector faceArea = face.GetArea() * face.GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 0.5 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void Gradient::GreenGaussCrossCorrected0(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void Gradient::GreenGaussCrossCorrected0(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void Gradient::GreenGaussCrossCorrected0(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    ElementField<TypeGradient> gradPhiTemp(mesh);    
    gradPhiTemp.Initialize();

    //第一次迭代
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算两心连线与面相交处的物理量（即f'）
        Type phiCross;
        if (mesh->JudgeBoundaryFace(faceID))
        {
            phiCross = 0.5 * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        }
        else
        {
            Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            Vector e = distance.GetNormal();
            Vector cross = ((faceCenter & faceNormal) / (e & faceNormal)) * e;
            Scalar ownerWeight = (mesh->GetElement(neighID).GetCenter() - cross).Mag() / (mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter()).Mag();
            Scalar neighWeight = 1.0 - ownerWeight;
            phiCross = phi.GetValue(ownerID) * ownerWeight + phi.GetValue(neighID) * neighWeight;
        }
        
        //计算面心通量并累加
        const TypeGradient phiFlux = faceArea * phiCross;
        gradPhiTemp.AddValue(ownerID, phiFlux);
        gradPhiTemp.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhiTemp.MultiplyValue(elementID, coefficient);
    }        

    gradPhiTemp.SetGhostlValueParallel();
    gradPhiTemp.SetGhostlValueOverset();
    gradPhiTemp.SetGhostlValueBoundary();

    //第二次迭代
    gradPhi.Initialize();
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心处的物理量（即f）
        Type phiFace;
        if (mesh->JudgeBoundaryFace(faceID))
        {
            phiFace = 0.5 * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        }
        else
        {
            Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            Vector e = distance.GetNormal();
            Vector cross = ((faceCenter & faceNormal) / (e & faceNormal)) * e;
            Scalar ownerWeight = (mesh->GetElement(neighID).GetCenter() - cross).Mag() / (mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter()).Mag();
            Scalar neighWeight = 1.0 - ownerWeight;
            Type phiCross = phi.GetValue(ownerID) * ownerWeight + phi.GetValue(neighID) * neighWeight;
            phiFace = phiCross
                    + ownerWeight * Dot(faceCenter - mesh->GetElement(ownerID).GetCenter(), gradPhiTemp.GetValue(ownerID))
                    + neighWeight * Dot(faceCenter - mesh->GetElement(neighID).GetCenter(), gradPhiTemp.GetValue(neighID));
        }

        //计算面心通量并累加
        const TypeGradient phiFlux = faceArea * phiFace;
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void Gradient::GreenGaussCrossCorrected1(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void Gradient::GreenGaussCrossCorrected1(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void Gradient::GreenGaussCrossCorrected1(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    GreenGauss(phi, gradPhi);

    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();
    
    //第二次迭代
    for (int step = 0; step < 1; ++step)
    {
        //梯度置零
        ElementField<TypeGradient> gradPhiTemp(mesh);
        gradPhiTemp.Initialize();

        for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
        {
            // 得到面相关信息
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();
            const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
            const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

            //计算面心处的物理量（即f）
            Type phiFace;
            if (mesh->JudgeBoundaryFace(faceID))
            {
                //0.5在单元循环中乘
                phiFace = phi.GetValue(ownerID) + phi.GetValue(neighID);
            }
            else
            {
                //0.5在单元循环中乘
                Type phiCross = phi.GetValue(ownerID) + phi.GetValue(neighID);
                Vector temp1 = faceCenter - 0.5 * (mesh->GetElement(ownerID).GetCenter() + mesh->GetElement(neighID).GetCenter());
                TypeGradient temp2 = gradPhi.GetValue(ownerID) + gradPhi.GetValue(neighID);
                phiFace = phiCross + Dot(temp1, temp2);
            }

            //计算面心通量并累加
            const TypeGradient phiFlux = faceArea * phiFace;
            gradPhiTemp.AddValue(ownerID, phiFlux);
            gradPhiTemp.AddValue(neighID, -phiFlux);
        }

        //除以体积
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            const Scalar coefficient = 0.5 / mesh->GetElement(elementID).GetVolume();
            gradPhiTemp.MultiplyValue(elementID, coefficient);
        }

        gradPhiTemp.SetGhostlValueParallel();
        gradPhiTemp.SetGhostlValueOverset();
        gradPhiTemp.SetGhostlValueBoundary();
        
        gradPhi = gradPhiTemp;
    }

    return;
}

template void Gradient::GreenGaussWeighted(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void Gradient::GreenGaussWeighted(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void Gradient::GreenGaussWeighted(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    //面循环
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector &ownerCenter = mesh->GetElement(ownerID).GetCenter();
        const Vector &neighCenter = mesh->GetElement(neighID).GetCenter();
        const Vector distLeft = ownerCenter - faceCenter;
        const Vector distRight = neighCenter - faceCenter;
        const Scalar distMagLeft = distLeft.Mag();
        const Scalar distMagRight = distRight.Mag();
        const Scalar weightLeft = distMagRight / Max(distMagLeft + distMagRight, SMALL);
        const Scalar weightRight = 1.0 - weightLeft;

        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (weightLeft * phi.GetValue(ownerID) + weightRight * phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void Gradient::LeastSquare(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template void Gradient::LeastSquare(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
void Gradient::LeastSquare(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    //得到网格指针
    const Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    // 内部面循环，基于QR分解计算最小二乘梯度
    if (LSVertexFlag)
    {
        const int connectNumber = nodeAdjacentConnectInner.size();
        for (int index = 0; index < connectNumber; ++index)
        {
            // 获取单元编号
            const int &ownerID = nodeAdjacentConnectInner[index].first;
            const int &neighID = nodeAdjacentConnectInner[index].second;
    
            // 梯度计算
            const Type delta = phi.GetValue(neighID) - phi.GetValue(ownerID);
            gradPhi.AddValue(ownerID,  LSWeight[index].first * delta);
            gradPhi.AddValue(neighID, -LSWeight[index].second * delta);
        }
    }
    else
    {
        const int connectNumber = mesh->GetInnerFaceNumberInDomain();
        for (int index = 0; index < connectNumber; ++index)
        {
            // 获取单元编号
            const int &faceID = mesh->GetInnerFaceIDInDomain(index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();
    
            // 梯度计算
            const Type delta = phi.GetValue(neighID) - phi.GetValue(ownerID);
            gradPhi.AddValue(ownerID,  LSWeight[index].first * delta);
            gradPhi.AddValue(neighID, -LSWeight[index].second * delta);
        }
    }
    
    // 边界面循环
    if(!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        const int connectNumber = LSVertexFlag ? nodeAdjacentConnectInner.size() : mesh->GetInnerFaceNumberInDomain();
        for (int patchID = 0, m = connectNumber; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                const int &neighID = mesh->GetFace(faceID).GetNeighborID();

                // 梯度计算
                const Type delta = 0.5 * (phi.GetValue(neighID) - phi.GetValue(ownerID)); // 采用面心值和单元值计算
                gradPhi.AddValue(ownerID,  LSWeight[m++].first * delta);
            }
        }
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

void Gradient::SearchNodeAdjacentElements()
{
	const int elementSize = mesh->GetElementNumberReal();
	const int nodeSize = mesh->GetNodeNumber();
	
	// 统计点相邻的单元数量
	std::vector<int> count(nodeSize, 0);
	for (auto elemID = 0; elemID < elementSize; ++elemID)
	{
		const Element &elem = this->mesh->GetElement(elemID);
		for (int index = 0; index < elem.GetNodeSize(); ++index)
			count[elem.GetNodeID(index)]++;
	}

	// 生成点相邻单元编号列表
	std::vector<std::vector<int>> nodeElemID(nodeSize);
	for (auto i = 0; i < nodeSize; ++i) nodeElemID[i].reserve(count[i]);
	for (auto elemID = 0; elemID < elementSize; ++elemID)
	{
		const Element &elem = this->mesh->GetElement(elemID);
		for (int index = 0; index < elem.GetNodeSize(); ++index)
			nodeElemID[elem.GetNodeID(index)].push_back(elemID);
	}
    std::vector<int>().swap(count);

	// 形成目标单元的点相邻单元列表并统计总相邻数量
    std::vector<std::unordered_set<int>> adjcentElemIDSet(elementSize);
    int adjcentConnectNum = 0;
	for (int elemID = 0; elemID < elementSize; elemID++)
	{
		const Element &elem = mesh->GetElement(elemID);
		const int nodeSize = elem.GetNodeSize();
		for (int i = 0; i < nodeSize; i++)
		{
			const int &nodeID = elem.GetNodeID(i);
			const std::vector<int> list = nodeElemID[nodeID];
			for (int j = 0; j < list.size(); j++)
            {
                if (list[j] > elemID) adjcentElemIDSet[elemID].insert(list[j]);
            }
		}
        adjcentConnectNum += adjcentElemIDSet[elemID].size();
    }
    std::vector<std::vector<int>>().swap(nodeElemID);

	// 形成单元与点相邻单元连接信息
    nodeAdjacentConnectInner.resize(adjcentConnectNum);
	for (int elemID = 0, index = 0; elemID < elementSize; elemID++)
	{
        const auto &setTemp = adjcentElemIDSet[elemID];
		for (auto it = setTemp.begin(); it != setTemp.end(); ++it)
		{
            nodeAdjacentConnectInner[index++].first = elemID;
            nodeAdjacentConnectInner[index++].second = *it;
		}
    }
}

enum { I_11 = 0, I_12, I_13, I_22, I_23, I_33 };

void Gradient::CalculateLSWeight()
{
    // 计算最小二乘矩阵
    std::vector<std::vector<Scalar>> mat;
    this->CalculateRMatrix(mat);
    
    // 大小统计
    int boundaryFaceSize = 0;
    if(!nodeCenter)
    {
        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        {
            boundaryFaceSize += mesh->GetBoundaryFaceNumberInDomain(patchID);
        }
    }
    const int connectNumber = LSVertexFlag ? nodeAdjacentConnectInner.size() : mesh->GetInnerFaceNumberInDomain();
    LSWeight.resize(connectNumber + boundaryFaceSize);

    // 内部面循环，基于QR分解计算最小二乘梯度
    for (int index = 0; index < connectNumber; ++index)
    {
        // 获取单元编号
        const int ownerID = LSVertexFlag ? nodeAdjacentConnectInner[index].first : mesh->GetFace(mesh->GetInnerFaceIDInDomain(index)).GetOwnerID();
        const int neighID = LSVertexFlag ? nodeAdjacentConnectInner[index].second : mesh->GetFace(mesh->GetInnerFaceIDInDomain(index)).GetNeighborID();

        // 单元距离
        const Vector dist = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
        const Scalar dx = dist.X();
        const Scalar dy = dist.Y();
        const Scalar dz = dist.Z();
        const Scalar ww = 1.0 / (dist & dist);

        // R矩阵元素
        const Scalar &l_11 = mat[ownerID][I_11];
        const Scalar &l_12 = mat[ownerID][I_12];
        const Scalar &l_13 = mat[ownerID][I_13];
        const Scalar &l_22 = mat[ownerID][I_22];
        const Scalar &l_23 = mat[ownerID][I_23];
        const Scalar rl11 = 1.0 / l_11;
        const Scalar rl22 = 1.0 / l_22;
        const Scalar &rl33_sqr = mat[ownerID][I_33];
        const Scalar &r_11 = mat[neighID][I_11];
        const Scalar &r_12 = mat[neighID][I_12];
        const Scalar &r_13 = mat[neighID][I_13];
        const Scalar &r_22 = mat[neighID][I_22];
        const Scalar &r_23 = mat[neighID][I_23];
        const Scalar rr11 = 1.0 / r_11;
        const Scalar rr22 = 1.0 / r_22;
        const Scalar &rr33_sqr = mat[neighID][I_33];

        // owner单元QR分解计算系数
        const Scalar chi1l = (l_12 * l_23 * rl22 - l_13) * rl11;
        const Scalar chi2l = (dy - l_12 * rl11 * dx) * rl22 * rl22;
        const Scalar chi3l = (dz - l_23 * rl22 * dy + chi1l * dx) * rl33_sqr;
        const Scalar wxl = ww * (dx * rl11 * rl11 - chi2l * l_12 * rl11 + chi1l * chi3l);
        const Scalar wyl = ww * (chi2l - l_23 * rl22 * chi3l);
        const Scalar wzl = ww * chi3l;
        LSWeight[index].first = Vector(wxl, wyl, wzl);

        // neigh单元QR分解计算系数
        const Scalar chi1r = (r_12 * r_23 * rr22 - r_13) * rr11;
        const Scalar chi2r = (dy - r_12 * rr11 * dx) * rr22 * rr22;
        const Scalar chi3r = (dz - r_23 * rr22 * dy + chi1r * dx) * rr33_sqr;
        const Scalar wxr = -ww * (dx * rr11 * rr11 - chi2r * r_12 * rr11 + chi1r * chi3r);
        const Scalar wyr = -ww * (chi2r - r_23 * rr22 * chi3r);
        const Scalar wzr = -ww * chi3r;
        LSWeight[index].second = Vector(wxr, wyr, wzr);
    }

    // 边界面循环
    if(!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0, m = connectNumber; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const int &ownerID = face.GetOwnerID();

                const Vector dist = face.GetCenter() - mesh->GetElement(ownerID).GetCenter();
                const Scalar dx = dist.X();
                const Scalar dy = dist.Y();
                const Scalar dz = dist.Z();
                const Scalar ww = 1.0 / (dist & dist);

                // R矩阵元素
                const Scalar &l_11 = mat[ownerID][I_11];
                const Scalar &l_12 = mat[ownerID][I_12];
                const Scalar &l_13 = mat[ownerID][I_13];
                const Scalar &l_22 = mat[ownerID][I_22];
                const Scalar &l_23 = mat[ownerID][I_23];
                const Scalar &rl33_sqr = mat[ownerID][I_33];
                const Scalar rl11 = 1.0 / l_11;
                const Scalar rl22 = 1.0 / l_22;

                // QR分解计算系数
                const Scalar chi1l = (l_12 * l_23 * rl22 - l_13) * rl11;
                const Scalar chi2l = (dy - l_12 * rl11 * dx) * rl22 * rl22;
                const Scalar chi3l = (dz - l_23 * rl22 * dy + chi1l * dx) * rl33_sqr;
                const Scalar wxl = ww * (dx * rl11 * rl11 - chi2l * l_12 * rl11 + chi1l * chi3l);
                const Scalar wyl = ww * (chi2l - l_23 * rl22 * chi3l);
                const Scalar wzl = ww * chi3l;
                LSWeight[m++].first = Vector(wxl, wyl, wzl);
            }
        }
    }
}

void Gradient::CalculateRMatrix(std::vector<std::vector<Scalar>> &mat)
{
    // 数据初始化
    std::vector<std::vector<Scalar>> sum(mesh->GetElementNumberReal());
    mat.resize(mesh->GetElementNumberReal());
    for (int elemID = 0; elemID < sum.size(); elemID++)
    {
        sum[elemID].resize(6, Scalar0);
        mat[elemID].resize(6, Scalar0);
    }

    // 内部面循环
    const int connectNumber = LSVertexFlag ? nodeAdjacentConnectInner.size() : mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < connectNumber; ++index)
    {
        const int ownerID = LSVertexFlag ? nodeAdjacentConnectInner[index].first : mesh->GetFace(mesh->GetInnerFaceIDInDomain(index)).GetOwnerID();
        const int neighID = LSVertexFlag ? nodeAdjacentConnectInner[index].second : mesh->GetFace(mesh->GetInnerFaceIDInDomain(index)).GetNeighborID();

        const Vector dist = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
        const Vector tmp = dist * 1.0 / (dist & dist);
        const Scalar wdxx = tmp.X() * dist.X(), wdyy = tmp.Y() * dist.Y(), wdzz = tmp.Z() * dist.Z();
        const Scalar wdxy = tmp.X() * dist.Y(), wdxz = tmp.X() * dist.Z(), wdyz = tmp.Y() * dist.Z();
        sum[ownerID][I_11] += wdxx; sum[neighID][I_11] += wdxx;
        sum[ownerID][I_22] += wdyy; sum[neighID][I_22] += wdyy;
        sum[ownerID][I_33] += wdzz; sum[neighID][I_33] += wdzz;
        sum[ownerID][I_12] += wdxy; sum[neighID][I_12] += wdxy;
        sum[ownerID][I_13] += wdxz; sum[neighID][I_13] += wdxz;
        sum[ownerID][I_23] += wdyz; sum[neighID][I_23] += wdyz;
    }

    // 边界面循环
    if(!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const int &ownerID = face.GetOwnerID();

                const Vector dist = face.GetCenter() - mesh->GetElement(ownerID).GetCenter();
                const Vector tmp = 1.0 / (dist & dist) * dist;
                const Scalar wdxx = tmp.X() * dist.X(), wdyy = tmp.Y() * dist.Y(), wdzz = tmp.Z() * dist.Z();
                const Scalar wdxy = tmp.X() * dist.Y(), wdxz = tmp.X() * dist.Z(), wdyz = tmp.Y() * dist.Z();
                sum[ownerID][I_11] += wdxx; sum[ownerID][I_22] += wdyy; sum[ownerID][I_33] += wdzz;
                sum[ownerID][I_12] += wdxy; sum[ownerID][I_13] += wdxz; sum[ownerID][I_23] += wdyz;
            }
        }
    }
    if(mesh->GetMeshDimension() == Mesh::MeshDim::md2D)
    {
        for (int elemID = 0; elemID < sum.size(); elemID++)
            sum[elemID][I_33] = INF;
    }

    // 计算R矩阵
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elemID = mesh->GetElementIDInDomain(index);
        
        mat[elemID][I_11] = sqrt(sum[elemID][I_11]); 
        Scalar rr11 = 1.0 / mat[elemID][I_11];
        mat[elemID][I_12] = sum[elemID][I_12] * rr11;
        mat[elemID][I_13] = sum[elemID][I_13] * rr11;
        mat[elemID][I_22] = sqrt(sum[elemID][I_22] - (mat[elemID][I_12] * mat[elemID][I_12]));
        Scalar rr22 = 1.0 / mat[elemID][I_22];
        mat[elemID][I_23] = (sum[elemID][I_23] * rr22) - sum[elemID][I_13] * mat[elemID][I_12] * rr11 * rr22;
        mat[elemID][I_33] = 1.0 / (sum[elemID][I_33] - (mat[elemID][I_13] * mat[elemID][I_13]) - (mat[elemID][I_23] * mat[elemID][I_23]));
    }

    std::vector<std::vector<Scalar>>().swap(sum);
}

} // namespace FieldManipulation