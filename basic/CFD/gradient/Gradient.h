﻿#ifndef _basic_CFD_gradient_Gradient_
#define _basic_CFD_gradient_Gradient_

#include "basic/field/ElementField.h"
#include "basic/configure/Configure.h"
#include "basic/configure/ConfigureMacro.h"

/**
 * @brief 场操作命名空间
 * 
 */
namespace Gradient
{

/**
 * @brief 梯度计算类
 * 
 */
class Gradient
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] mesh_ 当前网格
     * @param[in] gradientType_ 梯度计算方法
     * @param[in] nodeCenter_ 格点标识
     */
    Gradient(Mesh *mesh_, const FieldManipulation::GradientScheme method_ = FieldManipulation::GradientScheme::GREEN_GAUSS, const bool nodeCenter_ = false);

    /**
     * @brief 析构函数
     * 
     */
    ~Gradient();
    
    /**
     * @brief 梯度计算函数
     * 
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template<class Type, class TypeGradient>
    void Calculate(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

private:
    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGauss(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGaussCrossCorrected0(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGaussCrossCorrected1(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用GreenGauss方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void GreenGaussWeighted(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 利用最小二乘方法计算体心梯度
     *
     * @tparam Type 物理场类型，可为标量场或矢量场
     * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
     * @param[in] phi 标量场或矢量场
     * @param[in, out] gradPhi 计算得到的梯度场
     */
    template <class Type, class TypeGradient>
    void LeastSquare(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

    /**
     * @brief 搜索点相邻单元
     *
     */
    void SearchNodeAdjacentElements();
    
    /**
     * @brief 计算最小二乘系数
     *
     */
    void CalculateLSWeight();
    
    /**
     * @brief 计算最小二乘矩阵
     *
     * @param[out] mat 最小二乘矩阵
     */
    void CalculateRMatrix(std::vector<std::vector<Scalar>> &mat);

private:
    /// 当前物理场所对应的网格
	Mesh *mesh;
    
    ///< 梯度计算方法
    FieldManipulation::GradientScheme method;
    
    ///< 格点标识
    const bool nodeCenter;
    
    /// 内部点相邻单元连接信息，两个数标识相邻两个单元编号（真实单元），其中first小于second
    std::vector<std::pair<int, int>> nodeAdjacentConnectInner;

    /// 最小二乘系数，与单元相邻信息一致，其中first单元编号小于second单元编号
    std::vector<std::pair<Vector, Vector>> LSWeight;

    /// 采用顶点最小二乘标识
    bool LSVertexFlag;
};

}
#endif