﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BlockSparseMatrix.h
//! <AUTHOR>
//! @brief 块稀疏矩阵类
//! @date 2024-12-10
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-10 气动院
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_CFD_linearSystemSolver_BlockSparseMatrix_
#define _basic_CFD_linearSystemSolver_BlockSparseMatrix_

#include "basic/common/Configuration.h"
#include "basic/common/SystemControl.h"
#include "basic/mesh/Mesh.h"

/**
* @brief 块稀疏矩阵类
*
*/
class BlockSparseMatrix
{

public:
	BlockSparseMatrix(const int &rowSize1 = 1, const int &nVariable1 = 1);
	~BlockSparseMatrix();
	void Initialize(Mesh *mesh, const int &nvariable);
	void SetValZero();
	void MatrixVectorProduct(const std::vector<Scalar> &vec, std::vector<Scalar> &prod)const;
	void SetVal2Diag(const int &row, const Scalar &val);
	void AddVal2Diag(const int &row, const Scalar &val);
	void AddBlock2Diag(const int &row, const Matrix &val);
	void UpdateBlocks(const int &faceID, const int &elemI, const int &elemJ, const Matrix &valI, const Matrix &valJ);
	void MatrixInverse(Scalar *matrix, Scalar *inverse)const;	
	inline Scalar *GetBlock(const int &row, const int &col)
	{
		for (int index = rowPtr[row]; index < rowPtr[row + 1]; ++index)
		{
			if (col_ind[index] == col)
			{
				return &(value[index * nVariable2]);
			}
		}
		return nullptr;
	}

	inline void SetBlock(const int &row, const int &col, Scalar *val)
	{
		for (int index = rowPtr[row]; index < rowPtr[row + 1]; ++index)
		{
			if (col_ind[index] == col)
			{
				Scalar *p = &(value[index * nVariable2]);
				for (int i = 0; i < nVariable2; ++i) p[i] = val[i];
			}
		}
	}

	void DeleteValsRowi(const int &row, const int &index0)
	{
		for (int index = rowPtr[row]; index < rowPtr[row + 1]; index++)
		{
			for (int iVar = 0; iVar < nVariable; iVar++)
				value[index * nVariable2 + index0 * nVariable + iVar] = 0.0; // Delete row values in the block
			if (col_ind[index] == row)
				value[index * nVariable2 + index0 * nVariable + index0] = 1.0; // Set 1 to the diagonal element
		}
	}

public:
	int rowSize; ///< 矩阵块的行数
	int nVariable; ///< 每个块的每行元素数量
	int nVariable2;///< nVariable*nVariable
	int blockSize; ///< 非0块的数量

	std::vector<int> rowPtr; ///< 每行数据块的起始位置
	std::vector<int> diagPtr; ///< 每行对角阵块的位置
	std::vector<std::pair<int, int>> faceIDPtr; ///< 面的左右单元数据块的位置
	std::vector<int> col_ind; ///< 矩阵块所在的列号

	std::vector<Scalar> value; ///< 矩阵的存储
};

#endif