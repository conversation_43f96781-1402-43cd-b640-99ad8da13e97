﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file PreconditionMatrix.h
//! <AUTHOR>
//! @brief 预处理矩阵类（目前指块对角阵）
//! @date 2024-12-10
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-10 气动院
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_CFD_linearSystemSolver_PreconditionMatrix_
#define _basic_CFD_linearSystemSolver_PreconditionMatrix_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrix.h"
#include "sourceFlow/configure/FlowConfigure.h"

/**
* @brief 块稀疏矩阵类
*
*/
class PreconditionMatrix
{
public:
	PreconditionMatrix(const Configure::Flow::FlowConfigure &flowConfigure_,const int &rowSize1 = 1, const int &nVariable1 = 1);
	~PreconditionMatrix();
	void Build(BlockSparseMatrix &m);
	void MatrixVectorProduct(const std::vector<Scalar> &vec, std::vector<Scalar> &prod)const;
	void MatrixMatrixProduct(const Scalar *matrix_a, const Scalar *matrix_b,Scalar *prod);
	
private:
	const Configure::Flow::FlowConfigure &flowConfigure;
	int kind_of_precondition;
	int rowSize; ///< 矩阵块的行数
	int nVariable; ///< 每个块的每行元素数量
	int nVariable2;///< nVariable*nVariable

	std::vector<Scalar> matrixValue; ///< 矩阵的存储
	std::vector<Scalar> diagMatrixInv;//<对角块矩阵的逆矩阵
	BlockSparseMatrix *BSMatrix; //< 稀疏矩阵
};

#endif