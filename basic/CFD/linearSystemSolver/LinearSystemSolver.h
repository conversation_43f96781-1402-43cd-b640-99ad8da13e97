﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LinearSystemSolver.h
//! <AUTHOR>
//! @brief 大型线性方程组的求解类.
//! @date 2024-12-12
//
//------------------------------修改日志----------------------------------------
//
// 2024-12-12 李艳亮、孔名驰
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _basic_CFD_linearSystemSolver_LinearSystemSolver_
#define _basic_CFD_linearSystemSolver_LinearSystemSolver_

#include "basic/CFD/linearSystemSolver/BlockSparseMatrix.h"
#include "basic/CFD/linearSystemSolver/PreconditionMatrix.h"
#include "basic/mesh/Mesh.h"
#include "sourceFlow/configure/FlowConfigure.h"

/**
 * @brief 大型线性方程组的求解类
 * 
 */
class LinearSystemSolver
{
public:
    /**
     * @brief 线性方程组的求解方法
     * 
     */
    enum Operation
    {
		CG,
		GMRES,
		BCGSTAB
    };

public:
    /**
     * @brief 线性方程组求解的构造函数
     * 
     * @param[in] mesh_ 当前网格
	 * @param[in] flowConfig_ 参数文件
     * @param[in] nVariable_ 待求解物理量个数
     */
	LinearSystemSolver(Mesh *mesh_, const Configure::Flow::FlowConfigure &flowConfigure_, const int &nVariable_);

    /**
     * @brief 线性方程组求解的析构函数
     * 
     */
	~LinearSystemSolver();

    /**
     * @brief 线性方程组求解
     * 
     * @param[in] A 矩阵
     * @param[in] b 右端列向量
	 * @param[in，out] x 解向量
     * @return int 迭代次数 
     */
	int Solve(BlockSparseMatrix &A, std::vector<Scalar> &b, std::vector<Scalar> &x);
	
private:
	/**
	* @brief GMRES方法
	*
	* @param[in] A 矩阵
    * @param[in] b 右端列向量
	* @param[in，out] x 解向量
    * @return int 迭代次数 
    */
	int GmresSolver(const BlockSparseMatrix &A, const std::vector<Scalar> &b, std::vector<Scalar> &x, Scalar &residual);

private:
	/**
	* @brief 计算向量的norm
	*
	* @param[in] v 向量
	* @return Scalar
	*/
	inline Scalar Norm(const std::vector<Scalar> &v)
	{		
		return sqrt(Dot(v, v));
	}

	/**
	* @brief 计算两个向量的内积
	*
	* @param[in] v1 向量1
	* @param[in] v2 向量2
	* @return Scalar
	*/
	inline Scalar Dot(const std::vector<Scalar> &v1, const std::vector<Scalar> &v2)
	{
		Scalar sum = 0.0;
		for (int i = 0; i < vectorSize; ++i) sum += v1[i] * v2[i];
		SumAllProcessor(sum, 0);
		MPIBroadcast(sum, 0);
		return sum;
	}

	inline void Subtract(std::vector<Scalar> &left, const std::vector<Scalar> &right)
	{
		for (int i = 0; i < vectorSize; ++i) left[i] -= right[i];
	}

	inline void Divide(std::vector<Scalar> &left, const Scalar &right)
	{
		for (int i = 0; i < vectorSize; ++i) left[i] /= right;
	}

	inline void Plus_AX(std::vector<Scalar> &left, const Scalar &right1, const std::vector<Scalar> &right2)
	{
		for (int i = 0; i < vectorSize; ++i) left[i] += right1 * right2[i];
	}

	inline Scalar Sign(const Scalar &x, const Scalar &y)
	{
		if (y == 0.0) return 0.0;
		return fabs(x) * (y < 0.0 ? -1.0 : 1.0);
	}

	void ModGramSchmidt(const int &i);

	void ApplyGivens(const Scalar &s, const Scalar &c, Scalar &h1, Scalar &h2);

	void GenerateGivens(Scalar &dx, Scalar &dy, Scalar &s, Scalar &c);

	void SolveReduced(const int &n);

	void SetGhostlValueParallel(std::vector<Scalar> &b);

protected:
	Mesh *mesh; ///< 网格指针
	const Configure::Flow::FlowConfigure &flowConfigure; ///< 参数

	const int rowSize; ///< 矩阵块的行数
	const int nVariable; ///< 待求物理量数量
	const int vectorSize; ///< 实际列向量的数量（不含虚单元）

	std::vector<std::vector<Scalar>> W;
	std::vector<std::vector<Scalar>> Z;
	std::vector<Scalar> Z0;   

	std::vector<Scalar> g;
	std::vector<Scalar> sn;
	std::vector<Scalar> cs;
	std::vector<Scalar> y;
	std::vector<std::vector<Scalar>> H;

	PreconditionMatrix *P;
	bool restared_gmres_flag; //重启型GMRES标识
	int m; //Krylov子空间大小
};
#endif
