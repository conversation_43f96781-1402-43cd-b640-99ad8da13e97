﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MultigridCycle.h
//! <AUTHOR>
//! @brief 多重网格计算基类.
//! @date 2021-05-19
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释，并对函数参数名称及参数顺序进行调整
//
// 2021-03-13 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _basic_CFD_multigridCycle_MultigridCycle_
#define _basic_CFD_multigridCycle_MultigridCycle_

#include "basic/configure/ConfigureMacro.h"
#include "basic/field/ElementField.h"
#include "basic/mesh/SubMesh.h"

/**
 * @brief 多重网格求解类
 * 
 */
class MultigridCycle
{
public:
    /**
     * @brief 多重网格操作类型
     * 
     */
    enum Operation
    {
        ITERATE,
        RESTRICTION,
        CORRECTION
    };
    
    /**
     * @brief 多重网格限制方法枚举
     * 
     */
    enum RestrictType
    {
        NoWeight,
        VolumeWeight        
    };

public:
    /**
     * @brief 多重网格循环构造函数
     * 
     * @param[in] subMesh_ 当前网格
     * @param[in] restrictionOperator_ 细网格向粗网格传递限制操作插值方法
     * @param[in] prolongationOperator_ 粗网格向细网格传递延拓操作插值方法
     */
    MultigridCycle(SubMesh *subMesh_,
                   const MultigridType::TransferOperator &restrictionOperator_ = MultigridType::TransferOperator::NONE_TRANSFER,
                   const MultigridType::TransferOperator &prolongationOperator_ = MultigridType::TransferOperator::NONE_TRANSFER);

    /**
     * @brief 获取多重网格路径
     * 
     * @param[in] currentSequence 当前序列
     * @param[in] multigridType 多重网格循环类型
     * @return std::vector<std::pair<Operation, int>> 
     */
    std::vector<std::pair<Operation, int>> GetMultigridPath(const int &currentSequence, const MultigridType::Type &multigridType);

    /**
     * @brief 细网格上场插值到粗网格
     * 
     * @tparam Type 物理场类型，包括标量和矢量
     * @param[in] fineMeshLevel 细网格所在层级编号
     * @param[in] fineField 细网格物理场
     * @param[in, out] coarseField 粗网格物理场
     * @param[in] type 插值方法
     */
    template<class Type>
    void Restrict(const int &fineMeshLevel, ElementField<Type> &fineField, ElementField<Type> &coarseField, const RestrictType &type);

    /**
     * @brief 粗网格上的场对其所对应的细网格上的场进行延拓
     * 
     * @tparam Type 物理场类型，包括标量和矢量
     * @param[in] coarseMeshLevel 粗网格所在层级编号
     * @param[in] coarseField 粗网格物理场
     * @param[in, out] fineField 细网格物理场
     */
    template<class Type>
    void Prolongate(const int &coarseMeshLevel, ElementField<Type> &coarseField, ElementField<Type> &fineField);

private:
    /**
     * @brief 细网格上场插值到粗网格(无权重)
     * 
     * @tparam Type 物理场类型，包括标量和矢量
     * @param[in] fineMeshLevel 细网格所在层级编号
     * @param[in] fineField 细网格物理场
     * @param[in, out] coarseField 粗网格物理场
     */
    template<class Type>
    void RestrictNoWeight(const int &fineMeshLevel, const ElementField<Type> &fineField, ElementField<Type> &coarseField);

    /**
     * @brief 细网格上场插值到粗网格(体积作为权重)
     * 
     * @tparam Type 物理场类型，包括标量和矢量
     * @param[in] fineMeshLevel 细网格所在层级编号
     * @param[in] fineField 细网格物理场
     * @param[in, out] coarseField 粗网格物理场
     */
    template<class Type>
    void RestrictVolumeWeight(const int &fineMeshLevel, const ElementField<Type> &fineField, ElementField<Type> &coarseField);

    /**
     * @brief 粗网格上的场对其所对应的细网格上的场进行延拓(梯度修正)
     * 
     * @tparam Type 物理场类型，包括标量和矢量
     * @param[in] coarseMeshLevel 粗网格所在层级编号
     * @param[in] coarseField 粗网格物理场
     * @param[in, out] fineField 细网格物理场
     */
    template<class Type>
    void ProlongteWithGradient(const int &coarseMeshLevel, ElementField<Type> &coarseField, ElementField<Type> &fineField);

protected:
    /// 当前网格
    SubMesh *subMesh;
    
    /// 多重网格总层数
    int multigridLevel;

    /// 限制算子的类型
    MultigridType::TransferOperator restrictionOperator;
    
    /// 延拓算子的类型
    MultigridType::TransferOperator prolongationOperator;
};
#endif
