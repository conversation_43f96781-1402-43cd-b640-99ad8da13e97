﻿#include "basic/CFD/multigridCycle/MultigridCycle.h"
#include "basic/field/FieldManipulation.h"
#include "basic/CFD/smoother/Smoother.h"

MultigridCycle::MultigridCycle(SubMesh *subMesh_,
                               const MultigridType::TransferOperator &restrictionOperator_,
                               const MultigridType::TransferOperator &prolongationOperator_)
                :
                subMesh(subMesh_), restrictionOperator(restrictionOperator_), prolongationOperator(prolongationOperator_)
{
}

std::vector<std::pair<MultigridCycle::Operation, int>> MultigridCycle::GetMultigridPath(const int &currentSequence, const MultigridType::Type &multigridType)
{
    // 多重网格循环路径标识容器
    std::vector<int> multigridPathFlag;

	// 多重网格路径容器
	std::vector<std::pair<MultigridCycle::Operation, int>> multigridPath;

    //当前网格的多重网格路径深度
    int depth = multigridLevel - currentSequence;
prolong
    // 设置多重网格路径标识容器
    if (depth == 1)
    {
        multigridPathFlag = {0};
    }
    else if (depth == 2)
    {
        multigridPathFlag = {0, 1, 0, -1};
    }
    else if (depth == 3)
    {
        if(multigridType == MultigridType::Type::V)
        {
            multigridPathFlag = {0,  1,  0,  1,
                                 0, -1,  0, -1};
        }
        else if(multigridType == MultigridType::Type::W)
        {
            multigridPathFlag = {0,  1,  0,  1,
                                 0, -1,  0,  1,
                                 0, -1,  0, -1};
        }
        else
        {
			FatalError("MultigridCycle::MultigridCycle: multigridType is unkown!");
			return multigridPath;
        }
    }
    else if (depth == 4)
    {
        if(multigridType == MultigridType::Type::V)
        {
            multigridPathFlag = {0,  1,  0,  1, 0,  1,
                                 0, -1,  0, -1, 0, -1};
        }
        else if(multigridType == MultigridType::Type::W)
        {
            multigridPathFlag = {0,  1,  0,  1,  0,  1,
                                 0, -1,  0,  1,
                                 0, -1,  0, -1,  0,  1,  0,  1,
                                 0, -1,  0,  1,
                                 0, -1,  0, -1,  0, -1};
        }
        else
        {
			FatalError("MultigridCycle::MultigridCycle: multigridType is unkown!");
			return multigridPath;
        }
    }
    else if (depth == 5)
    {
        if(multigridType == MultigridType::Type::V)
        {
            multigridPathFlag = {0,  1,  0,  1,  0,  1, 0,  1,
                                 0, -1,  0, -1,  0, -1, 0, -1};
        }
        else if(multigridType == MultigridType::Type::W)
        {
            multigridPathFlag = { 0,  1,  0,  1,  0,  1,  0,  1, 
                                  0, -1,  0,  1,
                                  0, -1,  0, -1,  0,  1,  0,  1,
                                  0, -1,  0,  1,
                                  0, -1,  0, -1,  0, -1,  0,  1,  0,  1,  0,  1,
                                  0, -1,  0,  1,
                                  0, -1,  0, -1,  0,  1,  0,  1,
                                  0, -1,  0,  1,
                                  0, -1,  0, -1,  0, -1,  0, -1};
        }
        else
        {
			FatalError("MultigridCycle::MultigridCycle: multigridType is unkown!");
			return multigridPath;
        }
    }
    else
    {
		FatalError("MultigridCycle::MultigridCycle: multigridLevel is beyond!");
		return multigridPath;
    }
    
    // 定义多重网格操作数据对的临时变量，初值为细网格上（网格层级编号为0）的迭代求解（操作类型为ITERATE）
    std::pair<Operation, int> opeartionPair;//{ MultigridCycle::Operation::ITERATE, 0 };
    
    int level = currentSequence;
    for (int i = 0; i < multigridPathFlag.size(); ++i)
    {
        if(multigridPathFlag[i] == 0)
        {
            opeartionPair.first = Operation::ITERATE;
        }
        else if(multigridPathFlag[i] == 1)
        {
            opeartionPair.first = Operation::RESTRICTION;
        }
        else if(multigridPathFlag[i] == -1)
        {
            opeartionPair.first = Operation::CORRECTION;
        }
        opeartionPair.second = level;
        level += multigridPathFlag[i];

        multigridPath.push_back(opeartionPair);
    }

    return multigridPath;
}

template void MultigridCycle::Restrict(const int &fineMeshLevel,
                              ElementField<Scalar> &fineField,
                              ElementField<Scalar> &coarseField,
                              const RestrictType &type);
template void MultigridCycle::Restrict(const int &fineMeshLevel,
                              ElementField<Vector> &fineField,
                              ElementField<Vector> &coarseField,
                              const RestrictType &type);                         
template<class Type>
void MultigridCycle::Restrict(const int &fineMeshLevel,
                              ElementField<Type> &fineField,
                              ElementField<Type> &coarseField,
                              const RestrictType &type)
{
    fineField.SetGhostValueMultigrid();

    if (type == RestrictType::VolumeWeight)
      RestrictVolumeWeight(fineMeshLevel, fineField, coarseField);
    else
      RestrictNoWeight(fineMeshLevel, fineField, coarseField);
}

template void MultigridCycle::RestrictNoWeight(const int &fineMeshLevel,
                              const ElementField<Scalar> &fineField,
                              ElementField<Scalar> &coarseField);
template void MultigridCycle::RestrictNoWeight(const int &fineMeshLevel,
                              const ElementField<Vector> &fineField,
                              ElementField<Vector> &coarseField);                         
template<class Type>
void MultigridCycle::RestrictNoWeight(const int &fineMeshLevel,
                              const ElementField<Type> &fineField,
                              ElementField<Type> &coarseField)
{
    const int coarseMeshLevel = fineMeshLevel + 1;
    MultiGrid *coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);

    // 粗网格单元场置零
    coarseField.Initialize();

    // 直接累加计算粗网格单元场
    const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
    for (int index = 0; index < IDSize; ++index)
    {
        const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
        const int &fineID = IDPair.first;
        const int &coarseID = IDPair.second;
        coarseField.AddValue(coarseID, fineField.GetValue(fineID));
    }
}

template void MultigridCycle::RestrictVolumeWeight(const int &fineMeshLevel,
                              const ElementField<Scalar> &fineField,
                              ElementField<Scalar> &coarseField);
template void MultigridCycle::RestrictVolumeWeight(const int &fineMeshLevel,
                              const ElementField<Vector> &fineField,
                              ElementField<Vector> &coarseField);                         
template<class Type>
void MultigridCycle::RestrictVolumeWeight(const int &fineMeshLevel,
                              const ElementField<Type> &fineField,
                              ElementField<Type> &coarseField)
{
    int coarseMeshLevel = fineMeshLevel + 1;
    MultiGrid *fineMesh = subMesh->GetMultiGrid(fineMeshLevel);
    MultiGrid *coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);    

    // 粗网格单元场置零
    coarseField.Initialize();

    // 体积加权计算粗网格体积场
    const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
    for (int index = 0; index < IDSize; ++index)
    {
        const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
        const int &fineID = IDPair.first;
        const int &coarseID = IDPair.second;
        const Scalar &volume = fineMesh->GetElement(fineID).GetVolume();
        coarseField.AddValue(coarseID, fineField.GetValue(fineID) * volume);
    }

    // 除以体积
    const int elementNumberCoarse = coarseMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumberCoarse; ++index)
    {
        const int &coarseID = coarseMesh->GetElementIDInDomain(index);
        const Scalar temp = 1.0 / coarseMesh->GetElement(coarseID).GetVolume();
        coarseField.MultiplyValue(coarseID, temp);
    }
}

template void MultigridCycle::Prolongate(const int &coarseMeshLevel,
                                         ElementField<Scalar> &coarseField,
                                         ElementField<Scalar> &fineField);
template void MultigridCycle::Prolongate(const int &coarseMeshLevel,
                                         ElementField<Vector> &coarseField,
                                         ElementField<Vector> &fineField);
template<class Type>
void MultigridCycle::Prolongate(const int &coarseMeshLevel,
                                ElementField<Type> &coarseField,
                                ElementField<Type> &fineField)
{
    MultiGrid *coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);

    if (prolongationOperator == MultigridType::TransferOperator::INJECTION)
    {
        // 采用粗网格单元场的新值与旧值的差对所对应细网格单元场进行修正
        const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
        for (int index = 0; index < IDSize; ++index)
        {
            const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
            const int &fineID = IDPair.first;
            const int &coarseID = IDPair.second;
            fineField.SetValue(fineID, coarseField.GetValue(coarseID));
        }
    }
    else if (prolongationOperator == MultigridType::TransferOperator::LINEAR)
    {
        coarseField.SetGhostlValueParallel(); //这个应该调用者准备好，待确认
        coarseField.SetGhostlValueBoundary();

        this->ProlongteWithGradient(coarseMeshLevel, coarseField, fineField);
    }
    else
    {
		FatalError("MultigridCycle::Correct: prolongationOperator isnot supported! ");
		return;
    }
    
    fineField.SetRealValueMultigrid();
}

template<>
void MultigridCycle::ProlongteWithGradient(const int &coarseMeshLevel,
                                           ElementField<Scalar> &coarseField,
                                           ElementField<Scalar> &fineField)
{
    MultiGrid *coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);
    MultiGrid *fineMesh = subMesh->GetMultiGrid(coarseMeshLevel-1);

    ElementField<Vector> gradientField(coarseMesh, Vector0);
    FieldManipulation::Gradient(coarseField, gradientField);

    const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
    for (int index = 0; index < IDSize; ++index)
    {
        const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
        const int &fineID = IDPair.first;
        const int &coarseID = IDPair.second;

        // 计算粗网格体心到细网格体心的距离矢量
        const Vector distance = fineMesh->GetElement(fineID).GetCenter() - coarseMesh->GetElement(coarseID).GetCenter();

        // 计算粗网格修正量（原始修正量+梯度修正）
        const Scalar temp = coarseField.GetValue(coarseID) + (Dot(distance, gradientField.GetValue(coarseID)));

        // 更新细网格修正量
        fineField.SetValue(fineID, temp);
    }
}

template<>
void MultigridCycle::ProlongteWithGradient(const int &coarseMeshLevel,
                                           ElementField<Vector> &coarseField,
                                           ElementField<Vector> &fineField)
{
    MultiGrid *coarseMesh = subMesh->GetMultiGrid(coarseMeshLevel);
    MultiGrid *fineMesh = subMesh->GetMultiGrid(coarseMeshLevel-1);

    ElementField<Tensor> gradientField(coarseMesh, Tensor0);
    FieldManipulation::Gradient(coarseField, gradientField);

    const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
    for (int index = 0; index < IDSize; ++index)
    {
        const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
        const int &fineID = IDPair.first;
        const int &coarseID = IDPair.second;
        
        // 计算粗网格体心到细网格体心的距离矢量
        const Vector distance = fineMesh->GetElement(fineID).GetCenter() - coarseMesh->GetElement(coarseID).GetCenter();

        // 计算粗网格修正量（原始修正量+梯度修正）
        const Vector temp = coarseField.GetValue(coarseID) + (Dot(distance, (gradientField.GetValue(coarseID))));

        // 更新细网格修正量
        fineField.SetValue(fineID, temp);
    }
}