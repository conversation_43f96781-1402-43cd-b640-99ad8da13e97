﻿#include "basic/field/ElementField.h"
#include "basic/common/InterpolationTools.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

template ElementField<int>::ElementField(const ElementField<int> &field, const std::string name);
template ElementField<Scalar>::ElementField(const ElementField<Scalar> &field, const std::string name);
template ElementField<Vector>::ElementField(const ElementField<Vector> &field, const std::string name);
template ElementField<Tensor>::ElementField(const ElementField<Tensor> &field, const std::string name);
template ElementField<Matrix>::ElementField(const ElementField<Matrix> &field, const std::string name);
template<class Type>
ElementField<Type>::ElementField(const ElementField<Type> &field, const std::string name)
:BaseField<Type>(field.p_blockMesh, name)
{
    this->Initialize(field.v_value);
}

//Create element Field
template void ElementField<int>::Create();
template void ElementField<Scalar>::Create();
template void ElementField<Vector>::Create();
template void ElementField<Tensor>::Create();
template void ElementField<Matrix>::Create();
template<class Type>
void ElementField<Type>::Create()
{
    if (false == BaseField<Type>::Existence())
    {
        this->v_value.resize(this->p_blockMesh->GetElementNumberAll());
        this->fs_status = BaseField<Type>::fsCreated;
    }
}

//Initialize with a fixed value
template void ElementField<int>::Initialize(int initalValue);
template void ElementField<Scalar>::Initialize(Scalar initalValue);
template void ElementField<Vector>::Initialize(Vector initalValue);
template void ElementField<Tensor>::Initialize(Tensor initalValue);
template void ElementField<Matrix>::Initialize(Matrix initalValue);
template<class Type>
void ElementField<Type>::Initialize(Type initalValue)
{
    this->Create();
    
    for (int elementID = 0; elementID < (int)this->v_value.size(); ++elementID)
    {
        BaseField<Type>::SetValue(elementID, initalValue);
    }
    this->fs_status = BaseField<Type>::fsAssigned;
}

//Initialize with a zero value
template<> void ElementField<int>::Initialize(){ this->Initialize(0); }
template<> void ElementField<Scalar>::Initialize(){ this->Initialize(Scalar0); }
template<> void ElementField<Vector>::Initialize(){ this->Initialize(Vector0); }
template<> void ElementField<Tensor>::Initialize(){ this->Initialize(Tensor0); }
template<> void ElementField<Matrix>::Initialize(){ this->Initialize(Matrix(0, 0)); }

//Initialize with a list of values
template void ElementField<int>::Initialize(const std::vector<int>& valueList);
template void ElementField<Scalar>::Initialize(const std::vector<Scalar>& valueList);
template void ElementField<Vector>::Initialize(const std::vector<Vector>& valueList);
template void ElementField<Tensor>::Initialize(const std::vector<Tensor>& valueList);
template<class Type>
void ElementField<Type>::Initialize(const std::vector<Type>& valueList)
{
    this->Create();
    
    if (BaseField<Type>::v_value.size() != valueList.size())
    {
        FatalError("Cannot initialize field, Numbers of field points and given valueList are not consistent");
    }
    for (int elementID = 0; elementID < (int)BaseField<Type>::v_value.size(); ++elementID)
    {
        BaseField<Type>::SetValue(elementID, valueList[elementID]);
    }
    this->fs_status = BaseField<Type>::fsAssigned;
}

//Initialize with a given function (for 3D case)
template void ElementField<int>::Initialize(int(*udf)(Scalar, Scalar, Scalar));
template void ElementField<Scalar>::Initialize(Scalar(*udf)(Scalar, Scalar, Scalar));
template void ElementField<Vector>::Initialize(Vector(*udf)(Scalar, Scalar, Scalar));
template void ElementField<Tensor>::Initialize(Tensor(*udf)(Scalar, Scalar, Scalar));
template<class Type>
void ElementField<Type>::Initialize(Type(*udf)(Scalar, Scalar, Scalar))
{
    this->Create();
    
    for (int elementID = 0; elementID < (int)this->v_value.size(); ++elementID)
    {
        const Node& xyz = this->p_blockMesh->GetElement(elementID).GetCenter();
        BaseField<Type>::SetValue(elementID, udf(xyz.X(), xyz.Y(), xyz.Z()));
    }
    this->fs_status = BaseField<Type>::fsAssigned;
}


//interpolate the value on an lagrange point from this element field
template Scalar ElementField<Scalar>::LagrangeInterpolation(Vector, int);
template Vector ElementField<Vector>::LagrangeInterpolation(Vector, int);
template Tensor ElementField<Tensor>::LagrangeInterpolation(Vector, int);
template<class Type>
Type ElementField<Type>::LagrangeInterpolation
( Vector lagPoint,    //lagrange point
  int elemID)         //id of the nearest element
{
    if (false == BaseField<Type>::Assignment())
    {
        FatalError("Can not interpolate value to a point. element values are not assingned");
    }
    std::vector<int> elemList = this->p_blockMesh->SearchElementNeighbor(elemID);
    elemList.push_back(elemID);
    std::vector<Vector> rePosition;
    std::vector<Type> valueList;
    for (int elementID = 0; elementID < (int)elemList.size(); ++elementID)
    {
        rePosition.push_back(this->p_blockMesh->GetElement(elemList[elementID]).GetCenter() - lagPoint);
        valueList.push_back(this->v_value[elemList[elementID]]);
    }
    return Interpolation(rePosition, valueList);
}

template int ElementField<int>::Norm();
template Scalar ElementField<Scalar>::Norm();
template Vector ElementField<Vector>::Norm();
template Tensor ElementField<Tensor>::Norm();
template<class Type>
Type ElementField<Type>::Norm()
{
    Type temp = this->v_value[0] * this->p_blockMesh->GetElement(0).GetVolume();

    for (int elementID = 1; elementID < (int)this->v_value.size(); ++elementID)
    {
        temp += this->v_value[elementID] * this->p_blockMesh->GetElement(elementID).GetVolume();

    }
    return temp;
}

template int ElementField<int>::AverageValue();
template Scalar ElementField<Scalar>::AverageValue();
template Vector ElementField<Vector>::AverageValue();
template Tensor ElementField<Tensor>::AverageValue();
template<class Type>
Type ElementField<Type>::AverageValue()
{
    Type numerator = this->v_value[0] * this->p_blockMesh->GetElement(0).GetVolume();
    Scalar totalVolume = this->p_blockMesh->GetElement(0).GetVolume();
    for (int elementID = 1; elementID < (int)this->v_value.size(); ++elementID)
    {
        numerator = numerator + this->v_value[elementID] * this->p_blockMesh->GetElement(elementID).GetVolume();
        totalVolume = totalVolume + this->p_blockMesh->GetElement(elementID).GetVolume();
    }
    return numerator / totalVolume;
}

// "="overload
template ElementField<int>& ElementField<int>::operator = (const ElementField<int>& rhs);
template ElementField<Scalar>& ElementField<Scalar>::operator = (const ElementField<Scalar>& rhs);
template ElementField<Vector>& ElementField<Vector>::operator = (const ElementField<Vector>& rhs);
template ElementField<Tensor>& ElementField<Tensor>::operator = (const ElementField<Tensor>& rhs);
template ElementField<Matrix>& ElementField<Matrix>::operator = (const ElementField<Matrix>& rhs);
template<class Type>
ElementField<Type>& ElementField<Type>::operator = (const ElementField<Type>& rhs)
{
    this->p_blockMesh = rhs.p_blockMesh;
    this->Initialize(rhs.v_value);
    this->fs_status = BaseField<Type>::fsAssigned;
    return *this;
}

// "="overload
template ElementField<int>& ElementField<int>::operator = (const int& rhs);
template ElementField<Scalar>& ElementField<Scalar>::operator = (const Scalar& rhs);
template ElementField<Vector>& ElementField<Vector>::operator = (const Vector& rhs);
template ElementField<Tensor>& ElementField<Tensor>::operator = (const Tensor& rhs);
template ElementField<Matrix>& ElementField<Matrix>::operator = (const Matrix& rhs);
template<class Type>
ElementField<Type>& ElementField<Type>::operator = (const Type& rhs)
{
    this->Initialize(rhs);
    this->fs_status = BaseField<Type>::fsAssigned;
    return *this;
}

template void ElementField<int>::SetGhostlValueParallel();
template void ElementField<Scalar>::SetGhostlValueParallel();
template void ElementField<Vector>::SetGhostlValueParallel();
template void ElementField<Tensor>::SetGhostlValueParallel();
template void ElementField<Matrix>::SetGhostlValueParallel();
template<class Type>
void ElementField<Type>::SetGhostlValueParallel()
{
    Mesh *mesh = this->p_blockMesh;
    const auto &vv_ghostElement = mesh->GetGhostElementsParallel();
    const int &ghostBoundarySize = vv_ghostElement.size();
    
    if(ghostBoundarySize==0) return;

    if (GetMPISize() == 1)
    {
        for (int i = 0; i < ghostBoundarySize; ++i)
        {
            for (int j = 0; j < vv_ghostElement[i].size(); ++j)
            {
                const int &faceID = vv_ghostElement[i][j].GetID();
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                const int &neighID = mesh->GetFace(faceID).GetNeighborID();
                const int &ghostID = neighID;
                this->v_value[ghostID] = this->v_value[ownerID];
            }
        }
    }
    else
    {
#if defined(_BaseParallelMPI_)

        std::vector<mpi::request> sendRequests(ghostBoundarySize);
        std::vector<mpi::request> recvRequests(ghostBoundarySize);
        //std::vector < std::pair<int, int> > recvTags;

        //Owner elem value list set
		std::vector<std::vector<Type>> vv_recvList(ghostBoundarySize);
        for (int i = 0; i < ghostBoundarySize; ++i)
        {
            const std::pair<int, int> &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
            recvRequests[i] = MPI::mpiWorld.irecv(procPair.second, procPair.second, vv_recvList[i]);
            //recvTags.push_back(std::make_pair(procPair.second, procPair.second));
        }

        for (int i = 0; i < ghostBoundarySize; ++i)
        {
            const int ghostElementSize = vv_ghostElement[i].size();
            std::vector<Type> v_sendList(ghostElementSize);
            for (int j = 0; j < ghostElementSize; ++j)
            {
                const int &faceID = vv_ghostElement[i][j].GetID();
                const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                v_sendList[j] = this->v_value[ownerID];
            }
            const std::pair<int, int> &procPair = vv_ghostElement[i][0].GetProcessorIDPair();
            sendRequests[i] = MPI::mpiWorld.isend(procPair.second, procPair.first, v_sendList);
        }
        //MPIIprobe(recvTags);
        MPIWaitAll(recvRequests);
        MPIWaitAll(sendRequests);

        for (int i = 0; i < ghostBoundarySize; ++i)
        {
            for (int j = 0; j < (int)vv_ghostElement[i].size(); ++j)
            {
                const int &faceID = vv_ghostElement[i][j].GetID();
                const int &neighID = mesh->GetFace(faceID).GetNeighborID();
                this->v_value[neighID] = vv_recvList[i][j];
            }
        }
#endif
    }
}

template void ElementField<int>::SetGhostlValueOverset();
template void ElementField<Scalar>::SetGhostlValueOverset();
template void ElementField<Vector>::SetGhostlValueOverset();
template void ElementField<Tensor>::SetGhostlValueOverset();
template void ElementField<Matrix>::SetGhostlValueOverset();
template <class Type>
void ElementField<Type>::SetGhostlValueOverset()
{
#if defined(_EnableOverset_)
    Mesh *mesh = this->p_blockMesh;
    int pID = GetMPIRank();
    const std::vector<std::vector<Acceptor>> &acceptorList = mesh->GetOversetRegion().GetAcceptorList();
    const std::vector<std::vector<Donor>> &donorList = mesh->GetOversetRegion().GetDonorList();
    if (acceptorList.size()==0) // 未启动重叠模块
    {
        return;
    }

    // 处理当前进程需要并行传递的Acceptor信息
    // 接收数据结构: 按照贡献单元所在进程编号放入第一层, 第二层是 <贡献单元编号:插值结果> 的映射
    std::vector<std::map<int, Type>> recvList(GetMPISize());

    std::vector<mpi::request> recvRequests;
    for (int procI = 0; procI < acceptorList.size(); ++procI)
    {
        if (acceptorList[procI].size() > 0 && procI != pID) // 存在需要从其他进程接收信息的Acceptor
        {
            recvRequests.push_back(MPI::mpiWorld.irecv(procI, procI, recvList[procI]));
        }
    }

    // 处理当前进程的所有Donor信息
    // 发送数据结构：按照受体单元所在进程编号放入第一层, 第二层是 <贡献单元编号:插值结果> 的映射
    std::vector<std::map<int, Type>> sendList(GetMPISize());

    for (int i = 0; i < donorList.size(); i++) // 循环所有的插值关系
    {
        for (int j = 0; j < donorList[i].size(); ++j)
        {
            // 单个插值关系信息
            const std::vector<int> &donorIDlist = donorList[i][j].GetDonorIDlist();
            const std::vector<Scalar> &weights = donorList[i][j].GetDonorWeights();
            const int &acceptorProcID = donorList[i][j].GetAcceptorProcID();
            const int &acceptorID = donorList[i][j].GetAcceptorID();

            // 循环当前插值关系的所有贡献单元,计算插值
            Type interpolationResult = this->v_value[donorIDlist[0]] * weights[0];
            for (int k = 1; k < donorIDlist.size(); k++)
            {
                interpolationResult += this->v_value[donorIDlist[k]] * weights[k];
            }

            // 传递插值结果：
            if (acceptorProcID == pID) // 受体单元在本进程时直接赋值；
            {
                this->v_value[acceptorID] = interpolationResult;
            }
            else // 受体单元在其他进程，需要通信, 放入发送列表
            {
                sendList[acceptorProcID][donorIDlist[0]] = interpolationResult;
            }
        }
    }

    std::vector<mpi::request> sendRequests;
    for (int procI = 0; procI < GetMPISize(); procI++)
    {
        if (sendList[procI].size() > 0) // 目标进程需要发送信息
        {
            sendRequests.push_back(MPI::mpiWorld.isend(procI, pID, sendList[procI])); // 以贡献单元进程编号为tag
        }
    }
    MPIWaitAll(sendRequests);
    MPIWaitAll(recvRequests);

    // 将mpi传递的插值结果赋值
    for (int i = 0; i < GetMPISize(); i++)
    {
        if (acceptorList[i].size() > 0 && i != pID)
        {
            for (int j = 0; j < acceptorList[i].size(); j++)
            {
                const int &acceptorID = acceptorList[i][j].GetAcceptorID();
                const int &donorProcID = acceptorList[i][j].GetCentralDonorProcID();
                const int &donorID = acceptorList[i][j].GetCentralDonorID();
                this->v_value[acceptorID] = recvList[donorProcID][donorID];
            }
        }
    }
#endif
}

template void ElementField<int>::SetGhostValueMultigrid();
template void ElementField<Scalar>::SetGhostValueMultigrid();
template void ElementField<Vector>::SetGhostValueMultigrid();
template void ElementField<Tensor>::SetGhostValueMultigrid();
template void ElementField<Matrix>::SetGhostValueMultigrid();
template<class Type>
void ElementField<Type>::SetGhostValueMultigrid()
{
    Mesh *mesh = this->p_blockMesh;
    const auto &vv_ghostElements_all = mesh->GetGhostElementsMultigrid();
    if (vv_ghostElements_all.size() == 0) return;
    
    const int mpiRank = GetMPIRank();
    const int mpiSize = GetMPISize();
    
#if defined(_BaseParallelMPI_)
    if (mpiSize == 1)
    {
        // 串行调试使用
        for (int index = 0; index < vv_ghostElements_all.size(); ++index)
        {
            for (int j = 0; j < (int)vv_ghostElements_all[index].size(); ++j)
            {
                const auto &localIDPair = vv_ghostElements_all[index][j].GetLocalIDPair();
                const int &localIDGhost = localIDPair.first;
                this->v_value[localIDGhost] = this->v_value[0];
            }
        }
        return;
    }

    std::vector<std::vector<GhostElement> > vv_ghostElements_send, vv_ghostElements_recv;
    vv_ghostElements_send.reserve(mpiSize);
    vv_ghostElements_recv.reserve(mpiSize);
    for (int i = 0; i < vv_ghostElements_all.size(); ++i)
    {
        const auto &procPair = vv_ghostElements_all[i][0].GetProcessorIDPair();
        const int &procIDGhost = procPair.first;
        if (procIDGhost == mpiRank) vv_ghostElements_recv.push_back(vv_ghostElements_all[i]);
        else                       vv_ghostElements_send.push_back(vv_ghostElements_all[i]);
    }
    
    const int sendMPISize = (int)vv_ghostElements_send.size();
    const int recvMPISize = (int)vv_ghostElements_recv.size();

    std::vector<mpi::request> sendRequests(sendMPISize);
    std::vector<mpi::request> recvRequests(recvMPISize);
    //std::vector < std::pair<int, int> > recvTags;

    std::vector<std::vector<Type>> vv_recvList(recvMPISize);
    for (int i = 0; i < recvMPISize; ++i)
    {
        const auto &procPair = vv_ghostElements_recv[i][0].GetProcessorIDPair();
        const int &recvProcID = procPair.second;
        const int &recvTag = procPair.second;
        recvRequests[i] = MPI::mpiWorld.irecv(recvProcID, recvTag, vv_recvList[i]);
        //recvTags.push_back(std::make_pair(recvProcID, recvTag));
    }

    for (int i = 0; i < sendMPISize; ++i)
    {
        const int ghostElementSize = (int)vv_ghostElements_send[i].size();
        std::vector<Type> v_sendList(ghostElementSize);
        for (int j = 0; j < ghostElementSize; ++j)
        {
            const auto &localIDPair = vv_ghostElements_send[i][j].GetLocalIDPair();
            const int &localIDReal = localIDPair.second;
            v_sendList[j] = this->v_value[localIDReal];
        }

        const auto &procPair = vv_ghostElements_send[i][0].GetProcessorIDPair();
        const int &sendProcID = procPair.first;
        const int &sendTag = procPair.second;
        sendRequests[i] = MPI::mpiWorld.isend(sendProcID, sendTag, v_sendList);
    }
    //MPIIprobe(recvTags);
    MPIWaitAll(recvRequests);
    MPIWaitAll(sendRequests);

    for (int i = 0; i < recvMPISize; ++i)
    {
        for (int j = 0; j < (int)vv_ghostElements_recv[i].size(); ++j)
        {
            const auto &localIDPair = vv_ghostElements_recv[i][j].GetLocalIDPair();
            const int &localIDGhost = localIDPair.first;
            this->v_value[localIDGhost] = vv_recvList[i][j];
        }
    }
#endif
}

template void ElementField<int>::SetRealValueMultigrid();
template void ElementField<Scalar>::SetRealValueMultigrid();
template void ElementField<Vector>::SetRealValueMultigrid();
template void ElementField<Tensor>::SetRealValueMultigrid();
template void ElementField<Matrix>::SetRealValueMultigrid();
template<class Type>
void ElementField<Type>::SetRealValueMultigrid()
{
    Mesh *mesh = this->p_blockMesh;
    const auto &vv_ghostElements_all = mesh->GetGhostElementsMultigrid();
    if (vv_ghostElements_all.size() == 0) return;
    
    const int mpiRank = GetMPIRank();
    const int mpiSize = GetMPISize();
    
#if defined(_BaseParallelMPI_)
    if (mpiSize == 1) return;

    std::vector<std::vector<GhostElement> > vv_ghostElements_send, vv_ghostElements_recv;
    vv_ghostElements_send.reserve(mpiSize);
    vv_ghostElements_recv.reserve(mpiSize);
    for (int i = 0; i < vv_ghostElements_all.size(); ++i)
    {
        const auto &procPair = vv_ghostElements_all[i][0].GetProcessorIDPair();
        const int &procIDReal = procPair.second;
        if (procIDReal == mpiRank) vv_ghostElements_recv.push_back(vv_ghostElements_all[i]);
        else                      vv_ghostElements_send.push_back(vv_ghostElements_all[i]);
    }

    const int sendMPISize = (int)vv_ghostElements_send.size();
    const int recvMPISize = (int)vv_ghostElements_recv.size();

    std::vector<mpi::request> sendRequests(sendMPISize);
    std::vector<mpi::request> recvRequests(recvMPISize);
    //std::vector < std::pair<int, int> > recvTags;

    std::vector<std::vector<Type>> vv_recvList(recvMPISize);
    for (int i = 0; i < recvMPISize; ++i)
    {
        const auto &procPair = vv_ghostElements_recv[i][0].GetProcessorIDPair();
        const int &recvProcID = procPair.first;
        const int &recvTag = procPair.first;
        recvRequests[i] = MPI::mpiWorld.irecv(recvProcID, recvTag, vv_recvList[i]);
        //recvTags.push_back(std::make_pair(recvProcID, recvTag));
    }

    for (int i = 0; i < sendMPISize; ++i)
    {
        const int ghostElementSize = (int)vv_ghostElements_send[i].size();
        std::vector<Type> v_sendList(ghostElementSize);
        for (int j = 0; j < ghostElementSize; ++j)
        {
            const auto &localIDPair = vv_ghostElements_send[i][j].GetLocalIDPair();
            const int &localIDGhost = localIDPair.first;
            v_sendList[j] = this->v_value[localIDGhost];
        }

        const auto &procPair = vv_ghostElements_send[i][0].GetProcessorIDPair();
        const int &sendProcID = procPair.second;
        const int &sendTag = procPair.first;
        sendRequests[i] = MPI::mpiWorld.isend(sendProcID, sendTag, v_sendList);
    }
    //MPIIprobe(recvTags);
    MPIWaitAll(recvRequests);
    MPIWaitAll(sendRequests);

    for (int i = 0; i < recvMPISize; ++i)
    {
        for (int j = 0; j < (int)vv_ghostElements_recv[i].size(); ++j)
        {
            const auto &localIDPair = vv_ghostElements_recv[i][j].GetLocalIDPair();
            const int &localIDReal = localIDPair.second;
            this->v_value[localIDReal] = vv_recvList[i][j];
        }
    }
#endif
}

template void ElementField<int>::SetGhostlValueBoundary();
template void ElementField<Scalar>::SetGhostlValueBoundary();
template void ElementField<Vector>::SetGhostlValueBoundary();
template void ElementField<Tensor>::SetGhostlValueBoundary();
template void ElementField<Matrix>::SetGhostlValueBoundary();
template<class Type>
void ElementField<Type>::SetGhostlValueBoundary()
{
    Mesh *mesh = this->p_blockMesh;
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
    {
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
        {
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();

            this->v_value[neighID] = this->v_value[ownerID];
        }
    }
}

template<>
int ElementField<Scalar>::CheckAndLimit(const Scalar &minValue, const Scalar &maxValue)
{
    if (minValue > maxValue)
        FatalError("In ElementField<Scalar>::CheckAndLimit, minimum value is greater than the maximum one");

    if (false == this->Assignment())
        FatalError("ElementField<Scalar>::CheckAndLimit, value list is not given");

    std::string stringTemp = " of processor " + ToString(GetMPIRank());

    const int elementNumber = this->p_blockMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = this->p_blockMesh->GetElementIDInDomain(index);
        const Scalar &phiC = this->GetValue(elementID);

        if (std::isinf(phiC))
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is inf!");
            return elementID;
        }
        else if (std::isnan(phiC))
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is nan!");
            return elementID;
        }
        else if (phiC < minValue)
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is beyond minValue!");
            this->SetValue(elementID, minValue);
            return elementID;
        }
        else if (phiC > maxValue)
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is beyond maxValue!");
            this->SetValue(elementID, maxValue);
            return elementID;
        }
        else
        {
        }
    }

    return -1;
}

template<>
int ElementField<Vector>::CheckAndLimit(const Scalar &minValue, const Scalar &maxValue)
{
    if (minValue > maxValue)
        FatalError("In ElementField<Vector>::CheckAndLimit, minimum value is greater than the maximum one");

    if (false == this->Assignment())
        FatalError("ElementField<Vector>::CheckAndLimit, value list is not given");

    std::string stringTemp = " of processor " + ToString(GetMPIRank());

    const int elementNumber = this->p_blockMesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = this->p_blockMesh->GetElementIDInDomain(index);
        const Vector &phiC = this->GetValue(elementID);

        if (std::isinf(phiC.X()) || std::isinf(phiC.Y()) || std::isinf(phiC.Z()))
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is inf!");
            return elementID;
        }
        else if (std::isnan(phiC.X()) || std::isnan(phiC.Y()) || std::isnan(phiC.Z()))
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is nan!");
            return elementID;
        }
        else if (phiC.X() < minValue || phiC.Y() < minValue || phiC.Z() < minValue)
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is beyond minValue!");
            this->SetValue(elementID, Vector(Max(minValue, phiC.X()), Max(minValue, phiC.Y()), Max(minValue, phiC.Z())));
            return elementID;
        }
        else if (phiC.X() > maxValue || phiC.Y() > maxValue || phiC.Z() > maxValue)
        {
            Print(this->st_name + " at element " + ToString(elementID) + stringTemp + " is beyond maxValue!");
            this->SetValue(elementID, Vector(Min(maxValue, phiC.X()), Min(maxValue, phiC.Y()), Min(maxValue, phiC.Z())));
            return elementID;
        }
        else
        {
        }
    }

    return -1;
}

template<>
int ElementField<Tensor>::CheckAndLimit(const Scalar &minValue, const Scalar &maxValue)
{
    FatalError("ElementField<Tensor>::CheckAndLimit, CheckAndLimit is not supported");
    return -1;
}

template void ElementField<int>::ReadFile(const std::string fileName, const bool binary);
template void ElementField<Scalar>::ReadFile(const std::string fileName, const bool binary);
template void ElementField<Vector>::ReadFile(const std::string fileName, const bool binary);
template void ElementField<Tensor>::ReadFile(const std::string fileName, const bool binary);
template<class Type>
void ElementField<Type>::ReadFile(const std::string fileName, const bool binary)
{
    std::fstream file;
    if(binary) file.open(fileName, std::ios::in | std::ios::binary);
    else       file.open(fileName, std::ios::in);
    if (!file) FatalError("File is not existed: " + fileName);
    IO::Read(file, this->v_value, binary);
    file.close();
    return;
}

template void ElementField<int>::WriteFile(const std::string fileName, const bool binary);
template void ElementField<Scalar>::WriteFile(const std::string fileName, const bool binary);
template void ElementField<Vector>::WriteFile(const std::string fileName, const bool binary);
template void ElementField<Tensor>::WriteFile(const std::string fileName, const bool binary);
template<class Type>
void ElementField<Type>::WriteFile(const std::string fileName, const bool binary)
{
    std::fstream file;
    if(binary) file.open(fileName, std::ios::out | std::ios::binary);
    else       file.open(fileName, std::ios::out);
    IO::Write(file, this->v_value, binary);
    file.close();
    return;
}
