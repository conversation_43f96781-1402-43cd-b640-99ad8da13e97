﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FieldManipulation.h
//! <AUTHOR>
//! @brief 物理场相关操作.
//! @date 2021-03-30
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释，并对函数参数名称及参数顺序进行调整
//
// 2021-03-13 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _basic_field_FieldManipulation_
#define _basic_field_FieldManipulation_

#include "basic/common/InterpolationTools.h"
#include "basic/configure/ConfigureMacro.h"
#include "basic/field/BoundaryField.h"
#include "basic/field/ElementField.h"
#include "basic/field/NodeField.h"
#include "basic/mesh/Mesh.h"

/**
 * @brief 场运算命名空间
 * 
 */
namespace FieldManipulation
{

/**
 * @brief 利用某种方法计算体心梯度（默认为GreenGauss方法）
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 * @param[in] method 计算方法
 */
template<class Type, class TypeGradient>
void Gradient(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi,
              const GradientScheme method = GradientScheme::GREEN_GAUSS,
              const bool nodeCenter = false);

/**
 * @brief 利用GreenGauss方法计算体心梯度
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 */
template<class Type, class TypeGradient>
void GradientGreenGaussMethod0(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter = false);

/**
 * @brief 利用GreenGauss方法计算体心梯度
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 */
template<class Type, class TypeGradient>
void GradientGreenGaussMethod1(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter = false);

/**
 * @brief 利用GreenGauss方法计算体心梯度
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 */
template<class Type, class TypeGradient>
void GradientGreenGaussMethod2(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter = false);

/**
 * @brief 利用GreenGauss方法计算体心梯度
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 */
template<class Type, class TypeGradient>
void GradientGreenGaussMethod3(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter = false);

/**
 * @brief 利用GreenGauss方法计算体心梯度
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 */
template<class Type, class TypeGradient>
void GradientGreenGaussMethod4(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter = false);

/**
 * @brief 利用最小二乘方法计算体心梯度
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] phi 标量场或矢量场
 * @param[in, out] gradPhi 计算得到的梯度场
 */
template<class Type, class TypeGradient>
void GradientLeastSquare(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter = false);

/**
 * @brief 采用左右体心取均值计算内部面的面心值
 * 
 * @tparam Type 物理场类型，可以为标量、矢量或张量
 * @param[in] faceID 面编号
 * @param[in] phi 物理场
 * @return Type 
 */
template<class Type>
inline Type InnerfaceValue(const int &faceID, const ElementField<Type> &phi)
{
    const int &ownerID = phi.GetMesh()->GetFace(faceID).GetOwnerID();
    const int &neighborID = phi.GetMesh()->GetFace(faceID).GetNeighborID();
    return 0.5*(phi.GetValue(ownerID) + phi.GetValue(neighborID));
}

/**
 * @brief 计算内部面物理场梯度面心值
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] faceID 面编号
 * @param[in] phi 物理场
 * @param[in] gradPhi 物理场梯度
 * @return TypeGradient 
 */
template<class Type, class TypeGradient>
TypeGradient InnerfaceGradientValue(const int &faceID, ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

/**
 * @brief 计算面法向梯度大小
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @param[in] faceID 面编号
 * @param[in] phi 物理场
 * @return Type 
 */
template<class Type>
Type FaceNormalGradientValue(const int &faceID, ElementField<Type> &phi);

/**
 * @brief 计算面法向梯度大小
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @tparam TypeGradient 物理场梯度类型，可为矢量场或张量场
 * @param[in] faceID 面编号
 * @param[in] phi 物理场
 * @param[in] gradPhi 物理场梯度
 * @return Type 
 */
template<class Type, class TypeGradient>
Type FaceNormalGradientValue(const int &faceID, ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi);

/**
 * @brief 由边界场生成单元场
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @param[in] boundaryField 边界场
 * @param[in, out] elementField 单元场
 */
template<class Type>
void BoundaryFieldToElementField(const BoundaryField<Type> &boundaryField, ElementField<Type> &elementField);

/**
 * @brief 由单元场生成边界场
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @param[in] elementField 单元场
 * @param[in, out] boundaryField 边界场
 */
template<class Type>
void ElementFieldToBoundaryField(const ElementField<Type> &elementField, BoundaryField<Type> &boundaryField);

/**
 * @brief 由节点场生成单元场
 * 
 * @tparam Type 物理场类型，可为标量场或矢量场
 * @param[in] nodeField 节点场
 * @param[in, out] elementField 单元场
 */
template<class Type>
void NodeFieldToElementField(const NodeField<Type> &nodeField, ElementField<Type> &elementField);

} // namespace FieldManipulation

#endif
