﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DataPackage.h
//! <AUTHOR>
//! @brief 基本数据包类
//! @date 2020-07-23
//
//------------------------------修改日志----------------------------------------
// 2020-07-23 杨思源
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_field_DataPackage_
#define _basic_field_DataPackage_

#include "basic/field/BoundaryField.h"
#include "basic/field/ElementField.h"
#include "basic/field/NodeField.h"

/**
 * @brief 物理场包类
 * 物理场分类管理，包括创建、存储、调用及释放
 */
class DataPackage
{
public:
    /**
     * @brief 构造函数，创建场包对象
     * 
     * @param[in] pmesh_ 网格指针
     */
    DataPackage(Mesh* pmesh_);

    /**
     * @brief 析构函数
     * 释放场包中的所有物理场
     * 
     */
    ~DataPackage();

       /**
     * @brief 获取场包中的整型单元场
     * 
     * @param[in] fieldName 场位置编号
     * @return ElementField<int>& 
     */
    ElementField<int> &GetIntElementField(const int & fieldName);

    /**
     * @brief 获取场包中的标量单元场
     * 
     * @param[in] fieldName 场位置编号
     * @return ElementField<Scalar>& 
     */
    ElementField<Scalar> &GetScalarElementField(const int & fieldName);

    /**
     * @brief 获取场包中的矢量单元场
     * 
     * @param[in] fieldName 场位置编号
     * @return ElementField<Vector>& 
     */
    ElementField<Vector> &GetVectorElementField(const int & fieldName);

    /**
     * @brief 获取场包中的张量单元场
     * 
     * @param[in] fieldName 场位置编号
     * @return ElementField<Tensor>& 
     */
    ElementField<Tensor> &GetTensorElementField(const int & fieldName);

    /**
     * @brief 获取场包中的标量边界场
     * 
     * @param[in] fieldName 场位置编号
     * @return BoundaryField<Scalar>& 
     */
    BoundaryField<Scalar> &GetScalarBoundaryField(const int & fieldName);

    /**
     * @brief 获取场包中的矢量边界场
     * 
     * @param[in] fieldName 场位置编号
     * @return BoundaryField<Vector>& 
     */
    BoundaryField<Vector> &GetVectorBoundaryField(const int & fieldName);

    /**
     * @brief 获取场包中的张量边界场
     * 
     * @param[in] fieldName 场位置编号
     * @return BoundaryField<Tensor>& 
     */
    BoundaryField<Tensor> &GetTensorBoundaryField(const int & fieldName);

    /**
     * @brief 获取场包中的整型节点场
     * 
     * @param[in] fieldName 场位置编号
     * @return NodeField<int>& 
     */
    NodeField<int> &GetIntNodeField(const int & fieldName);

    /**
     * @brief 获取场包中的标量节点场
     * 
     * @param[in] fieldName 场位置编号
     * @return NodeField<Scalar>& 
     */
    NodeField<Scalar> &GetScalarNodeField(const int & fieldName);

    /**
     * @brief 获取场包中的矢量节点场
     * 
     * @param[in] fieldName 场位置编号
     * @return NodeField<Vector>& 
     */
    NodeField<Vector> &GetVectorNodeField(const int & fieldName);

    /**
     * @brief 获取场包中的张量节点场
     * 
     * @param[in] fieldName 场位置编号
     * @return NodeField<Tensor>& 
     */
    NodeField<Tensor> &GetTensorNodeField(const int & fieldName);

	/**
	* @brief 获取场包中的临时单元场
	*
	* @param[in] name 临时场名称
	* @param[in] initialValue 临时场初始值
	* @return ElementField<Type>&
	*/
	template<class Type>
	std::vector<ElementField<Type> *> &GetTempElementFieldAll(const Type initialValue);

	/**
	* @brief 获取场包中的临时边界场
	*
	* @param[in] name 临时场名称
	* @param[in] initialValue 临时场初始值
	* @return BoundaryField<Type>&
	*/
	template<class Type>
	std::vector<BoundaryField<Type> *> &GetTempBoundaryFieldAll(const Type initialValue);

	/**
	* @brief 获取场包中的临时单元场
	*
	* @param[in] name 临时场名称
	* @param[in] initialValue 临时场初始值
	* @return ElementField<Type>&
	*/
	template<class Type>
	ElementField<Type> &GetTempElementField(const std::string &name, const Type initialValue);

	/**
	* @brief 获取场包中的临时边界场
	*
	* @param[in] name 临时场名称
	* @param[in] initialValue 临时场初始值
	* @return BoundaryField<Type>&
	*/
	template<class Type>
	BoundaryField<Type> &GetTempBoundaryField(const std::string &name, const Type initialValue);

    /**
     * @brief 获取场包中的临时整型单元场
     * 
     * @param[in] name 临时场名称
     * @param[in] initialValue 临时场初始值
     * @return ElementField<int>& 
     */
	ElementField<int> &GetIntTempField(const std::string &name, const int initialValue = 0);

    /**
     * @brief 获取场包中的临时标量单元场
     * 
     * @param[in] name 临时场名称
     * @param[in] initialValue 临时场初始值
     * @return ElementField<Scalar>& 
     */
	ElementField<Scalar> &GetScalarTempField(const std::string &name, const Scalar initialValue = Scalar0);

    /**
     * @brief 获取场包中的临时矢量单元场
     * 
     * @param[in] name 临时场名称
     * @param[in] initialValue 临时场初始值
     * @return ElementField<Vector>& 
     */
	ElementField<Vector> &GetVectorTempField(const std::string &name, const Vector initialValue = Vector0);

	/**
	* @brief 释放临时场
	*
	* @param[in] phi 待释放临时场
	*/
	template<class Type>
	void FreeTempField(Type &phi);

    /**
     * @brief 释放临时整型单元场
     * 
     * @param[in] phi 待释放临时场
     */
    void FreeIntTempField(ElementField<Int> &phi);

    /**
     * @brief 释放临时标量单元场
     * 
     * @param[in] phi 待释放临时场
     */
    void FreeScalarTempField(ElementField<Scalar> &phi);

    /**
     * @brief 释放临时矢量单元场
     * 
     * @param[in] phi 待释放临时场
     */
    void FreeVectorTempField(ElementField<Vector> &phi);

    /**
     * @brief 检查给定位置编号处的场释放存在
     * 
     * @param dataPos 场位置编号
     * @return true 
     * @return false 
     */
    bool CheckField(const int& dataPos);

protected:
    /**
     * @brief 在指定位置创建整型单元场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return ElementField<int>* 
     */
    ElementField<int> *CreatIntElementField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建标量单元场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return ElementField<Scalar>* 
     */
    ElementField<Scalar> *CreatScalarElementField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建矢量单元场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return ElementField<Vector>* 
     */
    ElementField<Vector> *CreatVectorElementField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建张量单元场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return ElementField<Tensor>* 
     */
    ElementField<Tensor> *CreatTensorElementField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建标量边界场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return BoundaryField<Scalar>* 
     */
    BoundaryField<Scalar> *CreatScalarBoundaryField(const int& dataPos, const std::string &name);

    /**
     * @brief  在指定位置创建矢量边界场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return BoundaryField<Vector>* 
     */
    BoundaryField<Vector> *CreatVectorBoundaryField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建张量边界场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return BoundaryField<Tensor>* 
     */
    BoundaryField<Tensor> *CreatTensorBoundaryField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建整型节点场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return NodeField<int>* 
     */
    NodeField<int> *CreatIntNodeField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建标量节点场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return NodeField<Scalar>* 
     */
    NodeField<Scalar> *CreatScalarNodeField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建矢量节点场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return NodeField<Vector>* 
     */
    NodeField<Vector> *CreatVectorNodeField(const int& dataPos, const std::string &name);

    /**
     * @brief 在指定位置创建张量节点场
     * 
     * @param[in] dataPos 场位置
     * @param[in] name 场名称
     * @return NodeField<Tensor>* 
     */
    NodeField<Tensor> *CreatTensorNodeField(const int& dataPos, const std::string &name);

    /**
     * @brief 创建临时标量单元场和矢量单元场
     * 
     * @param[in] nScalarTempField 临时标量单元场数量
     * @param[in] nVectorTempField 临时矢量单元场数量
     */
    void SetTempField(const int &nScalarTempField, const int &nVectorTempField);
    
    /**
     * @brief 删除单元场 
     * 
     * @param[in] field 待删除单元场
     */
    template<class Type>
	void DeleteFieldPointer(ElementField<Type> *field){ if (field != nullptr) { delete field; field = nullptr; } }

    /**
     * @brief 删除边界场 
     * 
     * @param[in] field 待删除边界场
     */
    template<class Type>
	void DeleteFieldPointer(BoundaryField<Type> *field){ if (field != nullptr) { delete field; field = nullptr; } }

    /**
     * @brief 删除节点场 
     * 
     * @param[in] field 待删除节点场
     */
    template<class Type>
	void DeleteFieldPointer(NodeField<Type> *field){ if (field != nullptr) { delete field; field = nullptr; } }

private:
    Mesh* pmesh; ///< 场包所依赖的网格
    
    int IntElementPackageSize; ///< 整型单元场数量
    int ScalarElementPackageSize; ///< 标量单元场数量
    int VectorElementPackageSize; ///< 矢量单元场数量
    int TensorElementPackageSize; ///< 张量单元场数量

    int ScalarBoundaryPackageSize; ///< 标量边界场数量
    int VectorBoundaryPackageSize; ///< 矢量边界场数量
    int TensorBoundaryPackageSize; ///< 张量边界场数量

    int IntNodePackageSize; ///< 整型节点场数量
    int ScalarNodePackageSize; ///< 标量节点场数量
    int VectorNodePackageSize; ///< 矢量节点场数量
    int TensorNodePackageSize; ///< 张量节点场数量

    std::vector<ElementField<int>* > intElementFieldPackage; ///< 整型单元场包
    std::vector<ElementField<Scalar>* > scalarElementFieldPackage; ///< 标量单元场包
    std::vector<ElementField<Vector>* > vectorElementFieldPackage; ///< 矢量单元场包
    std::vector<ElementField<Tensor>* > tensorElementFieldPackage; ///< 张量单元场包

    std::vector<BoundaryField<Scalar>* > scalarBoundaryFieldPackage; ///< 标量边界场包
    std::vector<BoundaryField<Vector>* > vectorBoundaryFieldPackage; ///< 矢量边界场包
    std::vector<BoundaryField<Tensor>* > tensorBoundaryFieldPackage; ///< 张量边界场包

    std::vector<NodeField<int>* > intNodeFieldPackage; ///< 整型节点场包
    std::vector<NodeField<Scalar>* > scalarNodeFieldPackage; ///< 标量节点场包
    std::vector<NodeField<Vector>* > vectorNodeFieldPackage; ///< 矢量节点场包
    std::vector<NodeField<Tensor>* > tensorNodeFieldPackage; ///< 张量节点场包

	std::vector<ElementField<int>* > intElementFieldTemp; ///< 整型单元场包
    std::vector<ElementField<Scalar> *> scalarElementFieldTemp; ///< 标量临时单元场包
    std::vector<ElementField<Vector> *> vectorElementFieldTemp; ///< 矢量临时单元场包

	std::vector<BoundaryField<int>* > intBoundaryFieldTemp; ///< 整型边界场包
	std::vector<BoundaryField<Scalar> *> scalarBoundaryFieldTemp; ///< 标量临时边界场包
	std::vector<BoundaryField<Vector> *> vectorBoundaryFieldTemp; ///< 矢量临时边界场包

	int nIntElementTempFieldMax; ///< 整型临时单元场数量
	int nScalarElementTempFieldMax; ///< 标量临时单元场数量
	int nVectorElementTempFieldMax; ///< 矢量临时单元场数量

	int nIntBoundaryTempFieldFixed; ///< 整型临时边界场数量
	int nScalarBoundaryTempFieldFixed; ///< 标量临时边界场数量
	int nVectorBoundaryTempFieldFixed; ///< 矢量临时边界场数量
};

#endif