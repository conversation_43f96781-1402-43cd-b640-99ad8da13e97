﻿#include "basic/field/FieldManipulation.h"

namespace FieldManipulation
{

template void Gradient(const ElementField<Scalar> &phi, ElementField<Vector> &grad<PERSON>hi, const GradientScheme method, const bool nodeCenter);
template void Gradient(const ElementField<Vector> &phi, ElementField<Tensor> &grad<PERSON>hi, const GradientScheme method, const bool nodeCenter);
template<class Type, class TypeGradient>
void Gradient(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const GradientScheme method, const bool nodeCenter)
{
    if (method == GradientScheme::GREEN_GAUSS)
    {
        GradientGreenGaussMethod0(phi, gradPhi, nodeCenter);
    }
    else if (method == GradientScheme::LEAST_SQUARE)
    {
        GradientLeastSquare(phi, gradPhi, nodeCenter);
    }
    else
    {
        FatalError("Gradient: Unsupported method for gradient calculation");
    }

    return;
}

template void GradientGreenGaussMethod0(const ElementField<Scalar> &phi, ElementField<Vector> &grad<PERSON>hi, const bool nodeCenter);
template void GradientGreenGaussMethod0(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi, const bool nodeCenter);
template<class Type, class TypeGradient>
void GradientGreenGaussMethod0(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    // 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
		    // 得到面相关信息
		    const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();
            const Vector faceArea = face.GetArea() * face.GetNormal();

            //计算面心通量并累加（系数0.5放在最后处理）
            if(nodeCenter)
            {
                const TypeGradient phiFlux = faceArea * (2.0 * phi.GetValue(ownerID));
                gradPhi.AddValue(ownerID, phiFlux);
            }
            else
            {
                const TypeGradient phiFlux = faceArea * (phi.GetValue(ownerID) + phi.GetValue(neighID));
                gradPhi.AddValue(ownerID, phiFlux);
            }
        }
    }
    
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Vector faceArea = face.GetArea() * face.GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 0.5 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void GradientGreenGaussMethod1(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi, const bool nodeCenter);
template void GradientGreenGaussMethod1(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi, const bool nodeCenter);
template<class Type, class TypeGradient>
void GradientGreenGaussMethod1(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    ElementField<TypeGradient> gradPhiTemp(mesh);    
    gradPhiTemp.Initialize();

    //第一次迭代
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算两心连线与面相交处的物理量（即f'）
        Type phiCross;
        if (mesh->JudgeBoundaryFace(faceID))
        {
            phiCross = 0.5 * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        }
        else
        {
            Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            Vector e = distance.GetNormal();
            Vector cross = ((faceCenter & faceNormal) / (e & faceNormal)) * e;
            Scalar ownerWeight = (mesh->GetElement(neighID).GetCenter() - cross).Mag() / (mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter()).Mag();
            Scalar neighWeight = 1.0 - ownerWeight;
            phiCross = phi.GetValue(ownerID) * ownerWeight + phi.GetValue(neighID) * neighWeight;
        }
        
        //计算面心通量并累加
        const TypeGradient phiFlux = faceArea * phiCross;
        gradPhiTemp.AddValue(ownerID, phiFlux);
        gradPhiTemp.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhiTemp.MultiplyValue(elementID, coefficient);
    }        

    gradPhiTemp.SetGhostlValueParallel();
    gradPhiTemp.SetGhostlValueOverset();
    gradPhiTemp.SetGhostlValueBoundary();

    //第二次迭代
    gradPhi.Initialize();
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心处的物理量（即f）
        Type phiFace;
        if (mesh->JudgeBoundaryFace(faceID))
        {
            phiFace = 0.5 * (phi.GetValue(ownerID) + phi.GetValue(neighID));
        }
        else
        {
            Vector distance = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
            Vector e = distance.GetNormal();
            Vector cross = ((faceCenter & faceNormal) / (e & faceNormal)) * e;
            Scalar ownerWeight = (mesh->GetElement(neighID).GetCenter() - cross).Mag() / (mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter()).Mag();
            Scalar neighWeight = 1.0 - ownerWeight;
            Type phiCross = phi.GetValue(ownerID) * ownerWeight + phi.GetValue(neighID) * neighWeight;
            phiFace = phiCross
                    + ownerWeight * Dot(faceCenter - mesh->GetElement(ownerID).GetCenter(), gradPhiTemp.GetValue(ownerID))
                    + neighWeight * Dot(faceCenter - mesh->GetElement(neighID).GetCenter(), gradPhiTemp.GetValue(neighID));
        }

        //计算面心通量并累加
        const TypeGradient phiFlux = faceArea * phiFace;
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void GradientGreenGaussMethod2(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi, const bool nodeCenter);
template void GradientGreenGaussMethod2(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi, const bool nodeCenter);
template<class Type, class TypeGradient>
void GradientGreenGaussMethod2(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter)
{
    GradientGreenGaussMethod0(phi, gradPhi);

    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();
    
    //第二次迭代
    for (int step = 0; step < 1; ++step)
    {
        //梯度置零
        ElementField<TypeGradient> gradPhiTemp(mesh);
        gradPhiTemp.Initialize();

        for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
        {
            // 得到面相关信息
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();
            const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
            const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

            //计算面心处的物理量（即f）
            Type phiFace;
            if (mesh->JudgeBoundaryFace(faceID))
            {
                //0.5在单元循环中乘
                phiFace = phi.GetValue(ownerID) + phi.GetValue(neighID);
            }
            else
            {
                //0.5在单元循环中乘
                Type phiCross = phi.GetValue(ownerID) + phi.GetValue(neighID);
                Vector temp1 = faceCenter - 0.5 * (mesh->GetElement(ownerID).GetCenter() + mesh->GetElement(neighID).GetCenter());
                TypeGradient temp2 = gradPhi.GetValue(ownerID) + gradPhi.GetValue(neighID);
                phiFace = phiCross + Dot(temp1, temp2);
            }

            //计算面心通量并累加
            const TypeGradient phiFlux = faceArea * phiFace;
            gradPhiTemp.AddValue(ownerID, phiFlux);
            gradPhiTemp.AddValue(neighID, -phiFlux);
        }

        //除以体积
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            const Scalar coefficient = 0.5 / mesh->GetElement(elementID).GetVolume();
            gradPhiTemp.MultiplyValue(elementID, coefficient);
        }

        gradPhiTemp.SetGhostlValueParallel();
        gradPhiTemp.SetGhostlValueOverset();
        gradPhiTemp.SetGhostlValueBoundary();
        
        gradPhi = gradPhiTemp;
    }

    return;
}

template void GradientGreenGaussMethod3(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi, const bool nodeCenter);
template void GradientGreenGaussMethod3(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi, const bool nodeCenter);
template<class Type, class TypeGradient>
void GradientGreenGaussMethod3(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    //面循环
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector &ownerCenter = mesh->GetElement(ownerID).GetCenter();
        const Vector &neighCenter = mesh->GetElement(neighID).GetCenter();
        const Vector distLeft = ownerCenter - faceCenter;
        const Vector distRight = neighCenter - faceCenter;
        const Scalar distMagLeft = distLeft.Mag();
        const Scalar distMagRight = distRight.Mag();
        const Scalar weightLeft = distMagRight / Max(distMagLeft + distMagRight, SMALL);
        const Scalar weightRight = 1.0 - weightLeft;

        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (weightLeft * phi.GetValue(ownerID) + weightRight * phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void GradientGreenGaussMethod4(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi, const bool nodeCenter);
template void GradientGreenGaussMethod4(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi, const bool nodeCenter);
template<class Type, class TypeGradient>
void GradientGreenGaussMethod4(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter)
{
    //得到网格指针
    Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    //面循环
    for (int faceID = 0; faceID < mesh->GetFaceNumber(); ++faceID)
    {
        // 得到面相关信息
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
        const Vector &ownerCenter = mesh->GetElement(ownerID).GetCenter();
        const Vector &neighCenter = mesh->GetElement(neighID).GetCenter();
        const Vector distLeft = ownerCenter - faceCenter;
        const Vector distRight = neighCenter - faceCenter;
        const Scalar distMagLeft = distLeft.Mag();
        const Scalar distMagRight = distRight.Mag();
        const Scalar weightLeft = distMagRight / (distMagLeft + distMagRight);
        const Scalar weightRight = 1.0 - weightLeft;

        const Vector faceArea = mesh->GetFace(faceID).GetArea() * mesh->GetFace(faceID).GetNormal();

        //计算面心通量并累加（系数0.5放在最后处理）
        const TypeGradient phiFlux = faceArea * (weightLeft * phi.GetValue(ownerID) + weightRight * phi.GetValue(neighID));
        gradPhi.AddValue(ownerID, phiFlux);
        gradPhi.AddValue(neighID, -phiFlux);
    }

    //除以体积
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar coefficient = 1.0 / mesh->GetElement(elementID).GetVolume();
        gradPhi.MultiplyValue(elementID, coefficient);
    }

    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

template void GradientLeastSquare(const ElementField<Scalar> &phi, ElementField<Vector> &gradPhi, const bool nodeCenter);
template void GradientLeastSquare(const ElementField<Vector> &phi, ElementField<Tensor> &gradPhi, const bool nodeCenter);
template<class Type, class TypeGradient>
void GradientLeastSquare(const ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi, const bool nodeCenter)
{
    //得到网格指针
    const Mesh *mesh = gradPhi.GetMesh();

    //梯度置零
    gradPhi.Initialize();

    TypeGradient(*LeastSquareGradient)(const std::vector<Vector>&, const std::vector<Type>&) = nullptr;

    if (Mesh::md2D == mesh->GetMeshDimension())
    {
        LeastSquareGradient = &LeastSquareGradient2D;
    }
    else if (Mesh::md3D == mesh->GetMeshDimension())
    {
        LeastSquareGradient = &LeastSquareGradient3D;
    }

    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Type &phiC = phi.GetValue(elementID);
        const std::vector<int> &nbIDList = mesh->GetNeighborIDFaceAdjoin(elementID);
        std::vector<Type> deltaPhi;
        std::vector<Vector> deltaR;
        deltaPhi.resize(nbIDList.size());
        deltaR.resize(nbIDList.size());
        for (int k = 0; k < (int)nbIDList.size(); ++k)
        {
            const int &nbID = nbIDList[k];
            deltaPhi[k] = phi.GetValue(nbID) - phiC;
            deltaR[k] = mesh->GetElement(nbID).GetCenter() - mesh->GetElement(elementID).GetCenter();
        }
        gradPhi.SetValue(elementID, LeastSquareGradient(deltaR, deltaPhi));
    }
    
    gradPhi.SetGhostlValueParallel();
    gradPhi.SetGhostlValueOverset();
    gradPhi.SetGhostlValueBoundary();

    return;
}

//计算面心梯度大小
template Vector InnerfaceGradientValue(const int &faceID, ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template Tensor InnerfaceGradientValue(const int &faceID, ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
TypeGradient InnerfaceGradientValue(const int &faceID, ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    TypeGradient gradPhiFace = InnerfaceValue(faceID, gradPhi);
    
    //修正梯度值
    const  int &ownerID = phi.GetMesh()->GetFace(faceID).GetOwnerID();
    const  int &neighID = phi.GetMesh()->GetFace(faceID).GetNeighborID();
    Vector distance = phi.GetMesh()->GetElement(neighID).GetCenter() - phi.GetMesh()->GetElement(ownerID).GetCenter();
    Vector distanceNormal = distance.GetNormal();
    Type dPhidL = (phi.GetValue(neighID) - phi.GetValue(ownerID)) / distance.Mag();
    gradPhiFace -= distanceNormal * (Dot(distanceNormal, gradPhiFace) - dPhidL);

    return gradPhiFace;
}

//计算面法向梯度大小
template Scalar FaceNormalGradientValue(const int &faceID, ElementField<Scalar> &phi);
template Vector FaceNormalGradientValue(const int &faceID, ElementField<Vector> &phi);
template<class Type>
Type FaceNormalGradientValue(const int &faceID, ElementField<Type> &phi)
{
    const  int &ownerID = phi.GetMesh()->GetFace(faceID).GetOwnerID();
    const  int &neighID = phi.GetMesh()->GetFace(faceID).GetNeighborID();
    Vector distance = phi.GetMesh()->GetElement(neighID).GetCenter() - phi.GetMesh()->GetElement(ownerID).GetCenter();
    return (phi.GetValue(neighID) - phi.GetValue(ownerID)) / distance.Mag();
}

//计算面法向梯度大小
template Scalar FaceNormalGradientValue(const int &faceID, ElementField<Scalar> &phi, ElementField<Vector> &gradPhi);
template Vector FaceNormalGradientValue(const int &faceID, ElementField<Vector> &phi, ElementField<Tensor> &gradPhi);
template<class Type, class TypeGradient>
Type FaceNormalGradientValue(const int &faceID, ElementField<Type> &phi, ElementField<TypeGradient> &gradPhi)
{
    const  int &ownerID = phi.GetMesh()->GetFace(faceID).GetOwnerID();
    const  int &neighID = phi.GetMesh()->GetFace(faceID).GetNeighborID();
    const Vector &faceNormal = phi.GetMesh()->GetFace(faceID).GetNormal();
    
    const Vector distLeft = phi.GetMesh()->GetElement(ownerID).GetCenter() - phi.GetMesh()->GetFace(faceID).GetCenter();
    const Vector distRight = phi.GetMesh()->GetElement(neighID).GetCenter() - phi.GetMesh()->GetFace(faceID).GetCenter();
    const Scalar distNormalLeft = faceNormal & distLeft;
    const Scalar distNormalRight = faceNormal & distRight;
    
    // 计算面法向方向左右温度和速度大小
    Type phiLeft = phi.GetValue(ownerID) - Dot((distLeft - faceNormal * distNormalLeft), gradPhi.GetValue(ownerID));
    Type phiRight = phi.GetValue(neighID) - Dot((distRight - faceNormal * distNormalRight), gradPhi.GetValue(neighID));
    Type phiFace = 0.5 * (phiLeft + phiRight);

    // 计算面心速度和温度梯度法向分量
    const Scalar ratio = distNormalRight / distNormalLeft;
    Scalar weightLeft = 1.0 / (1.0 + ratio * ratio);
    return weightLeft * (phiLeft - phiFace) / distNormalLeft + (1.0 - weightLeft) * (phiRight - phiFace) / distNormalRight;
}

//由边界场生成单元场
template void BoundaryFieldToElementField(const BoundaryField<Scalar> &boundaryField, ElementField<Scalar> &elementField);
template void BoundaryFieldToElementField(const BoundaryField<Vector> &boundaryField, ElementField<Vector> &elementField);
template void BoundaryFieldToElementField(const BoundaryField<Tensor> &boundaryField, ElementField<Tensor> &elementField);
template<class Type>
void BoundaryFieldToElementField(const BoundaryField<Type> &boundaryField, ElementField<Type> &elementField)
{
    const Mesh *mesh = boundaryField.GetMesh();
    if (mesh != elementField.GetMesh())
        FatalError("BoundaryFieldToElementField: 边界场与单元场网格不匹配！");
    
    elementField.Initialize();

	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighborID = mesh->GetFace(faceID).GetNeighborID();

			// 虚单元存储面心值
            elementField.SetValue(neighborID, boundaryField.GetValue(patchID, index));
        }
    }
}

//由单元场生成边界场
template void ElementFieldToBoundaryField(const ElementField<Scalar> &elementField, BoundaryField<Scalar> &boundaryField);
template void ElementFieldToBoundaryField(const ElementField<Vector> &elementField, BoundaryField<Vector> &boundaryField);
template void ElementFieldToBoundaryField(const ElementField<Tensor> &elementField, BoundaryField<Tensor> &boundaryField);
template<class Type>
void ElementFieldToBoundaryField(const ElementField<Type> &elementField, BoundaryField<Type> &boundaryField)
{
    const Mesh *mesh = elementField.GetMesh();
    if (mesh != boundaryField.GetMesh())
        FatalError("ElementFieldToBoundaryField: 边界场与单元场网格不匹配！");
    
    boundaryField.Initialize();
    
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighborID = mesh->GetFace(faceID).GetNeighborID();

			// 虚单元存储面心值
            const Type value = 0.5 * (elementField.GetValue(ownerID) + elementField.GetValue(neighborID));
            boundaryField.SetValue(patchID, index, value);
        }
    }
}

template void NodeFieldToElementField(const NodeField<int> &nodeField, ElementField<int> &elementField);
template void NodeFieldToElementField(const NodeField<Scalar> &nodeField, ElementField<Scalar> &elementField);
template void NodeFieldToElementField(const NodeField<Vector> &nodeField, ElementField<Vector> &elementField);
template void NodeFieldToElementField(const NodeField<Tensor> &nodeField, ElementField<Tensor> &elementField);
template<class Type>
void NodeFieldToElementField(const NodeField<Type> &nodeField, ElementField<Type> &elementField)
{
    const Mesh *mesh = elementField.GetMesh();
    if (mesh != nodeField.GetMesh())
        FatalError("NodeFieldToElementField: 节点场与单元场网格不匹配！");
    
    elementField.Initialize();
    for (int elementID = 0; elementID < mesh->GetElementNumberReal(); ++elementID)
    {
        const int nodeSize = mesh->GetElement(elementID).GetNodeSize();
        for (int index = 0; index < nodeSize; ++index)
        {
            const int &nodeID = mesh->GetElement(elementID).GetNodeID(index);
            elementField.AddValue(elementID, nodeField.GetValue(nodeID));
        }
        elementField.SetValue(elementID, elementField.GetValue(elementID) / nodeSize);
    }

    // 边界面修改
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
    {
        for (int index = 0; index < mesh->GetBoundaryFaceSize(patchID); index++)
        {
            const int &faceID = mesh->GetBoundaryFaceID(patchID, index);
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();

            // 虚单元存储面心值
            Type value = elementField.GetValue(neighID);
            const int nodeSize = mesh->GetFace(faceID).GetNodeSize();
            for (int index1 = 0; index1 < nodeSize; ++index1)
            {
                const int &nodeID = mesh->GetFace(faceID).GetNodeID(index1);
                value += nodeField.GetValue(nodeID);
            }
            elementField.SetValue(neighID, value / nodeSize);
        }
    }
}

}//namespace FieldManipulation