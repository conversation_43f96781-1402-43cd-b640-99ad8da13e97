﻿#include "basic/field/BaseField.h"

template<>
Int BaseField<Int>::Get<PERSON><PERSON>() const
{
    return 0;
}

template<>
Scalar BaseField<Scalar>::Get<PERSON><PERSON>() const
{
    return Scalar0;
}

template<>
Vector BaseField<Vector>::Get<PERSON><PERSON>() const
{
    return Vector0;
}

template<>
Tensor BaseField<Tensor>::Get<PERSON>ero() const
{
    return Tensor0;
}

template<>
Matrix BaseField<Matrix>::GetZero() const
{
    Matrix value = this->v_value[0];
    return value - value;
}
