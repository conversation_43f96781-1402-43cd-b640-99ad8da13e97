﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BaseField.h
//! <AUTHOR> 张帅（西交大/数峰科技）
//! @brief 基本场类
//! @date 2020-07-23
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-23 凌空 张帅
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_field_BaseField_
#define _basic_field_BaseField_

#include "basic/mesh/Mesh.h"

/**
 * @brief 场基类
 * 
 * @tparam Type 场物理量类型
 */
template<class Type>
class BaseField
{
public:
    /**
     * @brief 枚举类型：场的状态
     * 
     */
    enum FieldStatus
    {
        fsNotExist, ///< 不存在
        fsCreated, ///< 创建
        fsAssigned ///< 分配
    };

public:
    /**
     * @brief 构造函数
     * 
     * @param[in] UGB 网格指针
     * @param[in] name 场名称
     */
	BaseField(Mesh* UGB, const std::string name = "NO_NAME") : p_blockMesh(UGB), fs_status(fsNotExist), st_name(name) {}

    /**
     * @brief 获取网格
     * 
     * @return Mesh* 
     */
    Mesh *GetMesh() const {return this->p_blockMesh;}
    
    /**
     * @brief 获得给定位置的场值
     * 
     * @param[in] ID 位置编号
     * @return const Type& 
     */
    const Type &GetValue(const int &ID) const { return this->v_value[ID];}

    /**
     * @brief 修改给定位置的场值
     * 
     * @param[in] ID 位置编号
     * @param value 
     */
    void SetValue(const int &ID, const Type &value) { this->v_value[ID] = value;}

    /**
     * @brief 给定位置的场值增加值
     * 
     * @param[in] ID 位置编号
     * @param value 增加值
     */
    void AddValue(const int &ID, const Type &value) { this->v_value[ID] += value;}

    /**
     * @brief 给定位置的场值乘值
     * 
     * @param[in] ID 位置编号
     * @param value 乘数
     */
    void MultiplyValue(const int &ID, const Scalar &value) { this->v_value[ID] *= value;}

    /**
     * @brief 获得场的名称
     * 
     * @return const std::string& 
     */
    const std::string &GetName() const { return this->st_name;}

    /**
     * @brief 修改场的名称
     * 
     * @param[in] name 场名称
     */
    void SetName(const std::string &name) { this->st_name = name;}

    /**
     * @brief 获得场的空间大小
     * 
     * @return const int 
     */
    const int GetSize() const { return this->v_value.size();}

    /**
     * @brief 检查场是否存在
     * 
     * @return true 存在
     * @return false 不存在
     */
	bool Existence() const { return BaseField<Type>::fsNotExist != this->fs_status; }

    /**
     * @brief 检查场空间是否已经分配
     * 
     * @return true 分配
     * @return false 未分配
     */
	bool Assignment() const { return BaseField<Type>::fsAssigned == this->fs_status; }

    /**
     * @brief 释放场
     * 修改场的状态为fsCreated，场存储空间保留，值没有意义
     * 
     */
    void Free() { this->fs_status = fsCreated; }

    /**
     * @brief 销毁场存储空间
     * 
     */
	void Destroy() { this->fs_status = BaseField<Type>::fsNotExist; std::vector<Type>().swap(this->v_value); }
    
    /**
     * @brief 获取零值
     * 
     */
    Type GetZero() const;
    
protected:    
    Mesh* p_blockMesh; ///< 网格指针    
    FieldStatus fs_status; ///< 场的状态    
    std::string st_name; ///< 场的名称    
    std::vector<Type> v_value; ///< 场（face/node/element）的存储值
};

#endif
