﻿#include "basic/field/BoundaryField.h"

template BoundaryField<Scalar>::Bo<PERSON>ry<PERSON>ield(Mesh*, const std::string);
template BoundaryField<Vector>::BoundaryField(Mesh*, const std::string);
template BoundaryField<Tensor>::Bo<PERSON>ry<PERSON>ield(Mesh*, const std::string);
template<class Type>
BoundaryField<Type>::BoundaryField(Mesh* UGB, const std::string name)
                :
                p_block<PERSON>esh(UGB), st_name(name), fs_status(fsNotExist)
{};

template BoundaryField<Scalar>::BoundaryField(Mesh*, const Scalar&, const std::string);
template BoundaryField<Vector>::BoundaryField(Mesh*, const Vector&, const std::string);
template BoundaryField<Tensor>::BoundaryField(Mesh*, const Tensor&, const std::string);
template<class Type>
BoundaryField<Type>::BoundaryField(Mesh* UGB, const Type &value, const std::string name)
                :
                p_blockMesh(UGB), st_name(name), fs_status(fsNotExist)
{
    this->Initialize(value);
}

//Create boundary Field
template void BoundaryField<Scalar>::Create();
template void BoundaryField<Vector>::Create();
template void BoundaryField<Tensor>::Create();
template<class Type>
void BoundaryField<Type>::Create()
{
    if (FieldStatus::fsNotExist == this->fs_status)
    {
        this->v_value.resize(p_blockMesh->GetBoundarySize());
        for (int i = 0; i < p_blockMesh->GetBoundarySize(); ++i)
            this->v_value[i].resize(p_blockMesh->GetBoundaryFaceSize(i));
        this->fs_status = FieldStatus::fsCreated;
    }
}

//Initialize with a fixed value
template void BoundaryField<Scalar>::Initialize(Scalar initalValue);
template void BoundaryField<Vector>::Initialize(Vector initalValue);
template void BoundaryField<Tensor>::Initialize(Tensor initalValue);
template<class Type>
void BoundaryField<Type>::Initialize(Type initalValue)
{
    this->Create();
    
    for (int i = 0; i < this->v_value.size(); ++i)
        for (int j = 0; j < this->v_value[i].size(); ++j)
            this->v_value[i][j] = initalValue;
        
    this->fs_status = FieldStatus::fsAssigned;
}

//Initialize with a zero value
template<> void BoundaryField<Scalar>::Initialize(){ this->Initialize(Scalar0); }
template<> void BoundaryField<Vector>::Initialize(){ this->Initialize(Vector0); }
template<> void BoundaryField<Tensor>::Initialize(){ this->Initialize(Tensor0); }

// "="overload
template BoundaryField<Scalar>& BoundaryField<Scalar>::operator = (const BoundaryField<Scalar>& rhs);
template BoundaryField<Vector>& BoundaryField<Vector>::operator = (const BoundaryField<Vector>& rhs);
template BoundaryField<Tensor>& BoundaryField<Tensor>::operator = (const BoundaryField<Tensor>& rhs);
template<class Type>
BoundaryField<Type>& BoundaryField<Type>::operator = (const BoundaryField<Type>& rhs)
{
    this->p_blockMesh = rhs.p_blockMesh;
    this->v_value = rhs.v_value;
    this->fs_status = FieldStatus::fsAssigned;
    return *this;
}

// "="overload
template BoundaryField<Scalar>& BoundaryField<Scalar>::operator = (const Scalar& rhs);
template BoundaryField<Vector>& BoundaryField<Vector>::operator = (const Vector& rhs);
template BoundaryField<Tensor>& BoundaryField<Tensor>::operator = (const Tensor& rhs);
template<class Type>
BoundaryField<Type>& BoundaryField<Type>::operator = (const Type& rhs)
{
    this->Initialize(rhs);
    this->fs_status = FieldStatus::fsAssigned;
    return *this;
}

template void BoundaryField<Scalar>::ReadFile(const std::string fileName, const bool binary);
template void BoundaryField<Vector>::ReadFile(const std::string fileName, const bool binary);
template void BoundaryField<Tensor>::ReadFile(const std::string fileName, const bool binary);
template<class Type>
void BoundaryField<Type>::ReadFile(const std::string fileName, const bool binary)
{
    std::fstream file;
    if(binary) file.open(fileName, std::ios::in | std::ios::binary);
    else       file.open(fileName, std::ios::in);
    if (!file) FatalError("File is not existed: " + fileName);
    IO::Read(file, this->v_value, binary);
    file.close();
    return;
}

template void BoundaryField<Scalar>::WriteFile(const std::string fileName, const bool binary);
template void BoundaryField<Vector>::WriteFile(const std::string fileName, const bool binary);
template void BoundaryField<Tensor>::WriteFile(const std::string fileName, const bool binary);
template<class Type>
void BoundaryField<Type>::WriteFile(const std::string fileName, const bool binary)
{
    std::fstream file;
    if(binary) file.open(fileName, std::ios::out | std::ios::binary);
    else       file.open(fileName, std::ios::out);
    IO::Write(file, this->v_value, binary);
    file.close();
    return;
}