﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Mesh.h
//! <AUTHOR>
//! @brief 网格类
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_Mesh_
#define _basic_mesh_Mesh_

#include "basic/mesh/BaseMesh.h"

// 用于友元类的声明（暂时）
class DecomposeManager;
class AgglomerateManager;
class DualMesh;
class MeshSorting;
class WallDistanceBase;
class WallDistanceManager;
class MeshProcess;
class CgnsMeshBase;
class CgnsMesh;
class DlgMesh;
class FluentMeshBlock;
class MeshConverter;
class MeshConvertManager;
class SubMesh;
class OversetMesh;
class BackgroundGrid;

class Mesh: public BaseMesh
{
public:
    Mesh(); ///< 构造函数
    Mesh(const std::string& MshFileName); ///< 构造函数
    const std::vector<std::vector<GhostElement> > &GetGhostElementsParallel()const {return this->vv_ghostElement_parallel;}
    const std::vector<std::vector<GhostElement> > &GetGhostElementsMultigrid()const { return this->vv_ghostElement_multigrid; }
    const int &GetBoundaryIDGlobal(const int &localID)const { return this->v_boundaryIDGlobal[localID]; }
    
    const Scalar &GetNearWallDistance(const int &elementID)const {return this->v_nearWallDistance[elementID];}
    std::vector<Scalar> &GetNearWallDistance(){ return this->v_nearWallDistance; }
    const std::vector<int> &GetNeighborIDFaceAdjoin(const int &elementID)const {return this->vv_neighborID_faceAdjoin[elementID];}
    const int &GetInnerElementIDForBoundaryElement(const int &patchID, const int &index) const
    {return this->vv_innerElementID[patchID][this->vv_boundaryFaceIndexInDomain[patchID][index]];}

    void SetInnerElementIDForBoundaryElement(const int &patchID, const int &index, const int &ID) { this->vv_innerElementID[patchID][index] = ID; }
    void SetNearWallDistance(const int &elementID, const Scalar &value) { this->v_nearWallDistance[elementID] = value; }
	    
    bool JudgeBoundaryFace(const int &faceID)const {return this->v_elem[v_face[faceID].n_neighbor].et_type == Element::ElemType::ghostBoundary;}
    bool JudgeRealElement(const int &elementID)const {return this->v_elem[elementID].et_type == Element::ElemType::real;}
    bool JudegBoundaryElemnt(const int &elementID)const { return this->v_boundaryElementFlag[elementID]; }

    bool JudgeHalfFaceCrossSymmetryBoundary(const int &faceID)const { return this->halfFaceCrossSymmetryBoundaryFlag[faceID]; }

    OversetRegion &GetOversetRegion(){ return this->oversetRegion; }
    const OversetRegion &GetOversetRegion() const { return this->oversetRegion; }

    const int GetElementNumberInDomain()const {return this->v_elementIDInDomain.size(); }
    const int &GetElementIDInDomain(const int &index)const { return this->v_elementIDInDomain[index]; }

    const int GetInnerFaceNumberInDomain()const {return this->v_innerFaceIDInDomain.size(); }
    const int &GetInnerFaceIDInDomain(const int &index)const { return this->v_innerFaceIDInDomain[index]; }

    const int GetBoundaryFaceNumberInDomain(const int &patchID)const {return this->vv_boundaryFaceIndexInDomain[patchID].size(); }
    const int &GetBoundaryFaceIDInDomain(const int &patchID, const int &index)const
    { return this->vv_boundaryFaceID[patchID][this->vv_boundaryFaceIndexInDomain[patchID][index]]; }

    void SetInnerElementIDForBoundaryElement();
    void CreateBoundaryGhostElement();
    void UpdateBoundaryElementFlag();
    void UpdateHalfFaceCrossSymmetryBoundaryFlag(const std::vector<int> symmetryPatchID);
    void UpdateInDomainInfo();

	void WriteMesh(std::fstream &file, const bool &binary, const bool &fullFlag = true)const;
	void ReadMesh(std::fstream &file, const bool &binary, const bool &fullFlag = true);
    
protected:    
    int n_elemNum_ghostBoundary; ///< 物理边界虚单元数量
    int n_elemNum_ghostParallel; ///< 并行边界虚单元数量
    int n_elemNum_ghostOverlap; ///< 嵌套边界虚单元数量
    

    std::vector<int> v_boundaryIDGlobal; ///< 边界全局编号

    std::vector<Scalar> v_nearWallDistance; ///< 壁面距离的容器

    std::vector<std::vector<int>> vv_neighborID_faceAdjoin; ///< 共享面的单元编号（含物理边界的虚拟单元号）
    
    std::vector<std::vector<int>> vv_innerElementID; ///< 原始网格边界节点相邻的内部节点（仅用于对偶网格）
    std::vector<bool> v_boundaryElementFlag; ///< 边界单元标识（仅用于对偶网格）
    std::vector<bool> halfFaceCrossSymmetryBoundaryFlag; ///< 与对称面相交的边界单元内部面标识，即对称边界边标识（仅用于对偶网格）

    std::vector<GhostElement> v_ghostElement; ///< information of IDs of ghost elements in other blocks 

    std::vector<std::vector<GhostElement> > vv_ghostElement_parallel; ///< 网格分区所形成的细网格虚单元信息

    std::vector<std::vector<GhostElement>> vv_ghostElement_multigrid; ///< 网格聚合所形成的细网格虚单元信息

    std::vector<int> v_elementIDInDomain; ///< 参与计算的真实单元编号列表
    std::vector<int> v_innerFaceIDInDomain; ///< 参与计算的内部面编号列表
    std::vector<std::vector<int>> vv_boundaryFaceIndexInDomain; ///< 参与计算的边界面局部编号列表

private:
    OversetRegion oversetRegion; ///< 重叠网格区域指针

public:    
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & zoneID;
        ar & st_fileName;
        ar & st_meshName;
        ar & n_elemNum;
        ar & n_elemNum_all;
        ar & n_faceNum;
        ar & n_nodeNum;
        ar & md_meshDim;

        ar & est_shapeType;
        ar & v_elem;
        ar & v_face;
        ar & v_node;

        ar & v_boundaryIDGlobal;
        ar & v_boundaryName;
        ar & vv_boundaryFaceID;
        ar & v_ghostElement;
        ar & vv_ghostElement_parallel;
        ar & vv_ghostElement_multigrid;
    }
#endif

    // 友元类（暂时）
    friend class DecomposeManager;
    friend class AgglomerateManager;
    friend class DualMesh;
    friend class MeshSorting;
    friend class WallDistanceBase;
    friend class WallDistanceManager;
    friend class MeshProcess;
    friend class CgnsMeshBase;
    friend class CgnsMesh;
    friend class DlgMesh;
    friend class FluentMeshBlock;
    friend class MeshConverter;
    friend class MeshConvertManager;
    friend class SubMesh;
	friend class OversetMesh;
	friend class BackgroundGrid;
};

#endif // _basic_mesh_Mesh_
