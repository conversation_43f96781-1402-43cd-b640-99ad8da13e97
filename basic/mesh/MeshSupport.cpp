﻿#include "basic/mesh/MeshSupport.h"
#include "basic/common/GeometryTools.h"

//--------------------------------Member functions of class Face-----------------------------------
//Constructor
Face::Face()
    : n_owner(-1), n_neighbor(-1)
{}

Face::Face(const int &Node1, const int &Node2)
    : n_owner(-1), n_neighbor(-1)
{
    v_nodeID = std::vector<int>{Node1, Node2};
}

Face::Face(const int &Node1, const int &Node2, const int &Node3)
    : n_owner(-1), n_neighbor(-1)
{
    v_nodeID = std::vector<int>{Node1, Node2, Node3};
}

Face::Face(const int &Node1, const int &Node2, const int &Node3, const int &Node4)
    : n_owner(-1), n_neighbor(-1)
{
    v_nodeID = std::vector<int>{Node1, Node2, Node3, Node4};
}

Face::Face(const std::vector<int> &nodeIDList)
    : n_owner(-1), n_neighbor(-1), v_nodeID(nodeIDList)
{}

Face::Face(const Face &face_)
{
    *this = face_;
}

Face::~Face()
{}

//Calculate center and area with a given set of nodes    
void Face::CalculateCenterAndArea(std::vector<Node> &gridNode)
{
    std::vector<Node> nodeList;
    for (int i = 0; i < v_nodeID.size(); i++)
        nodeList.push_back(gridNode[v_nodeID[i]]);

    Vector areaVector;
    GetCenterAndArea(nodeList, center, areaVector);
    
    this->areaMag = areaVector.Mag();
    this->normal = areaVector / this->areaMag;
}

//correct face sides in case of incorrect indications for owner and neighbor cells
void Face::CorrectSide()
{
    if (-1 == this->n_owner)
    {
        this->n_owner = this->n_neighbor;
        this->n_neighbor = -1;
        this->normal = -(this->normal);
        this->ReverseNodeID();
    }
}

void Face::ReverseNodeID()
{
    const int nodeSize = v_nodeID.size();
    for (int i = 0; i < nodeSize / 2; ++i)
    {
        const int swap = nodeSize - 1 - i;
        const int temp = v_nodeID[i];
        v_nodeID[i] = v_nodeID[swap];
        v_nodeID[swap] = temp;
    }
}

void Face::operator=(const Face &face_)
{
    this->center = face_.center;
    this->areaMag = face_.areaMag;
    this->normal = face_.normal;
    this->n_owner = face_.n_owner;
    this->n_neighbor = face_.n_neighbor;
    this->v_nodeID = face_.v_nodeID;
}

void Face::Write(std::fstream &file, const bool &binary) const
{
    IO::Write(file, this->n_owner, binary);
    IO::Write(file, this->n_neighbor, binary);
    IO::Write(file, this->areaMag, binary);
    IO::Write(file, this->center, binary);
    IO::Write(file, this->normal, binary);
    IO::Write(file, this->v_nodeID, binary);
}

void Face::Read(std::fstream &file, const bool &binary)
{
    IO::Read(file, this->n_owner, binary);
    IO::Read(file, this->n_neighbor, binary);
    IO::Read(file, this->areaMag, binary);
    IO::Read(file, this->center, binary);
    IO::Read(file, this->normal, binary);
    IO::Read(file, this->v_nodeID, binary);
}

//--------------------------------Member functions of class Element-----------------------------------

Element::Element(Element::ElemShapeType elemType)
    : est_shapeType(elemType), volume(0.0), et_type(ElemType::real)
{}

Element::Element(const Element &e)
{
    *this = e;
}

Element::~Element()
{}

//Calculate Center And Volume of a 2D element using polygon Geometry
void Element::CalculateCenterAndVolume(std::vector<Node>& gridNode)
{
    std::vector<Node> nodeList;
    //building up node list of this 2D element;
    for (int j = 0; j < v_nodeID.size(); j++)
    {
        nodeList.push_back(gridNode[v_nodeID[j]]);
    }
    //calculating center point and volume of this 2D elment;
    Vector elementArea;
    GetCenterAndArea(nodeList, center, elementArea);
    volume = elementArea.Mag();
}

//Calculate Center And Volume of a 3D element using polyhedron Geometry
void Element::CalculateCenterAndVolume(std::vector<Face> &gridFace)
{
    std::vector<Node> faceCenterList, faceAreaList;
    //building up face list of this 3D element;
    for (int j = 0; j < v_faceID.size(); j++)
    {
        faceCenterList.push_back(gridFace[v_faceID[j]].center);
        faceAreaList.push_back(gridFace[v_faceID[j]].areaMag * gridFace[v_faceID[j]].normal);
    }
    //calculating center point and volume of this 3D elment;
    GetCenterAndVolume(faceCenterList, faceAreaList, center, volume);
}

void Element::operator=(const Element &element)
{
    this->et_type = element.et_type;
    this->est_shapeType = element.est_shapeType;
    this->center = element.center;
    this->volume = element.volume;    
    this->n_ElementZoneID = element.n_ElementZoneID;
    this->v_faceID = element.v_faceID;
    this->v_nodeID = element.v_nodeID;
}

void Element::Write(std::fstream &file, const bool &binary)const
{
    IO::Write(file, (int)this->est_shapeType, binary);
    IO::Write(file, (int)this->et_type, binary);
    IO::Write(file, this->n_ElementZoneID, binary);
    IO::Write(file, this->volume, binary);
    IO::Write(file, this->center, binary);
    IO::Write(file, this->v_faceID, binary);
    IO::Write(file, this->v_nodeID, binary);
}

void Element::Read(std::fstream &file, const bool &binary)
{
    int temp;
    IO::Read(file, temp, binary);
    this->est_shapeType = (Element::ElemShapeType)temp;
    IO::Read(file, temp, binary);
    this->et_type = (Element::ElemType)temp;
    IO::Read(file, this->n_ElementZoneID, binary);
    IO::Read(file, this->volume, binary);
    IO::Read(file, this->center, binary);
    IO::Read(file, this->v_faceID, binary);
    IO::Read(file, this->v_nodeID, binary);
}

void GhostElement::Write(std::fstream &file, const bool &binary)const
{
    IO::Write(file, this->ID, binary);
    IO::Write(file, this->procPair, binary);
    IO::Write(file, this->localIDPair, binary);
}

void GhostElement::Read(std::fstream &file, const bool &binary)
{
    IO::Read(file, this->ID, binary);
    IO::Read(file, this->procPair, binary);
    IO::Read(file, this->localIDPair, binary);
}
