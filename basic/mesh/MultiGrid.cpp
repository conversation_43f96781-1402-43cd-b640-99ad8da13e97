﻿#include "basic/mesh/MultiGrid.h"

MultiGrid::MultiGrid()
{
}

MultiGrid::MultiGrid(const std::string& MshFileName)
    :Mesh(MshFileName)
{
    std::ifstream    inFile(MshFileName.c_str());

    if (inFile.fail())
    {
        FatalError(std::string("Can not find Mesh file:\n") + MshFileName);
    }
}

void MultiGrid::WriteMultiGrid(std::fstream &file, const int &level, const int &nLevel, const bool &binary)const
{
    if (level > 0)
    {
        this->WriteMesh(file, binary, true);
        IO::Write(file, this->v_elemMap, binary);

        if (level < nLevel - 1)
        {
        }
    }
    else
    {
        this->WriteMesh(file, binary, true);
    }
    PrintFile("\tWrite the level " + ToString(level + 1) + " grid done!");
}

void MultiGrid::ReadMultiGrid(std::fstream &file, const int &level, const int &nLevel, const bool &binary)
{
    this->ReadMesh(file, binary, true);
    if (level > 0) IO::Read(file, this->v_elemMap, binary);
}

void MultiGrid::WriteMultiGridGhostElement(std::fstream &file, const bool &binary)const
{
    //网格聚合引起的并行边界信息
    const int num1 = this->vv_ghostElement_multigrid.size();
    IO::Write(file, num1, binary);
    for (int i = 0; i< num1; ++i)
    {
        const int num2 = this->vv_ghostElement_multigrid[i].size();
        IO::Write(file, num2, binary);
        for (int j = 0; j< num2; ++j)
            this->vv_ghostElement_multigrid[i][j].Write(file, binary);
    }
}

void MultiGrid::ReadMultiGridGhostElement(std::fstream &file, const bool &binary)
{
    //网格聚合引起的并行边界信息
    int num1;
    IO::Read(file, num1, binary);
    this->vv_ghostElement_multigrid.resize(num1);
    for (int i = 0; i < num1; ++i)
    {
        int num2;
        IO::Read(file, num2, binary);
        this->vv_ghostElement_multigrid[i].resize(num2);
        for (int j = 0; j < num2; ++j)
            this->vv_ghostElement_multigrid[i][j].Read(file, binary);
    }
}

void MultiGrid::ClearMesh()
{
    this->Mesh::ClearMesh();
    std::vector<std::vector<int>>{}.swap(this->v_elemMap);
    std::vector<std::pair<int, int>>{}.swap(v_fineToCoarseIDPair);
}

void MultiGrid::CalculatefineToCoarseMap()
{
    int fineSize = 0;
    for (int i = 0; i < this->v_elemMap.size(); ++i) fineSize += this->v_elemMap[i].size();

    v_fineToCoarseIDPair.reserve(fineSize);
    const int elementNumberCoarse = this->GetElementNumberInDomain();
    for (int index = 0; index < elementNumberCoarse; ++index)
    {
        const int &coarseID = this->GetElementIDInDomain(index);
        for (int index1 = 0; index1 < this->v_elemMap[coarseID].size(); ++index1)
        {
            const int &fineID = this->v_elemMap[coarseID][index1];
            v_fineToCoarseIDPair.push_back(std::make_pair(fineID, coarseID));
        }
    }
}
