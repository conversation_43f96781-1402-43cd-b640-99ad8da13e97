﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MultiGrid.h
//! <AUTHOR>
//! @brief 基本网格类（多重网格类）
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_MultiGrid_
#define _basic_mesh_MultiGrid_

#include "basic/mesh/Mesh.h"

// 用于友元类的声明（暂时）
class AgglomerateManager;
class DecomposeManager;
class MeshProcess;
class SubMesh;
class DlgMesh;

class MultiGrid :public Mesh
{
public:
    MultiGrid();
    MultiGrid(const std::string& MshFileName);
    const int &GetFineID(const int &coarseID, const int &index)const {return this->v_elemMap[coarseID][index];}
    const int GetFineIDSize(const int &coarseID)const {return (int)this->v_elemMap[coarseID].size();}
    const std::pair<int, int> &GetFineToCoarseIDPair(const int &fineID)const {return this->v_fineToCoarseIDPair[fineID];}
    const int GetFineToCoarseIDPairSize()const {return (int)this->v_fineToCoarseIDPair.size();}
    
    void WriteMultiGrid(std::fstream &file, const int &level, const int &nLevel, const bool &binary = true)const;
    void ReadMultiGrid(std::fstream &file, const int &level, const int &nLevel, const bool &binary = true);

    void WriteMultiGridGhostElement(std::fstream &file, const bool &binary)const;
    void ReadMultiGridGhostElement(std::fstream &file, const bool &binary);

    void ClearMesh();
    
    void CalculatefineToCoarseMap();
    
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        //基类成员序列化
        ar & boost::serialization::base_object<Mesh>(*this);
        
        //当前类成员序列化
        ar & v_elemMap;
        
        //当前类成员序列化
        ar & v_fineToCoarseIDPair;
    }
#endif

protected:
    std::vector<std::vector<int>> v_elemMap; ///< 粗网格存储细网格编号

    /// 细网格单元所对应的粗网格编号<细网格单元编号，粗网格单元编号>
    std::vector<std::pair<int, int>> v_fineToCoarseIDPair; 

    // 友元类（暂时）
    friend class AgglomerateManager;
    friend class DecomposeManager;
    friend class MeshProcess;
    friend class SubMesh;
    friend class DlgMesh;
};

#endif // _basic_mesh_MultiGrid_
