﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BaseMesh.h
//! <AUTHOR>
//! @brief 基本网格类
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_BaseMesh_
#define _basic_mesh_BaseMesh_

#include "basic/configure/ConfigureMacro.h"
#include "basic/configure/ConfigureMacro.hxx"
#include "basic/common/ListTools.h"
#include "basic/mesh/MeshSupport.h"
#include "basic/mesh/MultiStructuredBlock.h"

// 用于友元类的声明（暂时）
class DecomposeManager;
class AgglomerateManager;
class AgglomerateStructuredMesh;
class DualMesh;
class MeshSorting;
class WallDistanceBase;
class WallDistanceManager;
class MeshProcess;
class CgnsMesh;
class CgnsMeshStructured;
class DlgMesh;
class FluentMeshBlock;
class MeshConverter;
class MeshConvertManager;
class SubMesh;
class OversetMesh;

class BaseMesh
{
public:    
    enum MeshDim ///< 枚举：网格维度
    {
        md2D    = 2,
        md3D    = 3,
        mdNoType
    };    
    enum MeshIOState ///< 枚举：网格状态
    {
        READ,
        WRITE
    };

public:
	BaseMesh(); ///< 构造函数
	BaseMesh(const std::string& MshFileName); ///< 构造函数
    const MeshDim &GetMeshDimension()const { return this->md_meshDim; }
    const std::string &GetMeshName()const { return this->st_meshName; }
    void SetMeshName(const std::string &name) { this->st_meshName = name; }
    const std::string &GetFileName()const { return this->st_fileName; }
    void SetFileName(const std::string &name) { this->st_fileName = name; }
    const Element::ElemShapeType &GetElemShapeType()const { return this->est_shapeType; }
    const int &GetElementNumberAll()const  {return this->n_elemNum_all;}
    const int &GetElementNumberReal()const  {return this->n_elemNum;}
    const int &GetFaceNumber()const  { return this->n_faceNum; }
    const int &GetNodeNumber()const  { return this->n_nodeNum; }
    const Element &GetElement(const int &elementID)const {return this->v_elem[elementID];}
    const Face &GetFace(const int &faceID)const { return this->v_face[faceID]; }
    const Node &GetNode(const int &nodeID)const { return this->v_node[nodeID]; }
    
    const int GetBoundarySize()const { return (int)this->vv_boundaryFaceID.size(); }
    const int GetBoundaryFaceSize(const int &patchID)const { return (int)this->vv_boundaryFaceID[patchID].size(); }
    const int &GetBoundaryFaceID(const int &patchID, const int &index)const {return this->vv_boundaryFaceID[patchID][index];}
    const std::string &GetBoundaryName(const int &patchID)const { return this->v_boundaryName[patchID]; }    

    void SetNode(const int &nodeID, const Node &node) { this->v_node[nodeID] = node; }
    void SetElementCenter(const int &elemID, const Vector &center) { this->v_elem[elemID].center = center; }
    void SetElementVolume(const int &elemID, const Scalar &volume) { this->v_elem[elemID].volume = volume; }
    void SetFaceCenter(const int &faceID, const Vector &center) { this->v_face[faceID].center = center; }
    void SetFaceNormal(const int &faceID, const Vector &normal) { this->v_face[faceID].normal = normal; }
    
	/**
	* @brief 获取边界的点列表
	*
	* @param[in] patchID 边界编号
	* @param[out] v_boundaryNodeID 当前边界上的点集合
	*/
    void PopulateBoundaryNodeID(const int &patchID, std::vector<int> &v_boundaryNodeID);
    
	/**
	* @brief 获取边界的点列表及面的局部构成
	*
	* @param[in] patchID 边界编号
	* @param[out] v_boundaryNodeID 当前边界上的点集合
	* @param[out] vv_boundaryFaceNodeID 当前边界上的每个面的点构成（点编号为当前边界内所有点的局部编号）
	*/
    void PopulateBoundaryFaceNodeID(const int &patchID, std::vector<int> &v_boundaryNodeID, std::vector<std::vector<int>> &vv_boundaryFaceNodeID);

    const int &GetMeshZoneID()const {return this->zoneID; }
    void SetMeshZoneID(const int &zoneID_) {this->zoneID = zoneID_; }

	const Block &GetBlock(const int &ID)const {return multiStructuredBlock.GetBlock(ID);}
	const Connection &GetConnection(const int &ID)const {return multiStructuredBlock.GetConnection(ID);}
	void AddBlock(const Block &block_) { multiStructuredBlock.AddBlock(block_); }
	void AddConnection(const Connection &connection_) {multiStructuredBlock.AddConnection(connection_);}
    void SetNodeIndex() { multiStructuredBlock.SetNodeIndex(true); }
    int GetNodeTotalSize() { return multiStructuredBlock.GetNodeTotalSize(); }
    
	bool JundgeStructured() { return multiStructuredBlock.GetBlockSize() > 0; }

public:    
    void Scale(Scalar);
    void Scale(Vector);
    void Translate(Vector);
    void Rotate(Vector, Vector, Scalar);
    void ClearMesh();
    void PrintMeshInfomation();
      
    std::vector<int> SearchElementNeighbor(int EID) const; ///< 得到该单元的邻居单元容器
    std::vector<int> SearchNearbyElements(int EID, Scalar dis) const; ///< 给定单元号和搜索距离，返回范围内的单元号容器
    std::vector<int> SearchNearbyElements(int EID, int layerNum) const; ///< 给定单元号和搜索层数，返回范围内的单元号容器
	
    void CalculateCenterAndVolume(const bool boundaryFlag = false);
    void CheckFaceDirection();
    Scalar GetCharacteristicLength() const;   

    void UpdateElementFaceID();
    
protected:
    int zoneID; ///< 当前网格所属网格域编号
    Element::ElemShapeType est_shapeType; ///< 构成网格的单元类型
    int    n_elemNum; ///< 实单元数量
    int    n_faceNum; ///< 面数量
    int    n_nodeNum; ///< 点数量
    int n_elemNum_all; ///< 所有单元数量（含虚单元）

    MeshDim    md_meshDim; ///< 网格维度
    std::string    st_fileName; ///< 网格文件名称,根据文件类型补上后缀后使用
    std::string    st_meshName; ///< 网格名称，输出文件前缀

    std::vector<Element>    v_elem; ///< 构成网格的单元容器
    std::vector<Face>        v_face; ///< 构成网格的面容器
    std::vector<Node>        v_node; ///< 构成网格的坐标容器

    std::vector<std::string> v_boundaryName; ///< 边界名称
    std::vector<std::vector<int>> vv_boundaryFaceID; ///< 边界面编号

    MultiStructuredBlock multiStructuredBlock; ///< 结构网格分块信息

public:    
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & zoneID;
        ar & st_fileName;
        ar & st_meshName;
        ar & n_elemNum;
        ar & n_elemNum_all;
        ar & n_faceNum;
        ar & n_nodeNum;
        ar & md_meshDim;

        ar & est_shapeType;
        ar & v_elem;
        ar & v_face;
        ar & v_node;

        ar & v_boundaryName;
        ar & vv_boundaryFaceID;
        /// 注意其他没有并行发送的数据，都是独自生成的
    }
#endif

    // 友元类（暂时）
    friend class DecomposeManager;
    friend class AgglomerateManager;
    friend class AgglomerateStructuredMesh;
    friend class DualMesh;
    friend class MeshSorting;
    friend class WallDistanceBase;
    friend class WallDistanceManager;
    friend class MeshProcess;
    friend class CgnsMesh;
    friend class CgnsMeshStructured;
    friend class DlgMesh;
    friend class FluentMeshBlock;
    friend class MeshConverter;
    friend class MeshConvertManager;
    friend class SubMesh;
    friend class OversetMesh;
};

#endif // _basic_mesh_BaseMesh_
