﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MultiStructuredBlock.h
//! <AUTHOR>
//! @brief 多块结构网格的辅助类
//! @date 2024-06-05
//
//------------------------------修改日志----------------------------------------
// 2024-06-05 李艳亮、乔龙（气动院）
//    说明：建立并规范化。
//
//------------------------------------------------------------------------------
#ifndef _basic_mesh_MultiStructuredBlock_
#define _basic_mesh_MultiStructuredBlock_

#include "basic/mesh/MeshSupport.h"

/**
* @brief 单块结构网格数据结构
*
*/
struct Block
{
public:
	Block() :nodeI(0), nodeJ(0), nodeK(0), nodeIJ(0),
		     cellI(0), cellJ(0), cellK(0), cellIJ(0), cellstart(0)
    {};

    Block(const int &idim, const int &jdim, const int &kdim, const int &cellstart, const std::vector<std::vector<int>> &boundaryRange_)
		:nodeI(idim), nodeJ(jdim), nodeK(kdim), nodeIJ(idim * jdim),
		 cellI(idim - 1), cellJ(jdim - 1), cellK(kdim - 1),
         cellIJ((idim - 1) * (jdim - 1)), cellstart(cellstart),
         boundaryRange(boundaryRange_)
    {};

	const int &GetNodeIndex(const int &i, const int &j, const int &k)const{ return nodeIndex[i][j][k]; }		
	const int GetElementIndex(const int &i, const int &j, const int &k)const { return cellstart + k * cellIJ + j * cellI + i; }
	void GetElementIJK(const int &index, int &i, int &j, int &k)const
	{
		int cellIJK = index - cellstart;
		k = cellIJK / cellIJ;
		int totalIJ = cellIJK % cellIJ;
		j = totalIJ / cellI;
		i = totalIJ % cellI;
	}

public:
	int nodeI, nodeJ, nodeK; ///< 三个方向的网格点数
	int nodeIJ; ///< k平面内网格点数(即 nodeI*nodeJ)
	std::vector<std::vector<std::vector<int>>> nodeIndex; ///< 局部点与全局点的对应关系

	int cellI, cellJ, cellK; ///< 三个方向的网格单元数
	int cellIJ; ///< k平面内网格单元数(即 cellI*cellJ)
	int cellstart; ///< 当前网格单元的起始编号

	std::vector<std::vector<int>> boundaryRange; ///< 当前块所有物理边界三个方向的范围容器
};

/**
* @brief 多块之间的邻接关系(一个交接面)
*
*/
struct Connection
{
	int leftID;
	int rightID;
	std::vector<int> leftRange;
	std::vector<int> rightRange;
	std::vector<int> transform;
    Connection(){};
    Connection(const Connection &c)
    {
        leftID = c.leftID; rightID = c.rightID;
        leftRange = c.leftRange; rightRange = c.rightRange;
        transform = c.transform;
    }
	Connection(const int &leftID_, const int &rightID_,
	          const std::vector<int> &leftRange_,
	          const std::vector<int> &rightRange_,
	          const std::vector<int> &transform_)
	{
		leftID = leftID_; rightID = rightID_;
		leftRange = leftRange_; rightRange = rightRange_;
		transform = transform_;
	}

	std::vector<int> ObtainLeftIndex(const int &i, const int &j, const int &k) const
	{
		std::vector<int> indexLeft(3);
		indexLeft[0] = leftRange[0] + i;
		indexLeft[1] = leftRange[1] + j;
		indexLeft[2] = leftRange[2] + k;
		return indexLeft;
	}

	std::vector<int> ObtainRightIndex(const int &i, const int &j, const int &k) const
	{
		std::vector<int> indexRight(3);
		
		if      (transform[0] ==  1) indexRight[0] = rightRange[0] + i;
		else if (transform[0] == -1) indexRight[0] = rightRange[0] - i;
		else if (transform[0] ==  2) indexRight[1] = rightRange[1] + i;
		else if (transform[0] == -2) indexRight[1] = rightRange[1] - i;
		else if (transform[0] ==  3) indexRight[2] = rightRange[2] + i;
		else if (transform[0] == -3) indexRight[2] = rightRange[2] - i;

		if      (transform[1] ==  1) indexRight[0] = rightRange[0] + j;
		else if (transform[1] == -1) indexRight[0] = rightRange[0] - j;
		else if (transform[1] ==  2) indexRight[1] = rightRange[1] + j;
		else if (transform[1] == -2) indexRight[1] = rightRange[1] - j;
		else if (transform[1] ==  3) indexRight[2] = rightRange[2] + j;
		else if (transform[1] == -3) indexRight[2] = rightRange[2] - j;

		if      (transform[2] ==  1) indexRight[0] = rightRange[0] + k;
		else if (transform[2] == -1) indexRight[0] = rightRange[0] - k;
		else if (transform[2] ==  2) indexRight[1] = rightRange[1] + k;
		else if (transform[2] == -2) indexRight[1] = rightRange[1] - k;
		else if (transform[2] ==  3) indexRight[2] = rightRange[2] + k;
		else if (transform[2] == -3) indexRight[2] = rightRange[2] - k;

		return indexRight;
	}
};

class MultiStructuredBlock
{
public:
	MultiStructuredBlock(){}; ///< 构造函数
	~MultiStructuredBlock(){}; ///< 析构函数 
	
	int GetBlockSize(){ return multiBlock.size(); }

	int GetConnectionSize(){ return connection.size(); }

	int GetNodeTotalSize(){ return nodeTotalSize; }
	
	const Block &GetBlock(const int &blockID)const {return multiBlock[blockID];}
	
	const Connection &GetConnection(const int &connectionID)const {return connection[connectionID];}

	void AddBlock(const Block &block_) { multiBlock.push_back(block_); }
	
	void AddConnection(const Connection &connection_) {connection.push_back(connection_);}

	void SetNodeIndex(const bool &removeConnection);

	void Clear(){ multiBlock.resize(0);  connection.resize(0); nodeTotalSize = 0; }

private:
	std::vector<Block> multiBlock;
	std::vector<Connection> connection;
	int nodeTotalSize;
};

#endif // _basic_mesh_Mesh_
