﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SubMesh.h
//! <AUTHOR>
//! @brief 基本网格类（分区后）
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_SubMesh_
#define _basic_mesh_SubMesh_

#include "basic/mesh/MultiGrid.h"

// 用于友元类的声明（暂时）
class DecomposeManager;
class DualMesh;
class MeshProcess;
class DlgMesh;

class SubMesh :public Mesh
{
public:
	SubMesh(const int &partID = 0, const int &nLevel = 1);
    
	SubMesh(const std::string &fileName, const int &nLevel, const bool &binary);
    
    const int &GetElementGlobalID(const int &localID)const;
    const int &GetFaceGlobalID(const int &localID)const;
	const int &GetNodeGlobalID(const int &localID)const;
	const int &GetBoundaryFaceGlobalIndex(const int &localPatchID, const int &localIndex)const;
    const int &GetBoundaryGlobalID(const int & level, const int &localPatchID)const;

    MultiGrid* GetMultiGrid(const int &level);
    const int &GetTotalLevel()const {return n_level;}    
	
	void ReadSubMesh(std::fstream &file, const int &nLevel, const bool &binary);

    void ClearMesh();

    void PreCalculate(const bool &dualFlag, const std::vector<std::vector<int>> &symmetryPatchIDList);
    
    /**
     * @brief 创建各类虚拟单元
     * 
     * @param[in] fineLevel 细网格编号
     */
    void CreateGhostElement(const int &fineLevel);

private:

	void WriteSubMesh(std::fstream &file, const int &nLevel, const bool &binary = true);

protected:
    int n_pros; ///< 网格所在进程号
    int n_level; ///< 多重网格总层数（含细网格）
    std::vector<int> v_nodeID_Global; ///< 局部网格与全局网格的点编号对应关系    
    std::vector<int> v_faceID_Global; ///< 局部网格与全局网格的面编号对应关系    
    std::vector<int> v_elemID_Global; ///< 局部网格与全局网格的单元编号对应关系   
	std::vector<std::vector<int>> vv_boundaryFaceIndex_Global; ///< 局部网格与全局网格的边界面索引对应关系   
    std::vector<std::vector<int>> v_boundaryID_Global; ///< 不同level局部网格与全局网格的边界编号对应关系
    std::vector<MultiGrid> v_multiGrid; ///< 存放粗网格的容器

#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive& ar, const unsigned int version)
    {
        //基类成员序列化
        ar & boost::serialization::base_object<Mesh>(*this);
        
        //当前类成员序列化
        ar& n_pros;
        ar& n_level;
        ar& v_nodeID_Global;
        ar& v_faceID_Global;
        ar& v_elemID_Global;
		ar& vv_boundaryFaceIndex_Global;
        ar& v_boundaryID_Global;
        ar& v_multiGrid;
    }
#endif

    // 友元类（暂时）
    friend class DecomposeManager;
    friend class DualMesh;
    friend class MeshProcess;
    friend class DlgMesh;
};

#endif // _basic_mesh_SubMesh_
