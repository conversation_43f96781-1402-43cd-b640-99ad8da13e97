﻿#include "basic/MRF/MRFModel.h"
#include "sourceFlow/boundaryCondition/FlowBoundaryManager.h"

MRFZONE::MRFZONE()
{
	name = "none";
	origin = Vector0;
	axis = Vector0;
	omega = 0;
	oumu = Vector0;
}

MRFZONE::MRFZONE( std::string name, Vector origin, Vector axis, Scalar omega, Mesh* mesh)
:
	name(name), 
	origin(origin), 
	axis(axis), 
	omega(omega),
    mesh(mesh)
{
	this->axis /= this->axis.Mag();
	oumu = this->axis*this->omega;
}

MRFZONE& MRFZONE::operator=(const MRFZONE& other){
	this->name = other.name;                  
	this->origin = other.origin;    
	this->axis = other.axis;
	this->omega = other.omega;
	this->oumu = other.oumu;
	return *this;
}

MRFZONE::~MRFZONE(){}

void MRFZONE::divideFaces(){}

void MRFZONE::make_relative(ElementField<Vector> & V, Boundary::Flow::FlowBoundaryManager & fb)const{
	for (int i = 0; i<V.GetSize(); i++){
		V.AddValue(i, -oumu ^ (V.GetMesh()->GetElement(i).GetCenter() - origin));
	}
																						
	for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
	{
		if (fb.GetBoundaryVector()[patchID] == 202){
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int j = 0; j < faceSize; j++){
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, j);
				const int &ownerID =  mesh->GetFace(faceID).GetOwnerID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();
	

				V.SetValue(neighID, -V.GetValue(ownerID));
			}
		}
	}
}

void MRFZONE::make_absolute(ElementField<Vector>&V, Boundary::Flow::FlowBoundaryManager& fb)const{
	for (int i = 0; i<V.GetSize(); i++){
		V.AddValue(i, oumu ^ (V.GetMesh()->GetElement(i).GetCenter() - origin));
	}

	for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
	{
		if (fb.GetBoundaryVector()[patchID] == 202){
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int j = 0; j < faceSize; j++){
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, j);
				const int &ownerID =  mesh->GetFace(faceID).GetOwnerID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();
				Vector U0 = oumu ^ (V.GetMesh()->GetElement(neighID).GetCenter() - origin);
				V.SetValue(neighID, 2 * U0 - V.GetValue(ownerID));
			}
		}
	}
}


void MRFZONE::addCoriolis(ElementField<Vector>* residualMomentum,ElementField<Scalar> &rho,ElementField<Vector> &U)
{
    for(int elementid = 0; elementid < mesh->GetElementNumberReal(); elementid++)
	{
		residualMomentum->AddValue(elementid, rho.GetValue(elementid) * oumu ^ \
				(U.GetValue(elementid)) * this->mesh->GetElement(elementid).GetVolume());
	}
}

void MRFZONE::RelativeFlux_Inerternal(const Face &face, const Vector &faceArea,const Scalar &gamma1,
									 Scalar& massFlux, Vector& momentumFlux,Scalar& energyFlux,
									 const Scalar &rhoLeft,const Vector &ULeft,const Scalar &pLeft,
									 const Scalar &rhoRight,const Vector &URight,const Scalar &pRight)
{
	Vector omegar;
	omegar = oumu ^ (face.GetCenter() - origin);
	Scalar omegaf = omegar & faceArea;
	massFlux -= 0.5*(rhoLeft + rhoRight) * omegaf;
	momentumFlux = momentumFlux - 0.5 * rhoLeft * ULeft * omegaf - 0.5 * rhoRight * URight * omegaf;
	energyFlux = energyFlux - 0.5*(pLeft / gamma1 + 0.5 * rhoLeft * (ULeft & ULeft)) * omegaf \
							- 0.5*(pRight / gamma1 + 0.5 * rhoRight * (URight & URight)) * omegaf;
}


void MRFZONE::RelativeFlux_Patch(Face &face,Scalar& massFlux,Vector& momentumFlux,Scalar& energyFlux,
								Scalar& rhoFace,Vector& UFace,Scalar& rhoEFace,const Vector &faceArea)
{
	Vector omegar;
	omegar = oumu ^ (face.GetCenter() - origin);
	Scalar omegaf = omegar & faceArea;
	massFlux -= rhoFace * omegaf;
	momentumFlux -= rhoFace* omegaf * UFace;
	energyFlux -= rhoEFace * omegaf;
}