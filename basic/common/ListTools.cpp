﻿#include "basic/common/ListTools.h"

template<>
std::vector<Scalar> operator + (const std::vector<Scalar>& left, const std::vector<Scalar>& right)
{
    int num = (int)left.size();
    std::vector<Scalar> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] + right[i];
    }
    return result;
}

template<>
std::vector<Vector> operator + (const std::vector<Vector>& left, const std::vector<Vector>& right)
{
    int num = (int)left.size();
    std::vector<Vector> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] + right[i];
    }
    return result;
}

template<>
std::vector<Scalar> operator - (const std::vector<Scalar>& left, const std::vector<Scalar>& right)
{
    int num = (int)left.size();
    std::vector<Scalar> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] - right[i];
    }
    return result;
}

template<>
std::vector<Vector> operator - (const std::vector<Vector>& left, const std::vector<Vector>& right)
{
    int num = (int)left.size();
    std::vector<Vector> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] - right[i];
    }
    return result;
}

//Kong Ling supplemented on 2019/12/23
template<>
std::vector<Scalar> operator *(const Scalar& left, const std::vector<Scalar>& right)
{
    int num = (int)right.size();
    std::vector<Scalar> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left * right[i];
    }
    return result;
}

//Kong Ling supplemented on 2019/12/23
template<>
std::vector<Vector> operator *(const Scalar& left, const std::vector<Vector>& right)
{
    int num = (int)right.size();
    std::vector<Vector> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left * right[i];
    }
    return result;
}

template<>
std::vector<Scalar> operator * (const std::vector<Scalar>& left, const std::vector<Scalar>& right)
{
    int num = (int)left.size();
    std::vector<Scalar> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] * right[i];
    }
    return result;
}

template<>
std::vector<Vector> operator * (const std::vector<Scalar>& left, const std::vector<Vector>& right)
{
    int num = (int)left.size();
    std::vector<Vector> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] * right[i];
    }
    return result;
}

template<>
std::vector<Scalar> operator / (const std::vector<Scalar>& left, const std::vector<Scalar>& right)
{
    int num = (int)left.size();
    std::vector<Scalar> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] / right[i];
    }
    return result;
}

template<>
std::vector<Vector> operator / (const std::vector<Vector>& left, const std::vector<Scalar>& right)
{
    int num = (int)left.size();
    std::vector<Vector> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] / right[i];
    }
    return result;
}

//Kong Ling supplemented on 2019/12/23
std::vector<Scalar> operator & (const std::vector<Vector>& left, const std::vector<Vector>& right)
{
    int num = (int)left.size();
    std::vector<Scalar> result;
    result.resize(num);
    for (int i = 0; i < num; ++i)
    {
        result[i] = left[i] & right[i];
    }
    return result;
}

std::vector<int> GetNonRepeatedList(const std::vector<int>& originalList)
{
    std::vector<int> v_nonRepeatList;
    v_nonRepeatList.reserve(originalList.size());
    for (int i = 0; i < (int)originalList.size(); ++i)
    {
        int currentID = originalList[i];
        bool repeated = false;
        for (int j = 0; j < (int)v_nonRepeatList.size(); ++j)
        {
            if (currentID == v_nonRepeatList[j])
            {
                repeated = true;
                break;
            }
        }
        if (false == repeated)
        {
            v_nonRepeatList.push_back(currentID);
        }
    }
    return v_nonRepeatList;
}

//Operator +
template<>
std::map<unsigned int, Scalar> operator + (const std::map<unsigned int, Scalar>& left, const std::map<unsigned int, Scalar>& right)
{
    std::map<unsigned int, Scalar> target;
    std::map<unsigned int, Scalar> *psmall;
    if (left.size() > right.size())
    {
        target = left;
        psmall = const_cast<std::map<unsigned int, Scalar> * > (&right);
    }
    else
    {
        target = right;
        psmall = const_cast<std::map<unsigned int, Scalar> * > (&left);
    }
    std::map<unsigned int, Scalar>::iterator it1, it2;
    for (it1 = (*psmall).begin(); it1 != (*psmall).end(); ++it1)
    { 
        it2 = target.find(it1->first);
        if (target.end() == it2)
        {
            target.insert(*it1);
        }
        else
        {
            (*it2).second += (*it1).second;
        }
    }
    return target;
}


//Operator += 
template<>
void operator += (std::map<unsigned int, Scalar>& left, const std::map<unsigned int, Scalar>& right)
{
    //Version 1 (direct approach): adding right to left directly 
    std::map<unsigned int, Scalar>::const_iterator it1;
    std::map<unsigned int, Scalar>::iterator it2;

    for (it1 = right.begin(); it1 != right.end(); ++it1)
    {
        it2 = left.find(it1->first);
        if (left.end() == it2)
        {
            left.insert(*it1);
        }
        else
        {
            (*it2).second += (*it1).second;
        }
    }
}

//Operator -
template<>
std::map<unsigned int, Scalar> operator - (const std::map<unsigned int, Scalar>& left, const std::map<unsigned int, Scalar>& right)
{
    std::map<unsigned int, Scalar> target = left;
    std::map<unsigned int, Scalar>::const_iterator it1;
    std::map<unsigned int, Scalar>::iterator it2;
    for (it1 = right.begin(); it1 != right.end(); ++it1)
    {
        it2 = target.find(it1->first);
        if (target.end() == it2)
        {
            target.insert(std::pair<unsigned int, Scalar>((*it1).first, -(*it1).second));
        }
        else
        {
            (*it2).second -= (*it1).second;
        }
    }
    return target;
}

//Operator -= 
template<>
void operator -= (std::map<unsigned int, Scalar>& left, const std::map<unsigned int, Scalar>& right)
{
    std::map<unsigned int, Scalar>::const_iterator it1;
    std::map<unsigned int, Scalar>::iterator it2;
    for (it1 = right.begin(); it1 != right.end(); ++it1)
    {
        it2 = left.find(it1->first);
        if (left.end() == it2)
        {
            left.insert(std::pair<unsigned int, Scalar>((*it1).first, -(*it1).second));
        }
        else
        {
            (*it2).second -= (*it1).second;
        }
    }
}

//Operator *
template<>
std::map<unsigned int, Scalar> operator * (const std::map<unsigned int, Scalar>& left, const std::map<unsigned int, Scalar>& right)
{
    std::map<unsigned int, Scalar> *psmall, *plarge;
    if (left.size() > right.size())
    {
        psmall = const_cast<std::map<unsigned int, Scalar> * > (&right);
        plarge = const_cast<std::map<unsigned int, Scalar> * > (&left);
    }
    else
    {
        psmall = const_cast<std::map<unsigned int, Scalar> * > (&left);
        plarge = const_cast<std::map<unsigned int, Scalar> * > (&right);
    }
    std::map<unsigned int, Scalar> target;
    std::map<unsigned int, Scalar>::iterator it1, it2;
    for (it1 = (*psmall).begin(); it1 != (*psmall).end(); ++it1)
    {
        it2 = (*plarge).find(it1->first);
        if ((*plarge).end() != it2)
        {
            target.insert(std::pair<unsigned int, Scalar>((*it1).first, (*it1).second*(*it2).second));
        }
    }
    return target;
}

//Operator *=
template<>
void operator *= (std::map<unsigned int, Scalar>& left, const std::map<unsigned int, Scalar>& right)
{
    std::map<unsigned int, Scalar> *psmall, *plarge;
    if (left.size() > right.size())
    {
        psmall = const_cast<std::map<unsigned int, Scalar> * > (&right);
        plarge = &left;
    }
    else
    {
        psmall = &left;
        plarge = const_cast<std::map<unsigned int, Scalar> * > (&right);
    }
    std::map<unsigned int, Scalar> target;
    std::map<unsigned int, Scalar>::iterator it1, it2;
    for (it1 = (*psmall).begin(); it1 != (*psmall).end(); ++it1)
    {
        it2 = (*plarge).find(it1->first);
        if ((*plarge).end() != it2)
        {
            target.insert(std::pair<unsigned int, Scalar>((*it1).first, (*it1).second*(*it2).second));
        }
    }
    left=target;
}


