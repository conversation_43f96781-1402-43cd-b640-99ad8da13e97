﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Tensor.h
//! <AUTHOR>
//! @brief 张量类
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-09-22 李艳亮、乔龙
//    说明：改写与规范化
//
// 2020-07-22 数峰科技/西交大
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_common_Tensor_
#define _basic_common_Tensor_

#if defined(_BaseParallelMPI_)
#include "basic/common/BoostLib.h"
#endif
#include "basic/common/SystemControl.h"
#include "basic/common/Vector.h"

#include <vector>
#include <fstream>

class Tensor
{
public:
    enum Type ///< 枚举：张量类型
    {
        general = 0, ///< 一般张量
        diag, ///< 对角
        sym, ///< 对称
        antiSym ///< 反对称
    };

public:
    Tensor(const Type &typ = Type::general) : type(typ) { }

    Tensor(const Scalar &xx_, const Scalar &yy_, const Scalar &zz_)
    {
        type = Type::diag;
        xx = xx_; yy = yy_; zz = zz_;
        xy = 0.0; xz = 0.0; yz = 0.0;
        yx = 0.0; zx = 0.0; zy = 0.0;
    }

    Tensor(const Scalar &xx_, const Scalar &yy_, const Scalar &zz_,
           const Scalar &xy_, const Scalar &xz_, const Scalar &yz_,
           const Type &typ = Type::sym )
    {
        type = typ;

        switch (typ)
        {
        case Type::sym:
        {
            xx = xx_; yy = yy_; zz = zz_;
            xy = xy_; xz = xz_; yz = yz_;
            yx = xy_; zx = xz_; zy = yz_;
            break;
        }
        case Type::antiSym:
        {
            xx = 0.0; yy = 0.0; zz = 0.0;
            xy = xy_; xz = xz_; yz = yz_;
            yx = -xy_; zx = -xz_; zy = -yz_;
            break;
        }
        default:
        {
            FatalError("Tensor::Tensor: type is wrong!");
        }
        }
    }

    Tensor(const Scalar &xx_, const Scalar &xy_, const Scalar &xz_,
           const Scalar &yx_, const Scalar &yy_, const Scalar &yz_,
           const Scalar &zx_, const Scalar &zy_, const Scalar &zz_)
    {
        type = Type::general;
        xx = xx_; yy = yy_; zz = zz_;
        xy = xy_; xz = xz_; yz = yz_;
        yx = yx_; zx = zx_; zy = zy_;
    }

    Tensor(const Tensor &t)
    {
        type = t.type;
        xx = t.xx; yy = t.yy; zz = t.zz;
        xy = t.xy; xz = t.xz; yz = t.yz;
        yx = t.yx; zx = t.zx; zy = t.zy;
    }

public:
    const Type &GetType()const{ return this->type; }    
    const Scalar &XX()const {return this->xx;}
    const Scalar &YY()const { return this->yy; }
    const Scalar &ZZ()const { return this->zz; }
    const Scalar &XY()const { return this->xy; }
    const Scalar &XZ()const { return this->xz; }
    const Scalar &YZ()const { return this->yz; }
    const Scalar &YX()const { return this->yx; }
    const Scalar &ZX()const { return this->zx; }
    const Scalar &ZY()const { return this->zy; }

    void SetType(const Type &value) { this->type = value; } 
    void SetXX(const Scalar &value) {this->xx = value;}
    void SetYY(const Scalar &value) {this->yy = value;}
    void SetZZ(const Scalar &value) {this->zz = value;}
    void SetXY(const Scalar &value) {this->xy = value;}
    void SetXZ(const Scalar &value) {this->xz = value;}
    void SetYZ(const Scalar &value) {this->yz = value;}
    void SetYX(const Scalar &value) {this->yx = value;}
    void SetZX(const Scalar &value) {this->zx = value;}
    void SetZY(const Scalar &value) {this->zy = value;}

public:
    Tensor Transpose() const;
    Scalar Det() const;
    Tensor Accompany() const;
    Tensor Inverse() const;
    Tensor Symm(const bool &half = true) const;
    Tensor AntiSymm(const bool &half = true) const;
    
    Scalar Trace() const
    {
        return xx + yy + zz;
    }
    void AddDiag(const Scalar &diag)
    {
        this->xx += diag;
        this->yy += diag;
        this->zz += diag;
    }

public:
    void Read(std::fstream &file, const bool binary = true);
    void Write(std::fstream &file, const bool binary = true)const;
    friend std::fstream& operator << (std::fstream &os, const Tensor &t);
    Scalar operator && (const Tensor &t) const;
    Vector operator*(const Vector &vv) const;
    friend Vector operator * (const Vector &v, const Tensor &t);
    
    Tensor& operator += (const Tensor&t)
    {
        this->xx += t.xx; this->yy += t.yy; this->zz += t.zz;
        this->xy += t.xy; this->xz += t.xz; this->yz += t.yz;
        this->yx += t.yx; this->zx += t.zx; this->zy += t.zy;

        return *this;
    }

    friend inline Tensor operator * (const Scalar &d, const Tensor &t)
    {
        return t*d;
    }
    
    Tensor operator+(const Tensor &t) const
    {
        Tensor t1(*this);
        t1 += t;
        return t1;
    }

    Tensor operator - () const
    {
        Tensor t1;
        t1.type = this->type;
        t1.xx = -this->xx; t1.yy = -this->yy; t1.zz = -this->zz;
        t1.xy = -this->xy; t1.xz = -this->xz; t1.yz = -this->yz;
        t1.yx = -this->yx; t1.zx = -this->zx; t1.zy = -this->zy;

        return t1;
    }

    Tensor operator-(const Tensor &t) const
    {
        Tensor t1(*this);
        t1 -= t;
        return t1;
    }

    Tensor& operator-=(const Tensor &t)
    {
        this->xx -= t.xx; this->yy -= t.yy; this->zz -= t.zz;
        this->xy -= t.xy; this->xz -= t.xz; this->yz -= t.yz;
        this->yx -= t.yx; this->zx -= t.zx; this->zy -= t.zy;

        return *this;
    }

    Tensor operator*(const Scalar &s) const
    {
        Tensor t1(*this);
        t1 *= s;
        return t1;
    }

    Tensor &operator*=(const Scalar &s)
    {
        this->xx *= s; this->yy *= s; this->zz *= s;
        this->xy *= s; this->xz *= s; this->yz *= s;
        this->yx *= s; this->zx *= s; this->zy *= s;

        return *this;
    }

    Tensor operator*(const Tensor& t) const
    {    
        return Tensor
            (xx * t.xx + xy * t.yx + xz * t.zx, xx * t.xy + xy * t.yy + xz * t.zy, xx * t.xz + xy * t.yz + xz * t.zz,
             yx * t.xx + yy * t.yx + yz * t.zx, yx * t.xy + yy * t.yy + yz * t.zy, yx * t.xz + yy * t.yz + yz * t.zz,
             zx * t.xx + zy * t.yx + zz * t.zx, zx * t.xy + zy * t.yy + zz * t.zy, zx * t.xz + zy * t.yz + zz * t.zz  );
    }

    Tensor operator/(const Scalar &s) const
    {
        return (*this) * (1.0 / s);
    }

    friend inline Tensor operator * (const Vector &p1, const Vector &p2)
    {
        return Tensor(p1.x_*p2.x_, p1.x_*p2.y_, p1.x_*p2.z_,
                      p1.y_*p2.x_, p1.y_*p2.y_, p1.y_*p2.z_,
                      p1.z_*p2.x_, p1.z_*p2.y_, p1.z_*p2.z_);
    }

    friend inline Vector Dot(const Tensor &t, const Vector &v)
    {
        return t * v;
    }

    friend inline Vector Dot(const Vector &v, const Tensor &t)
    {
        return v * t;
    }

private:
    Type type;    /* type == genral :  |  xx  xy  xz  |
                                       |  yx  yy  yz  |
                                       |  zx  zy  zz  |
                                         
                      type == diag :    |  xx          |
                                        |      yy      |
                                        |          zz  |

                      type == sym :     |  xx  xy  xz  | 
                                        |  xy  yy  yz  |
                                        |  xz  yz  zz  |

                     type == antiSym :  |   0  xy  xz  |
                                        | -xy   0  yz  |
                                        | -xz -yz   0  |
                    */
    Scalar xx, xy, xz;
    Scalar yx, yy, yz;
    Scalar zx, zy, zz;
#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & type;
        ar & xx;
        ar & xy;
        ar & xz;
        ar & yx;
        ar & yy;
        ar & yz;
        ar & zx;
        ar & zy;
        ar & zz;
    }
#endif

};

/// 张量零
#define Tensor0 (Tensor(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0))

/// 单位张量
#define Tensor1 (Tensor(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0))

#endif
