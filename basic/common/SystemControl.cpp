﻿#include "basic/common/LogLevel.h"
#include "basic/common/StringTools.h"
#include "basic/common/SystemControl.h"

#include <algorithm>
#include <iostream>
#include <iomanip>
#include <sstream>
#include <stdlib.h>

#if defined(_BasePlatformWinddows_)
    #include <direct.h>
#else
    #include <sys/types.h>
    #include <sys/stat.h>
#endif

//print error information and stop
void FatalError(const std::string &info)
{
    std::cout << LogLevel(LogLevel::mlError, std::string("\n FATAL ERROR:\n") + info) << std::endl;

    Info::infoFile << std::endl;
    Info::infoFile <<"FATAL ERROR:"<< std::endl;
    Info::infoFile << info << std::endl;
    
#if defined(_DebugMode_)
    getchar();
#endif
    
    Info::status = 1;
}

//print waring information and pause
void WarningPause(const std::string &info)
{
    std::cout << LogLevel(LogLevel::mlWarning, std::string("\n WARNING:\n") + info) << std::endl;

    Info::infoFile << std::endl;
    Info::infoFile << "WARNING:" << std::endl;
    Info::infoFile << info << std::endl;

    std::cout << "Do you want to continue?[y/n]";
    std::string yesOrNo;
    while (true)
    {
        std::cin >> yesOrNo;
        if ("y" == yesOrNo)
        {
            break;
        }
        else if ("n" == yesOrNo)
        {
            exit(1);
        }
        else
        {
            std::cout << "Please input \'y\' or \'n\': " << std::endl;
        }
    }
    return;
}

//print waring information but continue
void WarningContinue(const std::string &info)
{
    std::cout << LogLevel(LogLevel::mlWarning, std::string("\n WARNING:\n") + info) << std::endl;

    Info::infoFile << std::endl;
    Info::infoFile << "WARNING:" << std::endl;
    Info::infoFile << info << std::endl;

    return;
}

//Set info at namespace Info
void SetInfoFile(const std::string &s)
{
    Info::infoFile.close();
    Info::infoFile.open(s, std::ios::out);
    Info::status = 0;
    return;
}

void SetInfoLevel(const int &fileLevel, const int &screenLevel)
{
    Info::fileLevel = fileLevel;
    Info::screenLevel = screenLevel;
}

void CloseInfoFile()
{
    Info::infoFile.close();
    return;
}

void Print(const std::string &info, const int& level)
{
    if (level == 0)
    {
        PrintScreen(info);
        PrintFile(info);
    }
    else if (level == 1)
    {
        if(level <= Info::screenLevel) PrintScreen("\n >> " + info);
        if (level <= Info::fileLevel) PrintFile("\n >> " + info);
    }
    else if (level == 2)
    {
        if (level <= Info::screenLevel) PrintScreen("   ++ " + info);
        if (level <= Info::fileLevel) PrintFile("   ++ " + info);
    }
    else if (level == 3)
    {
        if (level <= Info::screenLevel) PrintScreen("     -- " + info);
        if (level <= Info::fileLevel) PrintFile("     -- " + info);
    }
    else if (level == 4)
    {
        if (level <= Info::screenLevel) PrintScreen("       %% " + info);
        if (level <= Info::fileLevel) PrintFile("       %% " + info);
    }
}

void PrintScreen(const std::string &info)
{
    std::cout << info << std::endl;    
    return;
}

void PrintFile(const std::string &info)
{
    Info::infoFile << info << std::endl;
    return;
}

void PrintTitleInfo(const std::string &info)
{
    Print(ObtainInfoTitle() + "\n" + ObtainInfoTitle(info) + "\n" + ObtainInfoTitle());
}

void PrintProgressRate(const std::string &processDescription, const int &currentStep, const int &totalStep)
{
    if ((currentStep + 1) % std::max(totalStep / 10, 1) == 0)
    {
        std::ostringstream stringStream;
        stringStream << processDescription << ":\t" << std::setprecision(3) 
                     << (Scalar)(currentStep + 1) / totalStep * 100.0 << "%";
        Print(stringStream.str());
    }
}

void MakeDirectory(const std::string &path)
{
#if defined(_BasePlatformWinddows_)
    _mkdir(path.c_str());
#else
    mkdir(path.c_str(), S_IRWXU);
#endif
}

void DeleteDirectory(const std::string &path)
{
#if defined(_BasePlatformWinddows_)

#else

#endif
}

void CheckStatus(const int &codeID)
{
    if (Info::status == 1)
    {
        std::cout << "Error ID: " << codeID << std::endl;
        Info::infoFile << "Error ID: " << codeID << std::endl;
        exit(codeID);
    }
}
