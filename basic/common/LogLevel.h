﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LogLevel.h
//! <AUTHOR>
//! @brief 屏幕打印信息类.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_LogLevel_
#define _basic_common_LogLevel_

#include <iostream>
#include <string>
#include "stdio.h"

#include "basic/common/Configuration.h"

#if defined(_BasePlatformWinddows_)
#include <Windows.h>

#define    FG_RED          FOREGROUND_INTENSITY | FOREGROUND_RED
#define    FG_GREEN        FOREGROUND_INTENSITY | FOREGROUND_GREEN
#define    FG_BLUE         FOREGROUND_INTENSITY | FOREGROUND_BLUE
#define    FG_GRAY         FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE
#define    FG_YELLOW       FOREGROUND_INTENSITY | FOREGROUND_RED | FOREGROUND_GREEN

#endif
/**
 * @brief 信息打印类
 * @note 1.Change screen output information color;
 * 2.This class allow user to compile not only in Windows, but also in Linux
 * 3.Color : RED,GREEN,BLUE,GRAY,YELLOW
 */
class LogLevel
{
public:
    /**
     * @brief 信息级别枚举
     * 
     */
    enum MsgLevel
    {
        mlError,
        mlWarning,
        mlOK,
        mlInfo    
    };

private:
    /**
     * @brief 系统标识
     * 
     */
    enum SystemPlatform
    {
        spWindows,
        spLinux,
        spNoType
    };

    static SystemPlatform sp_sysPlatform;
    
#if defined(_BasePlatformWinddows_)

    HANDLE        m_handle;

#endif

    MsgLevel    m_lv;

    std::string    m_stMsg;

public:
    LogLevel(const MsgLevel& lv, const std::string& stMsg);
        

    ~LogLevel();


    /// "<<" overload
    friend inline std::ostream& operator << (std::ostream& out, const LogLevel& rhs)
    {
        out << rhs.m_stMsg << "\t";
        return out;
    }
};

#endif
