﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ListTools.h
//! <AUTHOR> 张帅（数峰科技/西交大）
//! @brief 容器vector的各种附加功能.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_ListTools_
#define _basic_common_ListTools_

#include "basic/common/ConfigUtility.h"

/**
 * @brief Get the Non Repeated List object
 * 
 * @param originalList 
 * @note Kong Ling supplemented on 2019/12/23
 * @return std::vector<int> 
 */
std::vector<int> GetNonRepeatedList(const std::vector<int>& originalList);

/**
 * @brief add method
 * 
 * @tparam Type data type
 * @return std::vector<Type> return a new std::vector
 */
template<class Type>
std::vector<Type> operator + (const std::vector<Type>&, const std::vector<Type>&);

/**
 * @brief subtract method
 * 
 * @tparam Type data type
 * @return std::vector<Type> return a new std::vector
 */
template<class Type>
std::vector<Type> operator - (const std::vector<Type>&, const std::vector<Type>&);


/**
 * @brief multiply std::vector by a scalar
 * 
 * @tparam Type data type
 * @return std::vector<Type> return a new std::vector
 * @note Kong Ling supplemented on 2019/12/23
 */
template<class Type>
std::vector<Type> operator *(const Scalar&, const std::vector<Type>&);

/**
 * @brief multiply method
 * 
 * @tparam Type data type 
 * @return std::vector<Type> return a new std::vector
 */
template<class Type>
std::vector<Type> operator * (const std::vector<Scalar>&, const std::vector<Type>&);

/**
 * @brief division method
 * 
 * @tparam Type data type
 * @return std::vector<Type> return a new std::vector
 */
template<class Type>
std::vector<Type> operator / (const std::vector<Type>&, const std::vector<Scalar>&);

/**
 * @brief dot multiply method
 * 
 * @return std::vector<Scalar> return a new std::vector
 * @note Kong Ling supplemented on 2019/12/22
 */
std::vector<Scalar> operator & (const std::vector<Vector>&, const std::vector<Vector>&);

/**
 * @brief + operator
 * 
 * @tparam T1 key type
 * @tparam T2 value type
 * @param left first map
 * @param right second map
 * @return std::map<T1, T2> return a new map
 */
template<class T1, class T2>
std::map<T1, T2> operator + (const std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief += operator
 * 
 * @tparam T1 key type
 * @tparam T2 value type
 * @param left first map
 * @param right second map
 */
template<class T1, class T2>
void operator += (std::map<T1, T2>& left, const std::map<T1, T2>& right);


/**
 * @brief -operator
 * 
 * @tparam T1 key type
 * @tparam T2 value type
 * @param left first map
 * @param right second map
 * @return std::map<T1, T2> return a new map
 */
template<class T1, class T2>
std::map<T1, T2> operator - (const std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief -= operator
 * 
 * @tparam T1 key type
 * @tparam T2 value type
 * @param left first map
 * @param right second map
 */
template<class T1, class T2>
void operator -= (std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief * operator
 * 
 * @tparam T1 key type
 * @tparam T2 value type
 * @param left first map
 * @param right second map
 * @return std::map<T1, T2> return a new map
 */
template<class T1, class T2>
std::map<T1, T2> operator * (const std::map<T1, T2>& left, const std::map<T1, T2>& right);

/**
 * @brief *= operator
 * 
 * @tparam T1 key type
 * @tparam T2 value type
 * @param left first map
 * @param right second map
 */
template<class T1, class T2>
void operator *= (std::map<T1, T2>& left, const std::map<T1, T2>& right);

#endif
