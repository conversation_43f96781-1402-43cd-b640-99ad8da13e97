﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OpenMPMacro.h
//! <AUTHOR>
//! @brief OpenMP相关操作功能.
//! @date 2022-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-07-22 乔龙
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_OPENMPMACRO_
#define _basic_common_OPENMPMACRO_

#include <type_traits>

#if defined(_BasePlatformWinddows_)
#define PRAGMIZE(X) __pragma(X)
#else
#define PRAGMIZE(X) _Pragma(#X)
#endif

#if defined(_BaseParallelOpenMP_)

#include <omp.h>
#define ARI_OMP(ARGS) PRAGMIZE(omp ARGS)

#else

#define ARI_OMP(ARGS)
inline constexpr int omp_get_max_threads(void) {return 1;}
inline constexpr int omp_get_num_threads(void) {return 1;}
inline void omp_set_num_threads(int) { }
inline constexpr int omp_get_thread_num(void) {return 0;}

#endif

inline int GetWorkPerThread(int totalWork, int numThreads) {return (totalWork+numThreads-1)/numThreads;}

inline int computeStaticChunkSize(int totalWork, int numThreads, int maxChunkSize)
{
    if (!totalWork) return maxChunkSize;
    int workPerThread = GetWorkPerThread(totalWork, numThreads);
    int chunksPerThread = GetWorkPerThread(workPerThread, maxChunkSize);
    return GetWorkPerThread(workPerThread, chunksPerThread);
}

#endif

extern int maxChunkSize;
extern int nthreads;
extern int ChunkSize;