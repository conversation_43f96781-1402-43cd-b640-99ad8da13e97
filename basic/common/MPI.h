﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MPI.h
//! <AUTHOR>
//! @brief MPI相关操作功能.
//! @date 2022-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-07-22 乔龙
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_MPI_
#define _basic_common_MPI_

#include <string>
#include <vector>

#include "basic/common/BoostLib.h"
#if defined(_BaseParallelMPI_)
#include <mpi.h>
#endif

#if defined(_BaseParallelMPI_)
namespace MPI
{
    static boost::mpi::communicator mpiWorld;
}
#endif

#if defined(_BaseParallelMPI_)
#define InitializeMPI(argc, argv) boost::mpi::environment env(argc, argv);
#else
#define InitializeMPI(argc, argv)
#endif

#if defined(_BaseParallelMPI_)
#define FinalizeMPI() boost::mpi::environment::finalized();
#else
#define FinalizeMPI()
#endif

bool MPIInitialized();

int GetMPIRank();

int GetMPISize();

void MPIBarrier();

void MPIIprobe(std::vector<std::pair<int, int>> &tags);

#if defined(_BaseParallelMPI_)
void MPIWaitAll(std::vector<boost::mpi::request> &requests);
#endif

template<class Type>
void MPIBroadcast(Type &phi, const int &rootID = 0);

template<class Type>
int MaxAllProcessor(Type &phi, const int &rootID = 0);

template<class Type>
int MinAllProcessor(Type &phi, const int &rootID = 0);

template<class Type>
void SumAllProcessor(Type &phi, const int &rootID = 0);

#endif