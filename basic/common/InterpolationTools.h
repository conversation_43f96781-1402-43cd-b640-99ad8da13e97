﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file InterpolationTools.h
//! <AUTHOR> 张帅（数峰科技/西交大）
//! @brief 基本插值功能.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_InterpolationTools_
#define _basic_common_InterpolationTools_

#include "basic/common/ConfigUtility.h"

/**
 * @brief Interpolation based on distance
 * 
 * @tparam Type Scalar, Vector or Tensor
 * @param distance distances for interpolation
 * @param value values for interpolation
 * @return Type 
 */
template<class Type>
Type DistanceInterpolation
(
    const std::vector<Scalar>& distance,
    const std::vector<Type>& value
);

template<class Type>
Type RBFInterpolation
(
    /// list of positions interpolated from
    const std::vector<Vector>&,
    /// list of corresponding values
    const std::vector<Type>&
);

/// Gaussian elimination
template<class Type>
std::vector<Type> Gaussin_L
(
    std::vector<std::vector<Scalar> >&,
    std::vector<Type>&
);

/**
 * @brief Calculate gradient a set of surrounding points using least square fit in two dimensions
 * 
 * @param rePosition reference position
 * @param value value list
 * @return Vector return gradient of scalar
 */
Vector LeastSquareGradient2D
(
    const std::vector<Vector>& rePosition,
    const std::vector<Scalar>& value
);

/**
 * @brief Calculate gradient a set of surrounding points using least square fit in three dimensions
 * 
 * @param rePosition reference position
 * @param value value list
 * @return Vector return gradient of scalar
 */
Vector LeastSquareGradient3D
(
    const std::vector<Vector>& rePosition,
    const std::vector<Scalar>& value
);

/**
 * @brief Calculate gradient a set of surrounding points using least square fit in two dimensions
 * 
 * @param rePosition reference position
 * @param value value list
 * @return Tensor return gradient of vector
 */
Tensor LeastSquareGradient2D
(
    const std::vector<Vector> &rePosition,
    const std::vector<Vector> &value
);

/**
 * @brief Calculate gradient a set of surrounding points using least square fit in three dimensions
 * 
 * @param rePosition reference position
 * @param value value list
 * @return Tensor 
 */
Tensor LeastSquareGradient3D
(
    const std::vector<Vector>& rePosition,
    const std::vector<Vector>& value
);

/**
 * @brief Standard interpolation interpolating from a list of points around as well as corresponding values. 
 * 
 * @tparam Type data type
 * @param rePosition list of relative positions(the coordinate of the point interpolated for is (0,0,0))
 * @param value list of corresponding values
 * @return Type return the result
 */
template<class Type>
Type Interpolation
(    const std::vector<Vector>& rePosition,
    const std::vector<Type>& value
);

/**
 * @brief Linear interpolation interpolating from a list of points around as well as corresponding values. 
 * 
 * @tparam Type data type
 * @param distances distances to the point to be interpolated
 * @param values values interpolated from
 * @return Type return the result
 */
template<class Type>
Type LinearInterpolation
(
    const std::pair<Scalar, Scalar>& distances,
    const std::pair<Type, Type>& values
);

/**
 * @brief Hamonic interpolation interpolating from a list of points around as well as corresponding values. 
 * 
 * @tparam Type data type
 * @param distances distances to the point to be interpolated
 * @param values values interpolated from
 * @return Type return the result
 */
template<class Type>
Type HamonicInterpolation
(
    /// distances to the point to be interpolated
    const std::pair<Scalar, Scalar>& distances,
    /// values interpolated from
    const std::pair<Type, Type>& values
);

#endif
