﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SystemTime.h
//! <AUTHOR>
//! @brief 系统时间相关功能.
//! @date 2022-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-07-22 乔龙
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_SystemTime_
#define _basic_common_SystemTime_

#include <string>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <stdlib.h>
#include <time.h>

using namespace std::chrono;

class SystemTime
{
public:
    SystemTime();

    double GetElapsedTime();

    void UpdateTime();

    void PrintNowTime();
    
    /**
    * @brief 打印统计时间
    * 
    * @param[in] processDescription 打印信息
    * @param[in] startTime 起始时间
    */
    friend void PrintProcessTime(const std::string &processDescription, SystemTime &startTime);
    
private:
    time_point<system_clock> start;
    time_point<system_clock> end;
};

void PrintSystemTime();

/**
* @brief 打印统计时间
* 
* @param[in] processDescription 打印信息
* @param[in] startTime 起始时间
*/
void PrintProcessTime(const std::string &processDescription, clock_t &startTime);

#endif