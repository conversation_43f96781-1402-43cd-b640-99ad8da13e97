﻿#include "basic/common/LagrangeTools.h"
#include "basic/mesh/Mesh.h"

//find the nearest element from a given lagrange point;
int NearestElement
(
    //background grid block
    const Mesh* UGB,
    //coordinate of the given lagrange point
    const Vector& lagPoint,
    //ID of element where searching starts from
    int startEID
)
{
    //check startEID
    if (startEID<0 || startEID>(int)UGB->GetElementNumberReal())
    {
        FatalError("given element ID out of range in NearestElement()");
    }
    int currentID = startEID;
    while (true)
    {
        Scalar currentDis = (UGB->GetElement(currentID).GetCenter() - lagPoint).Mag();
        std::vector<int> nbEID = UGB->SearchElementNeighbor(currentID);
        int chosenID = currentID;
        for (int i = 0; i < (int)nbEID.size(); ++i)
        //check neighbors of current element to see wether closer element can be found
        {
            int newID = nbEID[i];
            Scalar dis = (UGB->GetElement(newID).GetCenter() - lagPoint).Mag();
            if (dis < currentDis)
            {
                currentDis = dis;
                chosenID = newID;
            }
        }
        //if found, replace the current element; otherwise the current element will be
        //considered as the nearest one, thus break loop and return 
        if (chosenID == currentID)
        {
            break;
        }
        else
        {
            currentID = chosenID;
        }
    }
    return currentID;
}

//find the nearest element from a given lagrange point;
int SearchNearestBoundaryFace
(
    //background grid block
    const Mesh* pmesh,
    //coordinate of the given lagrange point
    const Vector &lagPoint,
    //ID of the face which the search starts from
    int startID
)
{
    int currentID = startID;
    while (true)
    {
        Scalar currentDis = (pmesh->GetFace(currentID).GetCenter() - lagPoint).Mag();
        
        FatalError("SearchNearestBoundaryFace:");
        std::vector<int> v_nbID; // = pmesh->SearchBounaryFaceNeighbor(currentID);


        int chosenID = currentID;
        for (int i = 0; i < (int)v_nbID.size(); ++i)
        //check neighbors of current faces to see wether a closer one can be found
        {
            int newID = v_nbID[i];
            Scalar dis = (pmesh->GetFace(newID).GetCenter() - lagPoint).Mag();
            if (dis < currentDis)
            {
                currentDis = dis;
                chosenID = newID;
            }
        }
        //if found, replace the current face; otherwise the current face will be
        //considered as the nearest one, thus break loop and return 
        if (chosenID == currentID)
        {
            break;
        }
        else
        {
            currentID = chosenID;
        }
    }
    return currentID;
}
