﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Matrix.h
//! <AUTHOR>
//! @brief 基本矩阵类
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2022-02-15 李艳亮
//    说明：调整并规范化、增加新功能。
//
// 2020-07-27 数峰科技
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_common_Matrix_
#define _basic_common_Matrix_

#include "basic/common/BoostLib.h"
#include "basic/common/Configuration.h"
#include "basic/common/SystemControl.h"

#include <vector>

class Matrix
{
public:
    Matrix(const int row = 0, const int col = 0);
	~Matrix(){};
    //Matrix(const std::vector<std::vector<Scalar>> &matrixValue_);
    
	const Scalar &GetValue(const int &index) const { return matrixValue[index]; }
	const Scalar &GetValue(const int &row, const int &col) const { return matrixValue[row * colSize + col]; }
	int SizeRow() const { return rowSize; }
	int SizeCol() const { return colSize; }
	inline Scalar& operator()(const int &index){ return matrixValue[index]; }
	inline Scalar& operator()(const int &row, const int &col){ return matrixValue[row * colSize + col]; }

    void Resize(const int &row, const int &col);
	void SetValue(const int &index, const Scalar &value){ matrixValue[index] = value; };
	void SetValue(const int &row, const int &col, const Scalar &value){ matrixValue[row * colSize + col] = value; };
    //void AddValue(const int &row, const int &col, const Scalar &value);
    void SetIdentity();
    void SetZero();
    Matrix Inverse(); 
	/*
    Matrix DiagInverse();
    Scalar CalculateDeterminant();
    friend std::ostream& operator << (std::ostream& os, const Matrix& matrix);*/

    Matrix operator=(const Matrix &rhs);
    Matrix operator*(const Matrix &rhs);
    Matrix operator*(const Scalar &value);
    friend Matrix operator*(const Scalar value, const Matrix &rhs);
	
	Matrix operator-();
	
    Matrix operator-(const Matrix &rhs);
    Matrix operator+(const Matrix &rhs);
    void operator +=(const Matrix &rhs);
    void operator -=(const Matrix &rhs);
	
	/*
    std::vector<Scalar> operator*(const std::vector<Scalar>& curValue)
	{
		if (curValue.size()!= rowSize)
		{
			FatalError(" Check the sizes of matrix and vector! ");
		}

		std::vector<Scalar> result(curValue.size(), 0.0);

		for (int i = 0; i < rowSize; i++)
		{
			for (int j = 0; j < colSize; j++)
			{
				result[i] += curValue[j] * matrixValue[i * colSize + j];
			}
		}

		return result;
	}*/

#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        //ar & matrixValue;
    }
#endif

private:
    //std::vector< std::vector<Scalar>> matrixValue;
	std::vector<Scalar> matrixValue;
	int rowSize;
	int colSize;
};

#endif