﻿#include "basic/common/StringTools.h"


//This function can be used to extract content between a couple of symbols, 
//such as '(' and ')' from a given file.
//function returns:
//    0 for succussful extraction, and
//    1 for failure.
int GetBySymbol
(
std::ifstream& infile,
std::stringstream& outstringstream,
char leftChar,
char rightChar
)
{
    char onechar;
    outstringstream.str("");
    //find the position of the first left parathese
    while (infile.get(onechar))
    {
        if (onechar == leftChar) break;
    }

    if (leftChar == rightChar)
    {
        //For example you want to get the content between a pair of double quotes,
        //in which the left char (\") is the same as the right one
        while (infile.get(onechar))
        {
            if (onechar == rightChar)
            {
                return 0;
            }
            outstringstream << onechar;
        }
    }
    else
    {
        //For example you want to get the content between a pair of brackes {} or parentheses (),
        //where the left char (\") differs from the right one
        int bracketNum = 1;
        //read content until reaching the corresponding right parathese
        while (infile.get(onechar))
        {
            if (onechar == leftChar)
            {
                ++bracketNum;
            }
            else if (onechar == rightChar)
            {
                --bracketNum;
            }
            if (bracketNum == 0)
            {
                return 0;
            }
            outstringstream << onechar;
        }
    }
    //empty output stringstream and return failure information if corresponding right was not found.
    outstringstream.str("");
    return 1;
}

//This function is designed to extract content between a couple of symbols, 
//such as '(' and ')' from a given stringstream.
//function returns:
//    0 for succussful extraction, and
//    1 for failure.
int GetBySymbol
(
std::stringstream& instringstream,
std::stringstream& outstringstream,
char leftChar,
char rightChar
)
{
    char onechar;
    outstringstream.str("");
    //find the position of the first left parathese
    while (instringstream.get(onechar))
    {
        if (onechar == leftChar) break;
    }

    if (leftChar == rightChar)
    {
        //For example you want to get the content between a pair of double quotes,
        //in which the left char (\") is the same as the right one
        while (instringstream.get(onechar))
        {
            if (onechar == rightChar)
            {
                return 0;
            }
            outstringstream << onechar;
        }
    }
    //For example you want to get the content between a pair of brackes {} or parentheses (),
    //where the left char (\") differs from the right one
    else
    {
        int bracketNum = 1;
        //read content until reaching the corresponding right parathese
        while (instringstream.get(onechar))
        {
            if (onechar == leftChar)
            {
                bracketNum++;
            }
            else if (onechar == rightChar)
            {
                bracketNum--;
            }
            if (bracketNum == 0)
            {
                return 0;
            }
            outstringstream << onechar;
        }
    }
    //empty output stringstream and return failure information if corresponding right was not found.
    outstringstream.str("");
    return 1;
}

bool getline(std::stringstream& sstream, std::string& outstring, char symbol)
{
    char onechar;
    std::string stTemp;
    while (sstream.get(onechar))
    {
        if (onechar == symbol)
        {
            outstring = stTemp;
            return true;
        }
        stTemp.push_back(onechar);
    }
    if (stTemp.size() > 0)
    {
        outstring = stTemp;
        return true;
    }
    else
    {
        return false;
    }
}

void ReadLineToSStream(std::ifstream& inFile, std::string& text_line, std::stringstream& sstream)
{
    // Clear sstream
    sstream.str("");
    sstream.clear();

    getline(inFile, text_line);

    // Skip empty line
    while (text_line.size() == 0)
    {
        getline(inFile, text_line);
    }

    sstream << text_line;
}

template std::string ToString(const size_t &value);
template std::string ToString(const int &value);
template std::string ToString(const short &value);
template std::string ToString(const long &value);
#if defined(_BasePlatformWinddows_)
template std::string ToString(const __int64 &value);
#endif
template std::string ToString(const float &value);
template std::string ToString(const double &value);
template std::string ToString(const std::string &value);
template<class Type>
std::string ToString(const Type &value)
{
    std::ostringstream stringStream;
    stringStream << value;
    return stringStream.str();
}

template<>
std::string ToString(const bool &value)
{
    if(value) return "true";
    else      return "false";
}

template<>
std::string ToString(const Vector &value)
{
    std::ostringstream stringStream;
    stringStream << value.X() << " " << value.Y() << " " << value.Z();
    return stringStream.str();
}

template<>
std::string ToString(const Tensor &value)
{
    std::ostringstream stringStream;
    stringStream << "|" << value.XX() << ", " << value.XY() << ", " << value.XZ() << "|";
    stringStream << "|" << value.YX() << ", " << value.YY() << ", " << value.YZ() << "|";
    stringStream << "|" << value.ZX() << ", " << value.ZY() << ", " << value.ZZ() << "|";
    return stringStream.str();
}

template std::string ToStringVector(const std::vector<int> &value);
template std::string ToStringVector(const std::vector<double> &value);
template<class Type>
std::string ToStringVector(const std::vector<Type> &value)
{
    if(value.size()==0) return "{}";

    std::ostringstream stringStream;
    stringStream << "{" << value[0];
    for (int i = 1; i < value.size(); ++i) stringStream << ", " << value[i];
    stringStream << "}";
    return stringStream.str();
}

template<>
std::string ToString(const std::vector<int> &value)
{
    return ToStringVector(value);
}

template<>
std::string ToString(const std::vector<double> &value)
{
    return ToStringVector(value);
}

template std::string ToString(const int *value, const int &size);
template std::string ToString(const double *value, const int &size);
template<class Type>
std::string ToString(const Type *value, const int &size)
{
    if(size==0) return "{}";

    std::ostringstream stringStream;
    stringStream << "{" << value[0];
    for (int i = 1; i < size; ++i) stringStream << ", " << value[i];
    stringStream << "}";
    return stringStream.str();
}

bool JudgeBlank(const std::string& string)
{
    bool blankFlag = true;
    for (int i = 0; i < string.length(); ++i)
    {
        if(string[i] != ' ')
        {
            blankFlag = false;
            break;
        }
    }
    return blankFlag;
}

std::string TransformToCapital(std::string &currentString)
{
    int stringSize = currentString.size();
    for (int len = 0; len < stringSize; ++len)
    {
        currentString[len] = toupper(currentString[len]);
    }
    return currentString;
}

int StringToInt(const std::string &s)
{
    int value;
    std::istringstream (s) >> value;
    return value;
}

Scalar StringToScalar(const std::string &s)
{
    Scalar value;
    std::istringstream(s) >> value;
    return value;
}

Vector StringToVector(const std::string &s)
{
    Scalar x, y, z;
    std::istringstream(s) >> x >> y >> z;
    return Vector(x, y, z);
}

bool CompareTwoStrings(const std::string &str1, const std::string &str2)
{
    if (str1.empty() || str2.empty()) return false;
    if (str1.size() != str2.size()) return false;

    for (int i = 0; i < str1.size(); ++i)
    {
        if (str1[i] != str2[i]) return false;
    }

    return true;
}

std::string ScalarToString(const Scalar &value, const int &size, const int &precision, const bool &scientific)
{
	std::ostringstream stringStream;
    
	stringStream << std::setprecision(precision);
	stringStream << std::fixed << std::setw(size);
	if (scientific) stringStream << std::scientific;
	stringStream << value;

	return stringStream.str();
}

int CountUTF8Characters(const std::string &stringTmp)
{
    int count = 0;
    
    for (size_t i = 0; i < stringTmp.length();)
    {
        bool chineseFlag = false;
        if (i + 2 < stringTmp.size() &&
            (static_cast<unsigned char>(stringTmp[i    ]) & 0xF0) == 0xE0 &&
            (static_cast<unsigned char>(stringTmp[i + 1]) & 0xC0) == 0x80 &&
            (static_cast<unsigned char>(stringTmp[i + 2]) & 0xC0) == 0x80)
        {
            unsigned unicode = ((static_cast<unsigned char>(stringTmp[i    ]) & 0x0F) << 12) |
                               ((static_cast<unsigned char>(stringTmp[i + 1]) & 0x3F) << 6) |
                               ((static_cast<unsigned char>(stringTmp[i + 2]) & 0x3F));
            chineseFlag = (unicode >= 0x4E00 && unicode <= 0x9FFF);
        }

        if (chineseFlag)
        {
            i += 2;
#if defined(_BasePlatformWinddows_)
            count += 2;
#else
            count += 1;
#endif
        }
        else
        {
            i++;
            count++;
        }
    }
    
    return count;
}

std::string ObtainInfoTitle(const std::string info, const char symbol, const int length_, const int pos)
{
    const int infoLength = CountUTF8Characters(info); //.length();
    const int length = length_ > infoLength + 4 ? length_ : infoLength + 4;
    
	if (pos == 0)
	{
		const int leftLength = (length - infoLength) / 2;
		const int rightLength = length - infoLength - leftLength;
		return std::string(leftLength, symbol) + info + std::string(rightLength, symbol);
	}
	else if (pos < 0)
	{
		const int leftLength = 0;
		const int rightLength = length - infoLength;
		return std::string(leftLength, symbol) + info + std::string(rightLength, symbol);
	}
	else
	{
		const int leftLength = length - infoLength;
		const int rightLength = 0;
		return std::string(leftLength, symbol) + info + std::string(rightLength, symbol);
	}
}

bool EndWithSuffix(const std::string& str, const std::string &suffix)
{
    if (suffix.empty()) return true;

	if (str.length() >= suffix.length()) {
		return str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
	}
    
	return false;
}
