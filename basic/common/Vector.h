﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Vector.h
//! <AUTHOR>
//! @brief 矢量类
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2022-09-22 李艳亮、乔龙
//    说明：改写与规范化
//
// 2020-07-22 数峰科技/西交大
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_common_Vector_
#define _basic_common_Vector_

#include "basic/common/BoostLib.h"
#include "basic/common/Configuration.h"

#include <iostream>
#include <string>
#include <fstream>
#include <math.h>

class Tensor;

class Vector
{
public:    
    Vector() :x_(0.0), y_(0.0), z_(0.0){}
    Vector(const Scalar &X, const Scalar &Y, const Scalar &Z) :x_(X), y_(Y), z_(Z){}

public:
    const Scalar &X()const { return this->x_; }
    const Scalar &Y()const { return this->y_; }
    const Scalar &Z()const { return this->z_; }

    void SetX(const Scalar &value) { this->x_ = value; }
    void SetY(const Scalar &value) { this->y_ = value; }
    void SetZ(const Scalar &value) { this->z_ = value; }
    
public:
    Scalar Mag() const {return sqrt(x_*x_ + y_*y_ + z_*z_);}  
    Vector &Normalize() { (*this) *= (1.0 / this->Mag()); return *this; }
    Vector GetNormal() const {return (*this) * (1.0 / this->Mag());}
    void Projection(const Vector &direct) { const Vector norm = direct.GetNormal(); *this -= (*this & norm)*norm;}

public:
    Vector operator + (const Vector &rhs)const {return Vector(x_ + rhs.x_, y_ + rhs.y_, z_ + rhs.z_);}
    Vector operator - (const Vector &rhs)const {return Vector(x_ - rhs.x_, y_ - rhs.y_, z_ - rhs.z_);}
    Vector operator / (const Vector &rhs)const {return Vector(x_ / rhs.x_, y_ / rhs.y_, z_ / rhs.z_);}
    Vector operator - ()const {return Vector(-x_, -y_, -z_);}
    Vector Multiply(const Vector &rhs)const {return Vector(x_ * rhs.x_, y_ * rhs.y_, z_ * rhs.z_);}

    void operator += (const Vector &rhs) { this->x_ += rhs.x_; this->y_ += rhs.y_; this->z_ += rhs.z_; }
    void operator -= (const Vector &rhs) { this->x_ -= rhs.x_; this->y_ -= rhs.y_; this->z_ -= rhs.z_; }
    void operator *= (const Scalar &rhs) { this->x_ *= rhs; this->y_ *= rhs; this->z_ *= rhs; }
    void operator /= (const Scalar &rhs) { *this *= (1.0 / rhs); }
    bool operator == (const Vector &rhs) { return (this->x_ - rhs.x_ < SMALL) && (this->y_ - rhs.y_ < SMALL) &&  (this->z_ - rhs.z_ < SMALL); }

    void Read(std::fstream &file, const bool binary)
    {
        if(binary)
        {
            file.read((char *)&this->x_, sizeof(Scalar));
            file.read((char *)&this->y_, sizeof(Scalar));
            file.read((char *)&this->z_, sizeof(Scalar));
        }
        else
        {
            file >> this->x_ >> this->y_ >> this->z_;
        }
    }
    
    void Write(std::fstream &file, const bool binary)const
    {
        if(binary)
        {
            file.write((char *)&this->x_, sizeof(Scalar));
            file.write((char *)&this->y_, sizeof(Scalar));
            file.write((char *)&this->z_, sizeof(Scalar));
        }
        else
        {
            file << x_ << " " << y_ << " " << z_ << std::endl;
        }
    }

    //双目运算的重载
    //friend Vector operator + (const Vector &lhs, const Vector &rhs);
    //friend Vector operator - (const Vector &lhs, const Vector &rhs);
    friend inline Scalar Dot(const Vector &lhs, const Vector&rhs) {return lhs.x_ * rhs.x_ + lhs.y_ * rhs.y_ + lhs.z_ * rhs.z_;}

    friend inline std::ostream &operator << (std::ostream &out, const Vector &rhs)
    {
        out << "(" << rhs.x_ << ", " << rhs.y_ << ", " << rhs.z_ << ")";
        return out;
    }

    friend inline Vector operator * (const Scalar &d, const Vector &p) {return Vector(d * p.x_, d * p.y_, d * p.z_);}
    friend inline Vector operator * (const Vector &p, const Scalar &d) {return Vector(p.x_ * d, p.y_ * d, p.z_ * d);}
    friend inline Vector operator ^ (const Vector &v1, const Vector &v2)
    {
        return Vector
            ((v1.y_ * v2.z_ - v1.z_ * v2.y_),
            (v1.z_ * v2.x_ - v1.x_ * v2.z_),
            (v1.x_ * v2.y_ - v1.y_ * v2.x_));
    }
    friend inline Vector operator / (const Vector &p, const Scalar &d) {return Vector(p.x_ / d, p.y_ / d, p.z_ / d);}
    friend inline Scalar operator & (const Vector &lhs, const Vector &rhs){return lhs.x_ * rhs.x_ + lhs.y_ * rhs.y_ + lhs.z_ * rhs.z_;}
    
    //矢量用于张量的计算
    friend class Tensor;
    friend Vector operator * (const Vector &v, const Tensor &t);
    friend inline Tensor operator * (const Vector &p1, const Vector &p2);
    friend inline Vector Dot(const Tensor &t, const Vector &v);
    friend inline Vector Dot(const Vector &v, const Tensor &t);
    
private:
    Scalar x_, y_, z_;

#if defined(_BaseParallelMPI_)
public:
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & x_;
        ar & y_;
        ar & z_;
    }
#endif

};        

/// 矢量零
#define Vector0 (Vector(0.0, 0.0, 0.0))

#endif
