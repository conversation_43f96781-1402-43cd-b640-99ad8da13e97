﻿#include "basic/common/IO.h"

namespace IO
{

template void Write(std::fstream &file, const bool &value, const bool binary);
template void Write(std::fstream &file, const int &value, const bool binary);
template void Write(std::fstream &file, const size_t &value, const bool binary);
template void Write(std::fstream &file, const float &value, const bool binary);
template void Write(std::fstream &file, const Scalar &value, const bool binary);
template void Write(std::fstream &file, const long int &value, const bool binary);
template<class Type>
void Write(std::fstream &file, const Type &value, const bool binary)
{
    if(binary) file.write((char *) &value, sizeof(value));
    else       file << value << std::endl;
}

template<>
void Write(std::fstream &file, const Vector &value, const bool binary)
{
    value.Write(file, binary);
}

template<>
void Write(std::fstream &file, const Tensor &value, const bool binary)
{
    value.Write(file, binary);
}

template<>
void Write(std::fstream &file, const std::string &value, const bool binary)
{
    if(binary)
    {
        const int length = value.length();
        file.write((char *)& length, sizeof(length));
        file.write((char *)& value[0], sizeof(value[0]) * length);
    }
    else
    {
        file << value << std::endl;
    }
}

template void Write(std::fstream &file, const std::pair<bool, bool> &value, const bool binary);
template void Write(std::fstream &file, const std::pair<int, int> &value, const bool binary);
template void Write(std::fstream &file, const std::pair<float, float> &value, const bool binary);
template void Write(std::fstream &file, const std::pair<Scalar, Scalar> &value, const bool binary);
template void Write(std::fstream &file, const std::pair<Tensor, Tensor> &value, const bool binary);
template void Write(std::fstream &file, const std::pair<std::string, std::string> &value, const bool binary);
template<class Type>
void Write(std::fstream &file, const std::pair<Type, Type> &value, const bool binary)
{
    Write(file, value.first, binary);
    Write(file, value.second, binary);
}

template void Write(std::fstream &file, const std::vector<int> &valueVector, const bool binary, int vectorSize);
template void Write(std::fstream &file, const std::vector<Scalar> &valueVector, const bool binary, int vectorSize);
template void Write(std::fstream &file, const std::vector<Vector> &valueVector, const bool binary, int vectorSize);
template void Write(std::fstream &file, const std::vector<Tensor> &valueVector, const bool binary, int vectorSize);
template<class Type>
void Write(std::fstream &file, const std::vector<Type> &valueVector, const bool binary, int vectorSize)
{
    // 初始值
    size_t vectorSizeLong;

    if (vectorSize < 0)
    {
        // 写出容器大小
        vectorSizeLong = valueVector.size();
        if (vectorSizeLong < INT_MAX)
        {
            Write(file, (int)vectorSizeLong, binary);
        }
        else
        {
            Write(file, (int)(-1), binary);
            Write(file, vectorSizeLong, binary);
        }
    }
    else
    {
        // 不写出容器大小
         vectorSizeLong = vectorSize;
    }

    if (vectorSizeLong > 0)
    {
        if(binary) file.write((char *)& valueVector[0], sizeof(valueVector[0]) * vectorSizeLong);
        else       for (size_t i = 0; i < vectorSizeLong; ++i) Write(file, valueVector[i], binary);
    }
}


template<>
void Write(std::fstream &file, const std::vector<std::string> &valueVector, const bool binary, int vectorSize)
{
    if (vectorSize < 0)
    {
        vectorSize = valueVector.size();
        Write(file, vectorSize, binary);
    }
    for (int i = 0; i < vectorSize; ++i) Write(file, valueVector[i], binary);
}

template void Write(std::fstream &file, const std::vector<std::vector<int>> &valueVector, const bool binary, int vectorSize);
template void Write(std::fstream &file, const std::vector<std::vector<Scalar>> &valueVector, const bool binary, int vectorSize);
template void Write(std::fstream &file, const std::vector<std::vector<Vector>> &valueVector, const bool binary, int vectorSize);
template void Write(std::fstream &file, const std::vector<std::vector<Tensor>> &valueVector, const bool binary, int vectorSize);
template<class Type>
void Write(std::fstream &file, const std::vector<std::vector<Type>> &valueVector, const bool binary, int vectorSize)
{
    if (vectorSize < 0)
    {
        vectorSize = (int)valueVector.size();
        Write(file, vectorSize, binary);
    }

    if (binary)
    {
        int totalSize = 0;
        for (int i = 0; i < vectorSize; ++i) totalSize += valueVector[i].size();
        Write(file, totalSize, binary);
        std::vector<Type> intTemp1(totalSize);
        std::vector<int> intTemp2(vectorSize + 1);

        int index = 0;
        for (int i = 0; i < vectorSize; ++i)
        {
            intTemp2[i] = index;
            for (int j = 0; j < valueVector[i].size(); ++j) intTemp1[index++] = valueVector[i][j];
        }
        intTemp2[vectorSize] = totalSize;

        file.write((char *)&intTemp1[0], sizeof(intTemp1[0]) * totalSize);
        file.write((char *)&intTemp2[0], sizeof(intTemp2[0]) * (vectorSize + 1));
    }
    else
    {
        for (int i = 0; i < vectorSize; ++i) Write(file, valueVector[i], binary);
    }
}

template<>
void Write(std::fstream &file, const std::vector<std::vector<std::string>> &valueVector, const bool binary, int vectorSize)
{
    if (vectorSize < 0)
    {
        vectorSize = valueVector.size();
        Write(file, vectorSize, binary);
    }

    for (int i = 0; i < vectorSize; ++i) Write(file, valueVector[i], binary);
}

template void Read(std::fstream &file, bool &value, const bool binary);
template void Read(std::fstream &file, int &value, const bool binary);
template void Read(std::fstream &file, size_t &value, const bool binary);
template void Read(std::fstream &file, float &value, const bool binary);
template void Read(std::fstream &file, Scalar &value, const bool binary);
template void Read(std::fstream &file, long int &value, const bool binary);
template<class Type>
void Read(std::fstream &file, Type &value, const bool binary)
{
    if(binary) file.read((char *) &value, sizeof(value));
    else       file >> value;
}

template<>
void Read(std::fstream &file, Vector &value, const bool binary)
{
    value.Read(file, binary);
}

template<>
void Read(std::fstream &file, Tensor &value, const bool binary)
{
    value.Read(file, binary);
}

template<>
void Read(std::fstream &file, std::string &value, const bool binary)
{
    if(binary)
    {        
        int length;
        file.read((char *)& length, sizeof(length));
        value.resize(length);
        file.read((char *)& value[0], sizeof(value[0]) * length);
    }
    else
    {
        file >> value;
    }
}

template void Read(std::fstream &file, std::pair<bool, bool> &value, const bool binary);
template void Read(std::fstream &file, std::pair<int, int> &value, const bool binary);
template void Read(std::fstream &file, std::pair<float, float> &value, const bool binary);
template void Read(std::fstream &file, std::pair<Scalar, Scalar> &value, const bool binary);
template void Read(std::fstream &file, std::pair<Tensor, Tensor> &value, const bool binary);
template void Read(std::fstream &file, std::pair<std::string, std::string> &value, const bool binary);
template<class Type>
void Read(std::fstream &file, std::pair<Type, Type> &value, const bool binary)
{
    Read(file, value.first, binary);
    Read(file, value.second, binary);
}

template void Read(std::fstream &file, std::vector<int> &valueVector, const bool binary, int vectorSize);
template void Read(std::fstream &file, std::vector<Scalar> &valueVector, const bool binary, int vectorSize);
template void Read(std::fstream &file, std::vector<Vector> &valueVector, const bool binary, int vectorSize);
template void Read(std::fstream &file, std::vector<Tensor> &valueVector, const bool binary, int vectorSize);
template<class Type>
void Read(std::fstream &file, std::vector<Type> &valueVector, const bool binary, int vectorSize)
{
    // 初始值
    size_t vectorSizeLong;
    
    if (vectorSize < 0)
    {
        // 读取容器大小
        int num;
        Read(file, num, binary);
        if (num < 0) Read(file, vectorSizeLong, binary);
        else         vectorSizeLong = num;
    }
    else
    {
        // 不读取容器大小
        vectorSizeLong = vectorSize;
    }
    
    // 读取容器
    if (vectorSizeLong == 0)
    {
        // 空容器
        valueVector.clear();
    }
    else
    {
        // 非空容器
        valueVector.resize(vectorSizeLong);
        if (binary) file.read((char *)& valueVector[0], sizeof(valueVector[0]) * vectorSizeLong);
        else       for (size_t i = 0; i < vectorSizeLong; ++i) Read(file, valueVector[i], binary);
    }
}

template<>
void Read(std::fstream &file, std::vector<std::string> &valueVector, const bool binary, int vectorSize)
{
    if (vectorSize < 0) Read(file, vectorSize, binary);
    if (vectorSize == 0) valueVector.clear();
    if (vectorSize < 0) return;

    valueVector.resize(vectorSize);
    for (int i = 0; i < vectorSize; ++i) Read(file, valueVector[i], binary);
}

template void Read(std::fstream &file, std::vector<std::vector<int>> &valueVector, const bool binary, int vectorSize);
template void Read(std::fstream &file, std::vector<std::vector<Scalar>> &valueVector, const bool binary, int vectorSize);
template void Read(std::fstream &file, std::vector<std::vector<Vector>> &valueVector, const bool binary, int vectorSize);
template void Read(std::fstream &file, std::vector<std::vector<Tensor>> &valueVector, const bool binary, int vectorSize);
template<class Type>
void Read(std::fstream &file, std::vector<std::vector<Type>> &valueVector, const bool binary, int vectorSize)
{
    if (vectorSize < 0) Read(file, vectorSize, binary);
    if (vectorSize == 0) valueVector.clear();
    if (vectorSize < 0) return;

    valueVector.resize(vectorSize);
    if (binary)
    {
        int totalSize = 0;
        Read(file, totalSize, binary);
        std::vector<Type> intTemp1(totalSize);
        std::vector<int> intTemp2(vectorSize + 1);
        file.read((char *)&intTemp1[0], sizeof(intTemp1[0]) * totalSize);
        file.read((char *)&intTemp2[0], sizeof(intTemp2[0]) * (vectorSize + 1));
        for (int i = 0; i < vectorSize; ++i)
        {
            const int size = intTemp2[i + 1] - intTemp2[i];
            valueVector[i].resize(size);
            for (int j = 0; j < size; ++j) valueVector[i][j] = intTemp1[intTemp2[i] + j];
        }
    }
    else
    {
        for (int i = 0; i < vectorSize; ++i) Read(file, valueVector[i], binary);
    }
}

template<>
void Read(std::fstream &file, std::vector<std::vector<std::string>> &valueVector, const bool binary, int vectorSize)
{
    if (vectorSize < 0) Read(file, vectorSize, binary);
    if (vectorSize == 0) valueVector.clear();
    if (vectorSize < 0) return;

    valueVector.resize(vectorSize);
    for (int i = 0; i < vectorSize; ++i) Read(file, valueVector[i], binary);
}

}
