﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SystemControl.h
//! <AUTHOR>
//! @brief 程序控制及打印信息.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_SystemControl_
#define _basic_common_SystemControl_

#include <string>
#include <time.h>

/**
 * @brief print error information and stop
 * 
 * @param info error information
 */
void FatalError(const std::string &info);

/**
 * @brief print waring information and pause
 * 
 * @param info warning information
 */
void WarningPause(const std::string &info);

/**
 * @brief print waring information but continue
 * 
 * @param info warning information
 */
void WarningContinue(const std::string &info);

void SetInfoFile(const std::string &s);

void SetInfoLevel(const int &fileLevel = 0, const int &screenLevel = 0);

void CloseInfoFile();

void Print(const std::string &info, const int &level = 0);

template<class Type1, class ... Types>
void PrintAll(Type1 value1, Types ... rest)
{
    Print(ToString(value1));
    PrintAll(rest...);
}

void PrintScreen(const std::string &info);

void PrintFile(const std::string &info);

void PrintTitleInfo(const std::string &info);

/**
 * @brief 打印进度
 * 
 * @param 进度描述 
 * @param 当前步 
 * @param 总步数 
 */
void PrintProgressRate(const std::string &processDescription, const int &currentStep, const int &totalStep);

void MakeDirectory(const std::string &path);

void CheckStatus(const int &codeID);

bool CheckMessage(const std::string &message);

// 删除路径下的所有文件
void DeleteDirectory(const std::string& directoryPath);

#include <fstream>
namespace Info
{
    static std::ofstream infoFile;
    static int status;
	static int fileLevel = 1;
	static int screenLevel = 1;
}

#endif
