﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file StringTools.h
//! <AUTHOR>
//! @brief 字符串工具.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_StringTools_
#define _basic_common_StringTools_

#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "basic/common/Vector.h"
#include "basic/common/Tensor.h"

/// This function can be used to extract content between a couple of symbols, 
/// such as '(' and ')' from a given file.
/// function returns:
///     0 for succussful extraction, and
///     1 for failure.
/**
 * @brief This function can be used to extract content between a couple of symbols, 
 * such as '(' and ')' from a given file.
 * 
 * @param infile input file
 * @param outstringstream ouput string
 * @param leftChar start character
 * @param rightChar end character
 * @return int 0 for succussful extraction, and
 *                1 for failure.
 */
int GetBySymbol
(
std::ifstream& infile,
std::stringstream& outstringstream,
char leftChar,
char rightChar
);

/**
 * @brief This function is designed to extract content between a couple of symbols, 
 * such as '(' and ')' from a given stringstream.
 * 
 * @param instringstream 
 * @param outstringstream 
 * @param leftChar 
 * @param rightChar 
 * @return int 0 for succussful extraction, and
 *          1 for failure.
 */
int GetBySymbol
(
std::stringstream& instringstream,
std::stringstream& outstringstream,
char leftChar,
char rightChar
);

/**
 * @brief get line from stringstream 
 * 
 * @param sstream string stream
 * @param outstring output string
 * @param symbol end symbol
 * @return true successfully get line
 * @return false fail to get line 
 */
bool getline(std::stringstream& sstream, std::string& outstring, char symbol);

/**
 * @brief Read line to string stream
 * 
 * @param inFile input file
 * @param text_line get line to the string
 * @param sstream string stream
 */
void ReadLineToSStream(std::ifstream& inFile, std::string& text_line, std::stringstream& sstream);

/**
 * @brief 转换变量为字符串
 * 
 * @tparam Type 变量类型
 * @param value 变量值
 * @return std::string 
 */
template<class Type>
std::string ToString(const Type &value);

/**
 * @brief 转换容器为字符串
 * 
 * @tparam Type 变量类型
 * @param value 变量值
 * @return std::string 
 */
template<class Type>
std::string ToStringVector(const std::vector<Type> &value);

/**
 * @brief 转换数组为字符串
 * 
 * @tparam Type 变量类型
 * @param value 变量值
 * @param size 数组大小
 * @return std::string 
 */
template<class Type>
std::string ToString(const Type *value, const int &size);

/**
 * @brief judge blank string
 * 
 * @param string input string
 * @return bool
 */
bool JudgeBlank(const std::string& string);

/**
 * @brief transform to capital
 * 
 * @param string input string
 * @return string
 */
std::string TransformToCapital(std::string &currentString);

/**
* @brief 转换字符串为变量
*
* @param s 字符串
* @return 各种数据类型
*/
int StringToInt(const std::string &s);

Scalar StringToScalar(const std::string &s);

Vector StringToVector(const std::string &s);

std::string ScalarToString(const Scalar &value, const int &size, const int &precision, const bool &scientific);

bool CompareTwoStrings(const std::string &str1, const std::string &str2);

int CountUTF8Characters(const std::string &stringTmp);

std::string ObtainInfoTitle(const std::string info = "", const char symbol = '-', const int length = 71, const int pos = 0);

bool EndWithSuffix(const std::string& str, const std::string &suffix);

#endif
