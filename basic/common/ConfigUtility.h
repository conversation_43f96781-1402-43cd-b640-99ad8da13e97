﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ConfigUtility.h
//! <AUTHOR>
//! @brief 基本包含文件.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 杨思源（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_ConfigUtility_
#define _basic_common_ConfigUtility_

#if __cplusplus >= 201103L
#ifndef _Supports_CXX11_
#define _Supports_CXX11_
#endif
#endif

#include <algorithm>
#include <cmath>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <list>
#include <memory>
#include <queue>
#include <sstream>
#include <set>
#include <string>
#include <tuple>
#include <vector>

#include <map>
#ifdef _Supports_CXX11_
#include <unordered_map>
#endif

#ifndef _Supports_CXX11_
#define nullptr NULL
#endif

#include "basic/common/Configuration.h"
#include "basic/common/IO.h"
#include "basic/common/LogLevel.h"
#include "basic/common/Matrix.h"
#include "basic/common/MaxMin.h"
#include "basic/common/MPI.h"
#include "basic/common/StringTools.h"
#include "basic/common/SystemControl.h"
#include "basic/common/SystemTime.h"
#include "basic/common/Tensor.h"
#include "basic/common/Vector.h"

#if defined(_BaseParallelOpenMP_)
#include "basic/common/OpenMPMacro.h"
#endif

#endif