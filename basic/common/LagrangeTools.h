﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LagrangeTools.h
//! <AUTHOR> 张帅（数峰科技/西交大）
//! @brief 基本Lagrange工具.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 凌空 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_LagrangeTools_
#define _basic_common_LagrangeTools_

#include "basic/common/ConfigUtility.h"

class Mesh;

/**
 * @brief find the nearest element from a given lagrange point;
 * 
 * @param UGB background grid block
 * @param lagPoint coordinate of the given lagrange point
 * @param startEID ID of element where searching starts from
 * @return int ID of the nearest element
 */
int NearestElement
(
    const Mesh* UGB,
    const Vector &lagPoint,
    int startEID
);

/**
 * @brief find the nearest face from a given lagrange point on a specific patch;
 * 
 * @param UGB background grid block
 * @param lagPoint coordinate of the given lagrange point
 * @param startID ID of the face which the search starts from
 * @return int ID of the nearest element
 */
int SearchNearestBoundaryFace
(
    const Mesh *UGB,
    const Vector &lagPoint,
    int startID
);

#endif
