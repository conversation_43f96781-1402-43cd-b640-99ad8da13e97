﻿#include "basic/common/SystemTime.h"
#include "basic/common/SystemControl.h"

SystemTime::SystemTime()
{
    start = system_clock::now();
    end = system_clock::now();
}

double SystemTime::GetElapsedTime()
{
    end = system_clock::now();
    double duration = double((duration_cast<microseconds>(end - start)).count());
    return duration * microseconds::period::num / microseconds::period::den;
}

void SystemTime::UpdateTime()
{
    start = system_clock::now();
}

void SystemTime::PrintNowTime()
{
    std::time_t tt = system_clock::to_time_t(system_clock::now());
    std::tm* timeInfo = std::gmtime(&tt);

    std::ostringstream stringStream;
    stringStream << "\nsystem time: "
        << timeInfo->tm_year + 1900 << "-"
        << timeInfo->tm_mon + 1 << "-"
        << timeInfo->tm_mday << " "
        << timeInfo->tm_hour << ":"
        << timeInfo->tm_min << ":"
        << timeInfo->tm_sec << std::endl;
    PrintFile(stringStream.str());
}

void PrintProcessTime(const std::string &processDescription, SystemTime &startTime)
{
    startTime.end = system_clock::now();
    
    std::ostringstream stringStream;
    stringStream << processDescription << startTime.GetElapsedTime() << " s";
    Print(stringStream.str());
    
    startTime.start = system_clock::now();
}

void PrintSystemTime()
{
    SystemTime systemTime;
    systemTime.PrintNowTime();
}

void PrintProcessTime(const std::string &processDescription, clock_t &startTime)
{
    clock_t endTime = clock();

    std::ostringstream stringStream;
    stringStream << processDescription << ((double)(endTime - startTime)) / CLOCKS_PER_SEC << " s";
    Print(stringStream.str());

    startTime = endTime;
}