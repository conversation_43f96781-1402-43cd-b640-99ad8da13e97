﻿#include "basic/common/Tensor.h"

Tensor Tensor::Transpose() const
{
    switch (type)
    {
    case Type::diag:
    case Type::sym:
    {
        return *this;
    }
    case Type::antiSym:
    {
        return Tensor(0.0, 0.0, 0.0, -xy, -xz, -yz, Type::antiSym);
    }
    default:
    {
        return Tensor(xx, yx, zx,
                      xy, yy, zy,
                      xz, yz, zz);
    }
    }
}

Scalar Tensor::Det() const
{
    switch (type)
    {
    case Type::diag:
    {
        return xx * yy * zz;
    }
    case Type::sym:
    {
        return (xx * (yy * zz - yz * yz)
              - xy * (xy * zz - xz * yz)
              + xz * (xy * yz - xz * yy));
    }
    case Type::antiSym:
    {
        return 0.0;
    }
    default:
    {
        return (xx * (yy * zz - zy * yz)
              - xy * (yx * zz - zx * yz)
              + xz * (yx * zy - zx * yy));
    }
    }
}

Tensor Tensor::Accompany() const
{
    switch (type)
    {
    case Type::diag:
    {
        return Tensor(yy * zz, xx * zz, xx * yy);        
    }
    case Type::sym:
    {
        return Tensor((yy * zz - yz * yz),
                      (xx * zz - xz * xz),
                      (xx * yy - xy * xy),
                     -(xy * zz - yz * xz),
                      (xy * yz - yy * xz),
                     -(xx * yz - xy * xz), Type::sym);
    }
    case Type::antiSym:
    {
        return Tensor( yz * yz, xz * xz,  xy * xy, 
                      -xz * yz, xy * yz, -xy * xz, Type::sym); //sym
    }
    default:
    {
        // |  xx  xy  xz  |.Accompany() = |    yy*zz-zy*yz  -(xy*zz-zy*xz)   xy*yz-yy*xz  |
        // |  yx  yy  yz  |               |  -(yx*zz-zx*yz)   xx*zz-zx*xz  -(xx*yz-yx*xz) |
        // |  zx  zy  zz  |               |    yx*zy-zx*yy  -(xx*zy-zx*xy)   xx*yy-yx*xy  |

        return Tensor( yy * zz - zy * yz, zy * xz - xy * zz, xy * yz - yy * xz,
                       zx * yz - yx * zz, xx * zz - zx * xz, yx * xz - xx * yz,
                       yx * zy - zx * yy, zx * xy - xx * zy, xx * yy - yx * xy );
    }
    }    
}

Tensor Tensor::Inverse() const
{
    if (type == Type::antiSym) FatalError("Tensor::Inverse: antiSym wrong!");

    return (*this).Accompany() / (*this).Det();
}

Tensor Tensor::Symm(const bool &half) const
{
    switch (type)
    {
    case Type::diag:
    case Type::sym:
    {
        if (half) return *this;
        else      return *this * 2.0;
    }
    case Type::antiSym:
    {
        return Tensor(0.0, 0.0, 0.0); //diag
    }
    default:
    {
        if (half) return Tensor(xx, yy, zz, 0.5*(xy + yx), 0.5*(xz + zx), 0.5*(yz + zy), Type::sym);
        else      return Tensor(2.0 * xx, 2.0 * yy, 2.0 * zz, (xy + yx), (xz + zx), (yz + zy), Type::sym);
    }
    }
}

Tensor Tensor::AntiSymm(const bool &half) const
{
    switch (type)
    {
    case Type::diag:
    case Type::sym:
    {
        return Tensor(0.0, 0.0, 0.0); //diag
    }
    case Type::antiSym:
    {
        if (half) return *this;
        else      return *this * 2.0;
    }
    default:
    {
        if (half) return Tensor(0.0, 0.0, 0.0, 0.5*(xy - yx), 0.5*(xz - zx), 0.5*(yz - zy), Type::antiSym);
        else      return Tensor(0.0, 0.0, 0.0, (xy - yx), (xz - zx), (yz - zy), Type::antiSym);
    }
    }
}

void ReadScalar(std::fstream &file, Scalar &value, const bool binary)
{
    if(binary) file.read((char *) &value, sizeof(value));
    else       file >> value;
}

void WriteScalar(std::fstream &file, const Scalar &value, const bool binary)
{
    if(binary) file.write((char *) &value, sizeof(value));
    else       file << value << std::endl;
}

void Tensor::Read(std::fstream &file, const bool binary)
{
    int nType;
    if(binary) file.read((char *) &nType, sizeof(nType));
    else       file >> nType;
    this->type = (Type)nType;
    
    ReadScalar(file, this->xx, binary);
    ReadScalar(file, this->yy, binary);
    ReadScalar(file, this->zz, binary);

    switch (type)
    {
    case Type::diag:
    {
        break;
    }
    case Type::antiSym:
    case Type::sym:
    {
        ReadScalar(file, this->xy, binary);
        ReadScalar(file, this->xz, binary);
        ReadScalar(file, this->yz, binary);
        break;
    }
    default:
    {
        ReadScalar(file, this->xy, binary);
        ReadScalar(file, this->xz, binary);
        ReadScalar(file, this->yz, binary);
        ReadScalar(file, this->yx, binary);
        ReadScalar(file, this->zx, binary);
        ReadScalar(file, this->zy, binary);
    }
    }
}

void Tensor::Write(std::fstream &file, const bool binary)const
{
    int nType = (int)this->type;
    if(binary) file.write((char *) &nType, sizeof(nType));
    else       file << nType << std::endl;
  
    WriteScalar(file, this->xx, binary);
    WriteScalar(file, this->yy, binary);
    WriteScalar(file, this->zz, binary);

    switch (type)
    {
    case Type::diag:
    {
        break;
    }
    case Type::antiSym:
    case Type::sym:
    {
        WriteScalar(file, this->xy, binary);
        WriteScalar(file, this->xz, binary);
        WriteScalar(file, this->yz, binary);
        break;
    }
    default:
    {
        WriteScalar(file, this->xy, binary);
        WriteScalar(file, this->xz, binary);
        WriteScalar(file, this->yz, binary);
        WriteScalar(file, this->yx, binary);
        WriteScalar(file, this->zx, binary);
        WriteScalar(file, this->zy, binary);
    }
    }
}

std::fstream& operator<<(std::fstream &os, const Tensor &t)
{
    switch (t.type)
    {
    case Tensor::Type::diag:
    {
        os << "\t" << t.xx << "\t" << 0      << "\t" << 0      << "\n"
           << "\t" << 0      << "\t" << t.yy << "\t" << 0      << "\n"
           << "\t" << 0      << "\t" << 0.0    << "\t" << t.zz << "\n";
        return os;
    }
    case Tensor::Type::sym:
    {
        os << "\t" << t.xx << "\t" << t.xy << "\t" << t.xz << "\n"
           << "\t" << t.xy << "\t" << t.yy << "\t" << t.yz << "\n"
           << "\t" << t.xz << "\t" << t.yz << "\t" << t.zz << "\n";
        return os;
    }
    case Tensor::Type::antiSym:
    {
        os << "\t" << 0       << "\t" << t.xy  << "\t" << t.xz << "\n"
           << "\t" << -t.xy << "\t" << 0       << "\t" << t.yz << "\n"
           << "\t" << -t.xz << "\t" << -t.yz << "\t" << 0      << "\n";
        return os;
    }
    default:
    {
        os << "\t" << t.xx << "\t" << t.xy << "\t" << t.xz << "\n"
           << "\t" << t.yx << "\t" << t.yy << "\t" << t.yz << "\n"
           << "\t" << t.zx << "\t" << t.zy << "\t" << t.zz << "\n";
        return os;
    }
    }
}

Scalar Tensor::operator && (const Tensor &t) const
{
    if (type == Type::diag || t.type == Type::diag)
    {
        return this->xx * t.xx + this->yy * t.yy + this->zz * t.zz;
    }
    else if ((type == Type::sym && t.type == Type::antiSym) ||
             (type == Type::antiSym && t.type == Type::sym) )
    {
        return Scalar0;
    }
    else if (type == Type::sym && t.type == Type::sym)
    {
        return this->xx * t.xx + this->yy * t.yy + this->zz * t.zz
               + 2.0 * (this->xy * t.xy + this->xz * t.xz + this->yz * t.yz);
    }
    else if (type == Type::antiSym && t.type == Type::antiSym)
    {
        return 2.0 * (this->xy * t.xy + this->xz * t.xz + this->yz * t.yz);
    }
    else
    {
        return this->xx * t.xx + this->yy * t.yy + this->zz * t.zz
             + this->xy * t.xy + this->xz * t.xz + this->yz * t.yz
             + this->yx * t.yx + this->zx * t.zx + this->zy * t.zy;
    }
}

Vector Tensor::operator*(const Vector &vv) const
{
    switch (type)
    {
    case Type::diag:
    {
        return Vector(vv.x_*xx, vv.y_*yy, vv.z_*zz);
    }
    case Type::sym:
    {
        return Vector
            (vv.x_*xx + vv.y_*xy + vv.z_*xz,
             vv.x_*xy + vv.y_*yy + vv.z_*yz,
             vv.x_*xz + vv.y_*yz + vv.z_*zz);
    }
    case Type::antiSym:
    {
        return Vector
            ( vv.x_*xx + vv.y_*xy + vv.z_*xz,
             -vv.x_*xy + vv.y_*yy + vv.z_*yz,
             -vv.x_*xz - vv.y_*yz + vv.z_*zz);
    }
    default:
    {
        return Vector
            (vv.x_*xx + vv.y_*xy + vv.z_*xz,
             vv.x_*yx + vv.y_*yy + vv.z_*yz,
             vv.x_*zx + vv.y_*zy + vv.z_*zz);
    }
    }
}

Vector operator * (const Vector &v, const Tensor &t)
{
    switch (t.type)
    {
    case Tensor::Type::diag:
    {
        return Vector(v.x_*t.xx, v.y_*t.yy, v.z_*t.zz);
    }
    case Tensor::Type::sym:
    {
        return Vector(v.x_*t.xx + v.y_*t.xy + v.z_*t.xz,
                      v.x_*t.xy + v.y_*t.yy + v.z_*t.yz,
                      v.x_*t.xz + v.y_*t.yz + v.z_*t.zz);
    }
    case Tensor::Type::antiSym:
    {
        return Vector(v.x_*t.xx - v.y_*t.xy - v.z_*t.xz,
                      v.x_*t.xy + v.y_*t.yy - v.z_*t.yz,
                      v.x_*t.xz + v.y_*t.yz + v.z_*t.zz);
    }
    default:
    {
        return Vector(v.x_*t.xx + v.y_*t.yx + v.z_*t.zx,
                      v.x_*t.xy + v.y_*t.yy + v.z_*t.zy,
                      v.x_*t.xz + v.y_*t.yz + v.z_*t.zz);
    }
    }
}

