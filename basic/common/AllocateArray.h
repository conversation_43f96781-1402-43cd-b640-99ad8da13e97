﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file AllocateArray.h
//! <AUTHOR> 凌空（数峰科技/西交大）
//! @brief 容器vector重置大小.
//! @date 2020-07-22
//
//------------------------------修改日志----------------------------------------
// 2020-07-22 张帅 凌空（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _basic_common_AllocateArray_
#define _basic_common_AllocateArray_

#include <vector>

/// 一维容器重置大小
template<class Type>
void AllocateDim(std::vector<Type> &CrtVector, const int &M)
{
    CrtVector.resize(M);
}

/// 二维容器重置大小
template<class Type>
void AllocateDim(std::vector<std::vector<Type> >& CrtVector, const int &M, const int &N)
{
    CrtVector.resize(M);
    for (unsigned int i = 0; i < CrtVector.size(); ++i)
    {
        CrtVector[i].resize(N);
    }
}

/// 三维容器重置大小
template<class Type>
void AllocateDim(std::vector<std::vector<std::vector<Type> > >& CrtVector, const int &M, const int &N, const int &P)
{
    CrtVector.resize(M);
    for (unsigned int i = 0; i < CrtVector.size(); ++i)
    {
        CrtVector[i].resize(N);
        for (unsigned int j = 0; j < CrtVector[i].size(); ++j)
        {
            CrtVector[i][j].resize(P);
        }
    }
}

#endif
