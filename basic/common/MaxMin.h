﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MaxMin.h
//! <AUTHOR>
//! @brief 最大、最小和符号函数
//! @date 2022-04-09
//
//------------------------------修改日志----------------------------------------
// 2022-08-03 乔龙
//    说明：修改。
//
// 2020-07-22 张帅
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _basic_common_MaxMin_
#define _basic_common_MaxMin_

#include "basic/common/Vector.h"

inline size_t Min(const size_t& FirstVal, const size_t& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

inline int Min(const int& FirstVal, const int& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

inline float Min(const float& FirstVal, const float& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

inline Scalar Min(const Scalar& FirstVal, const Scalar& SecondVal)
{
    return FirstVal < SecondVal ? FirstVal : SecondVal;
}

inline Vector Min(const Vector& FirstVal, const Vector& SecondVal)
{
    return Vector( FirstVal.X() < SecondVal.X() ? FirstVal.X() : SecondVal.X(),
                   FirstVal.Y() < SecondVal.Y() ? FirstVal.Y() : SecondVal.Y(),
                   FirstVal.Z() < SecondVal.Z() ? FirstVal.Z() : SecondVal.Z() );
}

inline size_t Max(const size_t& FirstVal, const size_t& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

inline int Max(const int& FirstVal, const int& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

inline float Max(const float& FirstVal, const float& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

inline Scalar Max(const Scalar& FirstVal, const Scalar& SecondVal)
{
    return FirstVal > SecondVal ? FirstVal : SecondVal;
}

inline Vector Max(const Vector& FirstVal, const Vector& SecondVal)
{
    return Vector( FirstVal.X() > SecondVal.X() ? FirstVal.X() : SecondVal.X(),
                   FirstVal.Y() > SecondVal.Y() ? FirstVal.Y() : SecondVal.Y(),
                   FirstVal.Z() > SecondVal.Z() ? FirstVal.Z() : SecondVal.Z() );
}

inline int Sign(const int& value)
{
    if (value > 0) return 1;
    if (value < 0) return -1;
    return 0;
}

inline float Sign(const float& value)
{
    if (value > 0.0) return (float)1.0;
    if (value < 0.0) return (float)(-1.0);
    return (float)0.0;
}

inline Scalar Sign(const Scalar& value)
{
    if (value > 0.0) return 1.0;
    if (value < 0.0) return -1.0;
    return 0.0;
}

#endif