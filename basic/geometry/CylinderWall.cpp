﻿#include "basic/geometry/CylinderWall.h"

namespace Geometry
{

CylinderWall::CylinderWall()
{
    bothSide   = false;
    moving       = false;
    rotating   = false;
}

CylinderWall::CylinderWall(const Scalar &radius1_, const Scalar &radius2_, const Line &axis_,
                           const int &wallNumber_, const int &propType_, const bool &both_)
{
    this->radius1 = radius1_;
    this->radius2 = radius2_;
    this->axis = axis_;
    this->wallNumber = wallNumber_;
    this->propType = propType_;
    this->bothSide = both_;
    this->translationVelocity = Vector0;
    this->rotationVelocity = Scalar0;
    this->rotationLine = axis_;

    this->moving = false;
    this->rotating = false;

    this->CreateCylinder();
}

CylinderWall::CylinderWall(const Scalar &radius1_, const Scalar &radius2_, const Line &axis_,
                           const int &wallNumber_, const int &propType_,
                           const Vector &transVelocity_, const Scalar &rotationVelocity_, const Line &rotationLine_, const bool both_)
{
    this->radius1 = radius1_;
    this->radius2 = radius2_;
    this->axis = axis_;
    this->wallNumber = wallNumber_;
    this->propType = propType_;
    this->bothSide = both_;
    this->translationVelocity = transVelocity_;
    this->rotationVelocity   = rotationVelocity_;
    this->rotationLine  = rotationLine_;

    this->moving = false;
    this->rotating = false;
    
    this->CreateCylinder();
}

void CylinderWall::CreateCylinder()
{
    if( translationVelocity.Mag() > 0.0000001 ) this->moving = true;
    if( fabs(rotationVelocity) > 0.0000001 ) this->rotating = true;
    
    // 将axis旋转到z轴
    this->RotateToZ();
    
    // 轴线长度
    Scalar length = this->zAxis.GetLength();

    // 在新坐标系下创建周向坐标点
    Scalar theta = 0;
    std::vector<Vector> points1(wallNumber+1);
    std::vector<Vector> points2(wallNumber+1);
    for(int n = 0; n < wallNumber+1; ++n)
    {
        points1[n] = Vector( radius1*cos(theta) , radius1*sin(theta) , 0.0 );
        points2[n] = Vector( radius2*cos(theta) , radius2*sin(theta) , length );
        theta = theta + 2 * PI / wallNumber;
    }
    
    // 将周向坐标点映射到原始旋转轴坐标系下
    for(int n = 0; n < wallNumber+1; ++n)
    {
        points1[n] = this->RotateBackFromZ( points1[n] );
        points2[n] = this->RotateBackFromZ( points2[n] );
    }
    
    // 创建柱面的每个平面
    this->walls.resize(wallNumber);
    for(int n = 0; n < wallNumber; ++n)
    {
        if( this->walls[n].CreateWall( std::vector<Vector>{points1[n], points2[n], points2[n+1], points1[n+1]}) )
        {
            this->walls[n].SetWallProperty(0, propType, bothSide);
            this->walls[n].SetMovingInfo(translationVelocity, rotationVelocity, rotationLine);
        }
        else
        {
            FatalError("CylinderWall::CreateCylinder: Cannot create cylindrical wall" );
        }
    }
}

Vector CylinderWall::RotateToZ()
{
    this->TransformationMatrices();
    
    Matrix point(4, 1);

    point.SetValue(0, 0, axis.GetPoint0().X());
    point.SetValue(1, 0, axis.GetPoint0().Y());
    point.SetValue(2, 0, axis.GetPoint0().Z());
    point.SetValue(3, 0, 1.0);
    point = this->TransMatrix * point;
    const Vector lp0(point.GetValue(0, 0), point.GetValue(1, 0), point.GetValue(2, 0));

    point.SetValue(0, 0, axis.GetPoint1().X());
    point.SetValue(1, 0, axis.GetPoint1().Y());
    point.SetValue(2, 0, axis.GetPoint1().Z());
    point.SetValue(3, 0, 1.0);
    point = this->TransMatrix * point;
    const Vector lp1(point.GetValue(0, 0), point.GetValue(1, 0), point.GetValue(2, 0));

    this->zAxis = Line(lp0, lp1);
    
    return Vector0;
}

Vector CylinderWall::RotateBackFromZ(const Vector& p)
{
    Matrix point(4, 1);
    point.SetValue(0, 0, p.X());
    point.SetValue(1, 0, p.Y());
    point.SetValue(2, 0, p.Z());
    point.SetValue(3, 0, 1.0);

    point = this->ITransMatrix * point;
    return Vector(point.GetValue(0, 0), point.GetValue(1, 0), point.GetValue(2, 0));
}

void CylinderWall::TransformationMatrices()
{
    const Vector &p0 = this->axis.GetPoint0();
    const Vector &normal = this->axis.GetVector().GetNormal();
    const Scalar normalXY = Max(sqrt(normal.X() * normal.X() + normal.Y() * normal.Y()), SMALL);

    // 从轴线转换到z轴的转换矩阵
    Matrix TransP1 = Matrix(4, 4);
    TransP1.SetIdentity();
    TransP1.SetValue(0, 3, -p0.X());
    TransP1.SetValue(1, 3, -p0.Y());
    TransP1.SetValue(2, 3, -p0.Z());

    Matrix TransXZ = Matrix(4, 4);
    TransXZ.SetIdentity();
    TransXZ.SetValue(0, 0,  normal.X() / normalXY);
    TransXZ.SetValue(0, 1,  normal.Y() / normalXY);
    TransXZ.SetValue(1, 0, -normal.Y() / normalXY);
    TransXZ.SetValue(1, 1,  normal.X() / normalXY);

    Matrix TransZ = Matrix(4, 4);
    TransZ.SetIdentity();
    TransZ.SetValue(0, 0, normal.Z());
    TransZ.SetValue(0, 2, -normalXY);
    TransZ.SetValue(2, 0, normalXY);
    TransZ.SetValue(2, 2, normal.Z());

    this->TransMatrix = TransZ * (TransXZ, TransP1);

    // 从z轴转换到轴线的转换矩阵
    Matrix ITransP1 = Matrix(4, 4);
    ITransP1.SetIdentity();
    ITransP1.SetValue(0, 3, p0.X());
    ITransP1.SetValue(1, 3, p0.Y());
    ITransP1.SetValue(2, 3, p0.Z());

    Matrix ITransXZ = Matrix(4, 4);
    ITransXZ.SetIdentity();
    ITransXZ.SetValue(0, 0, normal.X() / normalXY);
    ITransXZ.SetValue(0, 1, -normal.Y() / normalXY);
    ITransXZ.SetValue(1, 0, normal.Y() / normalXY);
    ITransXZ.SetValue(1, 1, normal.X() / normalXY);

    Matrix ITransZ = Matrix(4, 4);
    ITransZ.SetIdentity();
    ITransZ.SetValue(0, 0, normal.Z());
    ITransZ.SetValue(0, 2, normalXY);
    ITransZ.SetValue(2, 0, -normalXY);
    ITransZ.SetValue(2, 2, normal.Z());

    this->ITransMatrix = ITransP1 * (ITransXZ * ITransZ);

    // 修正轴线恰好为z轴时，修正转换矩阵
    if (fabs(normal.Z() - 1.0) < SMALL)
    {
        this->TransMatrix = TransP1;
        this->ITransMatrix = ITransP1;
    }
}

} // namespace Geometry