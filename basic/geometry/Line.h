﻿#ifndef _basic_geometry_Line_
#define _basic_geometry_Line_

#include "basic/common/ConfigUtility.h"
#include "basic/common/GeometryTools.h"

/**
 * @brief 几何命名空间
 * 
 */
namespace Geometry
{
/**
 * @brief 线段类
 * 
 */
class Line
{
public:
    /**
     * @brief 默认构造函数
     * 
     */
    Line(){}
    
    /**
     * @brief 根据两点构造线段
     * 
     * @param[in] p0_ 端点0
     * @param[in] p1_ 端点1
     */
    Line(const Vector &p0_, const Vector &p1_) : p0(p0_), p1(p1_)
    {
        this->v = p1_ - p0_; this->dist = this->v.Mag();
    }
    
    /**
     * @brief 获得线段端点0
     * 
     * @return const Vector 坐标点
     */
    const Vector GetPoint0()const {return this->p0;}

    /**
     * @brief 获得线段端点1
     * 
     * @return const Vector 坐标点
     */
    const Vector GetPoint1()const {return this->p1;}

    /**
     * @brief 获得线段内指定点
     * 
     * @param[in] t 位置系数
     * @return const Vector 坐标点
     */
    const Vector GetPoint(const Scalar &t)const {return this->v * t + this->p0;}

    /**
     * @brief 获得投影投影点位置系数
     * 
     * @param[in] p 空间点
     * @return const Scalar 位置系数
     */
    const Scalar GetProject(const Vector &p)const {return (p - this->p0) & this->v / (this->v & this->v);}

    /**
     * @brief 获得线段方向向量
     * 
     * @return const Vector& 方向向量
     */
    inline const Vector &GetVector()const { return this->v; }

    /**
     * @brief 获得线段长度
     * 
     * @return const Scalar& 长度
     */
    inline const Scalar &GetLength()const {return this->dist;}

protected:
    Vector p0; ///< 线段起始端点
    Vector p1; ///< 线段终止端点
    Vector v; ///< 线段矢量
    Scalar dist; ///< 线段长度

#if defined(_BaseParallelMPI_)
public:
	template<class Archive>
	void serialize(Archive& ar, const unsigned int version)
	{
		//当前类成员序列化
		ar& p0;
		ar& p1;
		ar& v;
		ar& dist;
	}
#endif

};

} // namespace Geometry

#endif