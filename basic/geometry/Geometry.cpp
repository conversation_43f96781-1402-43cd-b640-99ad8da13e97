﻿#include "basic/geometry/Geometry.h"

namespace Geometry
{

Geometry::Geometry() : wallID0(100000000)
{
}

void Geometry::AddCylinder(const CylinderWall &cylinder)
{
	this->pWall.reserve(100);

    const int wallNumber = cylinder.GetWallNumber();
    
    for(int n = 0; n < wallNumber; ++n)
        this->AddPlaneWall( cylinder.GetWall(n) );
}

void Geometry::AddPlaneWall(const PlaneWall &wall)
{
    this->pWall.push_back(wall);
    const int wallID = this->pWall.size() - 1;
    this->pWall[wallID].SetWallID(wallID0 + wallID);
}

void Geometry::AddPlaneWall(const std::vector<Vector> &nodeList,
                            const int &propType,
                            const Vector &t_vel, const Scalar &r_vel,
                            const Line &r_line, const bool &both)
{
    PlaneWall wall;

    bool stat = wall.CreateWall(nodeList);
    if (!stat) FatalError( "Geometry::AddPlaneWall: Cannot create a plane wall. ");
    
    wall.SetWallProperty(0 , propType, both);
    wall.SetMovingInfo(t_vel , r_vel, r_line );
    
    this->AddPlaneWall(wall);
}

void Geometry::GetPointToWallInfo(const Vector &pos, const Scalar &diam, const int& wallID, Vector& nv, Scalar& dist, Vector& vel, int& pt)
{
    const int numWall = this->pWall.size();

    int i = 0;
    for (; i < numWall; i++)
    {
        if (this->pWall[i].wallID == wallID)
        {
            this->pWall[i].GetPointToWallInfo(pos, diam, nv, dist, vel, pt);
            break;
        }
    }

    if (i == numWall) FatalError("Geometry::GetPointToWallInfo: Wrong wall ID: " + ToString(wallID));
}

void Geometry::OutputTecplot(std::fstream &file, const Scalar& sol_time, const int& strnd_id)
{
    std::vector<Vector> points;
    std::vector < std::vector<int>> faces;
	
    const int wallNumber = this->GetPlaneWallNumber();

    for (int i = 0; i < wallNumber; ++i)
    {
        const PlaneWall &wall = this->GetWall(i);
        const int nNode = wall.GetPointNumber();

        std::vector<int> localNodeID;
        for (int i = 0; i < nNode; i++)
        {
            points.push_back(wall.GetPoint(i));
            localNodeID.push_back(points.size());
        }

        faces.push_back(localNodeID);
    }
    
    const int nPoints = points.size();
    const int nFaces = faces.size();

	// ZoneType: 
	// 0=ORDERED, 1=FELINESEG,
	// 2=FETRIANGLE, 3=FEQUADRILATERAL,
	// 4=FETETRAHEDRON, 5=FEBRICK,
	// 6=FEPOLYGON, 7=FEPOLYHEDRON
    std::string zoneType;
    if (faces[0].size() == 2) zoneType = "FELINESEG";
    else if (faces[0].size() == 3) zoneType = "FETRIANGLE";
    else if (faces[0].size() == 4) zoneType = "FEQUADRILATERAL";
    else FatalError("Geometry::OutputTecplot: zoneType isnot supported!");

    file << "VARIABLES = X, Y, Z" << std::endl;
    file << "ZONE NODES =" << nPoints << ",ELEMENTS =" << nFaces
		<< ",ZONETYPE = " << zoneType << " , DATAPACKING=POINT"
        << ", SOLUTIONTIME = " << sol_time << ", STRANDID = " << strnd_id << std::endl;

    for (int i = 0; i < nPoints; ++i)
    {
        file << points[i].X() << " " << points[i].Y() << " " << points[i].Z() << std::endl;
    }
    
    for (int i = 0; i < nFaces; ++i)
    {
        for (int j = 0; j < faces[i].size(); ++j)
        {
            file << faces[i][j];
            if (j < faces[i].size() - 1) file << " ";
        }
        if(i < nFaces - 1) file << std::endl;
    }
}

} // namespace Geometry