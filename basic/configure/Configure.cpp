﻿#include "basic/configure/Configure.h"

#if defined(_BasePlatformWinddows_)
#include <direct.h>
#else
#include <unistd.h>
#endif

namespace Configure
{
Configure::Configure()
{
#if defined(_BasePlatformWinddows_)
    workPath = _getcwd(nullptr, 0);
#else
    workPath = getcwd(nullptr, 0);
#endif
    workPath = workPath + "/";
#if defined(_BasePlatformWinddows_)
    while (workPath.rfind("\\") != workPath.npos) workPath.replace(workPath.rfind("\\"), 1, "/");
#endif

    caseName = "default";
    boundaryFilePath = "./";
    globalBoundaryNumber = 0;
    enableOversetMesh = false;
    enableMotion = false;

    fullOutputFlag = false;

    omp_set_num_threads(1);
}

void Configure::ReadCaseXml()
{
	// 读取前处理相关参数
	this->ReadBasicCaseXml(xmlFileName);

	// 检查前处理参数
	this->InspectConfigure();
}

void Configure::ReadCaseXml(const std::string &fileName)
{
	xmlFileName = fileName;

	this->ReadCaseXml();
}

void Configure::ReadBasicCaseXml(const std::string &fileName)
{
    //创建基本参数文件PropertyTree
    PropertyTree ptree(fileName, &mapPosValue);
    
    // 读取工程名称
    this->ReadCaseName(ptree);

	// 读取几何基本信息
	this->ReadGeometry(ptree);

    // 读取网格基本信息
    this->ReadMesh(ptree);

    // 读取边界信息
    this->ReadBoundaryCondition(ptree);

    // 读取前处理相关参数
	this->ReadPreprocess(ptree);
}

void Configure::ReadBoundaryXml(const std::string &fileName)
{
    // 创建边界PropertyTree
    PropertyTree boundaryTree(fileName, &mapPosValue);
    boundaryTree.SetTreeName("boundary");

    globalBoundaryNumber = 0;
    globalBoundary.clear();
    globalBoundaryType.clear();
    globalBoundaryName.clear();
    for (int zoneID = 0; zoneID < meshStruct.meshNumber; zoneID++)
    {
        // 边界信息节点
        std::string nodeName = "boundaryConditions";
        if(zoneID != 0) nodeName += ToString(zoneID);
        PropertyTree boundaryNode(boundaryTree, nodeName);

        // 读取边界数量
        int boundaryNumber = 0;
        ReadNodeValue(boundaryNode, std::string("number"), boundaryNumber);
        
        // 读取边界信息
        for (int i = 0; i < boundaryNumber; ++i)
        {
            globalBoundary.push_back(BoundaryStruct());
            const int globalID = globalBoundary.size() - 1;
            globalBoundary[globalID].globalID = globalID;
            globalBoundary[globalID].zoneID = zoneID;

            PropertyTree boundaryNodeTemp(boundaryNode, "boundary" + ToString(i));
            
            ReadNodeValue(boundaryNodeTemp, std::string("name"), globalBoundary[globalID].name, false);
			ReadNodeValue(boundaryNodeTemp, std::string("type"), globalBoundary[globalID].type, boundaryTypeMap, boundaryTypeReverseMap);

            std::vector<std::string> boundaryProperty = (boundaryPropertyMap.find(globalBoundary[globalID].type))->second;
            globalBoundary[globalID].value.resize(boundaryProperty.size());
            for (int n = 0; n < boundaryProperty.size(); ++n)
                ReadNodeValue(boundaryNodeTemp, std::string(boundaryProperty[n]), globalBoundary[globalID].value[n], false);

            globalBoundaryType.push_back(globalBoundary[globalID].type);
            globalBoundaryName.push_back(globalBoundary[globalID].name);
        }
    }
    globalBoundaryNumber = globalBoundary.size();

	nacelleInletGlobalIdVector.clear();
	nacelleOutletGlobalIdVector.clear();
    massFlowInletGlobalIdVector.clear();
    massFlowOutletGlobalIdVector.clear();
    for (int patchID = 0; patchID < globalBoundaryNumber; patchID++)
    {
        if (globalBoundary[patchID].type == Boundary::Type::OVERSET) enableOversetMesh = true;
		if (globalBoundary[patchID].type == Boundary::Type::NACELLE_INLET) nacelleInletGlobalIdVector.push_back(patchID);
		if (globalBoundary[patchID].type == Boundary::Type::NACELLE_EXHAUST) nacelleOutletGlobalIdVector.push_back(patchID);
        if (globalBoundary[patchID].type == Boundary::Type::MASSFLOW_INLET) massFlowInletGlobalIdVector.push_back(patchID);
        if (globalBoundary[patchID].type == Boundary::Type::MASSFLOW_OUTLET) massFlowOutletGlobalIdVector.push_back(patchID);
    }
}

void Configure::UpdateLocalBoundary(SubMesh *localMesh)
{
    localBoundary.resize(localMesh->GetTotalLevel());
    for (int level = 0; level < localBoundary.size(); ++level)
    {
        Mesh* currentMesh = localMesh->GetMultiGrid(level);
        localBoundary[level].resize(currentMesh->GetBoundarySize());
        for (int localID = 0; localID < localBoundary[level].size(); ++localID)
        {
            const int &globalID = localMesh->GetBoundaryGlobalID(level, localID);
            localBoundary[level][localID] = globalBoundary[globalID];
        }
    }
}

void Configure::SetOpenMPThread(const int threadNum)
{
    omp_set_num_threads(Max(preprocess.threadNumber, threadNum));
}

void Configure::ReadCaseName(PropertyTree &ptree)
{
    ReadNodeValue(ptree, std::string("caseName"), caseName, false);
    if(caseName.rfind("/") != caseName.npos || caseName.rfind("\\") != caseName.npos)
        FatalError("Configure::ReadCaseName: case name is wrong!");
}

void Configure::ReadGeometry(PropertyTree &ptree)
{
	PropertyTree geometryNode;
	if (!ptree.GetChildTree("geometry", geometryNode)) return;

    std::string stringTemp;
    ReadNodeValue(geometryNode, std::string("numShape"), geometryStruct.numShape);
    if (geometryStruct.numShape == 0) return;
    
    ReadNodeValue(geometryNode, std::string("dimension"), geometryStruct.dimension);

    for (int i = 0; i < geometryStruct.numShape; i++)
    {
        PropertyTree shapeNode(geometryNode, "shape" + ToString(i));
        ReadNodeValue(shapeNode, std::string("type"), stringTemp);
        if (stringTemp == "CYLINDER")
        {
            CylinderStruct cylinder;
            PropertyTree cylinderNode(shapeNode, "cylinder");
            ReadNodeValue(cylinderNode, std::string("radius0"), cylinder.radius0);
            ReadNodeValue(cylinderNode, std::string("radius1"), cylinder.radius1);
            ReadNodeValue(cylinderNode, std::string("axisP0"), cylinder.axisP0);
            ReadNodeValue(cylinderNode, std::string("axisP1"), cylinder.axisP1);
            ReadNodeValue(cylinderNode, std::string("wallNumber"), cylinder.wallNumber);
            geometryStruct.cylinders.push_back(cylinder);
        }
        else if (stringTemp == "PLANE")
        {
            PlaneStruct plane;
            PropertyTree planeNode(shapeNode, "plane");
            ReadNodeValue(planeNode, std::string("nodeSize"), plane.nodeSize);
            plane.nodes.resize(plane.nodeSize);
            for (int j = 0; j < plane.nodeSize; j++)
            {
                ReadNodeValue(planeNode, std::string("p") + ToString(j), plane.nodes[j]);
            }
            geometryStruct.planes.push_back(plane);
        }
    }
}

void Configure::ReadMesh(PropertyTree &ptree)
{
	PropertyTree meshNode;
	if (!ptree.GetChildTree("mesh", meshNode)) return;

    std::string stringTemp;

    meshStruct.meshPath.clear();
    meshStruct.fileName.clear();
    meshStruct.meshType.clear();

    ReadNodeValue(meshNode, std::string("meshNumber"), meshStruct.meshNumber);
    for (int i = 0; i < meshStruct.meshNumber; i++)
    {
        // 读取网格文件全路径，转换"\"为"/""，并提取文件夹、文件名和文件类型
        stringTemp.clear();
        if (i == 0) stringTemp = ReadNodeValue(meshNode, std::string("meshFile"), stringTemp, false);
        else        stringTemp = ReadNodeValue(meshNode, std::string("meshFile" + ToString(i)), stringTemp, false);
        while(stringTemp.rfind("\\") != stringTemp.npos) stringTemp.replace(stringTemp.rfind("\\"), 1, "/");
        meshStruct.meshPath.push_back(this->ObtainMeshPath(stringTemp));
        meshStruct.fileName.push_back(this->ObtainFileName(stringTemp));
        meshStruct.meshType.push_back(this->ObtainMeshType(stringTemp));
    }

    // 读取网格类型
    stringTemp = "2D";
    ReadNodeValue(meshNode, std::string("dimension"), stringTemp);
    if(stringTemp=="2D")      meshStruct.dimension = Mesh::MeshDim::md2D;
    else if(stringTemp=="3D") meshStruct.dimension = Mesh::MeshDim::md3D;
    else                      FatalError("Configure::ReadMeshParaments: mesh dimension is wrong!");

    // 网格操作节点，读取网格缩放、平移、旋转参数
    PropertyTree transformNode;
    if (meshNode.GetChildTree("meshTransform", transformNode))
    {
        auto &meshTransform = meshStruct.meshTransform;
        ReadNodeValue(transformNode, std::string("scale"), meshTransform.scale);
        ReadNodeValue(transformNode, std::string("transfer"), meshTransform.transfer);
        ReadNodeValue(transformNode, std::string("rotate"), meshTransform.rotate);
        if (meshStruct.dimension == Mesh::MeshDim::md2D)
        {
            meshTransform.scale.SetZ(1.0);
            meshTransform.transfer.SetZ(0.0);
            meshTransform.rotate.SetX(0.0);
            meshTransform.rotate.SetY(0.0);
        }
        meshTransform.scaleFlag = (meshTransform.scale - Vector(1.0, 1.0, 1.0)).Mag() > SMALL;
        meshTransform.transferFlag = (meshTransform.transfer - Vector0).Mag() > SMALL;
        meshTransform.rotateFlag = (meshTransform.rotate * PI / 180.0 - Vector0).Mag() > SMALL;
    }

    // 网格参考量节点，读取网格基本参考量（参考弦长、展长、面积、力矩中心）
    PropertyTree referenceNode;
    if (meshNode.GetChildTree("reference", referenceNode))
    {
        auto &reference = meshStruct.reference;
        ReadNodeValue(referenceNode, std::string("cRef"), reference.cRef);
        ReadNodeValue(referenceNode, std::string("bRef"), reference.bRef);
        ReadNodeValue(referenceNode, std::string("SRef"), reference.SRef);
        ReadNodeValue(referenceNode, std::string("cmRef"), reference.cmRef);
    }
}

void Configure::ReadBoundaryCondition(PropertyTree &ptree)
{
    PropertyTree boundaryNode;
    if (!ptree.GetChildTree("boundary", boundaryNode)) return;
    ReadNodeValue(boundaryNode, std::string("filePath"), boundaryFilePath);
}

void Configure::ReadPreprocess(PropertyTree &ptree)
{
    // 网格前处理节点
    PropertyTree preProcessNode;
    if (!ptree.GetChildTree("preprocess", preProcessNode)) return;

    // 前处理线程数
    ReadNodeValue(preProcessNode, std::string("threadNumber"), preprocess.threadNumber);
	preprocess.threadNumber = Max(preprocess.threadNumber, 1);
    
    // 网格分区数量
	ReadNodeValue(preProcessNode, std::string("partitionNumber"), preprocess.partitionNumber);
	preprocess.partitionNumber = Max(preprocess.partitionNumber, 1);

    // 分区方法
    if (preprocess.partitionNumber > 1)
    {
	    ReadNodeValue(preProcessNode, std::string("partitionMethod"), preprocess.partitionMethod, decomposeTypeMap, decomposeTypeReverseMap);
        if (preprocess.partitionMethod == Preprocessor::DecomposeType::NONE_DECOMPOSE)
        {
            preprocess.partitionMethod = Preprocessor::DecomposeType::METIS;
            mapPosValue.push_back(std::make_pair("preprocess.partitionMethod", Parameter(Parameter::STRING, "METIS")));
        }
    }

    // 对偶网格转换标识
    ReadNodeValue(preProcessNode, std::string("dualMeshFlag"), preprocess.dualMeshFlag);

    // 多重网格聚合，总层数、聚合率、边界层聚合层数、边界层聚合率
    PropertyTree multigridNode;
    if (preProcessNode.GetChildTree("multigrid", multigridNode))
    {
        auto &multigrid = preprocess.multigrid;

        // 总层数
        ReadNodeValue(multigridNode, std::string("totalLevel"), multigrid.totalLevel);
        multigrid.totalLevel = Min(Max(multigrid.totalLevel, 1), 5);

        if (multigrid.totalLevel == 1)
        {
            multigrid.type = Preprocessor::AgglomerateType::NONE_AGGLOMERATE;
        }
        else
        {
            // 聚合方法
		    ReadNodeValue(multigridNode, std::string("agglomerateType"), multigrid.type, agglomerateTypeMap, agglomerateTypeReverseMap);
            if (multigrid.type == Preprocessor::AgglomerateType::NONE_AGGLOMERATE)
            {
                multigrid.type = Preprocessor::AgglomerateType::SEED;
                mapPosValue.push_back(std::make_pair("preprocess.multigrid.agglomerateType", Parameter(Parameter::STRING, "SEED")));
            }
    
            // 聚合率
            multigrid.minCoarseRation = (int)(pow(2, (int)meshStruct.dimension) + 0.001);
            multigrid.maxCoarseRation = multigrid.minCoarseRation;
            ReadNodeValue(multigridNode, std::string("minCoarseRation"), multigrid.minCoarseRation);
            ReadNodeValue(multigridNode, std::string("maxCoarseRation"), multigrid.maxCoarseRation);

            // 边界层层数及聚合率
            ReadNodeValue(multigridNode, std::string("boundaryLayerNumber"), multigrid.boundaryLayerNumber);
            ReadNodeValue(multigridNode, std::string("boundaryLayerCoarseRationNormal"), multigrid.boundaryLayerCoarseRationNormal);
            ReadNodeValue(multigridNode, std::string("boundaryLayerCoarseRationTangent"), multigrid.boundaryLayerCoarseRationTangent);

            // 去除孤立单元标志
            ReadNodeValue(multigridNode, std::string("singletonsRemovementFlag"), multigrid.singletonsRemovementFlag);
        }
    }
    
    // 壁面距离计算方法
	ReadNodeValue(preProcessNode, std::string("wallDistanceMethod"), preprocess.wallDistanceMethod, wallDistanceMap, wallDistanceReverseMap);

    // 网格单元重排方法
	ReadNodeValue(preProcessNode, std::string("renumberMethod"), preprocess.renumberMethod, renumberTypeMap, renumberTypeReverseMap);

    // 前处理结果文件输出路径
    ReadNodeValue(preProcessNode, std::string("outputPath"), preprocess.outputPath, false);
    if (preprocess.outputPath.find_last_of("/") != preprocess.outputPath.length() - 1) preprocess.outputPath = preprocess.outputPath + "/";

    // 前处理结果文件二进制标识
    ReadNodeValue(preProcessNode, std::string("binaryFileFlag"), preprocess.binaryFileFlag);
}

std::string Configure::ObtainMeshPath(std::string stringTemp)
{
    // 将相对路径转为绝对路径
    if (stringTemp.rfind("/") != stringTemp.npos)
        stringTemp = stringTemp.substr(0, stringTemp.rfind("/") + 1);
    else
        stringTemp = "./";

    // // 将相对路径转为绝对路径
    // return ObtainAbsolutePath(stringTemp);
    return stringTemp;
}

std::string Configure::ObtainAbsolutePath(std::string stringTemp)
{
    // 判断是否为相对路径
    bool relativePathFlag = true;
#if defined(_BasePlatformWinddows_)
    if (stringTemp.find(":") != stringTemp.npos) relativePathFlag = false;
#else
    if (stringTemp.find("/") == 0) relativePathFlag = false;
#endif

    // 提取路径，并将相对路径转为绝对路径
    std::string absolutePath = stringTemp;
    if (relativePathFlag)
    {
        if (stringTemp.find("./") == 0) absolutePath = absolutePath.substr(2, -1);
        absolutePath = workPath + absolutePath;
    }

    while (absolutePath.find("../") != absolutePath.npos)
    {
        std::string::size_type pos = absolutePath.find("../");
        std::string tempPath = absolutePath.substr(0, pos - 1);

        std::string::size_type pos1 = tempPath.rfind("/");
        if (pos1 != tempPath.npos) tempPath = absolutePath.substr(0, pos1);
        else                       FatalError("Configure::ObtainAbsolutePath: path is wrong!");

        absolutePath = tempPath + absolutePath.substr(pos + 2, -1);
    }

    if (absolutePath.rfind("/") != absolutePath.length() - 1) absolutePath += "/";

    return absolutePath;
}

std::string Configure::ObtainMeshName(std::string stringTemp)
{
    std::string::size_type pos = stringTemp.rfind("/");
    if (stringTemp.rfind("\\") != stringTemp.npos) pos = std::max(pos, stringTemp.rfind("\\"));
    if (pos != stringTemp.npos) stringTemp = stringTemp.substr(pos + 1, -1);
    
    pos = stringTemp.rfind(".");
    if(pos == stringTemp.npos) FatalError("Configure::ObtainMeshName: Wrong mesh name!");
    else                       stringTemp = stringTemp.substr(0, pos);

    return stringTemp;
}

std::string Configure::ObtainFileName(std::string stringTemp)
{
    std::string::size_type pos = stringTemp.rfind("/");
    if (stringTemp.rfind("\\") != stringTemp.npos) pos = std::max(pos, stringTemp.rfind("\\"));
    if (pos != stringTemp.npos) stringTemp = stringTemp.substr(pos + 1, -1);

    pos = stringTemp.rfind(".");
    if (pos == stringTemp.npos) FatalError("Configure::ObtainMeshName: Wrong mesh name!");

    return stringTemp;
}

Preprocessor::MeshType Configure::ObtainMeshType(std::string stringTemp)
{
    // 提取文件后缀名
    std::string typeString;
    std::string::size_type pos = stringTemp.rfind(".");
    if (pos != stringTemp.npos) typeString = stringTemp.substr(pos + 1, -1);

    // 转换为大写
    TransformToCapital(typeString);
    
    // 根据文件后缀名给网格类型赋值
    const auto &position = meshTypeMap.find(typeString);
    if (position == meshTypeMap.end()) FatalError("Configure::ObtainMeshType: Wrong mesh type!");

    return (*position).second;
}

void Configure::InspectConfigure()
{
	auto &multigrid = preprocess.multigrid;
    if (multigrid.minCoarseRation > multigrid.maxCoarseRation)
    {
        multigrid.minCoarseRation = multigrid.maxCoarseRation;
        mapPosValue.push_back(std::make_pair("preprocess.multigrid.minCoarseRation", Parameter(Parameter::INT, ToString(multigrid.minCoarseRation))));
    }
	
	if (multigrid.totalLevel > 1)
	{
        if (multigrid.maxCoarseRation <= 1)
        {
            multigrid.maxCoarseRation = (meshStruct.dimension == Mesh::MeshDim::md2D) ? 4 : 8;
            mapPosValue.push_back(std::make_pair("preprocess.multigrid.maxCoarseRation", Parameter(Parameter::INT, ToString(multigrid.maxCoarseRation))));
        }
		
        if (multigrid.minCoarseRation < 1)
        {
            multigrid.minCoarseRation = (meshStruct.dimension == Mesh::MeshDim::md2D) ? 4 : 8;
            mapPosValue.push_back(std::make_pair("preprocess.multigrid.minCoarseRation", Parameter(Parameter::INT, ToString(multigrid.minCoarseRation))));
        }

        if (multigrid.boundaryLayerCoarseRationNormal < 1)
        {
            multigrid.boundaryLayerCoarseRationNormal = 2;
            mapPosValue.push_back(std::make_pair("preprocess.multigrid.boundaryLayerCoarseRationNormal", Parameter(Parameter::INT, ToString(multigrid.boundaryLayerCoarseRationNormal))));
        }

        if (multigrid.boundaryLayerCoarseRationTangent < 1)
        {
            multigrid.boundaryLayerCoarseRationTangent = 2;
            mapPosValue.push_back(std::make_pair("preprocess.multigrid.boundaryLayerCoarseRationTangent", Parameter(Parameter::INT, ToString(multigrid.boundaryLayerCoarseRationTangent))));
        }
	}
}

void Configure::PrintInformation()
{
    Print("\nCurrent parameters:");

    boost::property_tree::ptree pt;
    for (int i = 0; i < mapPosValue.size(); i++)
    {
        const auto &parameter = mapPosValue[i];
        const std::string pos = parameter.first;
        const std::string value = parameter.second.GetValue();
        const Parameter::Type valueType = parameter.second.GetType();

        if (parameter.second.GetValue().empty() || parameter.second.GetValue() == " " || valueType == Parameter::Type::INVALID) continue;

        for (int j = i + 1; j < mapPosValue.size(); j++)
        {
            if (mapPosValue[j].first == pos)
            {
                mapPosValue[i].second = mapPosValue[j].second;
                mapPosValue[j].second.SetType(Parameter::Type::INVALID);
            }
        }

        pt.put("root." + parameter.first, parameter.second.GetValue());
    }

    std::stringstream outputInfo;
    boost::property_tree::xml_writer_settings<std::string> settings;
    settings = boost::property_tree::xml_writer_make_settings<std::string>('\t', 1);
    boost::property_tree::write_xml(outputInfo, pt, settings);
    std::string stringTemp = outputInfo.str();
    size_t pos0 = stringTemp.find('\n');
    size_t pos1 = stringTemp.rfind('\n');
    if (pos1 != std::string::npos) stringTemp = stringTemp.substr(pos0, pos1 - pos0);
    Print(stringTemp);
}

}
