﻿#include "basic/configure/PropertyTree.h"

PropertyTree::PropertyTree()
{
}

PropertyTree::PropertyTree(const std::string &fileName, std::vector<std::pair<std::string, Parameter>> *mapPosValue_)
{
    try
	{
		PTree::read_xml(fileName, ptree);
        mapPosValue = mapPosValue_;
	}
	catch (std::exception e)
	{
		FatalError("PropertyTree::PropertyTree: " + fileName + " file cannot be found!");
	}
}

PropertyTree::PropertyTree(PropertyTree &propertyTree_, std::string nodeName)
{
    try
	{
        this->ptree = propertyTree_.ptree.get_child(nodeName);
        this->treeName = nodeName;
        if (!propertyTree_.treeName.empty()) this->treeName = propertyTree_.treeName + "." + nodeName;
        this->mapPosValue = propertyTree_.mapPosValue;
    }
	catch (std::exception e)
	{
        FatalError("PropertyTree::PropertyTree: " + nodeName + " node is not given!");
	}
}

bool PropertyTree::GetChildTree(std::string childName, PropertyTree &childTree)
{
    childTree.treeName = childName;
    if (!this->treeName.empty()) childTree.treeName = this->treeName + "." + childName;

    childTree.mapPosValue = this->mapPosValue;

    try
    {
        childTree.ptree = this->ptree.get_child(childName);
    }
    catch (std::exception e)
    {
        childTree.ptree = this->ptree.add(childName, "");
    }

    return true;
}

PropertyTree::~PropertyTree()
{
}

bool PropertyTree::ReadBool(std::string paraName, bool defaultValue)
{
    bool value;
    try
	{
        value = ptree.get<bool>(paraName);
    }
	catch (std::exception e)
	{
        value = defaultValue;
	}

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::BOOL, ToString(value));
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));
    return value;
}

int PropertyTree::ReadInt(std::string paraName, int defaultValue)
{
    int value;
    try
	{
        value = ptree.get<int>(paraName);
    }
	catch (std::exception e)
	{
        if(defaultValue==INT_MAX)
            FatalError("PropertyTree::ReadParament: " + paraName + " is not given!");
        
        value = defaultValue;
	}

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::INT, ToString(value));
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));
    return value;
}

Scalar PropertyTree::ReadScalar(std::string paraName, Scalar defaultValue)
{
    Scalar value;
    try
	{
        value = ptree.get<Scalar>(paraName);
    }
	catch (std::exception e)
	{
        if(defaultValue==INF)
            FatalError("PropertyTree::ReadParament: " + paraName + " is not given!");
        
        value = defaultValue;
	}

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::DOUBLE, ToString(value));
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));
    return value;
}

Vector PropertyTree::ReadVector(std::string paraName, Vector defaultValue)
{
    std::vector<Scalar> vectorTemp = this->ReadScalarList(paraName, std::vector<Scalar>{defaultValue.X(), defaultValue.Y(), defaultValue.Z()});
    Vector value(vectorTemp[0], vectorTemp[1], vectorTemp[2]);
    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::VECTOR, ToString(value));
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));
    return value;
}

std::string PropertyTree::ReadString(std::string paraName, const bool capitalFlag, std::string defaultValue)
{
    std::string value;
    try
	{
        value = ptree.get<std::string>(paraName);
    }
	catch (std::exception e)
	{
        if(defaultValue=="")
            FatalError("PropertyTree::ReadParament: " + paraName + " is not given!");
        
        value = defaultValue;
	}
	
    if(capitalFlag) value = TransformToCapital(value);

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::STRING, ToString(value));
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));
    return value;
}

std::vector<int> PropertyTree::ReadIntList(std::string paraName, std::vector<int> defaultValue)
{
    std::vector<int> value;
    try
    {
        int temp;
        std::istringstream stringStream(ptree.get<std::string>(paraName));
        while (stringStream >> temp) value.push_back(temp);
    }
    catch (std::exception e)
    {
        value = defaultValue;
    }

    std::string str = " ";
    for (int i = 0; i < value.size(); i++) str += ToString(value[i]) + " ";

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::INT_LIST, str);
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));

    return value;
}

std::vector<Scalar> PropertyTree::ReadScalarList(std::string paraName, std::vector<Scalar> defaultValue)
{
    std::vector<Scalar> value;
    try
    {
        Scalar temp;
        std::istringstream stringStream(ptree.get<std::string>(paraName));
        while (stringStream >> temp) value.push_back(temp);
    }
    catch (std::exception e)
    {
        value = defaultValue;
    }

    std::string str = " ";
    for (int i = 0; i < value.size(); i++) str += ToString(value[i]) + " ";

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::DOUBLE_LIST, str);
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));

    return value;
}

std::vector<std::string> PropertyTree::ReadStringList(std::string paraName, const bool capitalFlag, std::vector<std::string> defaultValue)
{
    std::vector<std::string> value;
    try
    {
        std::string temp;
        std::istringstream stringStream(ptree.get<std::string>(paraName));
        while (stringStream >> temp) value.push_back(temp);
    }
    catch (std::exception e)
    {
        value = defaultValue;
    }

	for (int i = value.size(); i < defaultValue.size(); i++) value.push_back(defaultValue[i]);

    std::string str = " ";
    for (int i = 0; i < value.size(); i++) str += ToString(value[i]) + " ";

    const std::string pos = treeName.empty() ? paraName : this->treeName + "." + paraName;
    const Parameter parameter(Parameter::Type::DOUBLE_LIST, str);
	if (mapPosValue) this->mapPosValue->push_back(std::make_pair(pos, parameter));

    if (capitalFlag) for (int i = 0; i < value.size(); ++i) TransformToCapital(value[i]);
    return value;
}

template <>
bool ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, bool &value0, const bool capitalFlag)
{
    value0 = tree_.ReadBool(nodeName, value0);
    return value0;
}

template <>
int ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, int &value0, const bool capitalFlag)
{
    value0 = tree_.ReadInt(nodeName, value0);
    return value0;
}

template <>
Scalar ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, Scalar &value0, const bool capitalFlag)
{
    value0 = tree_.ReadScalar(nodeName, value0);
    return value0;
}

template <>
Vector ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, Vector &value0, const bool capitalFlag)
{
    value0 = tree_.ReadVector(nodeName, value0);
    return value0;
}

template <>
std::string ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, std::string &value0, const bool capitalFlag)
{
    value0 = tree_.ReadString(nodeName, capitalFlag, value0);
    return value0;
}

template <>
std::vector<int> ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, std::vector<int> &value0, const bool capitalFlag)
{
    value0 = tree_.ReadIntList(nodeName, value0);
    return value0;
}

template <>
std::vector<Scalar> ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, std::vector<Scalar> &value0, const bool capitalFlag)
{
    value0 = tree_.ReadScalarList(nodeName, value0);
    return value0;
}

template <>
std::vector<std::string> ReadNodeValue(PropertyTree &tree_, const std::string &nodeName, std::vector<std::string> &value0, const bool capitalFlag)
{
    value0 = tree_.ReadStringList(nodeName, capitalFlag, value0);
    return value0;
}
