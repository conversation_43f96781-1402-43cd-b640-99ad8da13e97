﻿
#ifndef _basic_configure_Parameter_
#define _basic_configure_Parameter_

#include <string>

class Parameter
{
public:
    enum Type
    {
        INVALID = 0,
        INT,
        BOOL,
        DOUBLE,
        STRING,
        VECTOR,
        INT_LIST,
        BOOL_LIST,
        DOUBLE_LIST,
        STRING_LIST
    };

public:
    Parameter(Type type_ = Type::INT, std::string value_ = "") {type = type_; value = value_;}

    void SetType(Type type_) { type = type_;}
    void SetValue(std::string value_) {value = value_;}

    const Type GetType() const { return type;}
    const std::string GetValue() const {return value;}

private:

    Type type;
    std::string value;
};

#endif
