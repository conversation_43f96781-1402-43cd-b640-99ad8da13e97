<svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo for FeiLian CFD Solver -->
  <!-- Concept: A stylized wing representing <PERSON><PERSON><PERSON><PERSON> (God of Wind), composed of CFD streamlines. -->

  <defs>
    <linearGradient id="wingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0052D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#65C7F7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="vortexGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF8C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
  </defs>

  <g id="logo-mark" transform="translate(20, 30) scale(1.2)">
    <!-- Main wing/streamline shapes -->
    <path d="M 20,50 C 40,20 80,10 120,40 C 160,70 180,110 150,130 C 170,110 180,70 210,50" fill="none" stroke="url(#wingGradient)" stroke-width="8" stroke-linecap="round"/>
    <path d="M 25,65 C 45,35 85,25 125,55 C 165,85 175,120 145,140 C 165,120 175,80 205,60" fill="none" stroke="url(#wingGradient)" stroke-width="4" stroke-linecap="round" stroke-opacity="0.8"/>
    <path d="M 30,80 C 50,50 90,40 130,70 C 160,95 165,125 135,145 C 155,125 165,90 195,70" fill="none" stroke="url(#wingGradient)" stroke-width="2" stroke-linecap="round" stroke-opacity="0.6"/>

    <!-- Vortex accent -->
    <g transform="translate(115, 45)">
      <path d="M 0,0 C 5,-10 15,-10 20,0 C 15,10 5,10 0,0 Z" fill="url(#vortexGradient)" transform="rotate(45)"/>
       <circle cx="10" cy="0" r="2" fill="white" opacity="0.8"/>
    </g>
  </g>

  <g id="typography" transform="translate(230, 90)">
    <!-- Software Name -->
    <text x="0" y="0" font-family="Arial, Helvetica, sans-serif" font-size="42" font-weight="bold" fill="#333">
      FeiLian
    </text>
    <!-- Tagline -->
    <text x="5" y="30" font-family="Arial, Helvetica, sans-serif" font-size="18" fill="#777">
      CFD Solver
    </text>
  </g>

</svg>
