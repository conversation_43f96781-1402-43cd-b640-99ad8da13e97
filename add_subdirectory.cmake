# 获取当前文件夹下所有文件夹的名称
message(STATUS "current source dir is ${CURRENT_SOURCE_DIR}")
file(GLOB FILES ${CURRENT_SOURCE_DIR}/*)
set(SUBDIRS "")
foreach(FILE ${FILES})
    file(RELATIVE_PATH FILE_NAME ${CURRENT_SOURCE_DIR} ${FILE})
    if(IS_DIRECTORY ${CURRENT_SOURCE_DIR}/${FILE_NAME})
		set(FILE_LOCAL ${CURRENT_SOURCE_DIR}/${FILE_NAME})
		file(GLOB_RECURSE SOURCE_FILES ${FILE_LOCAL}/*.cpp)
		file(GLOB_RECURSE HEADER_FILES ${FILE_LOCAL}/*.h*)
		if((NOT ("${SOURCE_FILES}" STREQUAL "")) AND (NOT ("${HEADER_FILES}" STREQUAL "")))
			list(APPEND SUBDIRS ${FILE_NAME})
		endif()
    endif()
endforeach()

# 将各文件夹添加为子目录
foreach(SUBDIR ${SUBDIRS})
    add_subdirectory(${SUBDIR})
endforeach(SUBDIR ${SUBDIRS})