#include "meshProcess/wallDistance/KDTbase.h"

template <typename PointType, typename DataType>
DataStruct_KdtNode<PointType, DataType>::DataStruct_KdtNode()
{
}

template <typename PointType, typename DataType>
DataStruct_KdtNode<PointType, DataType>::DataStruct_KdtNode(const int &ndim_, int begin_, int end_, int level_)
    : ndim(ndim_), begin(begin_), end(end_), level(level_)
{
    splitAxis = level % ndim;  // 按坐标轴顺序，循环交替作为分割轴
}

template <typename PointType, typename DataType>
DataStruct_KdtNode<PointType, DataType>::~DataStruct_KdtNode()
{
    if (this->left)
        delete left;
    if (this->right)
        delete right;
}

template <typename PointType, typename DataType>
void DataStruct_KdtNode<PointType, DataType>::AddNode(std::vector<int> &pointIDlist,
											const std::vector<PointType> &pointCloud,
											const std::vector<DataType> &dataCloud)
{
    // 仅剩一个点
    if (end - begin == 0)
    {
        pointID = pointIDlist[begin];
        point = pointCloud[pointID];
        if(dataCloud.size() > 0) data = dataCloud[pointID];
        left = right = nullptr;
        spaceMin = point;
        spaceMax = point;
        spaceMiddle = point;
        // splitAxis = -1;
    }
    // 仅剩两个点，第一个直接作为本节点的点，第二个判断位于其左侧还是右侧
    else if (end - begin == 1)
    {
        // splitAxis = 0;
        pointID = pointIDlist[begin];
        point = pointCloud[pointID];
        if (dataCloud.size() > 0) data = dataCloud[pointID];
        if (pointCloud[pointIDlist[end]][splitAxis] <= point[splitAxis])
        {
            left = new KdtNode(ndim, end, end, level + 1);
            left->spaceMin = spaceMin;
            left->spaceMax = spaceMax;
            left->spaceMax[splitAxis] = point[splitAxis];
            left->spaceMiddle = spaceMiddle;
            left->spaceMiddle[splitAxis] = 0.5 * (left->spaceMin[splitAxis] + left->spaceMax[splitAxis]);
            left->AddNode(pointIDlist, pointCloud, dataCloud);
            right = nullptr;
        }
        else
        {
            right = new KdtNode(ndim, end, end, level + 1);
            right->spaceMin = spaceMin;
            right->spaceMax = spaceMax;
            right->spaceMin[splitAxis] = point[splitAxis];
            right->spaceMiddle = spaceMiddle;
            right->spaceMiddle[splitAxis] = 0.5 * (right->spaceMin[splitAxis] + right->spaceMax[splitAxis]);
            right->AddNode(pointIDlist, pointCloud, dataCloud);
            left = nullptr;
        }
    }
    // 有多个点，找到中间分割点进行均衡分割
    else if (end - begin > 1)
    {
        // // 以当前节点空间的主轴作为确定分割轴
        // Scalar maxLength = 0;
        // for (int i = 0; i < ndim; ++i)
        // {
        //     Scalar n = spaceMax[i]-spaceMin[i];
        //     if ( n > maxLength)
        //     {
        //         splitAxis = i;
        //         maxLength = n;
        //     }
        // }
        
        // 排序
        QuickSort(pointCloud, pointIDlist, begin, end);

        // 获取中间点位置，奇数点时直接可得到，偶数点时为中间两个点中的后一个
        int midID = (begin + end) / 2;

        // 将中间点作为该节点的point并进行空间分割
        pointID = pointIDlist[midID];
        point = pointCloud[pointID];
        if (dataCloud.size() > 0) data = dataCloud[pointID];

        // 创建左右节点
        left = new KdtNode(ndim, begin, midID - 1, level + 1);
        right = new KdtNode(ndim, midID + 1, end, level + 1);
        left->spaceMin = spaceMin;
        left->spaceMax = spaceMax;
        left->spaceMax[splitAxis] = point[splitAxis];
        left->spaceMiddle = spaceMiddle;
        left->spaceMiddle[splitAxis] = 0.5 * (left->spaceMin[splitAxis] + left->spaceMax[splitAxis]);

        right->spaceMin = spaceMin;
        right->spaceMax = spaceMax;
        right->spaceMin[splitAxis] = point[splitAxis];
        right->spaceMiddle = spaceMiddle;
        right->spaceMiddle[splitAxis] = 0.5 * (right->spaceMin[splitAxis] + right->spaceMax[splitAxis]);

        // 递归左右节点继续建树
        left->AddNode(pointIDlist, pointCloud, dataCloud);
        right->AddNode(pointIDlist, pointCloud, dataCloud);
    }
}

template <typename PointType, typename DataType>
void DataStruct_KdtNode<PointType, DataType>::QuickSort(const std::vector<PointType> &ref, std::vector<int> &src, int begin, int end)
{
    if (begin < end)
    {
        int i = begin, j = end;
        int key = src[begin];
        while (i < j)
        {
            while (i < j && ref[src[j]][splitAxis] > ref[key][splitAxis])
            {
                j--;
            }
            if (i < j)
            {
                src[i] = src[j];
                i++;
            }
            while (i < j && ref[src[i]][splitAxis] < ref[key][splitAxis])
            {
                i++;
            }
            if (i < j)
            {
                src[j] = src[i];
                j--;
            }
        }
        src[i] = key;
        QuickSort(ref, src, begin, i - 1);
        QuickSort(ref, src, i + 1, end);
    }
}

template <typename PointType, typename DataType>
void DataStruct_KdtNode<PointType, DataType>::FindNearestNeighbour(const PointType &tgtPoint, Scalar &nearestDist, DataType &nearestPointData)
{
    // 当前节点的point与目标的距离，如果小于已知最小距离则更新最小距离
    Scalar dist = 0;
    for (int i = 0; i < ndim; i++)
    {
        dist += pow((point[i] - tgtPoint[i]), 2);
    }

    if (dist < nearestDist) //注意：此时的nearestDist均为距离的平方
    {
        nearestDist = dist;
        nearestPointData = data;
    }

    // tgtPoint沿splitAxis到point的距离
    Scalar dAxis = tgtPoint[splitAxis] - point[splitAxis];

    // 搜索
    if (left && dAxis <= 0)
    {
        left->FindNearestNeighbour(tgtPoint, nearestDist, nearestPointData); // 近似搜索
        if (right) // 判断是否进入另一侧进行搜索, 如果以tgtPoint为圆心、以当前nearestDist为半径的圆与另一侧子树的空间范围相交，则搜索另一半子树
        {
            if (this->JudgeSphereNodeIntersection(right, tgtPoint, nearestDist))
            {
                right->FindNearestNeighbour(tgtPoint, nearestDist, nearestPointData);
            }
        }
    }
    else if (right && dAxis > 0)
    {
        right->FindNearestNeighbour(tgtPoint, nearestDist, nearestPointData); // 近似搜索
        if (left) // 判断是否进入另一侧进行搜索
        {
            if (this->JudgeSphereNodeIntersection(left, tgtPoint, nearestDist))
            {
                left->FindNearestNeighbour(tgtPoint, nearestDist, nearestPointData);
            }
        }
    }
}

template <typename PointType, typename DataType>
bool DataStruct_KdtNode<PointType, DataType>::JudgeSphereNodeIntersection(const KdtNode *node, const PointType &sphereCenter, const Scalar &sphereRadius)
{
    Scalar cubeCenterToCorner[ndim];
    Scalar cubeCornerToSphereCenter[ndim];
    Scalar newSphereCenter[ndim];
    Scalar dist=0; // 圆心到矩形的最小距离(平方值)
    // 判断圆与矩形（球与六面体）是否相交
    for (int i = 0; i < ndim; ++i)
    {
        // 1. 将圆心移至以矩形中心为坐标原点的局部坐标系的第一象限
        newSphereCenter[i] = abs(sphereCenter[i] - node->spaceMiddle[i]);
        // 2. 计算矩形中心到第一象限角点的矢量
        cubeCenterToCorner[i] = node->spaceMax[i] - node->spaceMiddle[i]; 
        // 3. 计算矩形的第一象限角点到新圆心的矢量A
        cubeCornerToSphereCenter[i] = newSphereCenter[i] - cubeCenterToCorner[i];
        // 矢量A的分量小于0时置为0
        if (cubeCornerToSphereCenter[i] < 0)
        {
            cubeCornerToSphereCenter[i] = 0;
        }
        // 4. 计算距离
        dist += pow(cubeCornerToSphereCenter[i], 2);
    }

    if (dist < sphereRadius) // 圆到矩形的最短距离小于半径说明相交
    {
        return true;
    }

    return false;
}

template <typename PointType, typename DataType>
void DataStruct_KdtNode<PointType, DataType>::SerializeNode(std::vector<KdtNode> &serializedTree)
{
    serializedTree.push_back(*this);
    int nodeID = serializedTree.size() - 1;
    if (left)
    {
        serializedTree[nodeID].leftID = serializedTree.size();
        left->SerializeNode(serializedTree);
    }
    else
    {
        serializedTree[nodeID].leftID = -1;
    }

    if (right)
    {
        serializedTree[nodeID].rightID = serializedTree.size();
        right->SerializeNode(serializedTree);
    }
    else
    {
        serializedTree[nodeID].rightID = -1;
    }
}

template <typename PointType, typename DataType>
void DataStruct_KdtNode<PointType, DataType>::DeSerializeNode(std::vector<KdtNode> &serializedTree)
{
    if (leftID == -1)
    {
        left = nullptr;
    }
    else
    {
        left = &serializedTree[leftID];
        left->DeSerializeNode(serializedTree);
    }
    if (rightID == -1)
    {
        right = nullptr;
    }
    else
    {
        right = &serializedTree[rightID];
        right->DeSerializeNode(serializedTree);
    }
}

template <typename PointType, typename DataType>
bool DataStruct_KdtNode<PointType,DataType>::IsInRegion(const PointType &rangeMin, const PointType &rangeMax)
{
    for (int i = 0; i < ndim; ++i)
    {
        if (point[i] < rangeMin[i] || point[i] > rangeMax[i])
        {
            return false;
        }
    }
    return true;
}

template <typename PointType, typename DataType>
void DataStruct_KdtNode<PointType, DataType>::FindNodesInRegion(const PointType &rangeMin, const PointType &rangeMax, std::vector<KdtNode *> &nodeList)
{
    if (IsInRegion(rangeMin, rangeMax))
    {
        nodeList.push_back(this);
    }

    if (left != nullptr)
    {
        if (rangeMin[splitAxis] <= point[splitAxis])
        {
            left->FindNodesInRegion(rangeMin, rangeMax, nodeList);
        }
    }

    if (right != nullptr)
    {
        if (rangeMax[splitAxis] >= point[splitAxis])
        {
            right->FindNodesInRegion(rangeMin, rangeMax, nodeList);
        }
    }
}

// template < typename PointType  >
// int DataStruct_KdtNode<PointType >::GetNodeNum()
// {
//     int count = 0;
//     count += 1;
//     if (this->left)
//     {
//         count += left->GetNodeNum();
//     }
//     if (this->right)
//     {
//         count += right->GetNodeNum();
//     }
//     return count;
// }

template <typename PointType, typename DataType>
DataStruct_KdtTree<PointType, DataType>::DataStruct_KdtTree(const int &ndim_)
	: ndim(ndim_),
	pointCloud({}),
	dataCloud({})
{
    root = nullptr;
    //空树的上下限设为极小
	for (int i = 0; i < ndim; i++)
	{
		pointCloudMin.push_back(-1 * INF);
		pointCloudMax.push_back(-1 * INF);
	}
}

template <typename PointType, typename DataType>
DataStruct_KdtTree<PointType, DataType>::DataStruct_KdtTree(const int &ndim_,
												  const std::vector<PointType> &pointCloud_,
												  const std::vector<DataType> &dataCloud_)
    : ndim(ndim_), 
	  pointCloud(pointCloud_),
	  dataCloud(dataCloud_)
{
    if (pointCloud.size() == 0) //传入的点集为空时创建一个空树，上下限设为极小
    {
        for (int i = 0; i < ndim_; i++)
        {
            pointCloudMin.push_back(-1* INF);
            pointCloudMax.push_back(-1* INF);
        }

        root = nullptr;

        return;
    }

    this->ComputePointCloudLimits();

    root = new KdtNode(ndim, 0, pointCloud.size() - 1, 0);
    root->spaceMin = pointCloudMin;
    root->spaceMax = pointCloudMax;
    root->spaceMiddle = pointCloudMin;
    for (int i = 0; i < ndim; ++i)
    {
        root->spaceMiddle[i] = 0.5 * (root->spaceMin[i] + root->spaceMax[i]);
    }

    root->AddNode(pointIDlist, pointCloud, dataCloud);
}

template <typename PointType, typename DataType>
DataStruct_KdtTree<PointType, DataType>::~DataStruct_KdtTree()
{
    // delete[] pmin;
    // delete[] pmax;
    delete root;
}

template <typename PointType, typename DataType>
void DataStruct_KdtTree<PointType, DataType>::ComputePointCloudLimits()
{
    pointIDlist.resize(pointCloud.size());
    pointCloudMin = pointCloud[0];
    pointCloudMax = pointCloud[0];

    for (int i = 0; i < pointCloud.size(); i++)
    {
        for (int j = 0; j < ndim; j++)
        {
            pointCloudMin[j] = std::min(pointCloudMin[j], pointCloud[i][j]);
            pointCloudMax[j] = std::max(pointCloudMax[j], pointCloud[i][j]);
        }
        pointIDlist[i] = i;
    }
}

template <typename PointType, typename DataType>
std::pair<Scalar, DataType> DataStruct_KdtTree<PointType, DataType>::FindNearestNeighbour(const PointType &tgtPoint)
{
    Scalar nearestDist = INF;
    DataType nearestPointData;

    if (root)
    {
        root->FindNearestNeighbour(tgtPoint, nearestDist, nearestPointData);
    }

    nearestDist = sqrt(nearestDist);

    std::pair<Scalar, DataType> nearest(nearestDist, nearestPointData);

    return nearest;
}

template <typename PointType, typename DataType>
void DataStruct_KdtTree<PointType, DataType>::FindNodesInRegion(const PointType &rangeMin, const PointType &rangeMax, std::vector<KdtNode *> &nodeList)
{
    if (root)
    {
        root->FindNodesInRegion(rangeMin, rangeMax, nodeList);
    }
}

template <typename PointType, typename DataType>
const PointType &DataStruct_KdtTree<PointType, DataType>::GetMin()
{
    return pointCloudMin;
}

template <typename PointType, typename DataType>
const PointType &DataStruct_KdtTree<PointType, DataType>::GetMax()
{
    return pointCloudMax;
}

// template <typename PointType >
// void DataStruct_KdtTree<PointType >::LevelTraverse() const
// {
//     std::queue< KdtNode * > nodeQueue;
//     KdtNode * start = root;
//     if (start == 0)
//     {
//         return;
//     }

//     nodeQueue.push(start);
//     while (!nodeQueue.empty())
//     {
//         start = nodeQueue.front();
//         nodeQueue.pop();

//         //cout<<start->item<<" ";
//         if (start->left)
//         {
//             nodeQueue.push(start->left);
//         }
//         if (start->right)
//         {
//             nodeQueue.push(start->right);
//         }
//     }
// }

// template <typename PointType >
// int  DataStruct_KdtTree<PointType >::GetNodeNum()
// {
//     if (root)
//     {
//         return root->GetNodeNum();
//     }
//     else
//     {
//         return 0;
//     }
// }

// template <typename PointType >
// typename DataStruct_KdtTree<PointType >::KdtNode * DataStruct_KdtTree<DataType, PointType>::GetRoot() const
// {
//    // return root;
// }

template <typename PointType, typename DataType>
void DataStruct_KdtTree<PointType, DataType>::SerializeTree()
{
    if (root == 0)
    {
        FatalError("Kdt: Kdt Tree is empty, please create this tree first!");
    }

    root->SerializeNode(serializedTree);
}

template <typename PointType, typename DataType>
void DataStruct_KdtTree<PointType, DataType>::DeSerializeTree()
{
    root = &serializedTree[0];
    root->DeSerializeNode(serializedTree);
}