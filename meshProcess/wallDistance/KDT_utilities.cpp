﻿#include "meshProcess/wallDistance/KDTutilities.h"

KDT::KDT(const int &dim_)
    :dim(dim_)
{
    nodeTree = nullptr;
    elemTree = nullptr;
}

KDT::~KDT()
{
    if (nodeTree != nullptr)
    {
        delete nodeTree;
    }
    if (elemTree != nullptr)
    {
        delete elemTree;
    }
    
}

void KDT::CreateNodeKDTree(const std::vector<std::pair<Face, std::vector<Node>>> &srcNodes)
{
    coor.resize(dim);
    std::vector<std::vector<Scalar>> nodeCloud(srcNodes.size(), coor);
    std::vector<int> nodeIDcloud(srcNodes.size(), -1);

    for (int i = 0; i < srcNodes.size(); ++i)
    {
        const Node &faceCenter = srcNodes[i].first.GetCenter();
        nodeCloud[i][0] = faceCenter.X();
        nodeCloud[i][1] = faceCenter.Y();
        if (dim == 3)
        {
            nodeCloud[i][0] = faceCenter.Z();
        }

        nodeIDcloud[i] = i;
    }

    nodeTree = new KdtTree(dim, nodeCloud, nodeIDcloud);
}

std::pair<Scalar, int> KDT::SearchNearestNode(const Vector &tgtPoint)
{
    if (!nodeTree)
    {
        FatalError("KDT:未创建KD树，无法进行搜索");
    }

    coor[0] = tgtPoint.X();
    coor[1] = tgtPoint.Y();
    if (dim == 3)
    {
        coor[2] = tgtPoint.Z();
    }
    
    nearestInfo = nodeTree->FindNearestNeighbour(coor);
    return nearestInfo;
}

void KDT::CreateElementKDTree(const Mesh *srcMesh)
{
    coor.resize(dim);
    superCoor.resize(2 * dim);

    const int &elemNum = srcMesh->GetElementNumberReal();
    std::vector<std::vector<Scalar>> elemBounds(elemNum, superCoor);   ///< 网格单元的上下界
    std::vector<int> dataCloud(elemNum);

    // 计算网格单元的上下界，作为超维点，并填充data
    this->ComputeElemBoundBox(srcMesh, elemBounds, dataCloud);

    elemTree = new KdtTree(2 * dim, elemBounds, dataCloud);

    // 创建KD树基础信息，并全进程同步
    globalTreeInfo.resize(GetMPISize());
    TreeInfo localTreeInfo(srcMesh->GetMeshZoneID(), GetMPIRank(), elemTree->GetMin(), elemTree->GetMax());
    boost::mpi::all_gather(mpi_world, localTreeInfo, globalTreeInfo);

    elemBounds.clear();
    dataCloud.clear();
}


void KDT::ComputeElemBoundBox(const Mesh *srcMesh, std::vector<std::vector<Scalar>> &elemBounds, std::vector<int> &dataCloud)
{
    const int &elemNum = srcMesh->GetElementNumberReal();
    // 获取所有单元的上下界，成为2*dim空间中的点
	for (int elemID = 0; elemID < elemNum; ++elemID)
	{
		for (int i = 0; i < dim; i++)
		{
			elemBounds[elemID][i] = INF;
			elemBounds[elemID][i + dim] = -1 * INF;
		}

		// 循环elem的点，找到其上下界
		for (int nodeI = 0; nodeI < srcMesh->GetElement(elemID).GetNodeSize(); ++nodeI)
		{
			const int &nodeID = srcMesh->GetElement(elemID).GetNodeID(nodeI);
			const Node &node = srcMesh->GetNode(nodeID);
            coor[0] = node.X();
            coor[1] = node.Y();
            if (dim == 3)
            {
                coor[2] = node.Z();
            }

            for (int i = 0; i < dim; i++)
			{
				elemBounds[elemID][i] = std::min(elemBounds[elemID][i], coor[i]);
				elemBounds[elemID][i + dim] = std::max(elemBounds[elemID][i + dim], coor[i]);
			}
		}

        dataCloud[elemID] = elemID;
	}
}