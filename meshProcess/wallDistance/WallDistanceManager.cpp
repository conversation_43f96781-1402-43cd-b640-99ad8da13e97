﻿#include "meshProcess/wallDistance/WallDistanceManager.h"
#include "basic/field/ElementField.h"

WallDistanceManager::WallDistanceManager(const Turbulence::WallDistance &type_, const bool &project_)
	: type(type_), project(project_)
{}

void WallDistanceManager::CollectFace(Mesh *mesh, const Configure::Flow::FlowConfigure &flowConfigure, const Boundary::Type &bType,
	std::vector<std::pair<Face, std::vector<Node>>> &faceVector, const int &zoneID)
{
	int faceSize = 0;
	if (mesh->GetMeshZoneID() == zoneID || zoneID < 0)
	{
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
			const Boundary::Type &type = flowConfigure.GetLocalBoundary(0, patchID).type;
			if (bType == type || (bType == Boundary::Type::WALL && type > Boundary::Type::WALL))
				faceSize += mesh->GetBoundaryFaceSize(patchID);
		}
	}

	std::vector<Scalar> faceMagList(faceSize);
	std::vector<Vector> faceCenterList(faceSize), faceNormalList(faceSize);
	std::vector<std::vector<Vector>> faceNodeList(faceSize);

	int count = 0;
	if (mesh->GetMeshZoneID() == zoneID || zoneID < 0)
	{
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
			const Boundary::Type &type = flowConfigure.GetLocalBoundary(0, patchID).type;
			if (bType == type || (bType == Boundary::Type::WALL && type > Boundary::Type::WALL))
			{
				for (int index = 0; index < mesh->GetBoundaryFaceSize(patchID); ++index)
				{
					const Face &face = mesh->GetFace(mesh->GetBoundaryFaceID(patchID, index));
					faceMagList[count] = face.GetArea();
					faceCenterList[count] = face.GetCenter();
					faceNormalList[count] = face.GetNormal();

					faceNodeList[count].resize(face.GetNodeSize());
					for (int k = 0; k < face.GetNodeSize(); ++k)
						faceNodeList[count][k] = mesh->GetNode(face.GetNodeID(k));

					count++;
				}
			}
		}
	}

	std::vector<std::vector<Scalar>> faceMagListTotal(faceSize);
	std::vector<std::vector<Vector>> faceCenterListTotal(faceSize), faceNormalListTotal(faceSize);
	std::vector<std::vector<std::vector<Vector>>> faceNodeListTotal(faceSize);

	if (GetMPISize() == 1)
	{
		faceMagListTotal.push_back(faceMagList);
		faceCenterListTotal.push_back(faceCenterList);
		faceNormalListTotal.push_back(faceNormalList);
		faceNodeListTotal.push_back(faceNodeList);
	}
	else
	{
#if defined(_BaseParallelMPI_)
		boost::mpi::all_gather(MPI::mpiWorld, faceMagList, faceMagListTotal);
		boost::mpi::all_gather(MPI::mpiWorld, faceCenterList, faceCenterListTotal);
		boost::mpi::all_gather(MPI::mpiWorld, faceNormalList, faceNormalListTotal);
		boost::mpi::all_gather(MPI::mpiWorld, faceNodeList, faceNodeListTotal);
#endif
	}

	faceSize = 0;
	for (int i = 0; i < faceMagListTotal.size(); ++i) faceSize += faceMagListTotal[i].size();

	faceVector.resize(faceSize);
	faceSize = 0;
	for (int i = 0; i < faceMagListTotal.size(); ++i)
	{
		for (int j = 0; j < faceMagListTotal[i].size(); ++j)
		{
			Face &face = faceVector[faceSize].first;
			face.areaMag = faceMagListTotal[i][j];
			face.center = faceCenterListTotal[i][j];
			face.normal = faceNormalListTotal[i][j];
			faceVector[faceSize].second = faceNodeListTotal[i][j];
			faceSize++;
		}
	}
}

void WallDistanceManager::Calculate(Mesh *mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallFaceVector, std::vector<Scalar> &distance, std::vector<int> &fromFaceID)
{   
	WallDistanceBase *wallDistancePointer; ///< 壁面距离基类指针
	
	switch (type)
	{
	case Turbulence::WallDistance::TRAVERSE:
		wallDistancePointer = new WallDistanceTraverse(mesh, wallFaceVector, distance, fromFaceID, project);
		break;
	case Turbulence::WallDistance::ADT:
		wallDistancePointer = new WallDistanceADT(mesh, wallFaceVector, distance, fromFaceID, project);
		break;
	case Turbulence::WallDistance::KDT:
		wallDistancePointer = new WallDistanceKDT(mesh, wallFaceVector, distance, fromFaceID, project);
		break;
	default:
		FatalError("WallDistanceManager::SetPointer: input method isnot supported!\n");
		break;
	}

	wallDistancePointer->Calculate();

	delete wallDistancePointer;

	// 检查网格的壁面距离
	// CheckDistance(mesh, wallFaceVector, distance, fromFaceID); // 重叠时存在负壁面距离，此处会修为正值，暂时取消
}

void WallDistanceManager::CheckDistance(Mesh *mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallFaceVector, std::vector<Scalar> &distance, std::vector<int> &fromFaceID)
{
    MPIBarrier();

	for (int i = 0; i < distance.size(); ++i)
	{
		if (distance[i] < Scalar0 || distance[i] > INF - 1)
		{
			distance[i] = INF;
			for (int index = 0; index < wallFaceVector.size(); ++index)
			{
				const Vector &elementCenter = mesh->GetElement(i).GetCenter();
				const Face &face = wallFaceVector[index].first;
				Scalar d = (face.GetCenter() - elementCenter).Mag();
				
				if (distance[i] < d)
				{
					distance[i] = d;
					fromFaceID[i] = index;
				}
			}
		}
	}
}