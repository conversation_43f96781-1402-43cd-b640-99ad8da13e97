﻿#include "meshProcess/wallDistance/WallDistanceADT.h"

WallDistanceADT::WallDistanceADT(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_,
	std::vector<Scalar> &distance_, std::vector<int> &fromFaceID_, const bool &project_)
	: WallDistanceBase(mesh, wallBoundaryFace_, distance_, fromFaceID_, project_), wallFaceTree(nullptr)
{}

WallDistanceADT::~WallDistanceADT()
{
    if (wallFaceTree != nullptr) { delete wallFaceTree; wallFaceTree = nullptr; }
}

void WallDistanceADT::Calculate()
{
    wallDistance.clear();
    wallDistance.resize(elementSize, -1.0);
    fromFaceID.resize(elementSize, -1);

    int dim = 2;
    if (pmesh->GetMeshDimension() == Mesh::MeshDim::md3D) dim = 3;
    
    // 建立叉树结构
    BuildUnstructuredSurfaceADT(dim);    

    // 盒子的界限
    Scalar pmin[3], pmax[3];

    // 壁面面元的数量
    int nTWallFace = wallBoundaryFace.size();

    // 盒子内含壁面面元的上下界
    int nBoxLimitedMax = Max((int)sqrt(nTWallFace * 1.0), 1);
    nBoxLimitedMax = Min(nBoxLimitedMax, 100);
    int nBoxLimitedMin = Min((int)(nBoxLimitedMax * 0.1), 10);

    const int interval = std::max(elementSize / 10, 1);

    const bool mpiRank0Flag = (GetMPIRank() == 0);
    const int tryMax = 2; //最大重复搜索次数(建议给2,测试用1)
    const int nSearchTimes = 100 / 5; //搜索半径变化次数的最大值
    for (int elementID = 0; elementID < elementSize; ++elementID)
    {
        if (mpiRank0Flag)
        {
            if ((elementID + 1) % interval == 0)
            {
                std::ostringstream stringStream;
                if ((elementID + 1) / interval == 1) stringStream << "\t";
                stringStream << std::setw(6) << std::setprecision(1) << std::fixed << (Scalar)(elementID + 1) / elementSize * 100.0 << "%";
                if ((elementID + 1) / interval == 10 || elementID + 1 == elementSize) stringStream << std::endl;
                Info::infoFile << stringStream.str();
                std::cout << stringStream.str();
            }
        }

        const Element& curElement = pmesh->GetElement(elementID);
        
        //初始化部分数值
        int boxMax = nBoxLimitedMax;
        int boxMin = nBoxLimitedMin;
        int iTry = 0; //重新搜索次数计算

        Scalar rMin = 0; //搜索半径的最小值
        Scalar rMax; //搜索半径的最大值
        Scalar rMaxPre = -1; //搜索半径最大值的预估值
        if (dim == 2) rMax = std::sqrt(curElement.GetVolume());
        else          rMax = std::pow(curElement.GetVolume(), 1.0 / 3.0);        

        while (iTry < tryMax)
        {
            ++iTry;

            bool isFoundNearestFace = false;
            int iSearch = 0;
            
            while (iSearch < nSearchTimes)
            {
                ++iSearch;

                Scalar ra = 0.5 * (rMin + rMax);
                pmin[0] = curElement.GetCenter().X() - ra;
                pmin[1] = curElement.GetCenter().Y() - ra;
                pmin[2] = curElement.GetCenter().Z() - ra;

                pmax[0] = curElement.GetCenter().X() + ra;
                pmax[1] = curElement.GetCenter().Y() + ra;                
                pmax[2] = curElement.GetCenter().Z() + ra;                
                
                WallFaceTree::AdtNodeList list;
                wallFaceTree->FindNodesInRegion(pmin, pmax, list, boxMax + 1);
                
                const int nNearWallFace = list.size();
                if ((nNearWallFace > boxMin && nNearWallFace <= boxMax))
                {
                    for (int iWall = 0; iWall < nNearWallFace; ++iWall)
                    {
                        const int &faceIndex = list[iWall]->GetData().first;
						const Scalar wdst = CalculateFromElementTOFace(elementID, faceIndex);
						if(wdst >= 0)
						{
							if (wallDistance[elementID] < 0 || wallDistance[elementID] > wdst)
							{
								wallDistance[elementID] = wdst;
                                fromFaceID[elementID] = faceIndex;
								isFoundNearestFace = true;
							}
						}
                    }
                    
                    break;
                }
                else if (nNearWallFace > boxMax)  //包含面元太多，缩小半径
                {
                    if (rMax < rMaxPre || rMaxPre < 0) rMaxPre = rMax;
                    rMax = ra;                    
                    continue;
                }
                else //没找到，扩大半径
                {
                    rMin = ra;
                    if (rMaxPre < 0) rMax = rMax * 2.0;
                    else rMax = rMaxPre;
                    continue;
                }
            }//! iSearch while.

            if (isFoundNearestFace) break;
            else //盒子上下界不合适，进行调整
            {
                boxMax = nTWallFace;
                boxMin = 0;                
                continue;
            }
        }

        if (iTry > tryMax)
            FatalError("WallDistanceADT::Calculate: iTry > tryMax");
    }
}

void WallDistanceADT::BuildUnstructuredSurfaceADT(const int &ndim)
{
    // 壁面边界上面元的个数
    const int NumberOfWallBoundaryFace = (int)wallBoundaryFace.size();

    // 初始化壁面边界上所有面元的界限盒子    
    std::vector<std::vector<Scalar>> box;//壁面边界上所有面元的界限盒子
    box.assign(NumberOfWallBoundaryFace, { INF, INF, INF, -INF, -INF, -INF });

    // 初始化壁面的界限盒子    
    Scalar wallBound[6];//壁面的界限盒子
    for (int iDim = 0; iDim < 3; ++iDim)
    {
        wallBound[iDim] = INF;
        wallBound[iDim + 3] = -1 * INF;
    }

    //计算壁面边界上所有面元的界限盒子+壁面的界限盒子
    for (int i = 0; i < NumberOfWallBoundaryFace; ++i)
    {
        const Vector &tempPoint = wallBoundaryFace[i].first.GetCenter();
        box[i][0] = Min(box[i][0], tempPoint.X() );
        box[i][1] = Min(box[i][1], tempPoint.Y() );
        box[i][2] = Min(box[i][2], tempPoint.Z() );
        box[i][3] = Max(box[i][3], tempPoint.X() );
        box[i][4] = Max(box[i][4], tempPoint.Y() );
        box[i][5] = Max(box[i][5], tempPoint.Z() );
        wallBound[0] = Min(wallBound[0], tempPoint.X());
        wallBound[1] = Min(wallBound[1], tempPoint.Y());
        wallBound[2] = Min(wallBound[2], tempPoint.Z());
        wallBound[3] = Max(wallBound[3], tempPoint.X());
        wallBound[4] = Max(wallBound[4], tempPoint.Y());
        wallBound[5] = Max(wallBound[5], tempPoint.Z());
    }

    Scalar wallTolerance = -INF;
    for (int iDim = 0; iDim < 3; ++iDim)
    {
        wallTolerance = Max(wallTolerance, wallBound[iDim + 3] - wallBound[iDim]);
    }
    wallTolerance *= 0.01;

    for (int iDim = 0; iDim < 3; ++iDim)
    {
        wallBound[iDim] -= wallTolerance;
        wallBound[iDim + 3] += wallTolerance;
    }

    // 根节点对应空间
    Scalar wallMin[6], wallMax[6];//壁面界限盒子对应的2*ndim点
    for (int iDim = 0; iDim < 3; ++iDim)
    {
        wallMin[iDim] = wallBound[iDim];
        wallMax[iDim] = wallBound[iDim + 3];
    }
    for (int iDim = 3; iDim < 6; ++iDim)
    {
        wallMin[iDim] = wallBound[iDim - 3];
        wallMax[iDim] = wallBound[iDim];
    }

    // 建母树,根节点
    if (!wallFaceTree) wallFaceTree = new WallFaceTree(ndim, wallMin, wallMax);
    
    // 将壁面所有面元挨个插入ADT树
    for (int i = 0; i < NumberOfWallBoundaryFace; ++i)
    {
        Scalar temp[6] = { box[i][0], box[i][1], box[i][2],
                box[i][3], box[i][4], box[i][5] };
        WallFaceNode* triNode = new WallFaceNode(ndim, temp, FaceID(i, -1));
        wallFaceTree->AddNode(triNode);
    }
}