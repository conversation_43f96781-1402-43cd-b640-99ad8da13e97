﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ADT_utilities.h
//! <AUTHOR>
//! @brief ADT节点和树类
//! @date  2021-5-12
// References:
// A<PERSON><PERSON><PERSON>, <PERSON>, "Solution Adaptive Cartesian Grid Methods for Aerody-
// namic Flows with Complex Geometries, " lecture notes for 28th Computational
// Fluid Dynamics Lecture Series, von Karman Institute for Fluid Dynamics,
// March 3 - 7, 1997.
//------------------------------修改日志----------------------------------------
//
// 2021-05-12 尹强 
// 说明：建立并规范化
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_ADT_utilities_
#define _meshProcess_wallDistance_ADT_utilities_

#include "basic/common/ConfigUtility.h"

template < typename DataType, typename CoorType >
class DataStruct_AdtNode
{
public:
    typedef DataStruct_AdtNode<DataType, CoorType>        AdtNode;
    typedef std::vector<AdtNode *>          AdtNodeList;
    typedef typename AdtNodeList::iterator  AdtNodeListIter;

public:
    /// ! 点坐标
    CoorType            *point;

    /// ! 点在树中的深度
    int          level;

    /// ! 左子树
    AdtNode      *left;

    /// ! 右子树
    AdtNode      *right;

    /// ! 存储数据
    DataType            item;

    /// ! 空间维度
    int          ndim;

public:
    /// ! 构造函数，默认三维
    DataStruct_AdtNode(int ndim = 3);

    DataStruct_AdtNode(int ndim, CoorType *coor, DataType data);

    /// ! 析构函数，释放动态申请内存
    ~DataStruct_AdtNode();

    void AddNode(AdtNode *node, CoorType *nwmin, CoorType *nwmax, const int &ndim);

    bool IsInRegion(CoorType *pmin, CoorType *pmax, const int &ndim);

    void FindNodesInRegion(CoorType *pmin, CoorType *pmax, CoorType *nwmin, CoorType *nwmax, const int &ndim, AdtNodeList &ld, const unsigned int & sizeLimit = 0);

    int           GetNodeNum();

    DataType GetData() const { return item; };
};

// 交替数字树类
template < typename DataType, typename CoorType >
class DataStruct_AdtTree
{
public:
    typedef typename DataStruct_AdtNode<DataType, CoorType>::AdtNode          AdtNode;
    typedef typename DataStruct_AdtNode<DataType, CoorType>::AdtNodeList      AdtNodeList;
    typedef typename DataStruct_AdtNode<DataType, CoorType>::AdtNodeListIter  AdtNodeListIter;
    typedef DataStruct_AdtTree<DataType, CoorType>         AdtTree;

private:
    int           ndim;

    CoorType   *pmin, *pmax;

    AdtNode    *root;

public:

    DataStruct_AdtTree(int ndim = 3);

    DataStruct_AdtTree(int ndim, CoorType *pmin, CoorType *pmax);

    /// ! 析构函数，释放动态申请内存
    ~DataStruct_AdtTree();

    /// ! 向树中增加一个节点
    void AddNode(AdtNode *node);

    void FindNodesInRegion(CoorType *pmin, CoorType *pmax, AdtNodeList &ld, const size_t & sizeLimit = 0);

    int  GetNodeNum();

    CoorType  *GetMin() const;

    CoorType  *GetMax() const;

    AdtNode * GetRoot() const;


    void LevelTraverse() const;

};

#endif // !_MESH_ADT_UTILITIES_