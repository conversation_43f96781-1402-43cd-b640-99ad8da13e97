﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ADT_utilities.h
//! <AUTHOR>
//! @brief ADT节点和树类操作函数的定义
//! @date  2021-5-12
//------------------------------修改日志----------------------------------------
//
// 2021-05-12 尹强 
// 说明：建立并规范化
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_wallDistance_ADT_utilities_XX_
#define _meshProcess_wallDistance_ADT_utilities_XX_

#include "meshProcess/wallDistance/ADT_utilities.h"

template < typename DataType, typename CoorType >
DataStruct_AdtNode<DataType, CoorType>::DataStruct_AdtNode(int ndim)
{
    this->ndim = ndim;
    point = new CoorType[ndim];
    level = 0;
    left = nullptr;
    right = nullptr;
}

template < typename DataType, typename CoorType >
DataStruct_AdtNode<DataType, CoorType>::DataStruct_AdtNode(int ndim, CoorType *coor, DataType data)
{
    this->ndim = ndim;
    point = new CoorType[ndim];
    memcpy(point, coor, ndim*sizeof(CoorType));

    level = 0;
    left = nullptr;
    right = nullptr;
    item = data;
}

template < typename DataType, typename CoorType >
DataStruct_AdtNode<DataType, CoorType>::~DataStruct_AdtNode()
{
    delete[] point;
    if (!left)     delete left;
    if (!right) delete right;
}

template < typename DataType, typename CoorType >
void DataStruct_AdtNode<DataType, CoorType>::AddNode(AdtNode *node, CoorType *nwmin, CoorType *nwmax, const int &ndim)
{
    int axis = level%ndim;
    CoorType mid = 0.5 * (nwmin[axis] + nwmax[axis]);

    if (node->point[axis] <= mid)
    {
        if (left)
        {
            nwmax[axis] = mid;
            left->AddNode(node, nwmin, nwmax, ndim);
        }
        else
        {
            left = node;
            node->level = level + 1;
        }
    }
    else
    {
        if (right)
        {
            nwmin[axis] = mid;
            right->AddNode(node, nwmin, nwmax, ndim);
        }
        else
        {
            right = node;
            node->level = level + 1;
        }
    }
}

template < typename DataType, typename CoorType >
bool DataStruct_AdtNode<DataType, CoorType>::IsInRegion(CoorType *pmin, CoorType *pmax, const int &ndim)
{
    for (int i = 0; i < ndim; ++i)
    {
        if (point[i] < pmin[i] || point[i] > pmax[i])
        {
            return false;
        }
    }

    return true;
}

template < typename DataType, typename CoorType >
void DataStruct_AdtNode<DataType, CoorType>::FindNodesInRegion(CoorType *pmin, CoorType *pmax, CoorType *nwmin, CoorType *nwmax, const int &ndim, AdtNodeList &ld, const unsigned int & sizeLimit)
{
    int            axis;
    CoorType       mid, temp;

    if (IsInRegion(pmin, pmax, ndim))
    {
        ld.push_back(this);
    }

    std::size_t sizeInList = ld.size();

    if (sizeLimit > 0 && sizeInList >= sizeLimit)
    {
        return;
    }

    axis = level%ndim;
    mid = 0.5 * (nwmin[axis] + nwmax[axis]);

    if (left)
    {
        if (pmin[axis] <= mid && pmax[axis] >= nwmin[axis])
        {
            temp = nwmax[axis];
            nwmax[axis] = mid;
            left->FindNodesInRegion(pmin, pmax, nwmin, nwmax, ndim, ld, sizeLimit);
            nwmax[axis] = temp;
        }
    }

    if (right)
    {
        if (pmax[axis] >= mid && pmin[axis] <= nwmax[axis])
        {
            temp = nwmin[axis];
            nwmin[axis] = mid;
            right->FindNodesInRegion(pmin, pmax, nwmin, nwmax, ndim, ld, sizeLimit);
            nwmin[axis] = temp;
        }
    }

    return;
}

template < typename DataType, typename CoorType >
int DataStruct_AdtNode<DataType, CoorType>::GetNodeNum()
{
    int count = 0;
    count += 1;
    if (this->left)
    {
        count += left->GetNodeNum();
    }
    if (this->right)
    {
        count += right->GetNodeNum();
    }
    return count;
}

template < typename DataType, typename CoorType >
DataStruct_AdtTree<DataType, CoorType>::DataStruct_AdtTree(int ndim)
{
    this->ndim = ndim;
    pmin = new CoorType[ndim];
    pmax = new CoorType[ndim];
    for (int i = 0; i < ndim; ++i)
    {
        pmin[i] = 0.0;
        pmax[i] = 1.0;
    }
    root = 0;
}

template < typename DataType, typename CoorType >
DataStruct_AdtTree<DataType, CoorType>::DataStruct_AdtTree(int ndim, CoorType *pmin, CoorType *pmax)
{
    this->ndim = ndim;
    this->pmin = new CoorType[ndim];
    this->pmax = new CoorType[ndim];
    for (int i = 0; i < ndim; ++i)
    {
        this->pmin[i] = pmin[i];
        this->pmax[i] = pmax[i];
    }
    root = 0;
}

template < typename DataType, typename CoorType >
DataStruct_AdtTree<DataType, CoorType>::~DataStruct_AdtTree()
{
    delete[] pmin;
    delete[] pmax;
    delete root;
}


template < typename DataType, typename CoorType >
void DataStruct_AdtTree<DataType, CoorType>::AddNode(AdtNode *node)
{

    CoorType *nwmin = new CoorType[ndim];
    CoorType *nwmax = new CoorType[ndim];
    memcpy(nwmin, this->pmin, ndim*sizeof(CoorType));
    memcpy(nwmax, this->pmax, ndim*sizeof(CoorType));

    if (root == 0)
    {
        root = node;
    }
    else
    {
        root->AddNode(node, nwmin, nwmax, ndim);
    }

    delete[] nwmin;
    delete[] nwmax;
}


template < typename DataType, typename CoorType >
void DataStruct_AdtTree<DataType, CoorType>::FindNodesInRegion(CoorType *pmin, CoorType *pmax, AdtNodeList &ld, const size_t & sizeLimit)
{
    CoorType *nwmin = new CoorType[ndim];
    CoorType *nwmax = new CoorType[ndim];
    memcpy(nwmin, this->pmin, ndim*sizeof(CoorType));
    memcpy(nwmax, this->pmax, ndim*sizeof(CoorType));

    if (root)
    {
        root->FindNodesInRegion(pmin, pmax, nwmin, nwmax, ndim, ld, sizeLimit);
    }
    delete[] nwmin;
    delete[] nwmax;
}



template < typename DataType, typename CoorType >
CoorType * DataStruct_AdtTree<DataType, CoorType>::GetMin() const
{
    return pmin;
}


template < typename DataType, typename CoorType >
CoorType * DataStruct_AdtTree<DataType, CoorType>::GetMax() const
{
    return pmax;
}

template < typename DataType, typename CoorType >
void DataStruct_AdtTree<DataType, CoorType>::LevelTraverse() const
{
    std::queue< AdtNode * > nodeQueue;
    AdtNode * start = root;
    if (start == 0)
    {
        return;
    }

    nodeQueue.push(start);
    while (!nodeQueue.empty())
    {
        start = nodeQueue.front();
        nodeQueue.pop();

        /// cout<<start->item<<" ";  
        if (start->left)
        {
            nodeQueue.push(start->left);
        }
        if (start->right)
        {
            nodeQueue.push(start->right);
        }
    }
}

template < typename DataType, typename CoorType >
int  DataStruct_AdtTree<DataType, CoorType>::GetNodeNum()
{
    if (root)
    {
        return root->GetNodeNum();
    }
    else
    {
        return 0;
    }
}

template < typename DataType, typename CoorType >
typename DataStruct_AdtTree<DataType, CoorType>::AdtNode * DataStruct_AdtTree<DataType, CoorType>::GetRoot() const
{
    return root;
}

#endif