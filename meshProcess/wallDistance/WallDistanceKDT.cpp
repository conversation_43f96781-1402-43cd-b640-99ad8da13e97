﻿#include "meshProcess/wallDistance/WallDistanceKDT.h"

WallDistanceKDT::WallDistanceKDT(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_,
	std::vector<Scalar> &distance_, std::vector<int> &fromFaceID_, const bool &project_)
	: WallDistanceBase(mesh, wallBoundaryFace_, distance_, fromFaceID_, project_)
{}

WallDistanceKDT::~WallDistanceKDT()
{}

void WallDistanceKDT::Calculate()
{
    wallDistance.clear();
    wallDistance.resize(elementSize, INF);
    fromFaceID.resize(elementSize, -1);

    int dim = 2;
    if (pmesh->GetMeshDimension() == Mesh::MeshDim::md3D) dim = 3;
    
    // 建立叉树结构
    KDT kdt(dim);
    kdt.CreateNodeKDTree(wallBoundaryFace);

    //计算壁面距离
    const int interval = std::max(elementSize / 10, 1);
    const bool mpiRank0Flag = (GetMPIRank() == 0);
    std::pair<Scalar, int> nearest;
    for (int iElem = 0; iElem < elementSize; ++iElem)
    {
        if ((iElem + 1) % interval == 0)
        {
            if (mpiRank0Flag)
            {
                std::ostringstream stringStream;
                if ((iElem + 1) / interval == 1) stringStream << "\t";
                stringStream << std::setprecision(3) << (Scalar)(iElem + 1) / elementSize * 100.0 << "%" << "  ";
                if ((iElem + 1) / interval == 10 || iElem + 1 == elementSize) stringStream << std::endl;
                Info::infoFile << stringStream.str();
                std::cout << stringStream.str();
            }
        }

        const Vector &center = pmesh->GetElement(iElem).GetCenter();
        nearest = kdt.SearchNearestNode(center);
		wallDistance[iElem] = CalculateFromElementTOFace(iElem, nearest.second);
		fromFaceID[iElem] = nearest.second;;
    }
}

// void WallDistanceKDT::BuildUnstructuredSurfaceKDT(const int &ndim)
// {    
//     std::vector<std::vector<Scalar>> faceCenterCloud(WallBoundaryFace.size());
//     std::vector<int> faceIDCloud(WallBoundaryFace.size());
//     for (int i = 0; i < WallBoundaryFace.size(); ++i)
//     {
//         const Node &faceCenter = WallBoundaryFace[i].first.GetCenter();
//         faceCenterCloud[i] = { faceCenter.X(), faceCenter.Y(), faceCenter.Z() };
//         faceIDCloud[i] = i;
//     }

//     KdtTrees = new KdtTree(ndim, faceCenterCloud, faceIDCloud);
// }