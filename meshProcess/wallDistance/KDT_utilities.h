﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ${ARI_SOURCE_DIR}/basic/mesh/KDTutilities.h
//! <AUTHOR>
//! @brief KDT算法应用类，利用KDTbase提供更加便捷的搜索接口
//!
//! @date  2024-10-15

#ifndef _meshProcess_wallDistance_KDTutilities_
#define _meshProcess_wallDistance_KDTutilities_

#include "meshProcess/wallDistance/KDTbase.h"
#include "meshProcess/wallDistance/KDTbase.hxx"
#include "basic/mesh/Mesh.h"

typedef DataStruct_KdtNode<std::vector<Scalar>, int> KdtNode;
typedef DataStruct_KdtTree<std::vector<Scalar>, int> KdtTree; ///< 重命名

// KDT树的基本信息
struct TreeInfo
{
public:
    TreeInfo()
        : zoneID(-1),
          procID(-1),
          spaceMin({}),
          spaceMax({})
    {
    }
    TreeInfo(const int &zoneID_,
             const int &procID_,
             const std::vector<Scalar> &spaceMin_,
             const std::vector<Scalar> &spaceMax_)
        : zoneID(zoneID_),
          procID(procID_),
          spaceMin(spaceMin_),
          spaceMax(spaceMax_)
    {
    }
    ~TreeInfo()
    {
    }

public:
    int zoneID;                   // tree所属子域
    int procID;                   // tree所在进程
    std::vector<Scalar> spaceMax; // tree的上界
    std::vector<Scalar> spaceMin; // tree的下界

#if defined(_BaseParallelMPI_)
public:
    template <class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        ar & zoneID;
        ar & procID;
        ar & spaceMax;
        ar & spaceMin;
    }
#endif
};

class KDT
{
public:
    KDT(const int &dim);
    ~KDT();

    /**
     * @brief 为坐标点创建KD树，此坐标点可以为2D/3D的网格点、面心、体心等
     *
     * @param nodeList
     */
    void CreateNodeKDTree(const std::vector<std::pair<Face, std::vector<Node>>> &srcNodes);

    /**
     * @brief 与CreateNodeKDTree配套使用，传入待搜索的目标点坐标，返回与点云的最短距离和最近点编号
     *
     * @param tgtPoint
     * @return std::pair<Scalar, int>
     */
    std::pair<Scalar, int> SearchNearestNode(const Vector &tgtPoint);

    /**
     * @brief 为传入的网格单元建立KD树
     *
     * @param srcMesh
     */
    void CreateElementKDTree(const Mesh *srcMesh);

    



    void SearchElementInRegion();

    inline const std::vector<TreeInfo> &GetGlobalTreeInfo() { return globalTreeInfo; }

private:
    /**
     * @brief 计算网格单元的上下界，组成超维点，并填充data
     *
     * @param srcMesh
     * @param elemBounds
     * @param dataCloud
     */
    void ComputeElemBoundBox(const Mesh *srcMesh,
                             std::vector<std::vector<Scalar>> &elemBounds,
                             std::vector<int> &dataCloud);

public:

private:
    KdtTree *nodeTree;                                            ///< 坐标点KD树
    KdtTree *elemTree;                                            ///< 网格单元KD树
    std::vector<TreeInfo> globalTreeInfo; ///< 全局KD树的基本信息，用于并行搜索

    const int &dim;
    boost::mpi::communicator mpi_world;

    std::vector<Scalar> coor;           ///< 临时量，用于将Vector坐标值转换为有序空间
    std::pair<Scalar, int> nearestInfo; ///< 临时量，用于存储最邻近搜索得到的最短距离和面编号
    std::vector<Scalar> superCoor;      ///< 临时量，用于存储网格单元上下界构成的超维点
};
#endif