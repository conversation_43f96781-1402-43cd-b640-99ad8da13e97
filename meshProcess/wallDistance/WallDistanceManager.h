﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallDistanceManager.h
//! <AUTHOR>
//! @brief 计算壁面距离的管理器
//! @date  2021-5-12
//
//------------------------------修改日志----------------------------------------
//
// 2021-04-12 李艳亮，乔龙
// 说明：建立并规范化
//
//------------------------------------------------------------------------------
#ifndef _meshProcess_wallDistance_WallDistanceManager_
#define _meshProcess_wallDistance_WallDistanceManager_

#include "sourceFlow/configure/FlowConfigure.h"
#include "basic/configure/ConfigureMacro.h"
#include "basic/configure/ConfigureMacro.hxx"
#include "basic/mesh/SubMesh.h"
#include "meshProcess/wallDistance/WallDistanceADT.h"
#include "meshProcess/wallDistance/WallDistanceKDT.h"
#include "meshProcess/wallDistance/WallDistanceTraverse.h"

/**
 * @brief 近壁面距离类
 * 
 */
class WallDistanceManager
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in] type_ 壁面距离计算方法
	* @param[in] project_ 是否采用投影方式计算壁面距离
	*/
	WallDistanceManager(const Turbulence::WallDistance &type_ = Turbulence::WallDistance::ADT, const bool &project_ = false);;
    
	/**
	* @brief 析构函数
	*
	*/
    ~WallDistanceManager(){};

	/**
	* @brief 收集指定边界上的面元
	*
	* @param[in] mesh 网格指针
	* @param[in] flowConfigure 输入文件对象
	* @param[in] bType 边界类型（如是WALL,则收集所有的WALL类型）
	* @param[out] faceVector 面元（面信息、面的点构成）的集合
	* @param[in] zoneID 仅收集指定域号的网格(如果zoneID<0表示收集所有域的面)
	*/
	void CollectFace
		(Mesh *mesh,
		 const Configure::Flow::FlowConfigure &flowConfigure,
		 const Boundary::Type &bType,
		 std::vector<std::pair<Face, std::vector<Node>>> &faceVector,
		 const int &zoneID = -1);

	/**
	* @brief 计算壁面距离
	*
	* @param[in] mesh 网格
	* @param[in] wallFaceVector 面元（面信息、面的点构成）的集合
	* @param[out] distance 距离的存储
	* @param[out] fromFaceID 最小距离来自哪个面编号
	*/
	void Calculate(Mesh *mesh, 
		           const std::vector<std::pair<Face, std::vector<Node>>> &wallFaceVector,
		           std::vector<Scalar> &distance,
				   std::vector<int> &fromFaceID);

	/**
	* @brief 从文件读取壁面面元
	*
	* @param[in] filePathAndName 文件路径与名称
	*/
    void ReadWallFace(const std::string &filePathAndName);

private:
	/**
	* @brief 检查壁面距离
	*
	* @param[in] mesh 网格
	* @param[in] wallFaceVector 面元（面信息、面的点构成）的集合
	* @param[in,out] distance 距离的存储
	* @param[in,out] fromFaceID 最小距离来自哪个面编号
	*/
	void CheckDistance(Mesh *mesh, 
			           const std::vector<std::pair<Face, 
		               std::vector<Node>>> &wallFaceVector, 
		               std::vector<Scalar> &distance, std::vector<int> &fromFaceID);

private:
	const Turbulence::WallDistance &type; ///< 壁面距离计算方法
	const bool &project; ///< 是否采用投影方式计算壁面距离
};

#endif
