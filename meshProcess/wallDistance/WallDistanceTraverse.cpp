﻿#include "meshProcess/wallDistance/WallDistanceTraverse.h"

WallDistanceTraverse::WallDistanceTraverse(Mesh* mesh, const std::vector<std::pair<Face, std::vector<Node>>> &wallBoundaryFace_,
	std::vector<Scalar> &distance_, std::vector<int> &fromFaceID_, const bool &project_)
	: WallDistanceBase(mesh, wallBoundaryFace_, distance_, fromFaceID_, project_)
{}

void WallDistanceTraverse::Calculate()
{
    wallDistance.clear();
    wallDistance.resize(elementSize, -1.0);
	fromFaceID.resize(elementSize, -1);

    const int interval = std::max(elementSize / 10, 1);

    const bool mpiRank0Flag = (GetMPIRank() == 0);
    const int nWallBoundaryFace = wallBoundaryFace.size();
    for (int elementID = 0; elementID < elementSize; ++elementID)
    {
        if (mpiRank0Flag)
        {
            if ((elementID + 1) % interval == 0)
            {
                std::ostringstream stringStream;
                if ((elementID + 1) / interval == 1) stringStream << "\t";
                stringStream << std::setw(6) << std::setprecision(1) << std::fixed << (Scalar)(elementID + 1) / elementSize * 100.0 << "%";
                if ((elementID + 1) / interval == 10 || elementID + 1 == elementSize) stringStream << std::endl;
                Info::infoFile << stringStream.str();
                std::cout << stringStream.str();
            }
        }
        
        for (int index = 0; index < nWallBoundaryFace; ++index)
        {
            const Scalar wdst = CalculateFromElementTOFace(elementID, index);

			if(wdst >= 0)
            {
				if (wallDistance[elementID] < 0.0 || wallDistance[elementID] > wdst)
				{
					wallDistance[elementID] = wdst;
					fromFaceID[elementID] = index;
				}
			}
        }
    }
}