﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Sixdof.h
//! <AUTHOR>
//! @brief 六自由度运动类
//! @date  2024-1-20
//

#ifndef _meshProcess_Motion_Sixdof_
#define _meshProcess_Motion_Sixdof_

#include "meshProcess/motion/Motion.h"

//状态量
typedef struct var{
	//状态量"xg yg zg ug vg wg pb qb rb q0 q1 q2 q3"
	Vector xg;
	Vector vg;
	Vector pb;
	std::vector<double> q;
	//角度
	Vector degree;
	//弧度
	Vector rad;

	var(){
		q = std::vector<double>(4, 0);
	}

	Scalar norm(){
		Scalar res2 = (xg&xg) + (vg&vg) + (pb&pb) + q[0] * q[0] + q[1] * q[1] + q[2] * q[2] + q[3] * q[3];
		return sqrt(res2);
	}

}var;



//体轴系惯性量
typedef struct II{
	//惯性矩[kg * m^2]
	double Ixx;
	double Iyy;
	double Izz;
	double Ixy;
	double Ixz;
	double Iyz;

	Matrix Ib;
	Matrix Iinv;

	double I;
	double I1;
	double I2;
	double I3;
	double I4;
	double I5;
	double I6;
	double Pl;
	double Pm;
	double Pn;
	double Ppp;
	double Ppq;
	double Ppr;
	double Pqq;
	double Pqr;
	double Prr;
	double Ql;
	double Qm;
	double Qn;
	double Qpp;
	double Qpq;
	double Qpr;
	double Qqq;
	double Qqr;
	double Qrr;

	double Rl;
	double Rm;
	double Rn;
	double Rpp;
	double Rpq;
	double Rpr;
	double Rqq;
	double Rqr;
	double Rrr;

	II(){
	}
	void SetIner(const double *MomentOfInertia)
	{
		Ixx = MomentOfInertia[0];
		Iyy = MomentOfInertia[1];
		Izz = MomentOfInertia[2];
		Ixy = MomentOfInertia[3];
		Ixz = MomentOfInertia[4];
		Iyz = MomentOfInertia[5];
		Ib.Resize(3, 3);
		Ib.SetValue(0, 0, Ixx);
		Ib.SetValue(0, 1, -Ixy);
		Ib.SetValue(0, 2, -Ixz);
		Ib.SetValue(1, 0, -Ixy);
		Ib.SetValue(1, 1, Iyy);
		Ib.SetValue(1, 2, -Iyz);
		Ib.SetValue(2, 0, -Ixz);
		Ib.SetValue(2, 1, -Iyz);
		Ib.SetValue(2, 2, Izz);
		
//		Iinv = Ib.Inverse();   此运算报错，函数有问题
//3*3矩阵求逆
		Iinv.Resize(3, 3);
		double a1 = Ib.GetValue(0, 0);
		double b1 = Ib.GetValue(0, 1);
		double c1 = Ib.GetValue(0, 2);
		double a2 = Ib.GetValue(1, 0);
		double b2 = Ib.GetValue(1, 1);
		double c2 = Ib.GetValue(1, 2);
		double a3 = Ib.GetValue(2, 0);
		double b3 = Ib.GetValue(2, 1);
		double c3 = Ib.GetValue(2, 2);
		double k = 1 / (a1*(b2*c3 - c2*b3) - a2*(b1*c3 - c1*b3) + a3*(b1*c2 - b2*c1));
		Iinv.SetValue(0, 0, k*(b2*c3 - c2*b3));
		Iinv.SetValue(0, 1, k*(c1*b3 - b1*c3));
		Iinv.SetValue(0, 2, k*(c2*b1 - b2*c1));
		Iinv.SetValue(1, 0, k*(c2*a3 - a2*c3));
		Iinv.SetValue(1, 1, k*(c3*a1 - a3*c1));
		Iinv.SetValue(1, 2, k*(c1*a2 - a1*c2));
		Iinv.SetValue(2, 0, k*(b3*a2 - b2*a3));
		Iinv.SetValue(2, 1, k*(b1*a3 - b3*a1));
		Iinv.SetValue(2, 2, k*(b2*a1 - b1*a2));
	//	Iinv.Display();
		I = Ixx * Iyy * Izz - 2.0 * Ixy * Ixz * Iyz - Ixx * Iyz * Iyz - Iyy * Ixz * Ixz - Izz * Ixy * Ixy;
		I1 = Iyy * Izz - Iyz * Iyz;
		I2 = Ixy * Izz + Iyz * Ixz;
		I3 = Ixy * Iyz + Iyy * Ixz;
		I4 = Ixx * Izz - Ixz * Ixz;
		I5 = Ixx * Iyz + Ixy * Ixz;
		I6 = Ixx * Iyy - Ixy * Ixy;
		Pl = I1 / I;
		Pm = I2 / I;
		Pn = I3 / I;
		Ppp = -(Ixz * I2 - Ixy * I3) / I;
		Ppq = (Ixz * I1 - Iyz * I2 - (Iyy - Ixx) * I3) / I;
		Ppr = -(Ixy * I1 + (Ixx - Izz) * I2 - Iyz * I3) / I;
		Pqq = (Iyz * I1 - Ixy * I3) / I;
		Pqr = -((Izz - Iyy) * I1 - Ixy * I2 + Ixz * I3) / I;
		Prr = -(Iyz * I1 - Ixz * I2) / I;

		Ql = I2 / I;
		Qm = I4 / I;
		Qn = I5 / I;
		Qpp = -(Ixz * I4 - Ixy * I5) / I;
		Qpq = (Ixz * I2 - Iyz * I4 - (Iyy - Ixx) * I5) / I;
		Qpr = -(Ixy * I2 + (Ixx - Izz) * I4 - Iyz * I5) / I;
		Qqq = (Iyz * I2 - Ixy * I5) / I;
		Qqr = -((Izz - Iyy) * I2 - Ixy * I4 + Ixz * I5) / I;
		Qrr = -(Iyz * I2 - Ixz * I4) / I;

		Rl = I3 / I;
		Rm = I5 / I;
		Rn = I6 / I;
		Rpp = -(Ixz * I5 - Ixy * I6) / I;
		Rpq = (Ixz * I3 - Iyz * I5 - (Iyy - Ixx) * I6) / I;
		Rpr = -(Ixy * I3 + (Ixx - Izz) * I5 - Iyz * I6) / I;
		Rqq = (Iyz * I3 - Ixy * I6) / I;
		Rqr = -((Izz - Iyy) * I3 - Ixy * I5 + Ixz * I6) / I;
		Rrr = -(Iyz * I3 - Ixz * I5) / I;

	}
}II;





class Sixdof :public Motion
{
public:

	Sixdof(const std::vector<Package::FlowPackage *> flowPackageVector_,
		   const Configure::Flow::MotionStruct &motionStruct_);

	~Sixdof();

	//参数赋值
	void InitValues();

	//角度转弧度
	Vector DegreeToRad(Vector&);

	//弧度转角度
	Vector RadToDegree(Vector&);

	//计算从大地系转换本体系的转换矩阵
	void CalTbg(Vector&);

	//计算从本体系转换大地系的转换矩阵
	void CalTgb();

	//计算从大地系转换本体系的转换矩阵
	void CalTbg();

	//根据本体系转换大地系的转换矩阵计算四元数
	void CalQuaternion(double*);

	//判断符号
	int Sgn(double);

	//设定自由度
	void SetFreeAndFixedDof(std::vector<int>free_degree = std::vector<int>{1,1,1,1,1,1});

	//得到当前时间步数
	inline const int& GetActTimestep(){ return act_timestep; }

	//得到当前时间
	inline const double& GetTime(){ return time; }
	
	//得到运动节点名称 
	inline const std::string& GetNodeName(){ return motionName; }

	//设定外力、力矩
	void SetExternalFM(double Fxb_external, double Fyb_external, double Fzb_external,
						double Mxb_external, double Myb_external, double Mzb_external);

	//设定气动力、力矩
	void SetCFDFM(std::vector<double>cfdfm);


	//得到运动数据用于CFD计算
	inline const std::vector<double>& GetMotionData();

	
	//根据气动力初始运动量残值
	void InitDofFM();


	//运动量更新
	void VarSwap();


	//物理时间推进
	void AdvanceTime();

	//对于不同阶数的bdf方法根据当前步数进行调整
	std::string GetPossibleBdf(std::string bdftype);

	//打开输出文件并将开头写下
	void OpenOutputFileAndWriteHead();

	
	//在输出文件添加数据
	void AddDataToOutputFile();


	//计算一个物理时间步
	void ComputeOnePysicalTimestep(std::string& BDFformular);

	
	//返回欧拉角
	std::pair<std::vector<double>::iterator, std::vector<double>::iterator> GetEulerAngle();

	
	//返回坐标
	std::pair<std::vector<double>::iterator, std::vector<double>::iterator> GetXYZ();


	//返回速度
	std::pair<std::vector<double>::iterator, std::vector<double>::iterator> GetVelocity();


	//返回状态量
	var& GetMotionData(std::string);

	
	//初始化力、力矩，并基于此初始化残差
	void InitForceMomentsResidual(std::vector<double>& cfd_fm);


	//计算对偶时间推进残值
	var CalDualTimeResidualBdf(std::string& BDFformular);


	//得到隐式格式系数
	void GetBDFCofficient(double& a0, double&a1, double& a2, double& a3
		, double& b0, double& b1,std::string& BDFformular);


	//将体轴气动力转为大地系，并计算合外力
	void SetForceMoments(std::vector<double>& cfd_fm, std::vector<double>&f);

	
	//计算残差
	void CalRes(var& vi,std::vector<double>& fi,var& resi);


	//进行牛顿迭代求解
	void NewtonStep(std::string& BDFformular);

	//计算双时间雅克比矩阵
	Matrix* CalDualTimeJacobian(std::string& BDFformular);


	//计算雅克比矩阵
	Matrix* CalJocobian(std::string& BDFformular);


	//LU分解方法求线性方程组算残差，f'x*delta_x=-fx,每一步对应牛顿迭代一步
	void LinalgSolve(Matrix* Jtilde);


	//根据四元数计算从本体转到大地系的转换矩阵
	void CalTransMatrixFromQuaternions();


	//根据转换矩阵计算欧拉角
	void CalEulerAnglesFromTransMatrix();


	//四元数标准化
	void NormalizeQuaternions();


	//返回BDF格式名称
	inline std::string& GetBdfType(){ return this->BDFtype; }


	//返回修正步数
	inline const int &GetCorrectorSteps()const { return this->CorrectorSteps; }


	//检查是否收敛
	bool CheckConvergence();


	/**
	* @brief 网格更新
	*
	*/
	virtual void MeshUpdate();

	/**
	* @brief 网格运动相关的场更新
	*
	*/
	virtual void FieldUpdate();

	


private:
	//体轴外力
	double force_b[6];
	//time level "n+1"
	var v0;
	//time level "n"
	var v1;
	//time level "n-1"
	var v2;
	//time level "n-2"
	var v3;
	var deltavar;
	var deltavar_ini;
	var res0;
	var res1;
	//双时间步残差
	var dual_res;
	var dual_res_ini;
	//不同时间层气动力
	std::vector<double>f0;
	std::vector<double>f1;
	std::vector<double>f2;
	std::vector<double>f3;
	//大地转到体轴
	Matrix Tgb;
	//体轴转到大地
	Matrix Tbg;
	//时间步长
	double dt;
	//当前时间
	double time;
	//当前时间步数
	int act_timestep;
	//体轴额外受力如推进力
	std::vector<double> f_ext;
	//质量kg
	double mass;
	//惯性矩[kg * m^2]
	II Ix;
	//大地轴系下质心m
	Vector centerg; 
	//初始欧拉角°
	Vector degree;
	//大地轴系下初始速度
	Vector velocityg;
	//大地轴系下初始角速度
	double pb; 
	double qb;
	double rb;
	//大地轴系加速度
	double gxg;
	double gyg;
	double gzg;
	//时间积分格式
	std::string BDFtype;
	//自由度
	std::vector<int>free_degrees;
	//第一次内迭代判断
	bool firstinnerIteration;
	//内循环步数
	int n_innersteps;
	//全部内迭代步数
	int totalInnerIters;

	//修正步数
	int CorrectorSteps;
};

#endif