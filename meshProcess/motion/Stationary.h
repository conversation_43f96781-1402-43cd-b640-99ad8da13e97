﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Stationary.h
//! <AUTHOR>
//! @brief 平移旋转运动类
//! @date  2023-12-15
//
//------------------------------修改日志----------------------------------------
//
// 2023-12-15 曾凯
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_Motion_Stationary_
#define _meshProcess_Motion_Stationary_

#include "meshProcess/motion/Motion.h"

class Stationary :public Motion
{
public:
	/**
	* @brief constructor
	*
	*/
	Stationary(const std::vector<Package::FlowPackage *> flowPackageVector_);
	/**
	* @brief destructor
	*
	*/
	~Stationary();

	/**
	* @brief 获取平移速度
	*
	*/
	inline const Vector& GetTranslationVelocity(){ return this->translationVelocity; }

	/**
	* @brief 网格更新
	*
	*/
	virtual void MeshUpdate();

	/**
	* @brief 网格运动相关的场更新
	*
	*/
	virtual void FieldUpdate();

private:
	const Vector translationVelocity;

};
#endif
