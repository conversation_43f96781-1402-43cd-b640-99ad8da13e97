﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file TranslationRotation.h
//! <AUTHOR>
//! @brief 平移旋转运动类
//! @date  2023-11-10
//
//------------------------------修改日志----------------------------------------
//
// 2023-11-10 曾凯
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_Motion_TranslationRotation_
#define _meshProcess_Motion_TranslationRotation_

#include "meshProcess/motion/Motion.h"

class TranslationRotation:public Motion
{
public:
	/**
	* @brief constructor
	*
	*/
	TranslationRotation(const std::vector<Package::FlowPackage *> flowPackageVector_ , 
		const Configure::Flow::MotionStruct &motionStruct_);
	/**
	* @brief destructor
	*
	*/
	~TranslationRotation();

	/**
	* @brief 获取平移速度
	*
	*/
	inline const Vector& GetTranslationVelocity(){ return translationVelocity; }

	/**
	* @brief 获取旋转坐标轴原点
	*
	*/
	inline const Vector& GetRotationAxisOrigin(){ return rotationAxisOrigin; }

	/**
	* @brief 获取旋转坐标轴方向
	*
	*/
	inline const Vector& GetRotationAxisDirection(){ return rotationAxisDirection; }

	/**
	* @brief 获取旋转角速度，rad
	*
	*/
	inline const Scalar& GetRotationRate(){ return rotationRate; }


	/**
	* @brief 网格更新
	*
	*/
	virtual void MeshUpdate();

	/**
	* @brief 网格运动相关的场更新
	*
	*/
	virtual void FieldUpdate();


private:


	Vector translationVelocity;
	Vector rotationAxisOrigin;
	Vector rotationAxisDirection;
	Scalar rotationRate;





	/**
	* @brief 设定平移速度
	*
	*/
	void SetTranslationVelocity(Vector &velocity) { this->translationVelocity = velocity; }

	/**
	* @brief 设定旋转轴原点
	*
	*/
	void SetRotationAxisOrigin(Vector &axisOrigin) { this->rotationAxisOrigin = axisOrigin; }

	/**
	* @brief 设定旋转轴方向矢量
	*
	*/
	void SetRotationAxisDirection(Vector &axisDirection){ this->rotationAxisDirection = axisDirection; }

	/**
	* @brief 设定旋转角速度，rad/s
	*
	*/
	void SetRotationRate(Scalar &rotationRate) { this->rotationRate = rotationRate; }

};
#endif
