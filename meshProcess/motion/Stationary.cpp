﻿#include "meshProcess/motion/Stationary.h"

Stationary::Stationary(const std::vector<Package::FlowPackage *> flowPackageVector_)
	:Motion(flowPackageVector_), translationVelocity(Vector0)
{
	Mesh *localMesh = flowPackageVector[0]->GetMeshStruct().mesh;
	//填充网格速度场
	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		flowPackageVector[0]->GetField().meshVelocity->SetValue(elemID, { 0, 0, 0 });
	}
}

Stationary::~Stationary()
{

}

void Stationary::MeshUpdate()
{
//无需更新
}

void Stationary::FieldUpdate()
{
	//无需更新
}