﻿#include "meshProcess/motion/Sixdof.h"

var operator +(const var& A, const var& B){
	var C;
	C.xg = A.xg + B.xg;
	C.vg = A.vg + B.vg;
	C.pb = A.pb + B.pb;
	for (int i = 0; i < 4; i++){
		C.q[i] = A.q[i] + B.q[i];
	}
	return C;
	//	C.degree = A.degree + B.degree;
	//	C.rad = A.rad + B.rad;
}

var operator -(const var& A, const var& B){
	var C;
	C.xg = A.xg - B.xg;
	C.vg = A.vg - B.vg;
	C.pb = A.pb - B.pb;
	for (int i = 0; i < 4; i++){
		C.q[i] = A.q[i] - B.q[i];
	}
	//	C.degree = A.degree - B.degree;
	//	C.rad = A.rad - B.rad;
	return C;
}


var operator *(const var& A, const double &B){
	var C;
	C.xg = A.xg * B;
	C.vg = A.vg * B;
	C.pb = A.pb * B;
	for (int i = 0; i < 4; i++){
		C.q[i] = A.q[i] * B;
	}
	return C;
	//	C.degree = A.degree * B;
	//	C.rad = A.rad * B;
}

var operator *(const double& A, const var &B){
	var C;
	C.xg = B.xg * A;
	C.vg = B.vg * A;
	C.pb = B.pb * A;
	for (int i = 0; i < 4; i++){
		C.q[i] = B.q[i] * A;
	}
	return C;
	//	C.degree = B.degree * A ;
	//	C.rad = B.rad * A;
}



Sixdof::Sixdof(const std::vector<Package::FlowPackage *> flowPackageVector_,
	const Configure::Flow::MotionStruct &motionStruct_)
	:Motion(flowPackageVector_)
{
	if (GetMPIRank() == 0) Print("\nInitializing Sixdof ...");
	f0=f1=f2=f3=std::vector<double>(6, 0);
	Tgb.Resize(3, 3);
	Tbg = Tgb;
	free_degrees = std::vector<int>(6, 1);
	dt = 0.001;
	time = motionStruct_.xdof.releaseTime;
	act_timestep = 0;
	f_ext = std::vector<double>(6, 0);
	firstinnerIteration = false;
	n_innersteps = 50;
	totalInnerIters = 0;
	mass = motionStruct_.xdof.mass;
	Ix.SetIner(motionStruct_.xdof.MomentOfInertia);
	centerg = motionStruct_.xdof.CenterOfMass;
	degree = motionStruct_.xdof.degree;
	velocityg = motionStruct_.xdof.initialVelocity;
	gzg = 9.81;
	BDFtype = "TRAPEZ";
	InitValues();
	SetFreeAndFixedDof();
}

Vector Sixdof::DegreeToRad(Vector& degree){
	 Vector rad = degree / (180 / PI);
	 return rad;
}

Vector Sixdof::RadToDegree(Vector& rad){
	Vector degree = rad * 180 / PI;
	return degree;
}


void Sixdof::InitValues(){
	Vector rad = DegreeToRad(degree);
	CalTbg(rad);
	CalTgb();
	double quat[4];
	CalQuaternion(quat);
	v0.xg = v1.xg = v2.xg = v3.xg = centerg;
	v0.vg = v1.vg = v2.vg = v3.vg = velocityg;
	v0.pb = v1.pb = v2.pb = v3.pb = degree;
	for (int i = 0; i < 4; i++){
		v0.q[i] = v1.q[i] = v2.q[i] = v3.q[i] = quat[i];
	}
	v0.degree = v1.degree = v2.degree = v3.degree = degree;
	v0.rad = v1.rad = v2.rad = v3.rad = rad;
}


void Sixdof::CalTbg(Vector& rad){
	double cosphi = cos(rad.X());
	double sinphi = sin(rad.X());
	double costheta = cos(rad.Y());
	double sintheta = sin(rad.Y());
	double cospsi = cos(rad.Z());
	double sinpsi = sin(rad.Z());

	Tbg.SetValue(0, 0, costheta * cospsi);
	Tbg.SetValue(0, 1, costheta * sinpsi);
	Tbg.SetValue(0, 2, -sintheta);
	Tbg.SetValue(1, 0, sinphi * sintheta * cospsi - cosphi * sinpsi);
	Tbg.SetValue(1, 1, sinphi * sintheta * sinpsi + cosphi * cospsi);
	Tbg.SetValue(1, 2, sinphi * costheta);
	Tbg.SetValue(2, 0, cosphi * sintheta * cospsi + sinphi * sinpsi);
	Tbg.SetValue(2, 1, cosphi * sintheta * sinpsi - sinphi * cospsi);
	Tbg.SetValue(2, 2, cosphi * costheta);	
}

void Sixdof::CalTgb(){
	for (int i = 0; i < 3; i++){
		for (int j = 0; j < 3; j++){
			Tgb.SetValue(i, j, Tbg.GetValue(j, i));
		}
	}
}

void Sixdof::CalTbg(){
	for (int i = 0; i < 3; i++){
		for (int j = 0; j < 3; j++){
			Tbg.SetValue(i, j, Tgb.GetValue(j, i));
		}
	}
}

void Sixdof::CalQuaternion(double* quat){
	quat[0] = 0.5*sqrt(std::max(0.0, 1 + Tgb.GetValue(0, 0) + Tgb.GetValue(1, 1) + Tgb.GetValue(2, 2)));
	quat[1] = 0.5*sqrt(std::max(0.0, 1 + Tgb.GetValue(0, 0) - Tgb.GetValue(1, 1) - Tgb.GetValue(2, 2)));
	quat[2] = 0.5*sqrt(std::max(0.0, 1 - Tgb.GetValue(0, 0) + Tgb.GetValue(1, 1) - Tgb.GetValue(2, 2)));
	quat[3] = 0.5*sqrt(std::max(0.0, 1 - Tgb.GetValue(0, 0) - Tgb.GetValue(1, 1) + Tgb.GetValue(2, 2)));
	quat[1] = fabs(quat[1]) * Sgn(Tgb.GetValue(2, 1) - Tgb.GetValue(1, 2));
	quat[2] = fabs(quat[2]) * Sgn(Tgb.GetValue(0, 2) - Tgb.GetValue(2, 0));
	quat[3] = fabs(quat[3]) * Sgn(Tgb.GetValue(1, 0) - Tgb.GetValue(0, 1));
}

int Sixdof::Sgn(double input){
	int sgn = 0;
	if (input > 0){
		sgn = 1;
	}
	else if (input < 0){
		sgn = -1;
	}
	return sgn;
}


void Sixdof::SetFreeAndFixedDof(std::vector<int>free_degree){
	this->free_degrees = free_degree;
}

/*
std::pair<std::vector<double>::iterator, std::vector<double>::iterator> Sixdof::GetEulerAngle(){
	auto beginit = v0.begin() + 16;
	auto endit = beginit + 3;
	return std::make_pair(beginit, endit);
}


std::pair<std::vector<double>::iterator, std::vector<double>::iterator> GetXYZ(){
	auto beginit = v0.begin();
	auto endit = beginit + 3;
	return std::make_pair(beginit, endit);
}
*/

var & Sixdof::GetMotionData(std::string level){
	if (level == "actual")
		return v0;
	else if (level == "old")
		return v1;
	else if (level == "oold")
		return v2;
	else
		return v3;
}

void Sixdof::AdvanceTime(){
	act_timestep += 1;
	time += dt;
}

void Sixdof::VarSwap(){
	v3 = v2;
	v2 = v1;
	v1 = v0;
	res1 = res0;
	f3 = f2;
	f2 = f1;
	f1 = f0;
}



void Sixdof::SetForceMoments(std::vector<double>& cfd_forcesmoments, std::vector<double>&f){
	Matrix F(3, 1),F_ext(3,1);
	for (int i = 0; i < 3; i++){
		F.SetValue(i, 0, cfd_forcesmoments[i]);
		F_ext.SetValue(i, 0, f_ext[i]);
	}
	F = this->Tgb*F;
	F_ext = this->Tgb*F_ext;
	f[0] = F.GetValue(0, 0) + F_ext.GetValue(0, 0) + gxg*mass;
	f[1] = F.GetValue(1, 0) + F_ext.GetValue(1, 0) + gyg*mass;
	f[2] = F.GetValue(2, 0) + F_ext.GetValue(2, 0) + gzg*mass;
	f[3] = cfd_forcesmoments[3] + f_ext[3];
	f[4] = cfd_forcesmoments[4] + f_ext[4];
	f[5] = cfd_forcesmoments[5] + f_ext[5];
}

void Sixdof::CalRes(var& vi, std::vector<double>& fi, var& resi){
	//res of x y z
	resi.xg.SetX(vi.vg.X());
	resi.xg.SetY(vi.vg.Y());
	resi.xg.SetZ(vi.vg.Z());

	//res of u v w
	resi.vg.SetX(fi[0] / mass);
	resi.vg.SetY(fi[1] / mass);
	resi.vg.SetZ(fi[2] / mass);

	//res of pqr
	Scalar p = vi.pb.X();
	Scalar q = vi.pb.Y();
	Scalar r = vi.pb.Z();
	Scalar L = fi[3];
	Scalar M = fi[4];
	Scalar N = fi[5];
	resi.pb.SetX(Ix.Ppp * p * p + Ix.Ppq * p * q + Ix.Ppr * p * r + Ix.Pqr * q * r + Ix.Prr * r * r 
		+ Ix.Pl * L + Ix.Pm * M + Ix.Pn * N);

	resi.pb.SetY(Ix.Qpp * p * p + Ix.Qpq * p * q + Ix.Qpr * p * r + Ix.Qqq * q * q + Ix.Qqr * q * r 
		+ Ix.Qrr * r * r + Ix.Ql * L + Ix.Qm * M + Ix.Qn * N);

	resi.pb.SetZ(Ix.Rpp * p * p + Ix.Rpq * p * q + Ix.Rpr * p * r + Ix.Rqq * q * q + Ix.Rqr * q * r 
		+ Ix.Rrr * r * r + Ix.Rl * L + Ix.Rm * M + Ix.Rn * N);


	//res of q0 q1 q2 q3
	Scalar q0 = vi.q[0];
	Scalar q1 = vi.q[1];
	Scalar q2 = vi.q[2];
	Scalar q3 = vi.q[3];
	resi.q[0] = 0.5 * (-q1 * p - q2 * q - q3 * r);
	resi.q[1] = 0.5 * (q0 * p - q3 * q + q2 * r);
	resi.q[2] = 0.5 * (q3 * p + q0 * q - q1 * r);
	resi.q[3] = 0.5 * (-q2 * p + q1 * q + q0 * r);
}

void Sixdof::InitForceMomentsResidual(std::vector<double>& cfd_fm){
	std::vector<double>cfd_forcesmoments(6);
	for (int i = 0; i < 6; i++){
		cfd_forcesmoments[i] = cfd_fm[i] * free_degrees[i];
	}
	SetForceMoments(cfd_forcesmoments,f0);
	SetForceMoments(cfd_forcesmoments, f1);

	CalRes(v1, f1, res1);
	
}


var Sixdof::CalDualTimeResidualBdf(std::string& BDFformular){
	double a0, a1, a2, a3, b0, b1;
	GetBDFCofficient(a0, a1, a2, a3, b0, b1, BDFformular);
	var dual_res = 1. / dt * (a0 * v0 + a1 * v1 + a2 * v2 + a3 * v3) - b0 * res0 - b1 * res1;
	return dual_res;
}


void Sixdof::GetBDFCofficient(double& a0, double&a1, double& a2, double& a3, double& b0
	                         , double& b1,  std::string& BDFformular)
{
	if(BDFformular == "BDF1"){
		a0 = 1.0;
		a1 = -1.0;
		a2 = 0.0;
		a3 = 0.0;
		b0 = 1.0;
		b1 = 0.0;
	}
	else if (BDFformular == "BDF2"){
		a0 = 1.5;
		a1 = -2.0;
		a2 = 0.5;
		a3 = 0.0;
		b0 = 1.0;
		b1 = 0.0;
	}
	else if (BDFformular == "BDF3"){
		a0 = 11.0 / 6.0;
		a1 = -3.0;
		a2 = 1.5;
		a3 = -1.0 / 3.0;
		b0 = 1.0;
		b1 = 0.0;
	}
	else if (BDFformular == "TRAPEZ"){
		a0 = 1.0;
		a1 = -1.0;
		a2 = 0.0;
		a3 = 0.0;
		b0 = 0.5;
		b1 = 0.5;
	}
	else{
		FatalError("BDFformular not exist");
	}
}

Matrix* Sixdof::CalJocobian(std::string& BDFformular){
	double a0, a1, a2, a3, b0, b1;
	GetBDFCofficient(a0, a1, a2, a3, b0, b1, BDFformular);

	Matrix* Jocobian = new Matrix(13,13);
	Jocobian->SetZero();
	
	double I11 = Ix.Ib.GetValue(0, 0);
	double I12 = Ix.Ib.GetValue(0, 1);
	double I13 = Ix.Ib.GetValue(0, 2);
	double I21 = Ix.Ib.GetValue(1, 0);
	double I22 = Ix.Ib.GetValue(1, 1);
	double I23 = Ix.Ib.GetValue(1, 2);
	double I31 = Ix.Ib.GetValue(2, 0);
	double I32 = Ix.Ib.GetValue(2, 1);
	double I33 = Ix.Ib.GetValue(2, 2); 

	double I11inv = Ix.Iinv.GetValue(0, 0);
	double I12inv = Ix.Iinv.GetValue(0, 1);
	double I13inv = Ix.Iinv.GetValue(0, 2);
	double I21inv = Ix.Iinv.GetValue(1, 0);
	double I22inv = Ix.Iinv.GetValue(1, 1);
	double I23inv = Ix.Iinv.GetValue(1, 2);
	double I31inv = Ix.Iinv.GetValue(2, 0);
	double I32inv = Ix.Iinv.GetValue(2, 1);
	double I33inv = Ix.Iinv.GetValue(2, 2);

	double p = v0.pb.X();
	double q = v0.pb.Y();
	double r = v0.pb.Z();

	double q0 = v0.q[0];
	double q1 = v0.q[1];
	double q2 = v0.q[2];
	double q3 = v0.q[3];

	Jocobian->SetValue(0, 3, 1.0);
	Jocobian->SetValue(1, 4, 1.0);
	Jocobian->SetValue(2, 5, 1.0);
	Jocobian->SetValue(6, 6, I11 * I12inv * r + I11inv * I31 * q + I13inv * I22 * q + I13inv * I23 * r
					  - I11 * I13inv * q - I11inv * I21 * r - I12inv * I32 * q - I12inv * I33 * r
					  - 2 * I12inv * I31 * p + 2 * I13inv * I21 * p);
	Jocobian->SetValue(6, 7, I11inv * I31 * p + I11inv * I33 * r + I12 * I12inv * r + I13inv * I22 * p 
					  - I11 * I13inv * p - I11inv * I22 * r - I12inv * I32 * p - I13 * I13inv * r 
					  - 2 * I12 * I13inv * q + 2 * I11inv * I32 * q);
	Jocobian->SetValue(6, 8, I11 * I12inv * p + I11inv * I33 * q + I12 * I12inv * q + I13inv * I23 * p
					  - I11inv * I21 * p - I11inv * I22 * q - I12inv * I33 * p - I13 * I13inv * q
		              - 2 * I11inv * I23 * r + 2 * I12inv * I13 * r);
	Jocobian->SetValue(7, 6, I11 * I22inv * r + I21inv * I31 * q + I22 * I23inv * q + I23 * I23inv * r 
					  - I11 * I23inv * q - I21 * I21inv * r - I22inv * I32 * q - I22inv * I33 * r 
					  - 2 * I22inv * I31 * p + 2 * I21 * I23inv * p);
	Jocobian->SetValue(7, 7, I12 * I22inv * r + I21inv * I31 * p + I21inv * I33 * r + I22 * I23inv * p 
					  - I11 * I23inv * p - I13 * I23inv * r - I21inv * I22 * r - I22inv * I32 * p 
		              - 2 * I12 * I23inv * q + 2 * I21inv * I32 * q);
	Jocobian->SetValue(7, 8, I11 * I22inv * p + I12 * I22inv * q + I21inv * I33 * q + I23 * I23inv * p 
					  - I13 * I23inv * q - I21 * I21inv * p - I21inv * I22 * q - I22inv * I33 * p 
					  - 2 * I21inv * I23 * r + 2 * I13 * I22inv * r);
	Jocobian->SetValue(8, 6, I11 * I32inv * r + I22 * I33inv * q + I23 * I33inv * r + I31 * I31inv * q 
					  - I11 * I33inv * q - I21 * I31inv * r - I32 * I32inv * q - I32inv * I33 * r 
					  - 2 * I31 * I32inv * p + 2 * I21 * I33inv * p);
	Jocobian->SetValue(8, 7, I12 * I32inv * r + I22 * I33inv * p + I31 * I31inv * p + I31inv * I33 * r 
					  - I11 * I33inv * p - I13 * I33inv * r - I22 * I31inv * r - I32 * I32inv * p 
					  - 2 * I12 * I33inv * q + 2 * I31inv * I32 * q);
	Jocobian->SetValue(8, 8, I11 * I32inv * p + I12 * I32inv * q + I23 * I33inv * p + I31inv * I33 * q 
					  - I13 * I33inv * q - I21 * I31inv * p - I22 * I31inv * q - I32inv * I33 * p 
					  - 2 * I23 * I31inv * r + 2 * I13 * I32inv * r);
	Jocobian->SetValue(9, 6, -0.5 * q1);
	Jocobian->SetValue(9, 7, -0.5 * q2);
	Jocobian->SetValue(9, 8, -0.5 * q3);
	Jocobian->SetValue(9, 10, -0.5 * p);
	Jocobian->SetValue(9, 11, -0.5 * q);
	Jocobian->SetValue(9, 12, -0.5 * r);
	Jocobian->SetValue(10, 6, 0.5 * q0);
	Jocobian->SetValue(10, 7, -0.5 * q3);
	Jocobian->SetValue(10, 8, 0.5 * q2);
	Jocobian->SetValue(10, 9, 0.5 * p);
	Jocobian->SetValue(10, 11, 0.5 * r);
	Jocobian->SetValue(10, 12, -0.5 * q);
	Jocobian->SetValue(11, 6, 0.5 * q3);
	Jocobian->SetValue(11, 7, 0.5 * q0);
	Jocobian->SetValue(11, 8, -0.5 * q1);
	Jocobian->SetValue(11, 9, 0.5 * q);
	Jocobian->SetValue(11, 10, -0.5 * r);
	Jocobian->SetValue(11, 12, 0.5 * p);
	Jocobian->SetValue(12, 6, -0.5 * q2);
	Jocobian->SetValue(12, 7, 0.5 * q1);
	Jocobian->SetValue(12, 8, 0.5 * q0);
	Jocobian->SetValue(12, 9, 0.5 * r);
	Jocobian->SetValue(12, 10, 0.5 * q);
	Jocobian->SetValue(12, 11, -0.5 * p);


	return Jocobian;
}


Matrix* Sixdof::CalDualTimeJacobian(std::string& BDFformular){
	double a0, a1, a2, a3, b0, b1;
	GetBDFCofficient(a0, a1, a2, a3, b0, b1, BDFformular);

	Matrix* Jocobian = CalJocobian(BDFformular);
	Matrix* Jtilde = new Matrix(13, 13);
	Jtilde->SetZero();
	for (int i = 0; i < 13; i++){
		Jtilde->SetValue(i, i, a0 / dt * 1);
	}
	*Jtilde = *Jtilde - b0* (*Jocobian);
	
	delete Jocobian;

	return Jtilde;
	
}

void Sixdof::LinalgSolve(Matrix* Jtilde){
	var Rhs = dual_res * (-1);
	Scalar b[13];
	b[0] = Rhs.xg.X();
	b[1] = Rhs.xg.Y();
	b[2] = Rhs.xg.Z();
	b[3] = Rhs.vg.X();
	b[4] = Rhs.vg.Y();
	b[5] = Rhs.vg.Z();
	b[6] = Rhs.pb.X();
	b[7] = Rhs.pb.Y();
	b[8] = Rhs.pb.Z();
	b[9] = Rhs.q[0]; 
	b[10] = Rhs.q[1];
	b[11] = Rhs.q[2];
	b[12] = Rhs.q[3];
	double l[13][13] = {0};
	double u[13][13] = {0};
	for (int i = 1; i <= 13; i++){
		u[0][i - 1] = Jtilde->GetValue(0, i - 1);
		l[i - 1][0] = Jtilde->GetValue(i - 1, 0) / u[0][0];
	}
	for (int r = 1; r < 13; r++){
		for (int i = r; i < 13; i++){
			double sum1 = 0;
			for (int k = 0; k < r; k++){
				sum1 = sum1 + l[r][k] * u[k][i];
			}
			u[r][i] = Jtilde->GetValue(r, i) - sum1;
		}
		if (r != 13){
			for (int i=r+1;i<13; i++){
				double sum2 = 0;
				for (int k = 0; k < r; k++){
					sum2 = sum2 + l[i][k] * u[k][r];
				}
				l[i][r] = (Jtilde->GetValue(i, r) - sum2) / u[r][r];
			}
		}
	}
	Scalar y[13] = { 0 };
	y[0] = b[0];
	for (int i = 1; i < 13; i++){
		double sum3 = 0;
		for (int k = 0; k < i; k++){
			sum3 = sum3+ l[i][k] * y[k];
		}
		y[i] = b[i] - sum3;
	}
	double x[13] = { 0 };
	x[12] = y[12] / u[12][12];
	for (int i = 11; i >= 0; i--){
		double sum4 = 0;
		for (int k = (i + 1); k < 13; k++){
			sum4 = sum4 + u[i][k] * x[k];
		}
		x[i] = (y[i] - sum4) / u[i][i];
	}
	
	deltavar.xg.SetX(x[0]);
	deltavar.xg.SetY(x[1]);
	deltavar.xg.SetZ(x[2]);
	deltavar.vg.SetX(x[3]);
	deltavar.vg.SetY(x[4]);
	deltavar.vg.SetZ(x[5]);
	deltavar.pb.SetX(x[6]);
	deltavar.pb.SetY(x[7]);
	deltavar.pb.SetZ(x[8]);
	deltavar.q[0] = x[9];
	deltavar.q[1] = x[10];
	deltavar.q[2] = x[11];
	deltavar.q[3] = x[12];
	
	v0 = v0 + deltavar;
}

void Sixdof::NewtonStep(std::string& BDFformular){
	Matrix* Jtilde = CalDualTimeJacobian(BDFformular);
//	Jtilde->Display();
	LinalgSolve(Jtilde);
	delete Jtilde;
}



void Sixdof::NormalizeQuaternions(){
	double& q0 = v0.q[0];
	double& q1 = v0.q[1];
	double& q2 = v0.q[2];
	double& q3 = v0.q[3];

	double qmag2 = q0 * q0 + q1 * q1 + q2 * q2 + q3 * q3;
	double eps = 2.107342e-08;
	if (fabs(1 - qmag2) < eps){
		q0 = q0 * 2 / (1 + qmag2);
		q1 = q1 * 2 / (1 + qmag2);
		q2 = q2 * 2 / (1 + qmag2);
		q3 = q3 * 2 / (1 + qmag2);
	}
	else{
		double qmag = sqrt(qmag2);
		q0 = q0 * 1 / qmag;
		q1 = q1 * 1 / qmag;
		q2 = q2 * 1 / qmag;
		q3 = q3 * 1 / qmag;
	}

}


bool Sixdof::CheckConvergence(){
	bool convergenced = false;
	Scalar dual_res_norm = dual_res.norm();
//	Scalar dual_res_ini_norm = dual_res_ini.norm();
	if (dual_res_norm < 1e-12){
		convergenced = true;
	}
	return convergenced;
}




void Sixdof::ComputeOnePysicalTimestep(std::string& BDFformular){
	firstinnerIteration = true;
	for (int i = 0; i < n_innersteps; i++){
		CalRes(v0, f0, res0);
		dual_res = CalDualTimeResidualBdf(BDFformular);

		NewtonStep(BDFformular);

		CalTransMatrixFromQuaternions();
		CalTbg();
		CalEulerAnglesFromTransMatrix();

		totalInnerIters += 1;

		if (firstinnerIteration){
			dual_res_ini = dual_res;
			deltavar_ini = deltavar;
		}

		firstinnerIteration = false;

		if (CheckConvergence()){
			break;
		}

	}
	NormalizeQuaternions();
}


void Sixdof::CalTransMatrixFromQuaternions(){
	this->Tgb.SetZero();

	double q0 = v0.q[0];
	double q1 = v0.q[1];
	double q2 = v0.q[2];
	double q3 = v0.q[3];

	Tgb.SetValue(0, 0, 2.0 * (q0 * q0 + q1 * q1 - 0.5));
	Tgb.SetValue(0, 1, 2.0 * (q1 * q2 - q0 * q3));
	Tgb.SetValue(0, 2, 2.0 * (q1 * q3 + q0 * q2));
	Tgb.SetValue(1, 0, 2.0 * (q1 * q2 + q0 * q3));
	Tgb.SetValue(1, 1, 2.0 * (q0 * q0 + q2 * q2 - 0.5));
	Tgb.SetValue(1, 2, 2.0 * (q2 * q3 - q0 * q1));
	Tgb.SetValue(2, 0, 2.0 * (q1 * q3 - q0 * q2));
	Tgb.SetValue(2, 1, 2.0 * (q2 * q3 + q0 * q1));
	Tgb.SetValue(2, 2, 2.0 * (q0 * q0 + q3 * q3 - 0.5));
}


void Sixdof::CalEulerAnglesFromTransMatrix(){
	double phi, theta, psi;
	if (1 - fabs(this->Tbg.GetValue(0, 2)) > SMALL){
		theta = -asin(Tbg.GetValue(0, 2));
		psi = atan2(Tbg.GetValue(0, 1) / cos(theta), Tbg.GetValue(0, 0) / cos(theta));
		phi = atan2(Tbg.GetValue(1, 2) / cos(theta), Tbg.GetValue(2, 2) / cos(theta));
	}
	else{
		phi = 0.0;
		if (fabs(1 + Tbg.GetValue(0, 2)) < SMALL){
			theta = PI / 2;
			psi = phi + atan2(Tbg.GetValue(0, 1), Tbg.GetValue(2, 0));
		}
		else{
			theta = -PI / 2;
			psi = -phi + atan2(-Tbg.GetValue(1, 0), -Tbg.GetValue(2, 0));
		}
	}

	v0.rad.SetX(phi);
	v0.rad.SetY(theta);
	v0.rad.SetZ(psi);

	v0.degree = RadToDegree(v0.rad);
}

Sixdof::~Sixdof(){
	
}

void Sixdof::MeshUpdate(){
	Print("Update mesh motion......");
}

void Sixdof::FieldUpdate(){
	Print("Update Field......");
}