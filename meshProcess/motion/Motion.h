﻿//////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Motion.h
//! <AUTHOR>
//! @brief 运动类基类
//! @date  2023-11-10
//
//------------------------------修改日志----------------------------------------
//
// 2023-11-10 曾凯
// 说明：建立
// 
//-----------------------------------------------------------------------------

#ifndef _meshProcess_Motion_Motion_
#define _meshProcess_Motion_Motion_

#include "sourceFlow/package/FlowPackage.h"

class Motion
{
public:
	/**
	* @brief constructor
	*
	*/
	Motion(const std::vector<Package::FlowPackage *> flowPackageVector_ );
	/**
	* @brief destructor
	*
	*/
	virtual ~Motion();

	/**
	* @brief 网格更新，纯虚函数，子类中实现
	*
	*/
	virtual void MeshUpdate() = 0;

	/**
	* @brief 网格运动相关的场更新，纯虚函数，子类中实现
	*
	*/
	virtual void FieldUpdate() = 0;
	

protected:

	std::vector<Package::FlowPackage *> flowPackageVector;
	const Configure::Flow::FlowConfigure &flowConfig;
	Mesh *mesh;

	std::string motionName;

	const Scalar& unsteadyCurrentTime;
	const Scalar& unsteadyTimeStep;

	Vector deltaDistance; // 单个时间步内运动的距离, m
	Scalar deltaAngle;  // 单个时间步内运动角度, rad


protected:
	/**
	* @brief 计算空间点绕指定轴线旋转的角速度
	* @param[in] point 空间点坐标
	* @param[in] axisPoint 轴线点坐标
	* @param[in] axisDirNor 轴线单位方向矢量
	* @param[in] omega 旋转角速度
	*/
	Vector CaculateLineVelocityAroundAxis(const Vector &point, 
										  const Vector &axisPoint,
										  const Vector &axisDirNor,
										  const Scalar &omega);


};






#endif
