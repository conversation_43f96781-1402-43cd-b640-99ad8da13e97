﻿#include "meshProcess/motion/MotionManager.h"

MotionManager::MotionManager(std::vector<Package::FlowPackage *> flowPackageVector_)
	: flowPackageVector(flowPackageVector_),
	flowConfig(flowPackageVector_[0]->GetFlowConfigure()),
	localMesh(flowPackageVector_[0]->GetMeshStruct().mesh)
{
	myZoneID = localMesh->GetMeshZoneID();

	//创建运动对象
	this->CreateMotions();
}

MotionManager::~MotionManager()
{
	for (int i = 0; i < motions.size(); i++)
	{
		if (motions[i] != nullptr)
		{
			delete motions[i];
			motions[i] = nullptr;
		}
	}
	motions.clear();
}

void MotionManager::CreateMotions()
{
	int n = flowConfig.GetMotion().size();  // 运动类型数量
	motions.resize(n);

	for (int i = 0; i < n; i++)
	{
		if (flowConfig.GetMotion()[i].motionType == MotionType::STATIONARY)
		{
			motions[i] = new Stationary(flowPackageVector);
		}
		else if (flowConfig.GetMotion()[i].motionType == MotionType::TRANSLATION_ROTATION)
		{
			motions[i] = new TranslationRotation(flowPackageVector, flowConfig.GetMotion()[i]);
		}
		else if (flowConfig.GetMotion()[i].motionType == MotionType::XDOF)
		{
			motions[i] = new Sixdof(flowPackageVector, flowConfig.GetMotion()[i]);
			Sixdof* DOF = dynamic_cast<Sixdof*> (motions[i]);
			SixdofCouple Cfd6DofCouple(*DOF, -2, 1.0, 0.001);
			std::vector<double>cfd_tem_test = { -2925.686956, 0.0, -2746.463862, 0.0, -1550.660558, 0.0 };
			DOF->InitForceMomentsResidual(cfd_tem_test);
			DOF->ComputeOnePysicalTimestep(DOF->GetBdfType());

		}
		else if (flowConfig.GetMotion()[i].motionType == MotionType::MORPHING)
		{
			FatalError("尚未开发");
		}
	}
}

const Motion* MotionManager::GetMotion(int motionID)
{
	return motions[motionID];
}

void MotionManager::MeshUpdate()
{
	//网格运动
	motions[myZoneID]->MeshUpdate();

	
}