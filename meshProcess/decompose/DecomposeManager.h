﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DecomposeManager.h
//! <AUTHOR>
//! @brief 网格分区类
//! @date 2021-07-26
//
//------------------------------修改日志----------------------------------------
// 2021-07-26 李艳亮、乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _meshProcess_decompose_DecomposeManager_
#define _meshProcess_decompose_DecomposeManager_

#include "meshProcess/decompose/DecomposeBase.h"
#include "meshProcess/decompose/DecomposeMetis.h"
#include "basic/configure/Configure.h"

/**
 * @brief 网格分区类
 * 
 */
class DecomposeManager
{
	/// 当前level和partID的基础数据
    struct LocalToGlobalInfo
    {
	    std::vector<int> elemLocalToGlobal;
	    std::vector<int> faceLocalToGlobal;
		std::vector<int> nodeLocalToGlobal;
		std::vector<std::vector<int>> vv_boundaryFaceIndex_Global;
        std::vector<std::vector<GhostElement>> vv_ghostElement_parallel;
        std::vector<std::vector<GhostElement>> vv_ghostElement_fine;
        std::vector<std::vector<int>> vv_elemMap_fine;
        std::vector<std::string> v_boundaryName;
		std::vector<std::vector<int>> vv_boundaryFaceID;
        std::vector<int> v_boundaryLocalToGlobal;
        std::vector<int> oppositeFaceFlag;
    };

public:

    /**
    * @brief 网格分区类构造函数 
	*
	* @param[in] globalMesh_ 全局网格
	* @param[in] type 分区方法
	* @param[in] nPart 分区数量
	* @param[in] startID_ 起始的分区号
    */
    DecomposeManager(SubMesh *globalSubMesh, const Preprocessor::DecomposeType &type, const int &nPart, const int &startID_ = 0);

	/**
	* @brief 析构函数
	*
	*/
    ~DecomposeManager();

	/**
	 * @brief 细网格分区函数
	 * 
	 */
	void GenerateDecomposeIDList();

	/**
	* @brief 将网格写入到文件（所有层次的网格）
	*
	* @param[in] outputPath 网格输出的文件路径
	* @param[in] meshName 网格文件名称
	* @param[in] binary 是否按二进制输出
	*/
    void GenerateSubMeshFile(const std::string &outputPath, const std::string &meshName, const bool &binary);

	/**
	* @brief 形成分区后的子网格（所有层次的网格）
	*
	* @param[out] subMeshVector 子网格容器
	*/
	void GenerateSubMesh(std::vector<SubMesh *> subMeshVector);

private:
	/**
	* @brief 建立局部网格和全局网格的对应关系
	*
	* @param[in] partID 网格所在分区
	* @param[in] level 网格所在层次
	* @param[in, out] localToGlobalInfo 局部网格和全局网格的对应关系
	*/
    void BuildGlobalAndLocal(const int &partID, const int &level, LocalToGlobalInfo &localToGlobalInfo);

	/**
	* @brief 更新虚单元的信息
	*
	* @param[in] partID 网格所在分区
	* @param[in] coarseLevel 所在粗网格层次
	* @param[in, out] localToGlobalInfo 局部网格和全局网格的对应关系
	*/
	void UpdateGhostElementsInfo(const int &partID, const int &coarseLevel, LocalToGlobalInfo &localToGlobalInfo);
	
	/**
	* @brief 根据分区方法建立分区类指针
	*
	*/
	void SetDecomposePointer();
    
	/**
	* @brief 生成粗网格的分区列表
	*
	* @param[in] coarseLevel 粗网格编号
	*/
	void GenerateCoarseDecomposeIDList(const int &coarseLevel);
	
	/**
	* @brief 针对网格单元，建立全局网格和局部网格的对应关系
	*
	* @param[in] level 当前网格层次
	*/
	void BuildElementGlobalToLocal(const int &level);
	
	/**
	* @brief 输出本层网格信息到文件
	*
	* @param[in] file 文件流
	* @param[in] partID 网格所在分区号
	* @param[in] level 网格层次
	* @param[in] binary 是否按二进制输出
	* @param[in] localToGlobalInfo 局部网格和全局网格的对应关系
	*/
    void WriteSubMesh(std::fstream &file, const int &partID, const int &level, const bool &binary,
                      const LocalToGlobalInfo &localToGlobalInfo);

	/**
	* @brief 输出虚单元信息到文件
	*
	* @param[in] file 文件流
	* @param[in] vv_ghostElement_multigrid 虚单元信息
	* @param[in] binary 是否按二进制输出
	*/
    void WriteGhostElement(std::fstream &file, const std::vector<std::vector<GhostElement>> &vv_ghostElement_multigrid, const bool &binary);

	/**
	* @brief 输出本层网格信息到文件
	*
	* @param[out] localMesh 子网格
	* @param[in] partID 网格所在分区号
	* @param[in] level 网格层次
	* @param[in] localToGlobalInfo 局部网格和全局网格的对应关系
	*/
	void BuildSubMesh(SubMesh *localMesh, const int &partID, const int &level, const LocalToGlobalInfo &localToGlobalInfo);

	/**
	* @brief 输出虚单元信息到文件
	*
	* @param[out] localMesh 子网格
	* @param[in] vv_ghostElement_multigrid 虚单元信息
	*/
	void BuildGhostElement(SubMesh *localMesh, const int fineLevel, const std::vector<std::vector<GhostElement>> &vv_ghostElement_multigrid);

	/**
	* @brief 计算单元权重
	*
	* @param[in] level 网格层次
	* @param[in, out] cellWeights 单元权重
	*/
	void CalculateCellWeights(const int &level, std::vector<int> &cellWeights);

private:
	SubMesh *globalSubMesh; ///< 全局网格指针
	DecomposeBase *p_Decompose; ///< 分区基类的指针
	const Preprocessor::DecomposeType &type; ///< 分区方法
	const int &nPart; ///< 分区数量
	const int &nLevel; ///< 总网格层次
	const int &startID; ///< 分区起始编号（主要用来多域分区）
	int nLevelReal; ///< 可分区的总网格层数
	
	std::vector<std::vector<int>> decomposeIDListVector; ///< 分区列表（包含所有网格层次）
	std::vector<std::vector<int>> elemGlobalToLocalVector; ///< 全局和局部网格的单元对应关系（包含所有网格层次）
};

#endif 