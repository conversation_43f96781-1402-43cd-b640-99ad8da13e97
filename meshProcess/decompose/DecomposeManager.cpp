﻿#include "meshProcess/decompose/DecomposeManager.h"
#include "basic/mesh/SubMesh.h"

DecomposeManager::DecomposeManager(SubMesh *globalSubMesh_, const Preprocessor::DecomposeType &type_, const int &nPart_, const int &startID_)
    :globalSubMesh(globalSubMesh_), type(type_), nPart(nPart_), nLevel(globalSubMesh_->n_level), startID(startID_)
{
	decomposeIDListVector.resize(nLevel);
	elemGlobalToLocalVector.resize(nLevel);
	nLevelReal = nLevel;
}

DecomposeManager::~DecomposeManager()
{
}

void DecomposeManager::GenerateDecomposeIDList()
{
    Print("生成细网格分区列表...");
    this->SetDecomposePointer();

    // 计算单元权重
	std::vector<int> cellWeights;
    this->CalculateCellWeights(0, cellWeights);

    if (nPart == 1)
    {
        decomposeIDListVector[0].clear();
        decomposeIDListVector[0].resize(globalSubMesh->GetElementNumberReal(), startID);
    }
    else
    {
        p_Decompose->Decompose(cellWeights, decomposeIDListVector[0]);
        for (int i = 0; i < decomposeIDListVector[0].size(); ++i)
            decomposeIDListVector[0][i] += startID;
    }
	
    this->BuildElementGlobalToLocal(0);
    if (p_Decompose != nullptr) { delete p_Decompose; p_Decompose = nullptr; }
	
    Print("生成所有粗网格分区列表...");
	for (int level = 1; level < nLevelReal; ++level)
	{
		this->GenerateCoarseDecomposeIDList(level);
		this->BuildElementGlobalToLocal(level);
	}
	
    Print("统计分区信息...");
	for (int level = 0; level < nLevelReal; ++level)
	{
		const auto &list = decomposeIDListVector[level];
		std::vector<int> localSize(nPart);
		for (int partID = 0; partID < nPart; ++partID)
			localSize[partID] = (int)(std::count(list.begin(), list.end(), partID+startID));
		
		int minSize = *(std::min_element(localSize.begin(), localSize.end()));
		int maxSize = *(std::max_element(localSize.begin(), localSize.end()));
		Print("  Level = " + ToString(level));
		Print("\tmin size = " + ToString(minSize));
		Print("\tmax size = " + ToString(maxSize));
		Print("");
	}
}

void DecomposeManager::GenerateSubMeshFile(const std::string &outputPath, const std::string &meshName, const bool &binary)
{
    int level;
    std::fstream file;
    LocalToGlobalInfo localToGlobalInfo;
    const int currentThreads = Min(nPart, (int)omp_get_max_threads());
    ARI_OMP(parallel for schedule(static) private(file, level, localToGlobalInfo) num_threads(currentThreads))
    for (int partID = startID; partID < startID + nPart; ++partID)
    {
        const std::string IDString = ToString(partID);
        const std::string nameString = outputPath + meshName + "_p" + IDString;
        
        if (binary) file.open(nameString + ".bMesh", std::ios::out | std::ios::binary);
        else        file.open(nameString + ".aMesh", std::ios::out);
        
        std::vector<int>().swap(localToGlobalInfo.v_boundaryLocalToGlobal);

		for (level = 0; level < nLevelReal; level++)
		{
			if (decomposeIDListVector[level].size() == 0) break;
			
            // 生成本层网格当地与全局对应关系
            this->BuildGlobalAndLocal(partID, level, localToGlobalInfo);
            
            // 输出上层细网格的聚合虚单元信息
            if (level > 0) this->WriteGhostElement(file, localToGlobalInfo.vv_ghostElement_fine, binary);
            
            // 输出本层网格信息
            this->WriteSubMesh(file, partID, level, binary, localToGlobalInfo);
        }

        file.close();
    }
}

void DecomposeManager::GenerateSubMesh(std::vector<SubMesh *> subMeshVector)
{
	int level;
	LocalToGlobalInfo localToGlobalInfo;
	const int currentThreads = Min(nPart, (int)omp_get_max_threads());
	ARI_OMP(parallel for schedule(static) private(level, localToGlobalInfo) num_threads(currentThreads))
	for (int partID = startID; partID < startID + nPart; ++partID)
	{
		std::vector<int>().swap(localToGlobalInfo.v_boundaryLocalToGlobal);

		for (level = 0; level < nLevelReal; level++)
		{
			if (decomposeIDListVector[level].size() == 0) break;

			// 生成本层网格当地与全局对应关系
			this->BuildGlobalAndLocal(partID, level, localToGlobalInfo);

			// 输出上层细网格的聚合虚单元信息
			if (level > 0) this->BuildGhostElement(subMeshVector[partID], level-1, localToGlobalInfo.vv_ghostElement_fine);

			// 输出本层网格信息
			this->BuildSubMesh(subMeshVector[partID], partID, level, localToGlobalInfo);
		}
	}
}

void DecomposeManager::GenerateCoarseDecomposeIDList(const int &coarseLevel)
{
    MultiGrid *coarseMesh = globalSubMesh->GetMultiGrid(coarseLevel);
    const std::vector<int> &decomposeIDListFine = decomposeIDListVector[coarseLevel - 1];
	std::vector<int> &decomposeIDListCoarse = decomposeIDListVector[coarseLevel];
	
    // 确定粗网格包含细网格分区信息是否一致
	std::vector<bool> uniformFlag(coarseMesh->n_elemNum, false);
	for (int coarseID = 0; coarseID < coarseMesh->n_elemNum; coarseID++)
	{
        const auto &fineIDlist = coarseMesh->v_elemMap[coarseID];
		
		int m = 1;
        for (; m < fineIDlist.size(); m++)
		{
			const int &decomposeID0 = decomposeIDListFine[fineIDlist[0]];
			const int &decomposeIDM = decomposeIDListFine[fineIDlist[m]];
			if(decomposeIDM != decomposeID0) break;
		}
		
        if(m == fineIDlist.size()) uniformFlag[coarseID] = true;
	}
	
	// 粗网格单元权重
	std::vector<int> cellWeights;
    this->CalculateCellWeights(coarseLevel, cellWeights);
	
    // 确定初始分区，并统计每个分区的粗网格总权重
	decomposeIDListCoarse.resize(coarseMesh->n_elemNum);
	std::vector<int> partCellWeights(nPart, 0);
	for (int coarseID = 0; coarseID < coarseMesh->n_elemNum; coarseID++)
	{
        const auto &fineIDlist = coarseMesh->v_elemMap[coarseID];
		if(uniformFlag[coarseID]) // 粗单元所对应细单元分布在一个分区中
        {
			const int &decomposeIDM0 = decomposeIDListFine[fineIDlist[0]];
            decomposeIDListCoarse[coarseID] = decomposeIDM0;
			partCellWeights[decomposeIDListCoarse[coarseID]-startID] += cellWeights[coarseID];
        }
		else // 粗单元所对应细单元分布在多个分区中
		{
			std::vector<int> count(nPart, 0);
			for (int m = 0; m < fineIDlist.size(); m++) count[decomposeIDListFine[fineIDlist[m]]-startID]++;
			decomposeIDListCoarse[coarseID] = (int)(std::max_element(count.begin(), count.end()) - count.begin())+startID;
			partCellWeights[decomposeIDListCoarse[coarseID]-startID] += cellWeights[coarseID];
		}
	}
	
    // 迭代优化粗网格分区
    for (int step = 0; step < 10; ++step)
    {
        int changeCount = 0;
        for (int coarseID = 0; coarseID < coarseMesh->n_elemNum; ++coarseID)
	    {
			// 仅对细单元分布在多个分区的单元进行优化
			if(uniformFlag[coarseID]) continue;
			
            // 粗网格所对应细网格在每个分区出现的次数
            std::vector<int> count(nPart, 0);
            const auto &fineIDlist = coarseMesh->v_elemMap[coarseID];
            for (int m = 0; m < fineIDlist.size(); m++) count[decomposeIDListFine[fineIDlist[m]]-startID]++;
			
            // 当前粗网格的分区
            const int &partID0 = decomposeIDListCoarse[coarseID];

            // 细网格出现最多的分区（相同次数取第一个）
            const int partID1 = (int)(std::max_element(count.begin(), count.end()) - count.begin())+startID;

            for (int m = 0; m < fineIDlist.size(); m++)
            {
                const int &partID2 = decomposeIDListFine[fineIDlist[m]];

                // 细网格出现最多的分区（相同次数的第二个）
                if (count[partID1-startID] == count[partID2-startID] && partID1 != partID2) 
                {
                    // 选择粗网格数量最小的分区作为当前粗网格分区
                    if (partCellWeights[partID1-startID] > partCellWeights[partID2-startID] && partID0 != partID2)
                    {
						partCellWeights[partID0-startID] -= cellWeights[coarseID];
                        decomposeIDListCoarse[coarseID] = partID2;
						partCellWeights[partID2-startID] += cellWeights[coarseID];
                        changeCount++;
                    }
                }
            }
        }
		
        if(changeCount==0) break;
    }
	
    // TODO::粗网格分区不产生新边界
	
	// 检查，分区单元数量小于10，不分区
	for (int partID = 0; partID < nPart; ++partID)
	{
		if(partCellWeights[partID] < 10)
		{
			WarningContinue(" At level " + ToString(coarseLevel) + ", element number in part " + ToString(partID+startID) +
			      " is small than 10, \n and this level cannot be decomposed.\n");
			decomposeIDListCoarse.clear();
			nLevelReal = coarseLevel;
			break;
		}
	}
}

void DecomposeManager::SetDecomposePointer()
{
    switch (type)
    {
    case Preprocessor::DecomposeType::METIS:
        this->p_Decompose = new DecomposeMetis(globalSubMesh->GetMultiGrid(0), nPart);
        break;
    default:
        FatalError("Decompose model type is not supported. Please check model type.\n");
        break;
    }
}

void DecomposeManager::BuildElementGlobalToLocal(const int &level)
{
    const std::vector<int> &decomposeIDList = decomposeIDListVector[level];
    std::vector<int> &elemGlobalToLocal = elemGlobalToLocalVector[level];
    
    const int globalElementSize = decomposeIDList.size();

    // 生成子网格单元与全局网格单元映射关系
    std::vector<int> localSize(nPart, 0);
    elemGlobalToLocal.resize(globalElementSize);
    for (int globalID = 0; globalID < globalElementSize; globalID++)
    {
        const int &partID = decomposeIDList[globalID];
        elemGlobalToLocal[globalID] = localSize[partID -startID] ++;
    }
}

void DecomposeManager::BuildGlobalAndLocal(const int &partID, const int &level, LocalToGlobalInfo &localToGlobalInfo)
{
    Mesh *globalMeshCurrentLevel = globalSubMesh->GetMultiGrid(level);
    const std::vector<int> &decomposeIDList = decomposeIDListVector[level];
    const std::vector<int> &elemGlobalToLocal = elemGlobalToLocalVector[level];
    auto &elemLocalToGlobal = localToGlobalInfo.elemLocalToGlobal;
    auto &faceLocalToGlobal = localToGlobalInfo.faceLocalToGlobal;
    auto &nodeLocalToGlobal = localToGlobalInfo.nodeLocalToGlobal;
	auto &vv_boundaryFaceIndex_Global = localToGlobalInfo.vv_boundaryFaceIndex_Global;
    auto &vv_ghostElement_parallel = localToGlobalInfo.vv_ghostElement_parallel;
    auto &v_boundaryName = localToGlobalInfo.v_boundaryName;
    auto &vv_boundaryFaceID = localToGlobalInfo.vv_boundaryFaceID;
    auto &oppositeFaceFlag = localToGlobalInfo.oppositeFaceFlag;
    auto &v_boundaryLocalToGlobal = localToGlobalInfo.v_boundaryLocalToGlobal;
    auto &vv_ghostElement_fine = localToGlobalInfo.vv_ghostElement_fine;
    auto &vv_elemMap_fine = localToGlobalInfo.vv_elemMap_fine;

	std::vector<int>().swap(elemLocalToGlobal);
	std::vector<int>().swap(faceLocalToGlobal);
	std::vector<int>().swap(nodeLocalToGlobal);
	std::vector<std::vector<int>>().swap(vv_boundaryFaceIndex_Global);
	std::vector<std::vector<GhostElement>>().swap(vv_ghostElement_parallel);
	std::vector<std::vector<GhostElement>>().swap(vv_ghostElement_fine);
	std::vector<std::vector<int>>().swap(vv_elemMap_fine);
	std::vector<std::string>().swap(v_boundaryName);
	std::vector<std::vector<int>>().swap(vv_boundaryFaceID);
	std::vector<int>().swap(oppositeFaceFlag);

    // step1: 创建elemLocalToGlobal
    //统计子网格单元数量
    const int globalElementSize = decomposeIDList.size();
    int localElementSize = 0;
    for (int globalID = 0; globalID < globalElementSize; ++globalID)
        if (decomposeIDList[globalID] == partID) localElementSize++;

    //建立子网格和大网格的单元对应关系
    elemLocalToGlobal.reserve(localElementSize);
    for (int globalID = 0; globalID < globalElementSize; ++globalID)
        if (decomposeIDList[globalID] == partID) elemLocalToGlobal.push_back(globalID);
	
    // step2: 创建faceLocalToGlobal
    // step2.1: 统计子网格面数量
    int localFaceSize = 0;
    std::vector<int> parallelFaceSize(nPart, 0);
    const int globalFaceSize = globalMeshCurrentLevel->v_face.size();
    for (int globalID = 0; globalID < globalFaceSize; ++globalID)
    {
        const int &ownerID = globalMeshCurrentLevel->v_face[globalID].n_owner;
        const int &neighID = globalMeshCurrentLevel->v_face[globalID].n_neighbor;

        const int &ownerRegionID = decomposeIDList[ownerID];
        int neighRegionID = -1;

        if (neighID == -1) // 物理边界面
        {
            if (ownerRegionID == partID) localFaceSize++;
            continue;
        }
        else // 内部面或物理边界面
        {
            neighRegionID = decomposeIDList[neighID];
        }

        if (ownerRegionID == neighRegionID) //内部面
        {
            if (ownerRegionID == partID) localFaceSize++;
        }
        else //并行交界面
        {
            if (ownerRegionID == partID)
            {
                localFaceSize++;
                parallelFaceSize[neighRegionID-startID]++;
            }

            if (neighRegionID == partID)
            {
                localFaceSize++;
                parallelFaceSize[ownerRegionID-startID]++;
            }
        }
    }

    // step2.2: 建立子网格和大网格的面对应关系
    faceLocalToGlobal.reserve(localFaceSize);
    oppositeFaceFlag.resize(localFaceSize, 0);
    vv_ghostElement_parallel.resize(nPart);
    for (int i = 0; i < nPart; i++) vv_ghostElement_parallel[i].reserve(parallelFaceSize[i]);
    for (int globalID = 0; globalID < globalFaceSize; ++globalID)
    {
        const int &ownerID = globalMeshCurrentLevel->v_face[globalID].n_owner;
        const int &neighID = globalMeshCurrentLevel->v_face[globalID].n_neighbor;

        const int &ownerRegionID = decomposeIDList[ownerID];
        int neighRegionID = -1;

        if (neighID == -1) // 物理边界面
        {
            if (ownerRegionID == partID) faceLocalToGlobal.push_back(globalID);
            continue;
        }
        else // 内部面或物理边界面
        {
            neighRegionID = decomposeIDList[neighID];
        }

        if (ownerRegionID == neighRegionID) //内部面
        {
            if (ownerRegionID == partID) faceLocalToGlobal.push_back(globalID);
        }
        else //并行交界面
        {
            if (ownerRegionID == partID)
            {
                faceLocalToGlobal.push_back(globalID);
                const int localFaceID = faceLocalToGlobal.size() - 1;
                std::pair<int, int> procMap(ownerRegionID, neighRegionID);
                std::pair<int, int> localID(-1, elemGlobalToLocal[neighID]); //获取其他线程的单元编号
                vv_ghostElement_parallel[neighRegionID - startID].push_back(GhostElement(procMap, localID, localFaceID));

                oppositeFaceFlag[localFaceID] = 1;
            }
            if (neighRegionID == partID)
            {
                faceLocalToGlobal.push_back(globalID);
                const int localFaceID = faceLocalToGlobal.size() - 1;
                std::pair<int, int> procMap(neighRegionID, ownerRegionID);
                std::pair<int, int> localID(-1, elemGlobalToLocal[ownerID]); //获取其他线程的单元编号
                vv_ghostElement_parallel[ownerRegionID - startID].push_back(GhostElement(procMap, localID, localFaceID));

                oppositeFaceFlag[localFaceID] = -1;
            }
        }
    }

    // step2.3: 并行面
    int size = 0;
    for (int i = 0; i < vv_ghostElement_parallel.size(); i++)
        if (vv_ghostElement_parallel[i].size() > 0)
            vv_ghostElement_parallel[i].swap(vv_ghostElement_parallel[size++]);
    vv_ghostElement_parallel.resize(size);

    // step2.4: 边界面
    std::vector<int> localIDFlag(globalMeshCurrentLevel->GetFaceNumber(), -1);
    for (int i = 0; i < faceLocalToGlobal.size(); ++i) localIDFlag[faceLocalToGlobal[i]] = i;

    const int boundarySize = globalMeshCurrentLevel->GetBoundarySize();
    v_boundaryName.reserve(boundarySize);
    vv_boundaryFaceID.reserve(boundarySize);
    v_boundaryLocalToGlobal.reserve(boundarySize);
	vv_boundaryFaceIndex_Global.reserve(boundarySize);
    for (int i = 0; i < boundarySize; i++)
    {
        const int faceSize = globalMeshCurrentLevel->GetBoundaryFaceSize(i);
		std::vector<int> boundaryfaceID, boundaryfaceIndex;
		boundaryfaceID.reserve(faceSize); boundaryfaceIndex.reserve(faceSize);
        for (int j = 0; j < faceSize; j++)
        {
            const int &globalID = globalMeshCurrentLevel->GetBoundaryFaceID(i, j);
            const int &localID = localIDFlag[globalID];
			if (localID >= 0)
			{
				boundaryfaceID.push_back(localID);
				boundaryfaceIndex.push_back(j);
			}
        }
        if (boundaryfaceID.size() > 0)
        {
            v_boundaryName.push_back(globalMeshCurrentLevel->GetBoundaryName(i));
            vv_boundaryFaceID.push_back(boundaryfaceID);
			vv_boundaryFaceIndex_Global.push_back(boundaryfaceIndex);
            v_boundaryLocalToGlobal.push_back(globalMeshCurrentLevel->GetBoundaryIDGlobal(i));
        }
    }

    // step3: 创建nodeLocalToGlobal
    // 按照单元的点构成及单元的分区信息形成点的分区信息（包含重复的分区）
    int localNodeSize = 0;
    std::vector<bool> localNodeFlag(globalMeshCurrentLevel->v_node.size(), false);
    for (int localID = 0; localID < elemLocalToGlobal.size(); ++localID)
    {
        const int &globalID = elemLocalToGlobal[localID];
        const Element &element = globalMeshCurrentLevel->v_elem[globalID];
        for (int index = 0; index < element.GetNodeSize(); ++index)
        {
            const int &globalNodeID = element.v_nodeID[index];
            if (!localNodeFlag[globalNodeID])
            {
                localNodeFlag[globalNodeID] = true;
                localNodeSize++;
            }
        }
    }

    nodeLocalToGlobal.reserve(localNodeSize);
    for (int globalID = 0; globalID < globalMeshCurrentLevel->v_node.size(); ++globalID)
        if (localNodeFlag[globalID]) nodeLocalToGlobal.push_back(globalID);    

    // 更新虚单元信息
    if(level > 0) this->UpdateGhostElementsInfo(partID, level, localToGlobalInfo);
}

void DecomposeManager::WriteSubMesh(std::fstream &file, const int &partID, const int &level, const bool &binary, const LocalToGlobalInfo &localToGlobalInfo)
{
    const auto &elemLocalToGlobal = localToGlobalInfo.elemLocalToGlobal;
    const auto &faceLocalToGlobal = localToGlobalInfo.faceLocalToGlobal;
    const auto &nodeLocalToGlobal = localToGlobalInfo.nodeLocalToGlobal;
	const auto &vv_boundaryFaceIndex_Global = localToGlobalInfo.vv_boundaryFaceIndex_Global;
    const auto &vv_ghostElement_parallel = localToGlobalInfo.vv_ghostElement_parallel;
    const auto &v_boundaryName = localToGlobalInfo.v_boundaryName;
    const auto &vv_boundaryFaceID = localToGlobalInfo.vv_boundaryFaceID;
    const auto &oppositeFaceFlag = localToGlobalInfo.oppositeFaceFlag;
    const auto &v_boundaryLocalToGlobal = localToGlobalInfo.v_boundaryLocalToGlobal;
    const auto &vv_elemMap_fine = localToGlobalInfo.vv_elemMap_fine;
    
    if (level == 0)
    {
		for (int i = 0; i < vv_boundaryFaceID.size(); i++)
		{
			for (int j = 0; j < vv_boundaryFaceID[i].size(); j++)
			{
				int localID = vv_boundaryFaceID[i][j];
				int globalID0 = faceLocalToGlobal[localID];
				int globalPatchID = v_boundaryLocalToGlobal[i]- globalSubMesh->GetMultiGrid(level)->GetBoundaryIDGlobal(0);
				int globalIndex = vv_boundaryFaceIndex_Global[i][j];
				int globalID1 = this->globalSubMesh->vv_boundaryFaceID[globalPatchID][globalIndex];
				if (globalID0 != globalID1)
					FatalError("globalIndex is wrong!");
			}
		}

	    IO::Write(file, partID, binary);
	    IO::Write(file, nodeLocalToGlobal, binary);
	    IO::Write(file, faceLocalToGlobal, binary);
	    IO::Write(file, elemLocalToGlobal, binary);
		IO::Write(file, vv_boundaryFaceIndex_Global, binary);
	    IO::Write(file, nLevelReal, binary);

        const int &boundaryLocalToGlobalSize = nLevelReal;
	    IO::Write(file, boundaryLocalToGlobalSize, binary);
    }
    IO::Write(file, v_boundaryLocalToGlobal, binary);

    MultiGrid *globalMultiGrid = globalSubMesh->GetMultiGrid(level);
    //建立全局到局部的映射
    std::vector<int> nodeGlobalToLocal(globalMultiGrid->GetNodeNumber(), -1);
    for (int i = 0; i < nodeLocalToGlobal.size(); ++i) nodeGlobalToLocal[nodeLocalToGlobal[i]] = i;
    
    // 基本信息
    const int elementSize = elemLocalToGlobal.size();
    const int faceSize = faceLocalToGlobal.size();
    const int nodeSize = nodeLocalToGlobal.size();
    const int n_elemNum_ghostBoundary = 0;
    const int n_elemNum_ghostParallel = 0;
    const int n_elemNum_ghostOverlap = 0;
    const int n_elemNum_all = elementSize;
    IO::Write(file, globalMultiGrid->st_meshName, binary);
    IO::Write(file, globalMultiGrid->st_fileName, binary);
    IO::Write(file, globalMultiGrid->zoneID, binary);
    IO::Write(file, elementSize, binary);
    IO::Write(file, n_elemNum_ghostBoundary, binary);
    IO::Write(file, n_elemNum_ghostParallel, binary);
    IO::Write(file, n_elemNum_ghostOverlap, binary);
    IO::Write(file, n_elemNum_all, binary);
    IO::Write(file, faceSize, binary);
    IO::Write(file, nodeSize, binary);
    IO::Write(file, (int)globalMultiGrid->md_meshDim, binary);
    IO::Write(file, (int)globalMultiGrid->est_shapeType, binary);
    
    // 单元信息
	std::vector<Vector> elemCenters(elementSize);
	std::vector<Scalar> elemVolumes(elementSize);
	std::vector<int> est_shapeTypes(elementSize);
	int elemNodeSizeAll = elementSize;
    for (int elementID = 0; elementID < elementSize; ++elementID)
    {
        const int &globalID = elemLocalToGlobal[elementID];
        const Element &element = globalMultiGrid->GetElement(globalID);
		elemCenters[elementID] = element.GetCenter();
		elemVolumes[elementID] = element.GetVolume();
		est_shapeTypes[elementID] = (int)element.est_shapeType;
		elemNodeSizeAll += element.GetNodeSize();
	}
        
	std::vector<int> elemNodeIDs(elemNodeSizeAll);
	for (int elementID = 0, index = 0; elementID < elementSize; ++elementID)
	{
		const Element &element = globalMultiGrid->GetElement(elemLocalToGlobal[elementID]);
		const int nodeSize = element.v_nodeID.size();
		elemNodeIDs[index++] = nodeSize;
		for (int i = 0; i < nodeSize; ++i)
			elemNodeIDs[index++] = nodeGlobalToLocal[element.GetNodeID(i)];
    }

	IO::Write(file, elemCenters, binary);
	IO::Write(file, elemVolumes, binary);
	IO::Write(file, elemNodeIDs, binary);
	IO::Write(file, est_shapeTypes, binary);
	std::vector<Vector>().swap(elemCenters);
	std::vector<Scalar>().swap(elemVolumes);
	std::vector<int>().swap(elemNodeIDs);
	std::vector<int>().swap(est_shapeTypes);

    // 面信息
	std::vector<Vector> faceCenters(faceSize);
	std::vector<Scalar> faceAreas(faceSize);
	int faceNodeIDsAll = faceSize;
    for (int faceID = 0; faceID < faceSize; ++faceID)
    {
		const Face &face = globalMultiGrid->GetFace(faceLocalToGlobal[faceID]);
		faceCenters[faceID] = face.GetCenter();
		faceAreas[faceID] = face.GetArea();
		faceNodeIDsAll += face.GetNodeSize();
	}
	std::vector<Vector> normals(faceSize);
	std::vector<int> ownerIDs(faceSize, -1);
	std::vector<int> neighIDs(faceSize, -1);
	std::vector<int> faceNodeIDs(faceNodeIDsAll);
	for (int faceID = 0, index = 0; faceID < faceSize; ++faceID)
	{
		const Face &face = globalMultiGrid->GetFace(faceLocalToGlobal[faceID]);
        const int &neighIDGlobal = face.GetNeighborID();
    
        bool faceNormalFlag = false;
		if (neighIDGlobal == -1)
        {
			ownerIDs[faceID] = elemGlobalToLocalVector[level][face.GetOwnerID()];
        }
        else
        {
			if (oppositeFaceFlag[faceID] == 0)
            {
				ownerIDs[faceID] = elemGlobalToLocalVector[level][face.GetOwnerID()];
				neighIDs[faceID] = elemGlobalToLocalVector[level][face.GetNeighborID()];
				if (ownerIDs[faceID] > neighIDs[faceID]) faceNormalFlag = true;
            }
			else if (oppositeFaceFlag[faceID] < 0)
            {
				ownerIDs[faceID] = elemGlobalToLocalVector[level][face.GetNeighborID()];
                faceNormalFlag = true;
            }
            else
            {
				ownerIDs[faceID] = elemGlobalToLocalVector[level][face.GetOwnerID()];
            }
        }
        
		const int nodeSize = face.v_nodeID.size();
		faceNodeIDs[index++] = nodeSize;
		if (faceNormalFlag)
		{
			normals[faceID] = -face.GetNormal();
			for (int i = 0; i < nodeSize; ++i)
				faceNodeIDs[index++] = nodeGlobalToLocal[face.GetNodeID(nodeSize - 1 - i)];
		}
		else
		{
			normals[faceID] = face.GetNormal();
			for (int i = 0; i < nodeSize; ++i)
				faceNodeIDs[index++] = nodeGlobalToLocal[face.GetNodeID(i)];
		}
	}
    
	IO::Write(file, faceCenters, binary);
	IO::Write(file, faceAreas, binary);
	IO::Write(file, normals, binary);
	IO::Write(file, ownerIDs, binary);
	IO::Write(file, neighIDs, binary);
	IO::Write(file, faceNodeIDs, binary);
    
	std::vector<Vector>().swap(faceCenters);
	std::vector<Scalar>().swap(faceAreas);
	std::vector<Vector>().swap(normals);
	std::vector<int>().swap(ownerIDs);
	std::vector<int>().swap(neighIDs);
	std::vector<int>().swap(faceNodeIDs);
    
    // 坐标点
	std::vector<Vector> nodes(nodeSize);
    for (int nodeID = 0; nodeID < nodeSize; ++nodeID)
    {
		nodes[nodeID] = globalMultiGrid->GetNode(nodeLocalToGlobal[nodeID]);
    }
	IO::Write(file, nodes, binary);
	std::vector<Vector>().swap(nodes);
    
    // 物理边界
    IO::Write(file, v_boundaryLocalToGlobal, binary);
    IO::Write(file, v_boundaryName, binary);
    IO::Write(file, vv_boundaryFaceID, binary);

    // 并行边界
    WriteGhostElement(file, vv_ghostElement_parallel, binary);

    // 粗网格单元对应细网格单元的编号列表
    if(level > 0) IO::Write(file, vv_elemMap_fine, binary);

}

void DecomposeManager::WriteGhostElement(std::fstream &file,
                                         const std::vector<std::vector<GhostElement>> &vv_ghostElement_,
                                         const bool &binary)
{
    const int num1 = vv_ghostElement_.size();
    IO::Write(file, num1, binary);
    for (int i = 0; i < num1; ++i)
    {
        const int num2 = vv_ghostElement_[i].size();
        IO::Write(file, num2, binary);
        for (int j = 0; j < num2; ++j)
            vv_ghostElement_[i][j].Write(file, binary);
    }
}

void DecomposeManager::BuildSubMesh(SubMesh *localMesh, const int &partID, const int &level, const LocalToGlobalInfo &localToGlobalInfo)
{
	const auto &elemLocalToGlobal = localToGlobalInfo.elemLocalToGlobal;
	const auto &faceLocalToGlobal = localToGlobalInfo.faceLocalToGlobal;
	const auto &nodeLocalToGlobal = localToGlobalInfo.nodeLocalToGlobal;
	const auto &vv_boundaryFaceIndex_Global = localToGlobalInfo.vv_boundaryFaceIndex_Global;
	const auto &vv_ghostElement_parallel = localToGlobalInfo.vv_ghostElement_parallel;
	const auto &v_boundaryName = localToGlobalInfo.v_boundaryName;
	const auto &vv_boundaryFaceID = localToGlobalInfo.vv_boundaryFaceID;
	const auto &oppositeFaceFlag = localToGlobalInfo.oppositeFaceFlag;
	const auto &v_boundaryLocalToGlobal = localToGlobalInfo.v_boundaryLocalToGlobal;
	const auto &vv_elemMap_fine = localToGlobalInfo.vv_elemMap_fine;

	if (level == 0)
	{
		for (int i = 0; i < vv_boundaryFaceID.size(); i++)
		{
			for (int j = 0; j < vv_boundaryFaceID[i].size(); j++)
			{
				int localID = vv_boundaryFaceID[i][j];
				int globalID0 = faceLocalToGlobal[localID];
				int globalPatchID = v_boundaryLocalToGlobal[i];
				int globalIndex = vv_boundaryFaceIndex_Global[i][j];
				int globalID1 = this->globalSubMesh->vv_boundaryFaceID[globalPatchID][globalIndex];
				if (globalID0 != globalID1)
					FatalError("globalIndex is wrong!");
			}
		}
		localMesh->n_pros = partID;
		localMesh->v_nodeID_Global = nodeLocalToGlobal;
		localMesh->v_faceID_Global = faceLocalToGlobal;
		localMesh->v_elemID_Global = elemLocalToGlobal;
		localMesh->vv_boundaryFaceIndex_Global = vv_boundaryFaceIndex_Global;
		localMesh->n_level = nLevelReal;

		const int &boundaryLocalToGlobalSize = nLevelReal;
        localMesh->v_boundaryID_Global.resize(boundaryLocalToGlobalSize);

		localMesh->v_multiGrid.resize(nLevelReal - 1);
	}

	MultiGrid *globalMultiGrid = globalSubMesh->GetMultiGrid(level);
	//建立全局到局部的映射
	std::vector<int> nodeGlobalToLocal(globalMultiGrid->GetNodeNumber(), -1);
	for (int i = 0; i < nodeLocalToGlobal.size(); ++i)
	{
		int localID = i;
		int globalID = nodeLocalToGlobal.at(i);
		nodeGlobalToLocal.at(globalID) = i;
	}

    if (nPart > 0) localMesh->v_boundaryID_Global[level] = v_boundaryLocalToGlobal;

    MultiGrid* coarseGrid = localMesh->GetMultiGrid(level);
    {
        const int elementSize = elemLocalToGlobal.size();
        const int faceSize = faceLocalToGlobal.size();
        const int nodeSize = nodeLocalToGlobal.size();
        const int n_elemNum_ghostBoundary = 0;
        const int n_elemNum_ghostParallel = 0;
        const int n_elemNum_ghostOverlap = 0;
        const int n_elemNum_all = elementSize;
        coarseGrid->st_meshName = globalMultiGrid->st_meshName;
        coarseGrid->st_fileName = globalMultiGrid->st_fileName;
        coarseGrid->zoneID = globalMultiGrid->zoneID;
        coarseGrid->n_elemNum = elementSize;
        coarseGrid->n_elemNum_ghostBoundary = n_elemNum_ghostBoundary;
        coarseGrid->n_elemNum_ghostParallel = n_elemNum_ghostParallel;
        coarseGrid->n_elemNum_ghostOverlap = n_elemNum_ghostOverlap;
        coarseGrid->n_elemNum_all = n_elemNum_all;
        coarseGrid->n_faceNum = faceSize;
        coarseGrid->n_nodeNum = nodeSize;
        coarseGrid->md_meshDim = globalMultiGrid->md_meshDim;
        coarseGrid->est_shapeType = globalMultiGrid->est_shapeType;

        // 单元信息
        coarseGrid->v_elem.resize(elementSize);
        for (int elementID = 0; elementID < elementSize; ++elementID)
        {
            const int &globalID = elemLocalToGlobal[elementID];
            const Element &element = globalMultiGrid->GetElement(globalID);
            coarseGrid->v_elem[elementID].center = element.GetCenter();
            coarseGrid->v_elem[elementID].volume = element.GetVolume();
            coarseGrid->v_elem[elementID].est_shapeType = element.est_shapeType;
            const int nodeSize = element.GetNodeSize();
            coarseGrid->v_elem[elementID].v_nodeID.resize(nodeSize);
            for (int i = 0; i < nodeSize; ++i)
                coarseGrid->v_elem[elementID].v_nodeID[i] = nodeGlobalToLocal[element.GetNodeID(i)];
        }

        // 面信息
        coarseGrid->v_face.resize(faceSize);
        for (int faceID = 0, index = 0; faceID < faceSize; ++faceID)
        {
            const Face &face = globalMultiGrid->GetFace(faceLocalToGlobal[faceID]);
            const int &neighIDGlobal = face.GetNeighborID();

            coarseGrid->v_face[faceID].center = face.GetCenter();
            coarseGrid->v_face[faceID].areaMag = face.GetArea();

            int &ownerID = coarseGrid->v_face[faceID].n_owner;
            int &neighID = coarseGrid->v_face[faceID].n_neighbor;
            bool faceNormalFlag = false;
            if (neighIDGlobal == -1)
            {
                ownerID = elemGlobalToLocalVector[level][face.GetOwnerID()];
            }
            else
            {
                if (oppositeFaceFlag[faceID] == 0)
                {
                    ownerID = elemGlobalToLocalVector[level][face.GetOwnerID()];
                    neighID = elemGlobalToLocalVector[level][face.GetNeighborID()];
                    if (ownerID > neighID) faceNormalFlag = true;
                }
                else if (oppositeFaceFlag[faceID] < 0)
                {
                    ownerID = elemGlobalToLocalVector[level][face.GetNeighborID()];
                    faceNormalFlag = true;
                }
                else
                {
                    ownerID = elemGlobalToLocalVector[level][face.GetOwnerID()];
                }
            }

            const int nodeSize = face.v_nodeID.size();
            coarseGrid->v_face[faceID].v_nodeID.resize(nodeSize);

            if (faceNormalFlag)
            {
                coarseGrid->v_face[faceID].normal = -face.GetNormal();
                for (int i = 0; i < nodeSize; ++i)
                    coarseGrid->v_face[faceID].v_nodeID[i] = nodeGlobalToLocal[face.GetNodeID(nodeSize - 1 - i)];
            }
            else
            {
                coarseGrid->v_face[faceID].normal = face.GetNormal();
                for (int i = 0; i < nodeSize; ++i)
                    coarseGrid->v_face[faceID].v_nodeID[i] = nodeGlobalToLocal[face.GetNodeID(i)];
            }
        }

        // 坐标点
        coarseGrid->v_node.resize(nodeSize);
        for (int nodeID = 0; nodeID < nodeSize; ++nodeID)
        {
            coarseGrid->v_node[nodeID] = globalMultiGrid->GetNode(nodeLocalToGlobal[nodeID]);
        }

        // 物理边界
        coarseGrid->v_boundaryIDGlobal = v_boundaryLocalToGlobal;
        coarseGrid->v_boundaryName = v_boundaryName;
        coarseGrid->vv_boundaryFaceID = vv_boundaryFaceID;

        // 并行边界
        coarseGrid->vv_ghostElement_parallel = vv_ghostElement_parallel;

        if (level > 0) coarseGrid->v_elemMap = vv_elemMap_fine;

    }
}

void DecomposeManager::BuildGhostElement(SubMesh *localMesh, const int fineLevel, const std::vector<std::vector<GhostElement>> &vv_ghostElement_)
{
	const int num1 = vv_ghostElement_.size();
	MultiGrid *fineMesh = localMesh->GetMultiGrid(fineLevel);
	fineMesh->vv_ghostElement_multigrid.resize(num1);
	for (int i = 0; i < num1; ++i)
	{
		const int num2 = vv_ghostElement_[i].size();
		fineMesh->vv_ghostElement_multigrid[i].resize(num2);
		for (int j = 0; j < num2; ++j)
		{
			fineMesh->vv_ghostElement_multigrid[i][j] = vv_ghostElement_[i][j];
		}
	}
}

void DecomposeManager::CalculateCellWeights(const int &level, std::vector<int> &cellWeights)
{
    MultiGrid *pmesh = globalSubMesh->GetMultiGrid(level);
    const int elementSize = pmesh->GetElementNumberReal();
    const int faceSize = pmesh->GetFaceNumber();
    cellWeights.resize(elementSize);
	for (int elementID = 0; elementID < elementSize; ++elementID)
    {
        if(faceSize > 3 * elementSize) cellWeights[elementID] = pmesh->GetElement(elementID).GetFaceSize();
        else                           cellWeights[elementID] = 1;
    }

    // if (nLevelReal > 1)
    // {
    //     MultiGrid *meshTemp = globalSubMesh->GetMultiGrid(level);
    //     for (int i = 0; i < meshTemp->GetElementNumberReal(); ++i)
    //     {
    //         const int fineSize = meshTemp->GetFineIDSize(i);
    //         const int faceSize = meshTemp->GetElement(i).GetFaceSize();
    //         const Scalar weight = faceSize / ((Scalar)fineSize);
    //         for (int j = 0; j < fineSize; ++j)
    //         {
    //             const int fineID = meshTemp->GetFineIDID(i, j);
    //             cellWeights[fineID] += weight;
    //         }
    //     }
    // }
}

void DecomposeManager::UpdateGhostElementsInfo(const int &partID, const int &coarseLevel, LocalToGlobalInfo &localToGlobalInfo)
{
    const int fineLevel = coarseLevel - 1;
    MultiGrid* coarseMeshGlobal = globalSubMesh->GetMultiGrid(coarseLevel);
    const std::vector<int> &decomposeIDListFine = decomposeIDListVector[fineLevel];
    const std::vector<int> &elemGlobalToLocalFine = elemGlobalToLocalVector[fineLevel];

    auto &elemLocalToGlobal = localToGlobalInfo.elemLocalToGlobal;
    auto &vv_ghostElement_fine = localToGlobalInfo.vv_ghostElement_fine;
    auto &vv_elemMap_fine = localToGlobalInfo.vv_elemMap_fine;
    
    const int &elementSize = elemLocalToGlobal.size();
    
    //for (int i=0; i< vv_ghostElement_fine.size(); i++) vv_ghostElement_fine[i].clear();
    //for (int i=0; i< vv_elemMap_fine.size(); i++) vv_elemMap_fine[i].clear();
    vv_ghostElement_fine.clear();
    vv_elemMap_fine.clear();
    vv_ghostElement_fine.resize(nPart);
    vv_elemMap_fine.resize(elementSize);

    // 根据子网格中粗网格单元所对应的细网格单元列表以及网格单元分区信息，
    // 对本区域不存在细网格单元生成与其相对应的虚单元，并存储虚单元索引关系
    std::vector<int> ghostSize(nPart, 0);
    const int &coarsePartID = partID;
    for (int coarseIDLocal = 0; coarseIDLocal < elementSize; ++coarseIDLocal)
    {
        const int &coarseIDGlobal = elemLocalToGlobal[coarseIDLocal];
        const int fineSize = coarseMeshGlobal->GetFineIDSize(coarseIDGlobal);
        for (int index = 0; index < fineSize; index++)
        {
            // 细网格单元在相邻Part (partID = finePartID)
            const int &fineIDGlobal = coarseMeshGlobal->GetFineID(coarseIDGlobal, index);
            const int &finePartID = decomposeIDListFine[fineIDGlobal];
            if (coarsePartID != finePartID) ghostSize[finePartID -startID]++;
        }
    }
    for (int i = 0; i < nPart; i++)
        if(ghostSize[i] > 0) vv_ghostElement_fine[i].reserve(ghostSize[i]);

    for (int coarseIDLocal = 0; coarseIDLocal < elementSize; ++coarseIDLocal)
    {
        const int &coarseIDGlobal = elemLocalToGlobal[coarseIDLocal];
        const int fineSize = coarseMeshGlobal->GetFineIDSize(coarseIDGlobal);
        vv_elemMap_fine[coarseIDLocal].reserve(fineSize);
        for (int index = 0; index < fineSize; index++)
        {
            const int &fineIDGlobal = coarseMeshGlobal->GetFineID(coarseIDGlobal, index);
            const int &finePartID = decomposeIDListFine[fineIDGlobal];
            const int &fineLocalID = elemGlobalToLocalFine[fineIDGlobal];
            
            if (coarsePartID == finePartID) // 细网格单元与粗网格单元在同一Part
            {
                vv_elemMap_fine[coarseIDLocal].push_back(fineLocalID);
            }
            else
            {
                std::pair<int, int> partIDPair(coarsePartID, finePartID);
                std::pair<int, int> localIDPair(-1, fineLocalID);
                vv_ghostElement_fine[finePartID -startID].push_back(GhostElement(partIDPair, localIDPair, coarseIDLocal));
            }
        }
    }

    int size = 0;
    for (int i = 0; i < nPart; i++)
    {
        if (vv_ghostElement_fine[i].size() > 0)
            vv_ghostElement_fine[i].swap(vv_ghostElement_fine[size++]);
    }
    vv_ghostElement_fine.resize(size);
}