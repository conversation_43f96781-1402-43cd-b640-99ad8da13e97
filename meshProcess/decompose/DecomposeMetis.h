﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DecomposeMetis.h
//! <AUTHOR>
//! @brief 调用外部库Metis分区的类.
//! @date 2017-10-09
//
//------------------------------修改日志----------------------------------------
// 2017-10-09 张帅（数峰科技/西交大）
//     说明：设计
//------------------------------------------------------------------------------

#ifndef _meshProcess_decompose_DecomposeMetis_
#define _meshProcess_decompose_DecomposeMetis_

#include <vector>

#include "meshProcess/decompose/DecomposeBase.h"

extern "C"
{
#include <metis.h>
}

/**
 * @brief 基于Metis的网格分区类
 * 
 */
class DecomposeMetis : public DecomposeBase
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in] pmesh 网格指针
	* @param[in] nPros 分区数量
	*/
    DecomposeMetis(Mesh* pmesh, const int &nPros);
    
	/**
	* @brief 析构函数
	*
	*/
    ~DecomposeMetis(){}

	/**
	* @brief 分区函数（虚函数）
	*
	* @param[in] cellWeights_ 单元权重
	* @param[in, out] decomposeIDList 每个网格单元所属分区的容器
	*/
    void Decompose(const std::vector<int> &cellWeights_, std::vector<int> &decomposeIDList);
};

#endif
