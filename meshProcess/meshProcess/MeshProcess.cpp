﻿#include "meshProcess/meshProcess/MeshProcess.h"

MeshProcess::MeshProcess(Configure::Configure &configure_)
    : configure(configure_)
{
	mpiSize = GetMPISize();
	mpiRank = GetMPIRank();
}

MeshProcess::~MeshProcess()
{
	if (writeFileFlag)
	{
    for (int zoneID = 0; zoneID < globalMeshVector.size(); zoneID++)
    {
        if (globalMeshVector[zoneID] != nullptr)
        {
            delete globalMeshVector[zoneID];
            globalMeshVector[zoneID] = nullptr;
        }
    }
}
	else
	{
		if (mpiRank == 0)
		{
			const int &nPart = configure.GetPreprocess().partitionNumber;
			for (int i = 1; i < nPart; i++)
			{
				delete subMeshVector[i];
				subMeshVector[i] = nullptr;
			}
		}
	}
}

void MeshProcess::PreProcess(SubMesh *localMesh, std::vector<Face> *wallFace_)
{
    wallFace = wallFace_;

	const int &nPart = configure.GetPreprocess().partitionNumber;
	if (localMesh == nullptr)
	{
		writeFileFlag = true;

		const auto &meshNumber = configure.GetMeshParameters().meshNumber;
		globalMeshVector.resize(meshNumber);
		for (int zoneID = 0; zoneID < meshNumber; zoneID++)
			globalMeshVector[zoneID] = new SubMesh();
	}
	else
	{
		writeFileFlag = false;
		subMeshVector.push_back(localMesh);
		if (mpiRank == 0)
		{
			subMeshVector.resize(nPart);
			for (int i = 1; i < nPart; i++) subMeshVector[i] = new SubMesh;

			const auto &meshNumber = configure.GetMeshParameters().meshNumber;

			if (nPart < meshNumber) FatalError("partitionNumber isnot enough!");

            if (nPart == 1)
            {
                globalMeshVector.push_back(subMeshVector[0]);
            }
            else
            {
			    globalMeshVector.resize(meshNumber);
			    for (int zoneID = 0; zoneID < meshNumber; zoneID++)
			    	globalMeshVector[zoneID] = new SubMesh;
            }
		}
	}

    SystemTime timeCurrent;
    SystemTime timeTotal;

	if (mpiRank == 0)
	{
        Print("\n" + ObtainInfoTitle("开始前处理"));

        //网格读取及转化
        Print("\n" + ObtainInfoTitle("Step1:网格读取与转化", '>'));
        GenerateMesh();
        CheckStatus(1100);
        PrintProcessTime("完成！耗时: ", timeCurrent);

        // 选择原始网格或者对偶网格参与流场计算
        Print("\n" + ObtainInfoTitle("Step2:对偶网格转化", '>'));
        this->ProcessDualMesh();
        CheckStatus(1200);
        PrintProcessTime("完成！耗时: ", timeCurrent);

        // 网格聚合
        Print("\n" + ObtainInfoTitle("Step3:网格聚合", '>'));
        this->AgglomerateMesh();
        CheckStatus(1300);
        PrintProcessTime("完成！耗时: ", timeCurrent);
	}

    // 网格分区
	if (mpiRank == 0)Print("\n" + ObtainInfoTitle("Step4:网格分区", '>'));
    this->ParallelPartition();
    CheckStatus(1400);
	if (mpiRank == 0)PrintProcessTime("完成！耗时: ", timeCurrent);

	if (mpiRank == 0)
	{
        Print("\n" + ObtainInfoTitle("前处理完成"));
        PrintProcessTime("前处理总耗时: ", timeTotal);
	}
}

void MeshProcess::GenerateMesh()
{
    const auto &meshNumber = configure.GetMeshParameters().meshNumber;
	const auto &meshDim = configure.GetMeshParameters().dimension;
    const auto &outputPath = configure.GetPreprocess().outputPath;
    MakeDirectory(outputPath);

    for (int zoneID = 0; zoneID < meshNumber; zoneID++)
    {
        const auto &meshPath = configure.GetMeshParameters().meshPath[zoneID];
        const auto &fileName = configure.GetMeshParameters().fileName[zoneID];
        const auto &meshType = configure.GetMeshParameters().meshType[zoneID];
        const auto &meshTransform = configure.GetMeshParameters().meshTransform;
        MeshConvertManager meshConvertManager(meshPath + fileName, meshType, meshDim, globalMeshVector[zoneID], meshTransform);
		
        // 读取网格信息
        if (meshConvertManager.ReadMesh(true)) return;

        // 构建网格拓扑关系
        if (meshConvertManager.BuildTopology()) return;

        // 修改网格zoneID
        globalMeshVector[zoneID]->zoneID = zoneID;

        // 检查边界名称并更新网格边界编号
        std::vector<std::string> v_boundaryNameOld = globalMeshVector[zoneID]->v_boundaryName;
        std::vector<std::vector<int>> vv_boundaryFaceIDOld = globalMeshVector[zoneID]->vv_boundaryFaceID; 
        globalMeshVector[zoneID]->v_boundaryName.clear();
        globalMeshVector[zoneID]->vv_boundaryFaceID.clear();
        const int globalBoundarySize = configure.GetGlobalBoundarySize();
        const int localBoundarySize = v_boundaryNameOld.size();
        for (int i = 0; i < globalBoundarySize; i++)
        {
            const std::string name0 = configure.GetGlobalBoundary(i).name;
            for (int j = 0; j < localBoundarySize; j++)
            {
                const std::string name1 = v_boundaryNameOld[j];
                if (CompareTwoStrings(name0, name1))
                {
                    globalMeshVector[zoneID]->v_boundaryName.push_back(v_boundaryNameOld[j]);
                    globalMeshVector[zoneID]->vv_boundaryFaceID.push_back(vv_boundaryFaceIDOld[j]);
                    globalMeshVector[zoneID]->v_boundaryIDGlobal.push_back(i);
                    break;
                }
            }
        }
        if (globalMeshVector[zoneID]->v_boundaryName.size() != localBoundarySize)
            FatalError("MeshProcess::GenerateMesh: boundary xml file does not match!");
        
        // 检查网格质量
        MeshChecker meshChecker(globalMeshVector[zoneID], 0, false);
        meshChecker.Check();
        
        Print("全局网格输出...");
        const auto &caseName = configure.GetCaseName();
        std::string zoneMeshName = outputPath + caseName + "_zone" + ToString(zoneID) + ".bMesh";
        std::fstream bMeshFile;
        bMeshFile.open(zoneMeshName, std::ios::in | std::ios::binary);
        bMeshFile.close();
        bMeshFile.open(zoneMeshName, std::ios::out | std::ios::binary);
        globalMeshVector[zoneID]->WriteMesh(bMeshFile, true, false);
        bMeshFile.close();
    }

    if (wallFace != nullptr)
    {
        int wallFaceSize = 0;
        for (int zoneID = 0; zoneID < meshNumber; zoneID++)
        {
            const int boundarySize = globalMeshVector[zoneID]->vv_boundaryFaceID.size();
            for (int i = 0; i < boundarySize; i++)
            {
                if (configure.JudgeWallGlobal(i))
                    wallFaceSize += globalMeshVector[zoneID]->vv_boundaryFaceID[i].size();
            }
        }
        wallFace->reserve(wallFaceSize);

        for (int zoneID = 0; zoneID < meshNumber; zoneID++)
        {
            const int boundarySize = globalMeshVector[zoneID]->vv_boundaryFaceID.size();
            for (int i = 0; i < boundarySize; i++)
            {
                if (!configure.JudgeWallGlobal(i)) continue;

                const int faceSize = globalMeshVector[zoneID]->vv_boundaryFaceID[i].size();
                for (int j = 0; j < faceSize; j++)
                {
                    const int &faceID = globalMeshVector[zoneID]->vv_boundaryFaceID[i][j];
                    wallFace->push_back(globalMeshVector[zoneID]->v_face[faceID]);
                }
            }
        }
    }
}

void MeshProcess::ProcessDualMesh()
{
    const bool &dualMeshFlag = configure.GetPreprocess().dualMeshFlag;
    if (!dualMeshFlag)
    {
        Print("不使用对偶网格，采用原始网格计算");
        return;
    }
    
    if (globalMeshVector.size() > 1)
    {
        Print("多域网格不支持对偶计算，采用原始网格计算");
        return;
    }

    for (int zoneID = 0; zoneID < globalMeshVector.size(); zoneID++)
    {
        const auto elemType = globalMeshVector[zoneID]->GetElemShapeType();
        if (elemType == Element::ElemShapeType::estPolygon || elemType == Element::ElemShapeType::estPolyhedron)
        {
            Print("该网格包含多边形/多面体网格，不支持对偶计算，采用原始网格计算");
            return;
        }
    }

    int boundaryIDGlobal = 0;

    Print("流场计算采用对偶网格");
    Print("\n对偶网格转换...");
    for (int zoneID = 0; zoneID < globalMeshVector.size(); zoneID++)
    {
        DualMesh dualMesh(globalMeshVector[zoneID]);
        dualMesh.PrimaryToDual();
        dualMesh.SwapMesh(globalMeshVector[zoneID]);
        globalMeshVector[zoneID]->PrintMeshInfomation();

        globalMeshVector[zoneID]->zoneID = zoneID;
        for (int i = 0; i<globalMeshVector[zoneID]->vv_boundaryFaceID.size(); i++)
            globalMeshVector[zoneID]->v_boundaryIDGlobal.push_back(boundaryIDGlobal++);

        // 检查对偶网格质量
        MeshChecker meshChecker(globalMeshVector[zoneID], 0, true);
        meshChecker.Check();
    }
}

void MeshProcess::AgglomerateMesh()
{
    const bool &dualMeshFlag = configure.GetPreprocess().dualMeshFlag;
    const int &nLevel = configure.GetPreprocess().multigrid.totalLevel;
    const int &nLayer = configure.GetPreprocess().multigrid.boundaryLayerNumber;
    const auto &type = configure.GetPreprocess().multigrid.type;
    const int &coarseRationLayer = configure.GetPreprocess().multigrid.boundaryLayerCoarseRationNormal;
    const int &coarseRationTang = configure.GetPreprocess().multigrid.boundaryLayerCoarseRationTangent;
    const int &minSize = configure.GetPreprocess().multigrid.minCoarseRation;
    const int &maxSize = configure.GetPreprocess().multigrid.maxCoarseRation;
    const bool &singletonsRemovementFlag = configure.GetPreprocess().multigrid.singletonsRemovementFlag;
    
    if(nLevel == 1)
    {
        Print("不采用多重网格");
    }
    else
    {
        Print("采用多重网格：\n\t粗网格数为 " + ToString(nLevel - 1));
        Print("\t聚合方法为 " + Configure::agglomerateTypeReverseMap.find(type)->second);
    }

    const int nZone = globalMeshVector.size();
    for (int zoneID = 0; zoneID < nZone; zoneID++)
    {
        SubMesh *globalMesh = globalMeshVector[zoneID];

        globalMesh->n_level = nLevel;
        globalMesh->v_multiGrid.resize(nLevel-1);

        if (nLevel < 2) continue;

        std::vector<int> wallList;
        for (int patchID = 0; patchID < (int)globalMesh->GetBoundarySize(); patchID++)
        {
            const int &globalID = globalMesh->GetBoundaryIDGlobal(patchID);
            if (configure.JudgeWallGlobal(globalID))
                wallList.push_back(patchID);
        }

        bool structuredFlag = false;
		if (globalMesh->multiStructuredBlock.GetBlockSize() > 0) structuredFlag = true;

        for (int coarseLevel = 1; coarseLevel < nLevel; coarseLevel++)
        {
            Mesh* fineMesh = globalMesh->GetMultiGrid(coarseLevel-1);
            MultiGrid* coarseMesh = globalMesh->GetMultiGrid(coarseLevel);
            coarseMesh->st_meshName = globalMesh->st_meshName + "_L" + ToString(coarseLevel);
            coarseMesh->v_boundaryIDGlobal = fineMesh->v_boundaryIDGlobal;
            
            if(nZone > 1) Print("\n" + ObtainInfoTitle("zone " + ToString(zoneID)));
            else          Print("\n" + ObtainInfoTitle());
            Print("创建level" + ToString(coarseLevel) + "层粗网格，细网格数：" + ToString(fineMesh->n_elemNum));

            const int currentNLayer = (int) (nLayer / pow(coarseRationLayer, coarseLevel - 1));
            AgglomerateManager agglomerateManager(type, currentNLayer, coarseRationLayer, coarseRationTang,
			                                      minSize, maxSize, singletonsRemovementFlag, dualMeshFlag, structuredFlag);
            agglomerateManager.CreateMultiGrid(fineMesh, coarseMesh, wallList, coarseLevel);
			
            Print("\t粗网格生成成功，粗网格数:" + ToString(coarseMesh->n_elemNum));

            // 检查粗网格质量
            MeshChecker meshChecker(coarseMesh, coarseLevel, dualMeshFlag);
            meshChecker.Check();
        }
    }

    Print("");
}

void MeshProcess::ParallelPartition()
{
    // 并行分区的数量、分区方法及多重网格总层数
    const auto &type = configure.GetPreprocess().partitionMethod;
    const int &nPart = configure.GetPreprocess().partitionNumber;
    const int &nLevel = configure.GetPreprocess().multigrid.totalLevel;
    const bool &binary = configure.GetPreprocess().binaryFileFlag;
    const std::string &meshName = configure.GetCaseName();
    const std::string &outputPath = configure.GetPreprocess().outputPath;
    
    const int nZone = globalMeshVector.size();
	if (nPart < nZone)
	{
		if(mpiRank == 0) FatalError("MeshProcess::ParallelPartition: partition number is wrong!");
	}

    if(nPart == 1)
    {
        Print("不进行网格分区，输出网格#0");
		if (writeFileFlag)
		{
            std::string nameString = outputPath + meshName + "_p0";
            std::fstream file;
            if (binary) file.open(nameString + ".bMesh", std::ios::out | std::ios::binary);
            else        file.open(nameString + ".aMesh", std::ios::out);
            globalMeshVector[0]->WriteSubMesh(file, nLevel, binary);
            file.close();
		}
		else
		{
            globalMeshVector[0] = nullptr;
		}

        return;
    }
    else
    {
		if (mpiRank == 0) Print("进行网格分区：\n\t分区数为 " + ToString(nPart));
    }

	if (mpiRank == 0)
	{
        std::vector<int> zoneInfo(nZone, 1);
        this->GenerateZonePartSize(nPart, zoneInfo);

        SystemTime timeCurrent;
        int startID = 0;
        for (int zoneID = 0; zoneID < nZone; zoneID++)
        {
	    	if (nZone > 1) Print("\n" + ObtainInfoTitle("zone " + ToString(zoneID)));
            else           Print("\n" + ObtainInfoTitle());

            DecomposeManager decomposeManager(globalMeshVector[zoneID], type, zoneInfo[zoneID], startID);
            decomposeManager.GenerateDecomposeIDList();
            PrintProcessTime("生成网格分区列表！耗时", timeCurrent);

	    	if (writeFileFlag)
	    	{
                decomposeManager.GenerateSubMeshFile(outputPath, meshName, binary);
	    	}
	    	else
	    	{
	    		decomposeManager.GenerateSubMesh(subMeshVector);
	    	}

            PrintProcessTime("生成子网格文件！耗时", timeCurrent);
            startID += zoneInfo[zoneID];
        }
	}

	std::vector<boost::mpi::request> sendRequests;
	std::vector<boost::mpi::request> recvRequests;
	if (mpiRank == 0)
	{
		sendRequests.resize(mpiSize - 1);
		for (int i = 1; i < mpiSize; ++i)
		{
			sendRequests[i - 1] = MPI::mpiWorld.isend(i, 0, *subMeshVector[i]);
		}
	}
	else
	{
		recvRequests.push_back(MPI::mpiWorld.irecv(0, 0, *subMeshVector[0]));
	}
	MPIWaitAll(recvRequests);
	MPIWaitAll(sendRequests);
}

void MeshProcess::GenerateZonePartSize(const int nPart, std::vector<int> &zonePartInfo)
{
    const int nZone = globalMeshVector.size();
    if (nZone == 1)
    {
        zonePartInfo[0] = nPart;
        return;
    }
    else if (nZone == nPart)
    {
        return;
    }

    int totalElementNum = 0;
    for (int zoneID = 0; zoneID < nZone; zoneID++)
        totalElementNum += globalMeshVector[zoneID]->GetElementNumberReal();

    Scalar average = totalElementNum / nPart;

    for (int zoneID = 0; zoneID < nZone; zoneID++)
    {
        const int nElement = globalMeshVector[zoneID]->GetElementNumberReal();
        int partTemp = Max((int)(nElement / average), 1);
        int averageTemp = nElement / partTemp;
        if (averageTemp < 1.2 * floor(average)) zonePartInfo[zoneID] = partTemp;
        else                                    zonePartInfo[zoneID] = partTemp + 1;
    }

    int sumPart = zonePartInfo[0], maxZoneID = 0;
    for (int zoneID = 1; zoneID < nZone; zoneID++)
    {
        sumPart += zonePartInfo[zoneID];
        if (zonePartInfo[zoneID] > zonePartInfo[maxZoneID]) maxZoneID = zoneID;
    }
    zonePartInfo[maxZoneID] -= sumPart - nPart;
}
