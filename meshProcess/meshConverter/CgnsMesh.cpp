﻿#include "meshProcess/meshConverter/CgnsMesh.h"

CgnsMesh::CgnsMesh(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_)
    : CgnsMeshBase(MshFileName, meshDimension_, mesh_),
    offsetCgns2ARI(-1), cellDim(0), nSections(0),
    nTotalNode(0), nTotalCell(0)
{
}

CgnsMesh::~CgnsMesh()
{
}

int CgnsMesh::ReadMesh(const bool &fullRead)
{
    Print("\n" + ObtainInfoTitle("Start reading mesh") + "\n");

    if (ReadGeneral())
    {
        FatalError("CgnsMesh::ReadMesh: Reading general info is wrong!");
        return 1;
    }

    if (ReadFamilies())
    {
        FatalError("CgnsMesh::ReadMesh: Reading families is wrong!");
        return 1;
    }

    if (ReadZone(fullRead))
    {
        FatalError("CgnsMesh::ReadMesh: Reading zone is wrong!");
        return 1;
    }

    if (cg_close(fileID))
    {
        FatalError("CgnsMesh::ReadMesh: Closing file is wrong!");
        return 1;
    }

    Print("\n" + ObtainInfoTitle("Finish reading mesh") + "\n");

    return 0;
}

int CgnsMesh::BuildTopology()
{
    Print("\n" + ObtainInfoTitle("Start building mesh topology") + "\n");

    Print("Check and update basic information ...");
    if (CheckBoundaryConnectivity())
    {
        FatalError("CgnsMesh::BuildTopology: Checking boundary connectivity is wrong!");
        return 1;
    }

    Print("Create boundary faces ...");
    if (ProcessBoundaryElementZone())
    {
        FatalError("CgnsMesh::BuildTopology: Processing boundary faces is wrong!");
        return 1;
    }

    Print("Create elements ...");
    if (ProcessVolumeElementZone())
    {
        FatalError("CgnsMesh::BuildTopology: Processing elements is wrong!");
        return 1;
    }

    Print("Update numbers of elements, faces and nodes in this mesh ...");
    CalculateParameters();

    Print("Calculate geometrical parameters ...");
    mesh->CalculateCenterAndVolume();

    CheckFaceDirection();
    
    mesh->PrintMeshInfomation();
    
    Print("\n" + ObtainInfoTitle("End building mesh topology") + "\n");
    
    return 0;
}

int CgnsMesh::BuildBCTopology()
{
    Print("\n" + ObtainInfoTitle("Start building mesh boundary topology") + "\n");
    
    Print("Check and update basic information ...");
    if (CheckBoundaryConnectivity())
    {
        FatalError("CgnsMesh::BuildTopology: Checking boundary connectivity is wrong!");
        return 1;
    }

    Print("Create boundary faces ...");
    if (ProcessBoundaryElementZone())
    {
        FatalError("CgnsMesh::BuildTopology: Processing boundary faces is wrong!");
        return 1;
    }

    Print("Update numbers of elements, faces and nodes in this mesh ...");
    CalculateParameters();

    Print("\n" + ObtainInfoTitle("End building mesh boundary topology") + "\n");
    
    return 0;
}

int CgnsMesh::BuildVolumeTopology()
{
    Print("\n" + ObtainInfoTitle("Start building mesh volume topology") + "\n");
    
    Print("Create elements ...");
    if (ProcessVolumeElementZone())
    {
        FatalError("CgnsMesh::BuildTopology: Processing elements is wrong!");
        return 1;
    }

    Print("Update numbers of elements, faces and nodes in this mesh ...");
    CalculateParameters();

    Print("Calculate geometrical parameters ...");
    mesh->CalculateCenterAndVolume();

    CheckFaceDirection();

    Print("\n" + ObtainInfoTitle("End building mesh volume topology") + "\n"); 
    
    return 0;
}

std::vector<std::string> CgnsMesh::GetBoundaryName()
{
    std::vector<std::string> boundaryNameVector;
    for (int i = 0; i < nBCRegions; i++)
    {
        boundaryNameVector.push_back(boundaryName[i] + "(" + BCTypeName[base_cgns_bc_type[i]] + ")");
    }

    return boundaryNameVector;
}

int CgnsMesh::ReadGeneral()
{
    //打开文件
    if (cg_open(fullFileName.c_str(), CG_MODE_READ, &fileID)) return 1;

    //确认版本号
    float fileVersion;
    if (cg_version(fileID, &fileVersion)) return 1;

    //确认文件存储类型
    int fileType;
    if (cg_get_file_type(fileID, &fileType)) return 1;

    std::string fileTypeName;
    switch (fileType)
    {
    case CG_FILE_NONE:
        fileTypeName = "FILE_NONE";
        break;
    case CG_FILE_ADF:
        fileTypeName = "FILE_ADF";
        break;
    case CG_FILE_HDF5:
        fileTypeName = "FILE_HDF5";
        break;
    case CG_FILE_ADF2:
        fileTypeName = "FILE_ADF2";
        break;
    default:
        FatalError("CgnsMesh::ReadGeneral：Not supported CGNS file type");
        return 1;
        break;
    }

    //确认数据存储精度
    int precision;
    if (cg_precision(fileID, &precision)) return 1;

    //确定文件中有几个base.
    int nbases;
    if (cg_nbases(fileID, &nbases)) return 1;
    if (nbases != 1)
    {
        FatalError("CgnsMesh::ReadGeneral: 仅支持1个base， 当前nbases = " + ToString(nbases));
        return 1;
    }
    baseID = 1;

    //确定文件中的网格zone数（目前只支持单域）
    int nBlocks;
    if (cg_nzones(fileID, baseID, &nBlocks))  return 1;
    if (nBlocks != 1)
    {
        FatalError("CgnsMesh::ReadGeneral: 仅支持1块网格， 当前nBlocks = " + ToString(nBlocks));
        return 1;
    }

    //确定网格维度
    char_33 basename;
    int physDim;
    if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim))  return 1;
    if ( meshDimension != Mesh::MeshDim::mdNoType && cellDim != (int)meshDimension)
    {
	    FatalError("CgnsMesh::ReadGeneral: 网格维度与输入参数不一致");
        return 1;
    }
    if (cellDim == 2) mesh->md_meshDim = Mesh::MeshDim::md2D;
    else              mesh->md_meshDim = Mesh::MeshDim::md3D;
	
    //打印基本信息
    Print("CGNS文件的基本信息： ");
    Print("\t   version = " + ToString(fileVersion));
    Print("\t      tpye = " + fileTypeName);
    Print("\t presicion = " + ToString(precision));
    Print("\t dimension = " + ToString((int)mesh->md_meshDim) + "D");

    return 0;
}

int CgnsMesh::ReadFamilies()
{
    //! 确定文件中的数据区数量（包含体、边界等）
    int nFamilies;
    if (cg_nfamilies(fileID, baseID, &nFamilies)) return 1;
    FamilyName.resize(nFamilies);
    FamilyBCType.resize(nFamilies);
    if (nFamilies <= 0) return 0; //允许nFamilies为0，边界条件未采用family指定

    //跳过体数据区，获得边界信息
    char_33 FamBCName;
    int nGeo, nFamBC;
    for (int Fam = 1; Fam <= nFamilies; ++Fam)
    {
        if (cg_family_read(fileID, baseID, Fam, FamBCName, &nFamBC, &nGeo)) return 1;
        FamilyName[Fam - 1] = FamBCName;
        if (nFamBC)
        {
            if (cg_fambc_read(fileID, baseID, Fam, 1, FamBCName, &FamilyBCType[Fam - 1])) return 1;
        }
        else
        {
            FamilyBCType[Fam - 1] = BCType_t::BCTypeNull;
            FamilyName[Fam - 1] = "unspecified";
        }
    }

    return 0;
}

int CgnsMesh::ReadZone(const bool &fullRead)
{
    nTotalNode = 0;
    nTotalCell = 0;

    int nodeSize, cellSize;
    int nBlocks;
    if (cg_nzones(fileID, baseID, &nBlocks)) return 1;

    for (int zoneID = 1; zoneID <= nBlocks; ++zoneID)
    {
        if (ReadZoneStructInfo(zoneID, nodeSize, cellSize)) return 1;
        if (fullRead) if (ReadCoordinates(zoneID, nodeSize)) return 1;
        if (ReadSectionData(zoneID, fullRead)) return 1;
        if (ReadBoundaryCondition(zoneID)) return 1;

        nTotalNode += nodeSize;
        nTotalCell += cellSize;
    }

    mesh->n_nodeNum = (int)nTotalNode;
    mesh->n_elemNum = (int)nTotalCell;

    return 0;
}

int CgnsMesh::ReadZoneStructInfo(const int &zoneID, int &nNode, int &nCell)
{
    //检查网格类型
    ZoneType_t zoneType;
    if (cg_zone_type(fileID, baseID, zoneID, &zoneType)) return 1;
    if (zoneType == Structured)
    {
        FatalError("CgnsMesh::ReadZoneStructInfo: the mesh must be UNSTRUCTURED!");
        return 1;
    }

    //读取总点数，总单元数
    cgsize_t isize[3][1];
    char_33 zonename;
    if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0])) return 1;

    nNode = (int)isize[0][0];
    nCell = (int)isize[1][0];

    // 检查坐标数据区个数
    int ngrids;
    if (cg_ngrids(fileID, baseID, zoneID, &ngrids)) return 1;
    if (ngrids != 1)
    {
        FatalError("CgnsMesh::ReadZoneStructInfo: ngrids > 1...");
        return 1;
    }

    return 0;
}

int CgnsMesh::ReadCoordinates(const int &zoneID, const int &nodeSize)
{
    //读取点维数
    int nCoords;
    if (cg_ncoords(fileID, baseID, zoneID, &nCoords)) return 1;
    mesh->v_node.clear();
    mesh->v_node.resize(mesh->v_node.size() + nodeSize, Vector0);

    //点数下限和上限
    cgsize_t irmin = 1, irmax = nodeSize;

    // 读取点坐标    
    char_33 coordname;
	DataType_t dataType;
	float *coor_float = nullptr;
	Scalar *coor_double = nullptr;
	if (cg_coord_info(fileID, baseID, zoneID, 1, &dataType, coordname)) return 1;
	
	if (dataType == DataType_t::RealSingle) coor_float = new float[nodeSize];
	else                                    coor_double = new Scalar[nodeSize];

	for (int coorID = 1; coorID <= nCoords; ++coorID)
	{
		if (cg_coord_info(fileID, baseID, zoneID, coorID, &dataType, coordname)) return 1;
		if (dataType == DataType_t::RealSingle)
		{
			if (cg_coord_read(fileID, baseID, zoneID, coordname, dataType, &irmin, &irmax, coor_float)) return 1;
			for (int i = 0; i < nodeSize; ++i)
			{
				if (coorID==1)        mesh->v_node[i].SetX(coor_float[i]);
				else if (coorID == 2) mesh->v_node[i].SetY(coor_float[i]);
				else                  mesh->v_node[i].SetZ(coor_float[i]);
			}
		}
		else
		{
			if (cg_coord_read(fileID, baseID, zoneID, coordname, dataType, &irmin, &irmax, coor_double)) return 1;
			for (int i = 0; i < nodeSize; ++i)
			{
				if (coorID == 1)      mesh->v_node[i].SetX(coor_double[i]);
				else if (coorID == 2) mesh->v_node[i].SetY(coor_double[i]);
				else                  mesh->v_node[i].SetZ(coor_double[i]);
			}
		}
	}

	delete[]coor_float;
	delete[]coor_double;

    return 0;
}

int CgnsMesh::ReadSectionData(const int &zoneID, const bool &fullRead)
{
    //读取内部单元域+边界面元域的域数量
    if (cg_nsections(fileID, baseID, zoneID, &nSections)) return 1;
    Print("\n数据域共计 " + ToString(nSections) + " 块, 如下...");
    if (nSections <= 0)
    {
        FatalError("CgnsMesh::ReadGridConnectivity: No Element Connectivity in CGNS mesh !");
        return 1;
    }

    base_cgns_istart.clear();
    base_cgns_iend.clear();
    base_cgns_elementdatasize.clear();
    base_cgns_elem_type.clear();
    base_cgns_name.clear();

    base_cgns_istart.reserve(nSections);
    base_cgns_iend.reserve(nSections);
    base_cgns_elementdatasize.reserve(nSections);
    base_cgns_elem_type.reserve(nSections);
    base_cgns_name.reserve(nSections);

    mesh->est_shapeType = Element::ElemShapeType::estNoType;

    //获取所有区域的单元点构成（含整域和边界）
    for (int iSection = 1; iSection <= nSections; ++iSection)
    {
        cgsize_t istart, iend; //每个域的起止点
        int nbndry, iparent_flag;
        ElementType_t  etype;//该域的单元类型
        char_33 sectionname;//该域的名称
        cgsize_t elementdatasize; //该域的数据数量

        if (cg_section_read(fileID, baseID, zoneID, iSection, sectionname, &etype,
            &istart, &iend, &nbndry, &iparent_flag)) return 1;

        if (cg_ElementDataSize(fileID, baseID, zoneID, iSection, &elementdatasize)) return 1;

        base_cgns_istart.push_back(istart);
        base_cgns_iend.push_back(iend);
        base_cgns_elem_type.push_back(etype);
        base_cgns_elementdatasize.push_back(elementdatasize);
        base_cgns_name.push_back(std::string(sectionname));

        if (cellDim == 2 && etype == ElementType_t::NGON_n) mesh->est_shapeType = Element::ElemShapeType::estPolygon;
        if (cellDim == 3 && etype == ElementType_t::NFACE_n) mesh->est_shapeType = Element::ElemShapeType::estPolyhedron;
    }

    std::ostringstream stringStream;
    stringStream << std::string(70, '-') << "\n";
    stringStream << "|   ID |      section_name |      type |  begin_index |    end_index |\n";
    stringStream << std::string(70, '-') << "\n";
    for (int iSection = 1; iSection <= nSections; ++iSection)
    {
        stringStream
            << "| " << std::setw(4) << ToString(iSection) << " "
            << "| " << std::setw(17) << std::string(base_cgns_name[iSection - 1]).substr(0, 17) << " "
            << "| " << std::setw(9) << std::string(ElementTypeName[base_cgns_elem_type[iSection - 1]]) << " "
            << "| " << std::setw(12) << ToString(base_cgns_istart[iSection - 1]) << " "
            << "| " << std::setw(12) << ToString(base_cgns_iend[iSection - 1]) << " |\n";
    }
    stringStream << std::string(70, '-');
    Print(stringStream.str());

    sectionType.clear();
    base_cgns_conn_list.clear();
    base_cgns_mixed_conn_list.clear();
    sectionType.resize(nSections, SectionType::NO_TYPE);
    base_cgns_conn_list.resize(nSections);
    base_cgns_mixed_conn_list.resize(nSections);
    for (int iSection = 1; iSection <= nSections; ++iSection)
    {
        const int &istart = (int)base_cgns_istart[iSection - 1];
        const int &iend = (int)base_cgns_iend[iSection - 1];
        const ElementType_t &etype = base_cgns_elem_type[iSection - 1];
        const int &elementdatasize = (int)base_cgns_elementdatasize[iSection - 1];

        const bool mixedFlag = (etype == ElementType_t::MIXED);

        if (!mixedFlag)
        {
            sectionType[iSection - 1] = this->GetSectionType(etype);
            if (sectionType[iSection - 1] == SectionType::NO_TYPE) continue;
            if (!fullRead && sectionType[iSection - 1] == SectionType::VOLUME) continue;
        }

        cgsize_t nelem = iend - istart + 1;
        if (!mixedFlag && etype != ElementType_t::NGON_n && etype != ElementType_t::NFACE_n)
        {
            int pointNum;
            if (cg_npe(etype, &pointNum)) return 1;
            if (elementdatasize != pointNum * nelem)
            {
                FatalError("CgnsMesh::ReadGridConnectivity: 单元的点构成数据不足...");
                return 1;
            }
        }
        base_cgns_conn_list[iSection - 1].resize(elementdatasize);

        if (cg_elements_read(fileID, baseID, zoneID, iSection, &base_cgns_conn_list[iSection - 1][0], nullptr)) return 1;

        if (mixedFlag || etype == ElementType_t::NGON_n || etype == ElementType_t::NFACE_n)
        {
            base_cgns_mixed_conn_list[iSection - 1].reserve(nelem);

            int index0 = 0, pointNum;
            for (int elemID = 0; elemID < nelem; ++elemID)
            {
                const int &velue0 = (int)base_cgns_conn_list[iSection - 1][index0];
                if (mixedFlag) cg_npe((ElementType_t)velue0, &pointNum);
                else           pointNum = velue0;
                base_cgns_mixed_conn_list[iSection - 1].push_back(index0);

                if (mixedFlag && index0 == 0) sectionType[iSection - 1] = this->GetSectionType((ElementType_t)velue0);

                index0 += pointNum + 1;
            }
        }
    }

    return 0;
}

CgnsMesh::SectionType CgnsMesh::GetSectionType(const ElementType_t &etype)
{
    int elementDimension = -1;
    switch (etype)
    {
    case ElementType_t::NODE:
        elementDimension = 0;
        break;

    case ElementType_t::BAR_2:
        elementDimension = 1;
        break;

    case ElementType_t::TRI_3:
    case ElementType_t::QUAD_4:
    case ElementType_t::NGON_n:
        elementDimension = 2;
        break;

    case ElementType_t::TETRA_4:
    case ElementType_t::HEXA_8:
    case ElementType_t::PYRA_5:
    case ElementType_t::PENTA_6:
    case ElementType_t::NFACE_n:
        elementDimension = 3;
        break;

    default:
        FatalError("CgnsMesh::GetElementDimension: ElementTpye not supported by ARI");
    }

    if (elementDimension == cellDim - 1)  return SectionType::BOUNDARY_FACE;
    else if (elementDimension == cellDim) return SectionType::VOLUME;
    else                                  return SectionType::NO_TYPE;
}

int CgnsMesh::ReadBoundaryCondition(const int &zoneID)
{
	//读取总点数，总单元数
	cgsize_t isize[3][1];
	char_33 zonename;
	if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0])) return 1;

	const int nNode = (int)isize[0][0];
    const int nCell = (int)isize[1][0];

	// 读取边界
	int nBCfromFile = 0;
	if (cg_nbocos(fileID, baseID, zoneID, &nBCfromFile)) return 1;

    base_cgns_nBCElem.clear();
    base_cgns_bc_elem_set_type.clear();
    base_cgns_bc_grid_location.clear();
    base_cgns_bc_type.clear();
    base_cgns_bc_conn_list.clear();
    boundaryName.clear();

	base_cgns_nBCElem.reserve(nBCfromFile);
	base_cgns_bc_elem_set_type.reserve(nBCfromFile);
	base_cgns_bc_grid_location.reserve(nBCfromFile);
	base_cgns_bc_type.reserve(nBCfromFile);
	base_cgns_bc_conn_list.reserve(nBCfromFile);
	boundaryName.reserve(nBCfromFile);

	int index = -1;
	for (int iBCRegion = 1; iBCRegion <= nBCfromFile; ++iBCRegion)
	{
		// 边界相关信息
		GridLocation_t bcGridLocation; //边界单元构成类型
		char_33 bocoName; //边界名称
		BCType_t bocoType;//边界条件类型
		PointSetType_t ptsetType;
		DataType_t normDataType;
		int normalIndex, nDataSet;
		cgsize_t nBCElem, normListFlag;

		//step0:读取边界基本信息
		if (cg_boco_info(fileID, baseID, zoneID, iBCRegion, bocoName, &bocoType, &ptsetType, &nBCElem, &normalIndex,
			&normListFlag, &normDataType, &nDataSet)) return 1;

        // ICEM输出二维网格，会将内部网格作为边界，以下判断为实现跳过这类数据
		if (cellDim == 2 && ptsetType == PointSetType_t::ElementList && nBCElem == nCell) continue;
        if (cellDim == 2 && ptsetType == PointSetType_t::PointList && nBCElem == nNode) continue;
        if (cellDim == 2 && ptsetType == PointSetType_t::PointList && nBCElem == nCell) continue;

		index++;

		//step1:确认边界信息构成不是点类型（igr /= Vertex）
		if (cg_goto(fileID, baseID, "Zone_t", zoneID, "ZoneBC_t", 1, "BC_t", iBCRegion, "end")) return 1;
		if (cg_gridlocation_read(&bcGridLocation)) return 1;
		//if (bcGridLocation == Vertex) FatalError("CgnsMesh::ReadBoundaryCondition: 暂不支持边界单元信息为点构成...");

		//step2:确认边界条件指定方式
		//当读取边界类型为FamilySpecified时，需要采用Family中的信息更新边界条件
        std::string BCNameTemp = bocoName;
		if (bocoType == FamilySpecified)
		{
			if (FamilyName.size() == 0)
            {
                FatalError("CgnsMesh::ReadBoundaryCondition: Family is empty...");
                return 1;
            }

            char_33 familyname;
            if (cg_famname_read(familyname)) return 1;

			for (int iFam = 1; iFam <= FamilyName.size(); ++iFam)
			{
                if (CompareTwoStrings(familyname, FamilyName[iFam - 1].c_str()))
                {
                    BCNameTemp = familyname;
					bocoType = FamilyBCType[iFam - 1];
					break;
				}
			}
		}
        else
        {
            char_33 familyname;
            if (cg_famname_read(familyname) == CG_OK)
            {
                bool found = false;
                for (int iFam = 1; iFam <= base_cgns_name.size(); ++iFam)
                {
                    if (CompareTwoStrings(familyname, base_cgns_name[iFam - 1].c_str()))
                    {
                        BCNameTemp = familyname;
                        found = true;
                        break;
                    }
                }
                if (!found) continue;
            }
        }

		//step3: 记录基本信息
		boundaryName.push_back(BCNameTemp);
        base_cgns_nBCElem.push_back((int)nBCElem);
        base_cgns_bc_conn_list.push_back(std::vector<cgsize_t>(nBCElem));
		base_cgns_bc_type.push_back(bocoType);
		base_cgns_bc_elem_set_type.push_back(ptsetType);
		base_cgns_bc_grid_location.push_back(bcGridLocation);

		//step4: 读取单元构成信息
		if (cg_boco_read(fileID, baseID, zoneID, iBCRegion, &base_cgns_bc_conn_list[index][0], nullptr)) return 1;

		//step5: 生成边界域与数据域对应关系
        if (CreateBoundarySectionMap(index)) return 1;

        //step6: 对应关系为空时不属于边界，剔除相关信息
        if (isectionsForThisBCVector[index].empty())
        {
            boundaryName.pop_back();
            base_cgns_nBCElem.pop_back();
            base_cgns_bc_conn_list.pop_back();
            base_cgns_bc_type.pop_back();
            base_cgns_bc_elem_set_type.pop_back();
            base_cgns_bc_grid_location.pop_back();
            isectionsForThisBCVector.pop_back();
            index--;
        }
	}
	nBCRegions = boundaryName.size();

    // 清除boundaryVertexID信息
    if (!boundaryVertexIDList.empty())
    {
        boundaryVertexIDList.clear();
        boundaryVertexIDListMax.clear();
        boundaryVertexIDListMin.clear();
    }

    //打印边界信息
    std::ostringstream stringStream;
    stringStream << "\n边界共计 " + ToString(nBCRegions) + " 个, 如下...\n";
    stringStream << std::string(70, '-') << "\n";
    stringStream << "|   ID |                            boundary_name |    boundary_type |\n";
    stringStream << std::string(70, '-') << "\n";
    int boundaryID = 0;
    for (int index = 0; index < nBCRegions; ++index)
    {
        stringStream
            << "| " << std::setw(4) << ToString(boundaryID + 1) << " "
            << "| " << std::setw(40) << std::string(boundaryName[boundaryID]) << " "
            << "| " << std::setw(16) << std::string(BCTypeName[base_cgns_bc_type[boundaryID]]) << " |\n";

        boundaryID++;
    }
    stringStream << std::string(70, '-');
    Print(stringStream.str());

    return 0;
}

int CgnsMesh::CreateBoundarySectionMap(const int &iBCRegion)
{
    isectionsForThisBCVector.push_back(std::vector<std::pair<int, std::vector<int>>>());
    auto &BCSectionList = isectionsForThisBCVector[isectionsForThisBCVector.size() - 1];

    bool findSectionFlag = false;
    //与某一数据域名称相同时，该边界域仅包含此数据域
    for (int i = 0; i < nSections; ++i)
    {
        if (sectionType[i] != SectionType::BOUNDARY_FACE) continue;

        if (boundaryName[iBCRegion] == base_cgns_name[i])
        {
            BCSectionList.push_back(std::make_pair(i, std::vector<int>()));
            findSectionFlag = true;
            break;
        }
    }

    //不存在名称相同的数据域时，该边界包含多个数据域
    //例如边界名称为body，数据域名称为tri_body和quad_body两部分
    if (!findSectionFlag)
    {
        const int &igr = base_cgns_bc_grid_location[iBCRegion];
        const int &bc_elem_set_type = base_cgns_bc_elem_set_type[iBCRegion];

        if ( igr != GridLocation_t::EdgeCenter && igr != GridLocation_t::FaceCenter
            && bc_elem_set_type == ElementRange && bc_elem_set_type == ElementList )
            WarningContinue("grid location of boundary isnot FaceCenter!");

        if ( ( (igr == GridLocation_t::EdgeCenter || igr == GridLocation_t::FaceCenter) && bc_elem_set_type == PointRange )
            || bc_elem_set_type == ElementRange ) /// 旧版ICEM
        {
            // 采用单元范围进行判断
            const cgsize_t &startElement = base_cgns_bc_conn_list[iBCRegion][0];
            const cgsize_t &endElement = base_cgns_bc_conn_list[iBCRegion][1];
            for (int i = 0; i < nSections; ++i)
            {
                if (sectionType[i] != SectionType::BOUNDARY_FACE) continue;
                if (startElement <= base_cgns_istart[i] && base_cgns_iend[i] <= endElement)
                    BCSectionList.push_back(std::make_pair(i, std::vector<int>()));
            }
        }
        else
        {
            //得到ibcregion边界上单元/边/点列表
            std::vector<cgsize_t> bcList;
            cgsize_t bcListMax = 0, bcListMin = 0;
            if (bc_elem_set_type == PointRange)
            {
                const int &istart = (int)base_cgns_bc_conn_list[iBCRegion][0];
                const int &iend = (int)base_cgns_bc_conn_list[iBCRegion][1];
                const int size = iend - istart + 1;
                bcList.resize(size);
                for (int j = 0; j < size; ++j) bcList[j] = istart + j;
                bcListMax = iend;
                bcListMin = istart;
            }
            else if (bc_elem_set_type == PointList || bc_elem_set_type == ElementList)
            {
                bcList.resize(base_cgns_nBCElem[iBCRegion]);
                bcListMax = base_cgns_bc_conn_list[iBCRegion][0];
                bcListMin = base_cgns_bc_conn_list[iBCRegion][0];
                for (int j = 0; j < base_cgns_nBCElem[iBCRegion]; ++j)
                {
                    bcList[j] = base_cgns_bc_conn_list[iBCRegion][j];
                    bcListMax = bcListMax > bcList[j] ? bcListMax : bcList[j];
                    bcListMin = bcListMin < bcList[j] ? bcListMin : bcList[j];
                }
            }
            else
            {
                FatalError("PointSetType of bc isnot supported!");
            }

            if (igr == GridLocation_t::EdgeCenter || igr == GridLocation_t::FaceCenter || bc_elem_set_type == ElementList)
            {
                for (int i = 0; i < nSections; ++i)
                {
                    if (sectionType[i] != SectionType::BOUNDARY_FACE) continue;
                    if (base_cgns_iend[i] < bcListMin || base_cgns_istart[i] > bcListMax) continue;
                    
                    if ((int)bcList.size() <= base_cgns_iend[i] - base_cgns_istart[i] + 1)
                    {
                        std::vector<int> dataList;
                        dataList.reserve(bcList.size());

                        for (int j = 0; j < bcList.size(); ++j)
                        {
                            if (base_cgns_istart[i] <= bcList[j] && bcList[j] <= base_cgns_iend[i])
                                dataList.push_back((int)(bcList[j] - base_cgns_istart[i]));
                        }

                        if (!dataList.empty()) BCSectionList.push_back(std::make_pair(i, dataList));
                    }
                    else
                    {
                        if (std::find(bcList.begin(), bcList.end(), base_cgns_istart[i]) != bcList.end())
                        {
                            BCSectionList.push_back(std::make_pair(i, std::vector<int>()));
                        }
                    }
                }
            }
            else if (igr == GridLocation_t::Vertex)
            {
                if (boundaryVertexIDList.empty())
                {
                    boundaryVertexIDListMax.clear();
                    boundaryVertexIDListMin.clear();
                    boundaryVertexIDList.resize(nSections);
                    boundaryVertexIDListMax.resize(nSections, -1);
                    boundaryVertexIDListMin.resize(nSections, INT_MAX);

                    for (int i = 0; i < nSections; ++i)
                    {
                        if (sectionType[i] != SectionType::BOUNDARY_FACE) continue;

                        //形成边界点列表
                        const ElementType_t &etype = base_cgns_elem_type[i];
                        if (etype == ElementType_t::MIXED || etype == ElementType_t::NGON_n)
                        {
                            cgsize_t nelemt = base_cgns_iend[i] - base_cgns_istart[i] + 1;

                            int size = 0;
                            for (cgsize_t k = 0; k < nelemt; k++)
                                size += (int)(base_cgns_conn_list[i][base_cgns_mixed_conn_list[i][k]]);

                            boundaryVertexIDList[i].reserve(size);
                            for (cgsize_t k = 0; k < nelemt; k++)
                            {
                                const int j0 = (int)(base_cgns_conn_list[i][base_cgns_mixed_conn_list[i][k] + 1]);
                                const int j1 = j0 + (int)(base_cgns_conn_list[i][base_cgns_mixed_conn_list[i][k]]);
                                for (int j = j0; j < j1; j++)
                                {
                                    boundaryVertexIDList[i].push_back(j);
                                    boundaryVertexIDListMax[i] = Max(boundaryVertexIDListMax[i], j);
                                    boundaryVertexIDListMin[i] = Min(boundaryVertexIDListMin[i], j);
                                }
                            }
                        }
                        else
                        {
                            boundaryVertexIDList[i].resize(base_cgns_elementdatasize[i]);
                            for (cgsize_t m = 0; m < base_cgns_elementdatasize[i]; m++)
                            {
                                boundaryVertexIDList[i][m] = (int)base_cgns_conn_list[i][m];
                                boundaryVertexIDListMax[i] = Max(boundaryVertexIDListMax[i], boundaryVertexIDList[i][m]);
                                boundaryVertexIDListMin[i] = Min(boundaryVertexIDListMin[i], boundaryVertexIDList[i][m]);
                            }
                        }
                    }
                }

                for (int i = 0; i < nSections; ++i)
                {
                    if (sectionType[i] != SectionType::BOUNDARY_FACE) continue;
                    if (boundaryVertexIDListMax[i] < bcListMin || boundaryVertexIDListMin[i] > bcListMax) continue;

                    //通过对照点决定ibcregion边界对应那个isection ElementZone
                    bool bcVertexNotFound = false;
                    for (int n = 0; n < bcList.size(); n++)
                    {
                        if (std::find(boundaryVertexIDList[i].begin(), boundaryVertexIDList[i].end(), bcList[n])
                            == boundaryVertexIDList[i].end())
                        {
                            bcVertexNotFound = true;
                            break;
                        }
                    }
                    if (bcVertexNotFound) continue;

                    //所有点全部找到
                    BCSectionList.push_back(std::make_pair(i, std::vector<int>()));
                }
            }
            else
            {
                FatalError("Boundary ElementZone " + ToString(iBCRegion) + "location Error");
                return 1;
            }
        }
    }

    return 0;
}

Element::ElemShapeType CgnsMesh::ConvertElementTypeCGNS2ARI(const ElementType_t &type)
{
    switch (type)
    {
    case ElementType_t::TRI_3:
        return Element::ElemShapeType::estTriangular;

    case ElementType_t::TETRA_4:
        return Element::ElemShapeType::estTetrahedral;

    case ElementType_t::QUAD_4:
        return Element::ElemShapeType::estQuadrilateral;

    case ElementType_t::HEXA_8:
        return Element::ElemShapeType::estHexahedral;

    case ElementType_t::PYRA_5:
        return Element::ElemShapeType::estPyramid;

    case ElementType_t::PENTA_6:
        return Element::ElemShapeType::estWedge;

    case ElementType_t::MIXED:
        return Element::ElemShapeType::estMixed;

    case ElementType_t::NFACE_n:
        return Element::ElemShapeType::estPolyhedron;
        break;
    case ElementType_t::NGON_n:
        return Element::ElemShapeType::estPolygon;

    case ElementType_t::BAR_2:
        return Element::ElemShapeType::estLine;

    case ElementType_t::ElementTypeNull:
    case ElementType_t::ElementTypeUserDefined:
    case ElementType_t::NODE:
    case ElementType_t::BAR_3:
    case ElementType_t::TRI_6:
    case ElementType_t::QUAD_8:
    case ElementType_t::QUAD_9:
    case ElementType_t::TETRA_10:
    case ElementType_t::PYRA_14:
    case ElementType_t::PENTA_15:
    case ElementType_t::PENTA_18:
    case ElementType_t::HEXA_20:
    case ElementType_t::HEXA_27:
    case ElementType_t::PYRA_13:
    case ElementType_t::BAR_4:
    case ElementType_t::TRI_9:
    case ElementType_t::TRI_10:
    case ElementType_t::QUAD_12:
    case ElementType_t::QUAD_16:
    case ElementType_t::TETRA_16:
    case ElementType_t::TETRA_20:
    case ElementType_t::PYRA_21:
    case ElementType_t::PYRA_29:
    case ElementType_t::PYRA_30:
    case ElementType_t::PENTA_24:
    case ElementType_t::PENTA_38:
    case ElementType_t::PENTA_40:
    case ElementType_t::HEXA_32:
    case ElementType_t::HEXA_56:
    case ElementType_t::HEXA_64:
    default:
        FatalError("CgnsMesh::ConvertElementTypeCGNS2ARI: ElementTpye not supported by ARI");
        return Element::ElemShapeType::estNoType;
    }
}

int CgnsMesh::ProcessVolumeElementZone()
{
    std::cout << "Process Volume Element ..." << std::endl;

    bool polyhedraFlag = false;
    for (int iSection = 0; iSection < nSections; ++iSection)
    {
        if ((int)base_cgns_elem_type[iSection] == (int)NFACE_n)
            polyhedraFlag = true;
    }

    if (polyhedraFlag)
    {
        std::vector<int> bcSectionIDList;
        for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
        {
            const auto &list = isectionsForThisBCVector[ibcregion];
            for (int i = 0; i < list.size(); ++i)
            {
                if (sectionType[list[i].first] == SectionType::BOUNDARY_FACE)
                    bcSectionIDList.push_back(list[i].first);
            }
        }

        bool hasInteriorFace = false;
        for (int iSection = 0; iSection < nSections; ++iSection)
        {
            if (sectionType[iSection] == SectionType::BOUNDARY_FACE)
            {
                if (std::find(bcSectionIDList.begin(), bcSectionIDList.end(), iSection) == bcSectionIDList.end())
                {
                    sectionType[iSection] = SectionType::INTERIOR_FACE;
                    hasInteriorFace = true;
                }
            }
        }
        if (!hasInteriorFace)
        {
            FatalError("CgnsMesh::ProcessVolumeElementZone: no polyfaces for polyhedra!");
            return 1;
        }
    }

    mesh->v_elem.reserve(mesh->n_elemNum);
    if (!polyhedraFlag)
    {
        if (PopulateV_elem()) return 1;
    }
    else
    {
        if (PopulateV_elem_Polyhedra()) return 1;
    }

    return 0;
}

int CgnsMesh::PopulateV_elem()
{
    std::vector<int> sectionIDList(nTotalCell);
    std::vector<int> localIDList(nTotalCell);
    int globalID = 0;
    for (int iSection = 0; iSection < nSections; ++iSection)
    {
        if (sectionType[iSection] != SectionType::VOLUME) continue;
        if (base_cgns_elem_type[iSection] == NFACE_n) continue;

        for (cgsize_t icell = base_cgns_istart[iSection]; icell <= base_cgns_iend[iSection]; ++icell)
        {
            sectionIDList[globalID] = iSection;
            localIDList[globalID] = (int)(icell - base_cgns_istart[iSection]);
            globalID++;
        }
    }

    mesh->v_elem.resize(nTotalCell);
    ARI_OMP(parallel for schedule(static))
    for (int globalID = 0; globalID < nTotalCell; ++globalID)
    {
        PopulateElement(globalID, sectionIDList[globalID], localIDList[globalID]);
    }
    
    //因效率问题，李艳亮修改添加
    Print("Create faces by elements and points ...");
    if (CreateFaceByElemAndPoint()) return 1;

    return 0;
}

int CgnsMesh::PopulateElement(const int &globalID, const int &iSection, const int &icellLocal)
{
    mesh->v_elem[globalID].n_ElementZoneID = iSection;

    //读取单元类型及节点数量
    const ElementType_t &etype = base_cgns_elem_type[iSection];
    const bool mixedFlag = (etype == MIXED || etype == NGON_n || etype == NFACE_n);

    mesh->v_elem[globalID].est_shapeType = ConvertElementTypeCGNS2ARI(etype);
    
    int istart, ptnum;
    if (!mixedFlag)
    {
        cg_npe(etype, &ptnum);
        istart = icellLocal * ptnum;
    }
    else
    {
        const int &typeIndex = (int)base_cgns_mixed_conn_list[iSection][icellLocal];
        const int &typeValue = (int)base_cgns_conn_list[iSection][typeIndex];
        if (etype == ElementType_t::MIXED)
        {
            ElementType_t etypeTemp = static_cast<ElementType_t>(typeValue);
            mesh->v_elem[globalID].est_shapeType = ConvertElementTypeCGNS2ARI(etypeTemp);
            cg_npe(etypeTemp, &ptnum);
        }
        else
        {
            ptnum = typeValue;
        }
        istart = typeIndex + 1;
    }

    mesh->v_elem[globalID].v_nodeID.resize(ptnum);
    for (int n = 0; n < ptnum; ++n)
        mesh->v_elem[globalID].v_nodeID[n] = (int)base_cgns_conn_list[iSection][istart + n] - 1;

    return 0;
}

int CgnsMesh::PopulateV_elem_Polyhedra()
{
    std::vector<Face>().swap(mesh->v_face);
    int faceSizeTotal = 0;
    for (int iSection = 0; iSection < nSections; ++iSection)
    {
        if (sectionType[iSection] == SectionType::BOUNDARY_FACE ||
            sectionType[iSection] == SectionType::INTERIOR_FACE)
        {
            const int &iFaceStart = (int)base_cgns_istart[iSection];
            const int &iFaceEnd = (int)base_cgns_iend[iSection];
            const int faceSize = iFaceEnd - iFaceStart + 1;
            faceSizeTotal += faceSize;
        }
    }
    mesh->v_face.resize(faceSizeTotal);

    for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
        mesh->vv_boundaryFaceID[ibcregion].clear();

    std::vector<int> faceIDCG2FLMap(faceSizeTotal, -1);
    int faceIDFL = 0;

    // 创建边界面，保证边界面在面列表起始位置
    for (int iSection = 0; iSection < nSections; ++iSection)
    {
        if (sectionType[iSection] != SectionType::BOUNDARY_FACE) continue;

        int bcID = -1;
        for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
        {
            const auto &list = isectionsForThisBCVector[ibcregion];
            for (auto it = list.begin(); it != list.end(); ++it)
            {
                if (iSection == it->first)
                {
                    bcID = ibcregion;
                    break;
                }
            }
            if (bcID != -1) break;
        }
        
        const int &iFaceStart = (int)base_cgns_istart[iSection];
        const int &iFaceEnd = (int)base_cgns_iend[iSection];
        for (int iFace = iFaceStart; iFace <= iFaceEnd; ++iFace)
        {
            const int &index = (int)base_cgns_mixed_conn_list[iSection][iFace - iFaceStart];
            const int &faceNodeSize = (int)base_cgns_conn_list[iSection][index];

            const int faceIDCG = iFace - 1;
            faceIDCG2FLMap[faceIDCG] = faceIDFL;
            
            Face &face = mesh->v_face[faceIDFL];
            face.v_nodeID.resize(faceNodeSize);
            for (cgsize_t iNode = 0; iNode < faceNodeSize; ++iNode)
                face.v_nodeID[iNode] = (int)base_cgns_conn_list[iSection][index + iNode + 1] - 1;

            mesh->vv_boundaryFaceID[bcID].push_back(faceIDFL);

            faceIDFL++;
        }
    }

    // 创建内部面
    for (int iSection = 0; iSection < nSections; ++iSection)
    {
        if (sectionType[iSection] != SectionType::INTERIOR_FACE) continue;

        const int &iFaceStart = (int)base_cgns_istart[iSection];
        const int &iFaceEnd = (int)base_cgns_iend[iSection];
        for (int iFace = iFaceStart; iFace <= iFaceEnd; ++iFace)
        {
            const int &index = (int)base_cgns_mixed_conn_list[iSection][iFace - iFaceStart];
            const int &faceNodeSize = (int)base_cgns_conn_list[iSection][index];

            const int faceIDCG = iFace - 1;
            faceIDCG2FLMap[faceIDCG] = faceIDFL;
            
            Face &face = mesh->v_face[faceIDFL];
            face.v_nodeID.resize(faceNodeSize);
            for (cgsize_t iNode = 0; iNode < faceNodeSize; ++iNode)
                face.v_nodeID[iNode] = (int)base_cgns_conn_list[iSection][index + iNode + 1] - 1;
                
            faceIDFL++;
        }
    }

    // 创建单元信息
    for (int iSection = 0; iSection < nSections; ++iSection)
    {
        if (sectionType[iSection] != SectionType::VOLUME) continue;

        const ElementType_t &etype = base_cgns_elem_type[iSection];

        const int &iCellStart = (int)base_cgns_istart[iSection];
        const int &iCellEnd = (int)base_cgns_iend[iSection];
        for (cgsize_t iCell = iCellStart; iCell <= iCellEnd; ++iCell)
        {
            mesh->v_elem.push_back(Element());
            const int elemID = mesh->v_elem.size() - 1;
            Element &TempElem = mesh->v_elem[elemID];
            TempElem.n_ElementZoneID = iSection;
            TempElem.est_shapeType = ConvertElementTypeCGNS2ARI(etype);

            const int &typeIndex = (int)base_cgns_mixed_conn_list[iSection][iCell - iCellStart];
            const int &faceNum = (int)base_cgns_conn_list[iSection][typeIndex];
            const int istart = typeIndex + 1;

            TempElem.v_faceID.resize(faceNum);
            for (int n = 0; n < faceNum; ++n)
            {
                const int &faceIDLocal = (int)base_cgns_conn_list[iSection][istart + n];
                const int faceIDCG = abs(faceIDLocal) - 1;
                const int faceIDFL = faceIDCG2FLMap[faceIDCG];

                TempElem.v_faceID[n] = faceIDFL;
                if (faceIDLocal > 0) mesh->v_face[faceIDFL].n_owner = elemID;
                else                 mesh->v_face[faceIDFL].n_neighbor = elemID;
            }

            PopulatePolyhedronCell(elemID);
        }
    }

    return 0;
}

int CgnsMesh::ProcessBoundaryElementZone()
{
    std::cout << "Process Boundary ElementZone ..." << std::endl;

    int totalBCFaceSize = 0;
    for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
    {
        const auto &isectionsForThisBC = isectionsForThisBCVector[ibcregion];
        if (isectionsForThisBC.empty()) continue;

        // 开始填充v_boundaryFaceZone
        int BCfaceSize = 0;
        for (int i = 0; i < isectionsForThisBC.size(); ++i)
        {
            if (isectionsForThisBC[i].second.empty())
            {
                const int &sectionID = isectionsForThisBC[i].first;
                BCfaceSize += (int)(base_cgns_iend[sectionID] - base_cgns_istart[sectionID] + 1);
            }
            else
            {
                BCfaceSize += isectionsForThisBC[i].second.size();
            }
        }
        mesh->vv_boundaryFaceID.push_back(std::vector<int>());
        int boundaryID = mesh->vv_boundaryFaceID.size() - 1;
        mesh->vv_boundaryFaceID[boundaryID].reserve(BCfaceSize);
        mesh->v_boundaryName.push_back(boundaryName[ibcregion]);
        totalBCFaceSize += BCfaceSize;
    }

    mesh->v_face.reserve(totalBCFaceSize);

    int boundaryID = -1;
    for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
    {
        const auto &isectionsForThisBC = isectionsForThisBCVector[ibcregion];
        if (isectionsForThisBC.empty()) continue;

        boundaryID++;

        for (int i = 0; i < isectionsForThisBC.size(); ++i)
        {
            const int &sectionID = isectionsForThisBC[i].first;
            if (isectionsForThisBC[i].second.empty())
            {
                const int &startFace = (int)base_cgns_istart[sectionID];
                const int &endFace = (int)base_cgns_iend[sectionID];
                const int faceSize = endFace - startFace + 1;
                for (int j = 0; j < faceSize; ++j) PopulateBCFace(sectionID, boundaryID, j);
            }
            else
            {
                const auto &indexList = isectionsForThisBC[i].second;
                const int faceSize = indexList.size();
                for (int j = 0; j < faceSize; ++j) PopulateBCFace(sectionID, boundaryID, indexList[j]);
            }
        }
    }

    return 0;
}

int CgnsMesh::CheckBoundaryConnectivity()
{
    int ibase = 1;

    for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
    {
        for (int j = 0; j < base_cgns_nBCElem[ibcregion]; ++j)
        {
            if (base_cgns_bc_conn_list[ibcregion][j] <= nTotalCell)
            {
                ibase = 0;
                break;
            }
        }
        if (ibase == 0) break;
    }

    if (ibase == 0)
    {
        for (int ibcregion = 0; ibcregion < nBCRegions; ++ibcregion)
        {
            if ((base_cgns_bc_grid_location[ibcregion] != Vertex))
            {
                for (int j = 0; j < base_cgns_nBCElem[ibcregion]; ++j)
                {
                    base_cgns_bc_conn_list[ibcregion][j] += nTotalCell;
                }
            }
        }
    }

    return 0;
}

int CgnsMesh::PopulateBCFace(const int &isection, const int &ibcregion, const cgsize_t &icellLocal)
{
    int facept_num;

    const ElementType_t &etype = base_cgns_elem_type[isection];
    if (etype == MIXED || etype == NGON_n)
    {
        const int &typeIndex = (int)base_cgns_mixed_conn_list[isection][icellLocal];
        const int &typeValue = (int)base_cgns_conn_list[isection][typeIndex];
        if (etype == MIXED) cg_npe(ElementType_t(typeValue), &facept_num);
        else                facept_num = typeValue;
    }
    else
    {
        if (cg_npe(ElementType_t(etype), &facept_num)) return 1;
    }

    const bool flag = ((etype == MIXED) || (etype == NGON_n));
    std::vector<int> v_nodeID(facept_num);
    for (int n = 0; n < facept_num; n++)
    {
        int index0;
        if (flag) index0 = (int)base_cgns_mixed_conn_list[isection][icellLocal] + n + 1;
        else      index0 = (int)icellLocal * facept_num + n;
        v_nodeID[n] = (int)base_cgns_conn_list[isection][index0] - 1;
    }
    mesh->v_face.push_back(Face(v_nodeID));
    mesh->vv_boundaryFaceID[ibcregion].push_back(mesh->v_face.size() - 1);

    return 0;
}