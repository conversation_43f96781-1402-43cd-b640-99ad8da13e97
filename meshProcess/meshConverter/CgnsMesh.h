﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file CgnsMesh.h
//! <AUTHOR>
//! @brief 读取CGNS格式非结构网格文件
//! 
//! @date  2024-06-17
//
//------------------------------修改日志----------------------------------------
//
// 2021-05-12 尹强 
// 说明：建立并规范化
// 
// 2024-06-17 乔龙、李艳亮
// 说明：建立CGNS基类，实现结构网格CGNS文件读取
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshConverter_CgnsMesh_
#define _meshProcess_meshConverter_CgnsMesh_

#include "meshProcess/meshConverter/CgnsMeshBase.h"

/**
* @brief cgns网格转换类
*
*/
class CgnsMesh :public CgnsMeshBase
{
    enum SectionType
    {
        UNDEFINED = 0, ///< 未定义类型
        BOUNDARY_FACE, ///< 边界面
        INTERIOR_FACE, ///< 内部面
        VOLUME, ///< 体单元
        NO_TYPE ///< 无类型
    };

public:
	/**
	* @brief 构造函数
	*
	* @param[in] MshFileName 网格文件名称
    * @param[in] meshDimension_ 网格维度
	* @param[out] mesh_ 网格指针
	*/
    CgnsMesh(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_);
	
	/**
	* @brief 析构函数
	*
	*/
    ~CgnsMesh();

    /**
     * @brief 读取cgns文件
     * 
     * @param[in] fullRead 是否读取所有数据标识，false时仅读取边界拓扑信息
     */
    int ReadMesh(const bool &fullRead = true);

    /**
     * @brief 建立网格的拓扑结构
     * 
     */
    int BuildTopology();

    /**
     * @brief 建立边界拓扑结构
     * 
     */
    int BuildBCTopology();

	/**
	* @brief 建立网格空间拓扑结构
	*
	*/
    int BuildVolumeTopology();

    /**
     * @brief 获取边界名称
     * 
     * @return std::vector<std::string> 边界名称
     */
    std::vector<std::string> GetBoundaryName();

private:
    /**
     * @brief 读取基本信息
     * 
     */
    int ReadGeneral();

    /**
     * @brief 读取Families
     * 
     */
    int ReadFamilies();

    /**
     * @brief 读取所有数据域，目前仅支持读取一个数据域
     * 
     * @param[in] fullRead 是否读取所有数据标识，false时仅读取边界拓扑信息
     */
    int ReadZone(const bool &fullRead);

    /**
     * @brief 读取数据域信息
     * 
     * @param[in] zoneID 数据域编号
     * @param[out] nNode 当前数据域网格节点数量
     * @param[out] nCell 当前数据域网格单元数量
     */
    int ReadZoneStructInfo(const int &zoneID, int &nNode, int &nCell);
    
    /**
     * @brief 读取数据域信息
     * 
     * @param zoneID 数据域编号
     * @param[in] fullRead 是否读取所有数据标识，false时仅读取边界拓扑信息
     */
    int ReadSectionData(const int &zoneID, const bool &fullRead);

    /**
     * @brief 读取网格节点坐标
     * 
     * @param[in] zoneID 数据域编号
     * @param[in] nodeSize 节点数量
     */
    int ReadCoordinates(const int &zoneID, const int &nodeSize);

    /**
     * @brief 读取边界数据
     * 
     * @param[in] zoneID 数据域编号
     */
    int ReadBoundaryCondition(const int &zoneID);

    /**
     * @brief 创建空间单元
     * 
     */
    int ProcessVolumeElementZone();
    
    /**
     * @brief 创建边界单元
     * 
     */
    int ProcessBoundaryElementZone();
    
    /**
     * @brief 创建常规空间单元
     * 
     */
    int PopulateV_elem();
    
    /**
     * @brief 创建多面体空间单元
     * 
     */
    int PopulateV_elem_Polyhedra();

    /**
     * @brief 生成边界面
     * 
     * @param isection section编号
     * @param ibcregion bc编号
     * @param icellLocal 单元起始编号
     */
    int PopulateBCFace(const int &isection, const int &ibcregion, const cgsize_t &icellLocal);

    /**
     * @brief 检查边界连接性
     * 
     */
    int CheckBoundaryConnectivity();

    /**
     * @brief 创建单元的面
     * 
     */
    void PopulateCellFaces();

    /**
     * @brief 创建边界与Section对应关系
     * 
     * @param iBCRegion 边界编号
     */
    int CreateBoundarySectionMap(const int &iBCRegion);

    /**
     * @brief 生成单元
     * 
     * @param globalID 单元全局编号
     * @param iSection 数据域编号
     * @param icellLocal 单元编号
     */
    int PopulateElement(const int &globalID, const int &iSection, const int &icellLocal);

    /**
     * @brief 获取Section Type对象
     * 
     * @param[in] etype 单元类型
     * @return SectionType Section类型
     */
    SectionType GetSectionType(const ElementType_t &etype);

    /**
     * @brief 单元类型转换
     * 
     * @param type cgns单元类型
     * @return Element::ElemShapeType 飞廉单元类型
     */
    Element::ElemShapeType ConvertElementTypeCGNS2ARI(const ElementType_t &type);

private:
    /// 局部常量，其值为-1
    const int offsetCgns2ARI;

    /// 字符串
    typedef char char_33[33];
    
    /// Family名称
    std::vector<std::string> FamilyName;

    /// 边界类型
    std::vector<BCType_t> FamilyBCType;

    /// 网格维度
    int cellDim;

    /// 文件号,数据区号
    int fileID, baseID;
    int nSections, nBCRegions;

    /// 网格节点总数和单元总数
    cgsize_t nTotalNode, nTotalCell;
    
    /// ElementZone数据
    std::vector<cgsize_t> base_cgns_istart;
    std::vector<cgsize_t> base_cgns_iend;
    std::vector<std::vector<cgsize_t>> base_cgns_conn_list;
    std::vector<std::vector<cgsize_t>> base_cgns_mixed_conn_list;
    std::vector<cgsize_t> base_cgns_elementdatasize;
    std::vector<ElementType_t> base_cgns_elem_type;
    std::vector<std::string> base_cgns_name;

    /// 边界相关数据
    std::vector<int> base_cgns_nBCElem;
    std::vector<int> base_cgns_bc_elem_set_type;
    std::vector<int> base_cgns_bc_grid_location;
    std::vector<int> base_cgns_bc_type;
    std::vector<std::vector<cgsize_t>> base_cgns_bc_conn_list;
    std::vector<std::string> boundaryName;
    std::vector<std::vector<std::pair<int, std::vector<int>>>> isectionsForThisBCVector;

    /// 数据域类型
    std::vector<SectionType> sectionType;
    
    /// 边界点编号列表及最大最小编号
    std::vector<std::vector<int>> boundaryVertexIDList;
    std::vector<int> boundaryVertexIDListMax;
    std::vector<int> boundaryVertexIDListMin;

};

#endif
