﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file CgnsMeshBase.h
//! <AUTHOR>
//! @brief 读取CGNS格式网格文件
//! 
//! @date  2024-06-17
//
//------------------------------修改日志----------------------------------------
//
// 2021-05-12 尹强 
// 说明：建立并规范化
// 
// 2024-06-17 乔龙、李艳亮
// 说明：建立CGNS基类，实现结构网格CGNS文件读取
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshConverter_CgnsMeshBase_
#define _meshProcess_meshConverter_CgnsMeshBase_

#include "basic/configure/Configure.h"
#include "meshProcess/meshConverter/MeshConverter.h"
#include <cgnslib.h>

#if CGNS_VERSION < 3100
#define cgsize_t int
#endif

/// 字符串
typedef char char_33[33];

/**
* @brief cgns网格转换类
*
*/
class CgnsMeshBase : public MeshConverter
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in] MshFileName 网格文件名称
    * @param[in] meshDimension_ 网格维度
	* @param[out] mesh_ 网格指针
	*/
    CgnsMeshBase(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_);
	
	/**
	* @brief 析构函数
	*
	*/
    ~CgnsMeshBase();

    /**
     * @brief 获取网格类型
     * 
     * @return int 1为报错, 2为结构网格，3为非结构网格
     */
    int ObtainZoneType();

public:
    /**
     * @brief 读取cgns文件
     * 
     * @param[in] fullRead 是否读取所有数据标识，false时仅读取边界拓扑信息
     */
    virtual int ReadMesh(const bool &fullRead = true) = 0;

    /**
     * @brief 建立网格的拓扑结构
     * 
     */
    virtual int BuildTopology() = 0;

    /**
     * @brief 建立边界拓扑结构
     * 
     */
    virtual int BuildBCTopology() = 0;

	/**
	* @brief 建立网格空间拓扑结构
	*
	*/
    virtual int BuildVolumeTopology() = 0;

    /**
     * @brief 获取边界名称
     * 
     * @return std::vector<std::string> 边界名称
     */
    virtual std::vector<std::string> GetBoundaryName() = 0;

protected:
    /**
     * @brief 检查网格面法向矢量方向
     * 
     */
    void CheckFaceDirection();

};

#endif
