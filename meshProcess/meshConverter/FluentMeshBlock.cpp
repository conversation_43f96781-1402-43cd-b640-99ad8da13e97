﻿#include "meshProcess/meshConverter/FluentMeshBlock.h"
#include "basic/postTools/Ensight.h"

FluentMeshBlock::FluentMeshBlock(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_)
    :MeshConverter(MshFileName, meshDimension_, mesh_), offset(1), internalBCID(2)
{    
}

int FluentMeshBlock::BuildTopology()
{
    // 填充单元的v_faceID
    PopulateCellFaces();

    // 使用单元的v_faceID填充单元的v_nodeID
    PopulateCellNodes();

    // 建立边界信息
    this->EstablishBoundary();

    //Calculate numbers of elements, faces and nodes in this mesh
    this->CalculateParameters();

    //Calculate geometrical parameters
    mesh->CalculateCenterAndVolume();

    //检查面法向
    mesh->CheckFaceDirection();
    
    mesh->PrintMeshInfomation();
    
    // TODO:目前只支持单域网格
    //assert(v_elementZone.size()==1);

    //if (v_elementZone.size() > 1)
    //{
    //    RegionConnection bridges;
    //    Decompose(bridges);
    //}
    
    return 0;
}


int FluentMeshBlock::BuildBCTopology()
{
    // 建立边界信息
    this->EstablishBoundary();

    //Calculate numbers of elements, faces and nodes in this mesh
    this->CalculateParameters();

    mesh->PrintMeshInfomation();
    
    return 0;
}


void FluentMeshBlock::PopulateCellFaces()
{
    for (int i = 0; i < (int)mesh->v_face.size(); ++i)
    {
        const int &nOwn = mesh->v_face[i].n_owner;
        const int &nNei = mesh->v_face[i].n_neighbor;

        mesh->v_elem[nOwn].v_faceID.push_back(i);
        if (nNei != -1) mesh->v_elem[nNei].v_faceID.push_back(i);
    }
}

std::vector<std::string> FluentMeshBlock::GetBoundaryName()
{
    std::vector<std::string> boundaryName;
    for (int i = 0; i < v_FaceZoneInfo.size(); ++i)
    {
        boundaryName.push_back(v_FaceZoneInfo[i].st_name);
    }
    
    return boundaryName;
}

int FluentMeshBlock::ReadMesh(const bool &fullRead)
{
    // 打开文件
    FILE *F_FilePointer;
    F_FilePointer = fopen(fullFileName.c_str(), "r");
    if (F_FilePointer == nullptr)
        FatalError("FluentMeshBlock::ReadMesh: Can not find Mesh file: " + mesh->st_fileName);

    // 读取文件
    std::string s_Index;
    char temp=' ';
    s_Index = fgetc(F_FilePointer);

//    int mCharLocation(0), mStringStart(0);

    while (1)
    {
        while (s_Index != "(")                            //erase space before "("
        {
            s_Index = fgetc(F_FilePointer);
            temp = s_Index[0];
            if (temp == -1)
            {
                //std::cout <<"run end of file" << std::endl;
                PrintFile("run end of file");
                break;
            }
        }
        if (temp == -1)
            break;
        switch (GetIndex(F_FilePointer))
        {
        case 0:
        {            
            ReadComment(F_FilePointer);                                
            break;
        }
        case 2:
        {
            ReadDimensionNumber(F_FilePointer);
            break;
        }
        case 10:
        {
            ReadNode(F_FilePointer);
            break;
        }
        case 12:
        {
            ReadElement(F_FilePointer, fullRead);
            break;
        }
        case 13:
        {
            ReadFace(F_FilePointer, fullRead);
            break;
        }
        case 39:
        {
            ReadZone(F_FilePointer);
            break;
        }
        case 45:
        {
            ReadZone(F_FilePointer);
            break;
        }
        }
        
        s_Index = "";                                        //Info of marked pos should be reseted
        //    cout <<"run here"<< endl;
    }

    return 0;
}

int FluentMeshBlock::GetIndex(FILE *F_FilePointer)
{
    int n_Index;
    fscanf(F_FilePointer, "%d", &n_Index);
    return n_Index;
}

void FluentMeshBlock::ReadComment(FILE *F_FilePointer)
{
    char c_Temp;
    c_Temp = fgetc(F_FilePointer);
    std::ostringstream stringStream;
    stringStream.str("");
    
    while (c_Temp != ')')
    {
        if (c_Temp != '\r'&&c_Temp != '\"'&&c_Temp !='\n')    //erase effect of "Enter"
            stringStream << c_Temp;        
        c_Temp = fgetc(F_FilePointer);
    }
    PrintFile(stringStream.str());
}

void FluentMeshBlock::ReadDimensionNumber(FILE *F_FilePointer)
{
    char c_temp;
    int dimension;
    fscanf(F_FilePointer, "%d%c", &dimension, &c_temp);
	if (meshDimension != Mesh::MeshDim::mdNoType && dimension != (int)meshDimension)
        FatalError("FluentMeshBlock::ReadDimensionNumber: 网格维度与输入参数不一致");
	
    mesh->md_meshDim = (Mesh::MeshDim)dimension;
    if (c_temp != ')') FatalError(std::string(") is missing."));    
    Print("Mesh demension: " + ToString((int)mesh->md_meshDim));
}

Scalar FluentMeshBlock::GetData(FILE *F_FilePointer, std::string &s_InputData, int n_DataType)
{
    std::string s_OutPutData;
    s_OutPutData = "";
    if (n_DataType==1)
    { 
    while (s_InputData == "\t" || s_InputData == "\r" || s_InputData == " "|| s_InputData == "\n")
    {
        s_InputData = fgetc(F_FilePointer);
    }
    while (s_InputData != "\t" && s_InputData != "\r" && s_InputData != " "&&s_InputData != ")"&&s_InputData != "\n")
    {
        s_OutPutData = s_OutPutData + s_InputData;
        s_InputData = fgetc(F_FilePointer);
    }
    }
    return std::stod(s_OutPutData);
}

int FluentMeshBlock::GetData(FILE *F_FilePointer, std::string &s_InputData)
{
    std::string s_OutPutData;
    s_OutPutData = "";
    int n_Dst(0), n_Temp;

    while (s_InputData == "\t" || s_InputData == "\r" || s_InputData == " "|| s_InputData == "\n")
    {
        s_InputData = fgetc(F_FilePointer);
    }
    while (s_InputData != "\t" && s_InputData != "\r" && s_InputData != " "&&s_InputData != ")"&&s_InputData != "\n")
    {
        s_OutPutData = s_OutPutData + s_InputData;
        s_InputData = fgetc(F_FilePointer);
    }

    for (int i = 0; i < (int)s_OutPutData.length(); i++)
    {
        if (s_OutPutData[i] <= '9')
        {
            n_Temp = s_OutPutData[i] - '0';
        }
        else
            n_Temp = s_OutPutData[i] - 'a' + 10;
        n_Dst = n_Dst*16 + n_Temp;
    }
        return n_Dst;
}

void FluentMeshBlock::ReadNew(FILE *F_FilePointer, int n_Index)
{
    int n_Dst(0), n_Temp;
    std::string s_Temp, s_TotalChar;
    v_parameters.resize(5);
    if (10 == n_Index)
    {
        for (int i = 0; i < 5; i++)
        {
            fscanf(F_FilePointer, "%x", &v_parameters[i]);
        }
    }
    else if (12 == n_Index || 13 == n_Index)
    {
        std::string v_parameters_Data;
        for (int i = 0; i < 4; i++)
        {
            fscanf(F_FilePointer, "%x", &v_parameters[i]);
        }
        s_Temp = fgetc(F_FilePointer);
        while (s_Temp == "\t" || s_Temp == "\r" || s_Temp == " " || s_Temp == "\n")
        {
            s_Temp = fgetc(F_FilePointer);
        }
        if (")" != s_Temp)
        {
            s_TotalChar = s_TotalChar + s_Temp;
            s_Temp = fgetc(F_FilePointer);
            while (s_Temp != "\t" && s_Temp != "\r" && s_Temp != " "&&s_Temp != ")"&&s_Temp != "\n")
            {
                s_TotalChar = s_TotalChar + s_Temp;
            }
            for (int i = 0; i < s_TotalChar.length(); i++)
            {
                if (s_TotalChar[i] <= '9')
                {
                    n_Temp = s_TotalChar[i] - '0';
                }
                else
                    n_Temp = s_TotalChar[i] - 'a' + 10;
                n_Dst = n_Dst * 16 + n_Temp;
            }
            v_parameters[4] = n_Dst;
            /*cout << v_parameters [4]<< endl;
            cout << s_TotalChar << endl;*/
        }
        else
            v_parameters[4] = 0;
    }
}

void FluentMeshBlock::ReadNode(FILE *F_FilePointer)
{
    
    std::string s_leftChar;
    int n_ZoneId, n_FirstIndex, n_LastIndex, n_dimensionalityOfGrid;
    s_leftChar = fgetc(F_FilePointer);
    while (s_leftChar != "(")                            //erase space before "("
    {
        s_leftChar = fgetc(F_FilePointer);
    }
    ReadNew(F_FilePointer,10);
    if (v_parameters.size() != 5) FatalError("FluentMeshBlock::ReadNode：Incorrect parameter number detected in node section.");
    
    n_ZoneId = v_parameters[0];
    n_FirstIndex = v_parameters[1];
    n_LastIndex = v_parameters[2];
    n_dimensionalityOfGrid = v_parameters[4];
    
    if (0 != n_ZoneId)
    {        
        PrintFile("Reading node coordinates");
        s_leftChar = fgetc(F_FilePointer);
        while (s_leftChar != "(")                            //erase space before "("
        {
            s_leftChar = fgetc(F_FilePointer);
        }
        s_leftChar = fgetc(F_FilePointer);

        mesh->v_node.reserve(mesh->v_node.size() + (n_LastIndex - n_FirstIndex + 1));
        switch (n_dimensionalityOfGrid)
        {
            case Mesh::md2D:
            {
                for (int i = 0; i < (n_LastIndex - n_FirstIndex + 1); i++)
                {
                    Node nodeTemp;
                    nodeTemp.SetX(GetData(F_FilePointer, s_leftChar, 1));
                    nodeTemp.SetY(GetData(F_FilePointer, s_leftChar, 1));
                    mesh->v_node.push_back(nodeTemp);
                }

                break;
            }
            case Mesh::md3D:
            {
                for (int i = 0; i < (n_LastIndex - n_FirstIndex + 1); i++)
                {
                    Node nodeTemp;
                    nodeTemp.SetX(GetData(F_FilePointer, s_leftChar, 1));
                    nodeTemp.SetY(GetData(F_FilePointer, s_leftChar, 1));
                    nodeTemp.SetZ(GetData(F_FilePointer, s_leftChar, 1));
                    mesh->v_node.push_back(nodeTemp);
                }
                break;
            }
        }

    }
}

void FluentMeshBlock::ReadElement(FILE *F_FilePointer, const bool &fullRead)
{
    std::string s_leftChar;
    while (s_leftChar != "(")                            //erase space before "("
    {
        s_leftChar = fgetc(F_FilePointer);
    }
    ReadNew(F_FilePointer, 12);
    if (v_parameters.size() != 5) FatalError("FluentMeshBlock::ReadElement: Incorrect parameter number detected in element section.");
    
    if (0 == v_parameters[0])
    {
        PrintFile("Reading element declaration");
        int beginNum = v_parameters[1];
        int endNum = v_parameters[2];
        mesh->n_elemNum = endNum - beginNum + 1;
    }
    else
    {
        PrintFile("Reading shape types of the elements");
        
        const int &n_elemShape = v_parameters[4];
        const int &n_elemNum = v_parameters[2] - v_parameters[1] + 1;
        auto est_shapeType = (Element::ElemShapeType)n_elemShape;

        if (mesh->est_shapeType == Element::estNoType)
            mesh->est_shapeType = (Element::ElemShapeType)n_elemShape;    
        
        if(fullRead) mesh->v_elem.reserve(n_elemNum);

        if (est_shapeType == Element::estMixed)
        {
            s_leftChar = fgetc(F_FilePointer);
            while (s_leftChar != "(")                            //erase space before "("
            {
                s_leftChar = fgetc(F_FilePointer);
            }
            s_leftChar = fgetc(F_FilePointer);
            for (int i = 0; i < n_elemNum; i++)
            {
                Element elemTemp;
                int    nElemType(-1);
                nElemType=GetData(F_FilePointer, s_leftChar);
                elemTemp.est_shapeType = (Element::ElemShapeType)nElemType;
                if(fullRead) mesh->v_elem.push_back(elemTemp);
            }
        }
        else
        {
            for (int i = 0; i < n_elemNum; i++)
            {
                Element elemTemp;
                elemTemp.est_shapeType = (Element::ElemShapeType)n_elemShape;
                if(fullRead) mesh->v_elem.push_back(elemTemp);
            }
        }
    }
}

void FluentMeshBlock::ReadFace(FILE *F_FilePointer, const bool &fullRead)
{
    std::string s_leftChar;
    while (s_leftChar != "(")                            //erase space before "("
    {
        s_leftChar = fgetc(F_FilePointer);
    }
    ReadNew(F_FilePointer, 13);
    if (v_parameters.size() != 5) FatalError("FluentMeshBlock::ReadFace: Incorrect parameter number detected in face section.");
        
    if (0 == v_parameters[0])
    {
        PrintFile("Reading face declaration");
        const int &beginNum = v_parameters[1];
        const int &endNum = v_parameters[2];
        mesh->n_faceNum = endNum - beginNum + 1;
    }
    else
    {
        s_leftChar = fgetc(F_FilePointer);
        while (s_leftChar != "(")                            //erase space before "("
        {
            s_leftChar = fgetc(F_FilePointer);
        }
        s_leftChar = fgetc(F_FilePointer);
        PrintFile("Reading face topologies");
        MshFaceZone fzTemp;

        //(zone-id first-n_index last-n_index type element-type)
        //type : mixed,linear,triangular,quadrilateral
        fzTemp.n_zoneID = v_parameters[0];
        fzTemp.n_begID = v_parameters[1] - offset;
        fzTemp.n_endID = v_parameters[2] - offset;
        fzTemp.n_BCType = v_parameters[3];
        fzTemp.n_faceShape = v_parameters[4];
        fzTemp.n_faceNum = fzTemp.n_endID - fzTemp.n_begID + 1;

        const bool fillFlag = (fullRead || (!fullRead && fzTemp.n_BCType != internalBCID));
        if(fillFlag)
        {
            if(!fullRead)
            {
                fzTemp.n_begID = mesh->v_face.size();
                fzTemp.n_endID = fzTemp.n_begID + fzTemp.n_faceNum - 1;
            }
            v_FaceZoneInfo.push_back(fzTemp);
            mesh->v_face.reserve(mesh->v_face.size() + fzTemp.n_faceNum);
        }
        
        //Read face
        for (int i = 0; i < fzTemp.n_faceNum; i++)
        {
            int nFaceType = fzTemp.n_faceShape;
            if(0 == fzTemp.n_faceShape || 5 == fzTemp.n_faceShape)
                nFaceType = GetData(F_FilePointer, s_leftChar);

            std::vector<int> v_nodeID(nFaceType);
            for (int i = 0; i < nFaceType; ++i)
                v_nodeID[i] = GetData(F_FilePointer, s_leftChar) - offset;

            Face faceTemp(v_nodeID);
            faceTemp.n_owner = GetData(F_FilePointer, s_leftChar) - offset;
            faceTemp.n_neighbor = GetData(F_FilePointer, s_leftChar) - offset;

            if(fillFlag) mesh->v_face.push_back(faceTemp);
        }
        
        for (int i = 0; i < (int)mesh->v_face.size(); ++i)
        {
            Face &faceTemp = mesh->v_face[i];
            if (-1 == faceTemp.n_owner)
            {
                faceTemp.n_owner = faceTemp.n_neighbor;
                faceTemp.n_neighbor = -1;
                faceTemp.ReverseNodeID();
            }
        }
    }
}

void FluentMeshBlock::ReadZone(FILE *F_FilePointer)
{
    int zoneNumber;
    std::string c_Temp;
    c_Temp = fgetc(F_FilePointer);
    while (c_Temp !="(")
    {
        c_Temp = fgetc(F_FilePointer);
    }
    std::string  zoneName_Second;
    fscanf(F_FilePointer, "%d", &zoneNumber);                
    c_Temp = fgetc(F_FilePointer);
    while (c_Temp != ")")
    {
        zoneName_Second = "";
        while (c_Temp == "\t" || c_Temp == "\n" || c_Temp == " ")
        {
            c_Temp = fgetc(F_FilePointer);
        }
        while (c_Temp != "\t" && c_Temp != "\n" && c_Temp != " "&&c_Temp != ")")
        { 
            zoneName_Second = zoneName_Second + c_Temp;
            c_Temp = fgetc(F_FilePointer);
        }

    }
    
    for (int i = 0; i < (int)v_FaceZoneInfo.size(); i++)
    {
        if (zoneNumber == v_FaceZoneInfo[i].n_zoneID)
            v_FaceZoneInfo[i].st_name = zoneName_Second;
    }
    if(c_Temp == ")")
    {
        c_Temp = fgetc(F_FilePointer);
        while (c_Temp != "(")
        {
            c_Temp = fgetc(F_FilePointer);
        }
        while (c_Temp != ")")
        {
            c_Temp = fgetc(F_FilePointer);
        }
        c_Temp = fgetc(F_FilePointer);
        while (c_Temp != ")")
        {
            c_Temp = fgetc(F_FilePointer);
        }
    }
    else
    {
        c_Temp = fgetc(F_FilePointer);
        while (c_Temp != ")")
        {
            c_Temp = fgetc(F_FilePointer);
        }
        while (c_Temp != "(")
        {
            c_Temp = fgetc(F_FilePointer);
        }
        while (c_Temp != ")")
        {
            c_Temp = fgetc(F_FilePointer);
        }
        c_Temp = fgetc(F_FilePointer);
        while (c_Temp != ")")
        {
            c_Temp = fgetc(F_FilePointer);
        }
    }
}

void FluentMeshBlock::EstablishBoundary()
{    
    //Determing the face zone type: interior? external? internal? or mixed?
    for (int i = 0; i < this->v_FaceZoneInfo.size(); ++i)
    {
        if (internalBCID == v_FaceZoneInfo[i].n_BCType) continue;

        const int &firstFaceID = v_FaceZoneInfo[i].n_begID;
        if (-1 != mesh->v_face[firstFaceID].n_owner && -1 == mesh->v_face[firstFaceID].n_neighbor)
        {
            mesh->v_boundaryName.push_back(v_FaceZoneInfo[i].st_name);

            std::vector<int> faceIDList(v_FaceZoneInfo[i].n_endID - v_FaceZoneInfo[i].n_begID + 1);
            int count = 0;
            for (int j = v_FaceZoneInfo[i].n_begID; j <= v_FaceZoneInfo[i].n_endID; ++j)
            {
                faceIDList[count++] = j;
            }
            mesh->vv_boundaryFaceID.push_back(faceIDList);
        }
    }
}