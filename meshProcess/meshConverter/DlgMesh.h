﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DlgMesh.h
//! <AUTHOR>
//! @brief 声明Dlg类，Dlg格式网格文件的数据结构与操作函数
//!        其父类为Mesh
//! 
//! @date  2022-5-19
//
//------------------------------修改日志----------------------------------------
//
// 2022-05-19 乔龙
// 说明：建立并规范化
// 
//------------------------------------------------------------------------------
#ifndef _meshProcess_meshConverter_DlgMesh_
#define _meshProcess_meshConverter_DlgMesh_

#include "meshProcess/meshConverter/MeshConverter.h"
#include "basic/configure/Configure.h"
#include "basic/mesh/SubMesh.h"

/**
 * @brief dlg格式网格转换类
 * 
 */
class DlgMesh :public MeshConverter
{
	struct InfoStruct
	{
		std::string name;
		std::string type;
		int valueDimension;
		int valueNumber;
		int subNodesNumber;
	};
	
	struct DlgElementGroup
	{
		std::string element_type;
		std::vector<std::vector<int>> element_nodes;
		DlgElementGroup()
		{
			element_nodes.clear();
		}
	};
	
	struct DlgBoundary
	{
		std::string b_name;
		int n_b_nodes;
		std::string b_class;
		std::vector<DlgElementGroup> belem_groups;
		std::vector<int> b_nodes;
		std::vector<Vector> b_surfaces;
		std::vector<int> b_inner_nodes;
		DlgBoundary()
		{
			belem_groups.clear();
			b_nodes.clear();
			b_surfaces.clear();
			b_inner_nodes.clear();
		}
	};
	
	struct DlgGrid
	{
		int n_nodes;
		int n_edges;
		int n_edge_colors;
		int n_bedges;
		int n_b_colors;
		int n_bound;
		std::vector<std::pair<int, int>> edge_nodes;
		std::vector<DlgElementGroup> element_groups;
		std::vector<int> node_edgelist_index;
		std::vector<int> node_edgelist_value;
		std::vector<Scalar> volumes;
		std::vector<Vector> coordinates;
		std::vector<Vector> edge_surfaces;
		std::vector<std::pair<int, int>> color_indices;
		std::vector<DlgBoundary> boundarys;
		std::vector<int> node_f2c;
		std::vector<int> node_c2f_idx;
		std::vector<int> node_c2f;
        std::vector<Scalar> wall_distances;
	};
	
	struct DlgStruct
	{
		std::string title;
		int n_dim;
		std::vector<DlgGrid> regions;
	};

public:
	/**
	* @brief 构造函数
	*
	* @param[in] MshFileName 网格文件名称
    * @param[in] meshDimension_ 网格维度
	* @param[out] mesh_ 网格指针
	* @param[in] binary_ 二进制文件标识
	*/
    DlgMesh(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_, const bool &binary_);

	/**
	* @brief 析构函数
	*
	*/
    ~DlgMesh();

    int ReadMesh(const bool &fullRead = true);

    int BuildTopology(){ return 0; }

    int BuildBCTopology(){ return 0; }

	/**
	* @brief 建立网格空间拓扑结构
	*
	*/
    int BuildVolumeTopology(){ return 0; }

	void ConvertSubMesh(SubMesh *subMesh);
	
	void ConvertMesh(Mesh *mesh_, const int &level);
	
	void ConvertElementMap(std::vector<std::vector<int>> &v_elemMap, const int &level);

    /// 获取边界名称
    std::vector<std::string> GetBoundaryName();

private:
    bool ReadInfoLine(std::fstream &file, InfoStruct &info);
    void ReadGrid(std::fstream &file, DlgGrid &grid, const int &subNodesNumber);
    void ReadElementGroup(std::fstream &file, DlgElementGroup &element_group, const int &subNodesNumber);
    void ReadBoundary(std::fstream &file, const int &patchID, DlgBoundary &dlgBoundary, const int &subNodesNumber);
    void ReadString(std::fstream &file, std::string &stringTemp);
    void ReadPairVector(std::fstream &file, InfoStruct &info, std::vector<std::pair<int, int>> &pairVector);
    void ReadIntVector(std::fstream &file, InfoStruct &info, std::vector<int> &intVector);
    void ReadVectorVector(std::fstream &file, InfoStruct &info, std::vector<Vector> &vectorVector);
    
private:
    DlgStruct dlgMesh;
    std::vector<Vertice> v_vertice;
    const bool &binary;
};

#endif