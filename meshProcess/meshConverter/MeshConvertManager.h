﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshConvertManager.h
//! <AUTHOR>
//! @brief 用于网格转换的管理类
//! @date 2022-01-07
//
//------------------------------修改日志----------------------------------------
// 2022-01-07 李艳亮、乔龙
//    说明：建立并规范化。
//     
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshConverter_MeshConvertManager_
#define _meshProcess_meshConverter_MeshConvertManager_

#include "basic/configure/Configure.h"
#include "meshProcess/meshConverter/DlgMesh.h"
#include "meshProcess/meshConverter/CgnsMesh.h"
#include "meshProcess/meshConverter/CgnsMeshStructured.h"
#include "meshProcess/meshConverter/FluentMeshBlock.h"
#include "meshProcess/meshConverter/MeshConverter.h"

/**
 * @brief 网格转换管理器类
 * 
 */
class MeshConvertManager
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in] meshFileName_ 网格文件名称
	* @param[in] meshType_ 网格文件类型
	* @param[in] meshDimension_ 网格维度
	* @param[out] mesh_ 网格指针
	* @param[in] meshTransform_ 网格转换信息（平移、旋转、缩放等）
	*/
    MeshConvertManager(const std::string &meshFileName_,
                       const Preprocessor::MeshType &meshType_,
					   const Mesh::MeshDim &meshDimension_,
                       Mesh *mesh_,
                       const Configure::MeshTransformStruct &meshTransform_ = Configure::MeshTransformStruct());

	/**
	* @brief 析构函数
	*
	*/
    ~MeshConvertManager();

	/**
	* @brief 读取网格
	*
	* @param[in] fullRead 是否完全读取，还是仅读取边界信息
	*/
    int ReadMesh(const bool &fullRead = true);

	/**
	* @brief 建立网格的拓扑结构
	*
	*/
    int BuildTopology();

	/**
	* @brief 建立网格边界面的拓扑结构
	*
	*/
    void BuildBoundaryTopology();

	/**
	* @brief 建立网格空间拓扑结构
	*
	*/
    void BuildVolumeTopology();

	/**
	* @brief 获取边界的名称
	*
	*/
	std::vector<std::string> GetBoundaryName();

private:
	/**
	* @brief 根据网格文件类型，建立基类转换指针
	*
	*/
    void SetMeshConverterPointer();

	/**
	* @brief 根据网格转换信息，进行网格的平移、旋转、缩放等
	*
	*/
    void TransformMesh();

private:
	Mesh *mesh; ///< 生成的网格
	MeshConverter *meshConverter; ///< 网格转换基类指针
	const std::string meshFileName; ///< 网格文件名称
	const Preprocessor::MeshType meshType; ///< 网格文件类型
	const Mesh::MeshDim &meshDimension; ///< 网格维度
	const Configure::MeshTransformStruct meshTransform; ///< 网格转换信息（平移、旋转、缩放等）
};

#endif // _mesh_meshConvert_MeshConvertManager_
