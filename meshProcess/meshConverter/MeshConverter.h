﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshConverter.h
//! <AUTHOR>
//! @brief 用于网格转换的基类
//! @date 2022-01-07
//
//------------------------------修改日志----------------------------------------
// 2022-01-07 尹强、李艳亮、乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshConverter_MeshConverter_
#define _meshProcess_meshConverter_MeshConverter_

#include "basic/mesh/Mesh.h"

/**
 * @brief 网格转换类
 * 
 */
class MeshConverter
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in] MshFileName 网格文件名称
    * @param[in] meshDimension_ 网格维度
	* @param[out] mesh_ 网格指针
	*/
    MeshConverter(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_);

	/**
	* @brief 虚析构函数
	*
	*/
    virtual ~MeshConverter() {}

	/**
	* @brief 读取网格（虚函数）
	*
	* @param[in] fullRead 是否完全读取，还是仅读取边界信息
	*/
    virtual int ReadMesh(const bool &fullRead = true) = 0;

	/**
	* @brief 建立网格的拓扑结构（虚函数）
	*
	*/
    virtual int BuildTopology() = 0;

	/**
	* @brief 建立网格边界面的拓扑结构（虚函数）
	*
	*/
    virtual int BuildBCTopology() = 0;
	
	/**
	* @brief 建立网格空间拓扑结构（虚函数）
	*
	*/
    virtual int BuildVolumeTopology() = 0;

	/**
	* @brief 获取边界的名称（虚函数）
	*
	*/
	virtual std::vector<std::string> GetBoundaryName() = 0;
    
	/**
	* @brief 获取边界的点构成信息
	*
	* @param[out] vv_boundaryNodeID 每个边界上的点集合
	* @param[out] vvv_boundaryFaceNodeID 每个边界上的每个面的点构成
	*/
    void GetBoundaryFaceNodeID(std::vector<std::vector<int>> &vv_boundaryNodeID, std::vector<std::vector<std::vector<int>>> &vvv_boundaryFaceNodeID);
    
protected:
	/**
	* @brief 用单元信息构建面信息（包含边界面）
	*
	*/
    int CreateFaceByElemAndPoint();
    
	/**
	* @brief 获取边界的点构成信息
	*
	* @param[in] elementList 单元容器
	* @param[in] start 待处理单元的起始编号
	* @param[in] elementSize 待处理单元的数量
	* @param[in, out] faceList 形成的面容器
	* @param[in, out] faceIDListVector 用于查找的面索引
	*/
    void CreateFace(const std::vector<Element> &elementList, 
                    const int &start,
                    const int &elementSize,
                    std::vector<Face> &faceList,
                    std::vector<std::vector<int>> &faceIDListVector);

	/**
	* @brief 整理面的点构成，从最小编号点开始排序
	*
	* @param[in, out] nodeList 面的点构成
	*/
    void SortNodeList(std::vector<int> &nodeList);

	/**
	* @brief 根据面的点编号构成，求编号的均值
	*
	* @param[in] nodeList 面的点构成
	*/
    int SumNodeList(const std::vector<int> &nodeList);

	/**
	* @brief 根据面的点编号构成，判定两个面是否相同
	*
	* @param[in] face1 面1
	* @param[in] face2 面2
	*/
    bool JudgeNodeList(const Face &face1, const Face &face2);
    
	/**
	* @brief 根据单元信息，创建面信息
	*
	* @param[in] CurElem 单元
	* @param[out] vFace 形成的面容器
	* @param[in] ownerID 单元编号
	* 支持类型包含：三角形、四边形、四面体、六面体、三棱柱、金字塔、多面体
	*/
	void CreateInteriorFaceForTriangular(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);
	void CreateInteriorFaceForQuadrilateral(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);
	void CreateInteriorFaceForTetrahedral(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);
	void CreateInteriorFaceForHexahedral(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);
	void CreateInteriorFaceForWedge(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);
	void CreateInteriorFaceForPyramid(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);
	void CreateInteriorFaceForPolygon(const Element &CurElem, std::vector<Face> &vFace, const int &ownerID);

	/**
	* @brief 已知各Face组成的点列表，owner/neighbor, 构建单元和点列表的相关函数，目前Fluent格式网格需要用
	*
	* @param[in] i 单元编号
	*/
    void PopulateCellNodes();
	void PopulateTriangleCell(const int &i);
	void PopulateTetraCell(const int &i);
	void PopulateQuadCell(const int &i);
	void PopulateHexahedronCell(const int &i);
	void PopulatePyramidCell(const int &i);
	void PopulateWedgeCell(const int &i);
	void PopulatePolyhedronCell(const int &i);
	void PopulatePolygonCell(const int &i);
    
	/**
	* @brief 统计网格基本信息并打印
	*
	*/
    void CalculateParameters();
    
private:
	/**
	* @brief 将各线程形成的面整理合并，放入网格
	*
	* @param[in] faceIDList 面索引信息
	* @param[in] faceVector 待合并的面容器
	* @param[in, out] faceIDListVectorAll 合并后面的总索引信息
	*/
    void MergeFaceVector(const std::vector<std::vector<int>> &faceIDList, 
                         const std::vector<Face> &faceVector,
                         std::vector<std::vector<int>> &faceIDListVectorAll);

protected:              
    Mesh *mesh; ///< 生成的网格
    const std::string fullFileName; ///< 含路径的文件名
	const Mesh::MeshDim &meshDimension; ///< 输入的网格维度
};

#endif // _mesh_meshConvert_MeshConvert_
