﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file CgnsMeshStructured.h
//! <AUTHOR>
//! @brief 读取CGNS格式结构网格文件
//! 
//! @date  2024-06-17
//
//------------------------------修改日志----------------------------------------
//
// 2021-05-12 尹强 
// 说明：建立并规范化
// 
// 2024-06-17 乔龙、李艳亮
// 说明：建立CGNS基类，实现结构网格CGNS文件读取
// 
//------------------------------------------------------------------------------

#ifndef _meshProcess_meshConverter_CgnsMeshStructured_
#define _meshProcess_meshConverter_CgnsMeshStructured_

#include "meshProcess/meshConverter/CgnsMeshBase.h"

/**
* @brief cgns网格转换类
*
*/
class CgnsMeshStructured :public CgnsMeshBase
{
public:
    struct Connection1to1
    {
        char_33 connectName;
        char_33 donorName;
        std::vector<int> transform;
        std::vector<cgsize_t> range;
        std::vector<cgsize_t> rangeDonor;
    };

    struct CgnsZoneInfo
    {
        /// 数据域名称
        char_33 zoneName;

        /// 网格节点总数和单元总数
        cgsize_t nNode, nCell;

        /// 单元起始编号
        cgsize_t cellStart;

        /// 网格节点总数和单元数量
        std::vector<int> nodeSize, cellSize;

        /// 边界相关数据
        std::vector<int> boundaryType;
        std::vector<std::string> boundaryName;
        std::vector<std::vector<cgsize_t>> boundaryNodeIDRange;

        /// 多域连接关系
        std::vector<Connection1to1> connectivity1to1;
    };

public:
	/**
	* @brief 构造函数
	*
	* @param[in] MshFileName 网格文件名称
    * @param[in] meshDimension_ 网格维度
	* @param[out] mesh_ 网格指针
	*/
    CgnsMeshStructured(const std::string& MshFileName, const Mesh::MeshDim &meshDimension_, Mesh *mesh_);
	
	/**
	* @brief 析构函数
	*
	*/
    ~CgnsMeshStructured();

    /**
     * @brief 读取cgns文件
     * 
     * @param[in] fullRead 是否读取所有数据标识，false时仅读取边界拓扑信息
     */
    int ReadMesh(const bool &fullRead = true);

    /**
     * @brief 获取边界名称
     * 
     * @return std::vector<std::string> 边界名称
     */
    std::vector<std::string> GetBoundaryName();

    /**
     * @brief 读取基本信息
     * 
     */
    int ReadGeneral();

    /**
     * @brief 读取Families
     * 
     */
    int ReadFamilies();

    /**
     * @brief 读取所有数据域，目前仅支持读取一个数据域
     * 
     * @param[in] fullRead 是否读取所有数据标识，false时仅读取边界拓扑信息
     */
    int ReadZone(const bool &fullRead);

    /**
     * @brief 读取边界数据
     * 
     * @param[in] zoneID 数据域编号
     */
    int ReadBoundaryCondition(const int &zoneID);

    /**
    * @brief 读取域连接关系
    *
    * @param[in] zoneID 数据域编号
    */
    int ReadZoneGridConnectivity(const int &zoneID);
    
    /**
     * @brief 建立网格的拓扑结构
     * 
     */
    int BuildTopology();

    /**
     * @brief 建立边界拓扑结构
     * 
     */
    int BuildBCTopology();

	/**
	* @brief 建立网格空间拓扑结构
	*
	*/
    int BuildVolumeTopology();

    /**
    * @brief 创建空间单元
    *
    * @param[in] zoneID 数据域编号
    * @param[in] faceID0 起始面编号
    */
    int ProcessVolumeElementZone(const int &zoneID, const int &faceID0);

    /**
    * @brief 创建边界单元
    *
    * @param[in] zoneID 数据域编号
    * @param[in] faceID0 起始面编号
    */
    int ProcessBoundaryElementZone(const int &zoneID, const int &faceID0);

    /**
    * @brief 创建一对一连接边界
    *
    * @param[in] faceID0 起始面编号
    */
    int Process1to1Connection(const int &faceID0);

    /**
     * @brief 生成结构网格Block信息
     * 
     */
    int GenerateStructuredBlock();

    /**
    * @brief 读取数据域基本信息
    *
    */
    int ReadZoneGeneral();

    /**
    * @brief 读取数据域信息
    *
    */
    int ReadZoneSize();

    /**
     * @brief 读取网格节点坐标
     * 
     * @param[in] zoneID 数据域编号
     */
    int ReadCoordinates(const int &zoneID);

    /**
    * @brief 生成并打印边界信息
    *
    */
    void GenerateAndPrintBoundaryInfo();

    /**
    * @brief 计算边界面的数量
    *
    */
    void CalculateBoundaryFaceSize();

    /**
    * @brief 计算内部面的数量
    *
    */
    void CalculateInnerFaceSize();

protected:
    /// 边界Family名称
    std::vector<std::string> FamilyBCName;

    /// 边界类型
    std::vector<BCType_t> FamilyBCType;

    /// 当地边界所对应的全局边界编号
    std::vector<std::vector<int>> boundaryIndexMap;

    /// 边界名称（无重复）
    std::vector<std::pair<std::string, std::string>> boundaryInfoList;

    /// 指定的边界名称标识
    bool boundaryNameFamilySpecified;
    
    /// 三维标识，true为三维，false为二维
    bool dim3;
    
    /// 网格维度
    int cellDim;

    /// zone数量
    int nZones;
    
    /// 文件号,数据区号
    int fileID, baseID;

    /// cgns文件所包含zone的信息
    std::vector<CgnsZoneInfo> cgnsZones;
    
    /// 网格节点总数和单元总数
    cgsize_t nTotalNode, nTotalCell;
    
    std::vector<int> boundaryFaceIndex;
    std::vector<int> innerFaceIndex;
    int connectionFaceSize;
};

#endif
