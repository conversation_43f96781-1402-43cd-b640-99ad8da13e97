﻿#include "meshProcess/meshSorting/MeshSorting.h"

#include <list>

class MHT_less
{
    const std::vector<int> &values_;

public:

    MHT_less(const std::vector<int>& values)
        :
        values_(values)
    {}

    bool operator()(const int a, const int b)
    {
        return values_[a] < values_[b];
    }
};

MeshSorting::MeshSorting(Mesh* pmesh_, Preprocessor::RenumberType renumberType, std::vector<int> &elementIDMap_)
            : pmesh(pmesh_), elementIDMap(elementIDMap_)
{
    switch (renumberType)
    {
    case Preprocessor::RenumberType::RCM:
        RCMRenumber();
        break;
    default:
        FatalError("MeshSorting::MeshSorting: input MeshSorting method isnot supported!\n");
        break;
    }
}

void MeshSorting::RCMRenumber()
{
    int matrixBand0 = 0;
    for (int faceID = 0; faceID < pmesh->GetFaceNumber(); faceID++)
    {
        const int &ownerID = pmesh->GetFace(faceID).GetOwnerID();
        const int &neighID = pmesh->GetFace(faceID).GetNeighborID();

        if (pmesh->JudgeRealElement(neighID))
            matrixBand0 = Max(matrixBand0, std::abs(neighID - ownerID) + 1);
    }
    matrixBand0 = 2 * matrixBand0 - 1;

    // Reverse Cuthill-McKee renumbering
    BandCompression(elementIDMap);
    std::reverse(elementIDMap.begin(), elementIDMap.end());

    std::vector<int> elementIDOldToNew(elementIDMap.size());
    for (int i = 0; i < elementIDMap.size(); ++i) elementIDOldToNew[elementIDMap[i]] = i;
    int matrixBand1 = 0;
    for (int faceID = 0; faceID < pmesh->GetFaceNumber(); faceID++)
    {
        const int &ownerID = pmesh->GetFace(faceID).GetOwnerID();
        const int &neighID = pmesh->GetFace(faceID).GetNeighborID();

        if (pmesh->JudgeRealElement(neighID))
            matrixBand1 = Max(matrixBand1, std::abs(elementIDOldToNew[neighID] - elementIDOldToNew[ownerID]) + 1);
    }
    matrixBand1 = 2 * matrixBand1 - 1;

    if (GetMPIRank() == 0)
        Print("\tmatrix bandwidth of #0: " + ToString(matrixBand0) + " ==> " + ToString(matrixBand1));
}

void MeshSorting::BandCompression(std::vector<int>& newOrder)
{
    newOrder.resize(pmesh->GetElementNumberReal());

    std::vector<int> nNbrs(pmesh->GetElementNumberReal(), 0);
    std::vector<int> offsets(pmesh->GetElementNumberReal() + 1, 0);

    // 获取每个单元相邻单元数量
    for (int i = 0; i < pmesh->GetFaceNumber(); i++)
    {
        const int &ownerID = pmesh->GetFace(i).GetOwnerID();
        const int &neighID = pmesh->GetFace(i).GetNeighborID();
        if (pmesh->JudgeRealElement(neighID))
        {
            nNbrs[ownerID] += 1;
            nNbrs[neighID] += 1;
        }
    }

    // 生成单元偏移量数组
    for (int celli = 0; celli < pmesh->GetElementNumberReal(); celli++)
        offsets[celli + 1] = offsets[celli] + nNbrs[celli];

    // reset the whole list to use as counter
    for (int i = 0; i < nNbrs.size(); i++)   nNbrs[i] = 0;

    // Here "2 * pmesh->v_face.size()" is faceOwnerNum + faceNeiNum
    std::vector<int> cellCells(2 * pmesh->GetFaceNumber());
    for (int faceID = 0; faceID < pmesh->GetFaceNumber(); faceID++)
    {
        const int &ownerID = pmesh->GetFace(faceID).GetOwnerID();
        const int &neighID = pmesh->GetFace(faceID).GetNeighborID();

        if (pmesh->JudgeRealElement(neighID))
        {
            // l1 and l2 is cell idx in CSR format
            const int l1 = offsets[ownerID] + nNbrs[ownerID]++;
            const int l2 = offsets[neighID] + nNbrs[neighID]++;

            cellCells[l1] = neighID;
            cellCells[l2] = ownerID;
        }
    }

    // bandCompression begin
    // Count number of neighbours
    std::vector<int> numNbrs(pmesh->GetElementNumberReal(), 0);
    for (int celli = 0; celli < numNbrs.size(); celli++)
    {
        int start = offsets[celli];
        int end = offsets[celli + 1];

        for (int facei = start; facei < end; facei++)
        {
            numNbrs[celli]++;
            numNbrs[cellCells[facei]]++;
        }
    }

    // the business bit of the renumbering
    std::list<int> nextCell;

    std::vector<int> visited(pmesh->GetElementNumberReal(), 0);

    int cellInOrder = 0;

    // Work arrays. Kept outside of loop to minimise allocations.
    // - neighbour cells
    std::vector<int> nbrs;
    // - corresponding weights
    std::vector<int> weights;

    // - ordering
    std::vector<int> order;

    while (true)
    {
        // For a disconnected region find the lowest connected cell.

        int currentCell = -1;
        int minWeight = std::numeric_limits<int>::max();

        for (int celli = 0; celli < visited.size(); celli++)
        {
            // find the lowest connected cell that has not been visited yet
            if (!visited[celli])
            {
                if (numNbrs[celli] < minWeight)
                {
                    minWeight = numNbrs[celli];
                    currentCell = celli;
                }
            }
        }
        
        if (currentCell == -1) break;

        // Starting from currentCell walk breadth-first

        // use this cell as a start
        nextCell.push_back(currentCell);

        // loop through the nextCell list. Add the first cell into the
        // cell order if it has not already been visited and ask for its
        // neighbours. If the neighbour in question has not been visited,
        // add it to the end of the nextCell list

        while (nextCell.size())
        {
            currentCell = *nextCell.begin();
            nextCell.pop_front();

            if (!visited[currentCell])
            {
                visited[currentCell] = 1;

                // add into cellOrder
                newOrder[cellInOrder] = currentCell;
                cellInOrder++;

                // Add in increasing order of connectivity

                // 1. Count neighbours of unvisited neighbours
                nbrs.clear();
                weights.clear();

                int start = offsets[currentCell];
                int end = offsets[currentCell + 1];

                for (int facei = start; facei < end; facei++)
                {
                    int nbr = cellCells[facei];
                    if (!visited[nbr])
                    {
                        // not visited, add to the list
                        nbrs.push_back(nbr);
                        weights.push_back(numNbrs[nbr]);
                    }
                }
                
                // 2. Sort in ascending order
                order.resize(weights.size());
                for (int i = 0; i < order.size(); i++)
                {
                    order[i] = i;
                }
                std::stable_sort(order.begin(), order.end(), MHT_less(weights));

                // 3. Add in sorted order
                for (int i = 0; i < order.size(); i++)
                {
                    nextCell.push_back(nbrs[i]);
                }
            }
        }
    }
}
