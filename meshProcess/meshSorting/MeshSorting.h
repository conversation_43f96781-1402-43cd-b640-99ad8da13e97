﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshSorting.h
//! <AUTHOR>
//! @brief 用于网格重排的类
//! @date 2022-02-10
//
//------------------------------修改日志----------------------------------------
// 2022-01-07 李艳亮、乔龙
//    说明：基于新框架修改并规范化。
//
// 2021-12-03 杨思源（数峰科技）
//    说明：初步实现。
//------------------------------------------------------------------------------
//

#ifndef _meshProcess_meshSorting_MeshSorting_
#define _meshProcess_meshSorting_MeshSorting_

#include "basic/configure/Configure.h"
#include "basic/mesh/Mesh.h"

/**
 * @brief 网格单元排序类
 * 
 */
class MeshSorting
{
public:
	/**
	* @brief 构造函数
	*
	* @param[in, out] pmesh_ 网格指针
	* @param[in] renumberType 网格排序方法
	* @param[in, out] elementIDMap_ 网格排序结果的容器
	*/
    MeshSorting(Mesh* pmesh_, Preprocessor::RenumberType renumberType, std::vector<int> &elementIDMap_);

private:
	/**
	* @brief 重排的壳函数
	*	
	*/
    void RCMRenumber();
    
	/**
	* @brief 重排的核心函数
	*
	* @param[in, out] newOrder 新单元所对应旧单元的编号
	*/
    void BandCompression(std::vector<int>& newOrder);

private:    
    std::vector<int> &elementIDMap; ///< 新单元所对应旧单元的编号
    Mesh *pmesh; ///< 网格
};

#endif
