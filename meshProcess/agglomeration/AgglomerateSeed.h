﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file AgglomerateSeed.h
//! <AUTHOR>
//! @brief 用于多重网格聚合的类(采用UNSMB中的Seed方法)
//! @date 2022-08-15
//
//------------------------------修改日志----------------------------------------
// 2022-08-15 李艳亮、乔龙
//    说明：建立
//
//
//------------------------------修改日志----------------------------------------

#ifndef _meshProcess_agglomeration_AgglomerateSeed_
#define _meshProcess_agglomeration_AgglomerateSeed_

#include "basic/mesh/MultiGrid.h"

/**
 * @brief 基于Seed的网格聚合类
 * 
 */
class AgglomerateSeed
{	
	struct ControlVolume
	{
		std::vector<int> elementIDList; ///< 该粗网格控制体的细网格单元号构成
		std::vector<int> boundList; ///< 该控制体邻接的边界编号
		std::vector<int> faceIDList; ///< 该控制体邻接的有效面编号(不含边界)
		Scalar area; ///< 该控制体所有面的面积大小之和
		Scalar volume; ///< 该控制体的体积
		ControlVolume()	{ elementIDList.clear(); boundList.clear(); faceIDList.clear(); area = Scalar0; volume = Scalar0; }
		ControlVolume(const ControlVolume &rhs) { *this = rhs; }
		void operator= (const ControlVolume &rhs)
		{
			this->elementIDList = rhs.elementIDList;
			this->boundList = rhs.boundList;
			this->faceIDList = rhs.faceIDList;
			this->area = rhs.area;
			this->volume = rhs.volume;
		}
	};

public:
    /**
     * @brief 创建基于Seed方法的网格聚合对象
     * 
     * @param[in] nLayer 边界层法向聚合层数
     * @param[in] coarseRationLayer 边界层法向聚合率
     * @param[in] coarseRationTang 边界层切向聚合率
     * @param[in] minsize 最小聚合网格数量
     * @param[in] maxsize 最大聚合网格数量
     * @param[in] structured_ 结构形式网格标识
     */
    AgglomerateSeed(const int &nLayer, const int &coarseRationLayer, const int &coarseRationTang,
	                const int &minsize, const int &maxsize, const bool &structured_ = false);

	/**
	 * @brief 析构函数
	 * 
	 */
    ~AgglomerateSeed();

    /**
     * @brief 生成网格聚合列表
     * 
     * @param[in] mesh 细网格
     * @param[in] wallPatchIDList 壁面边界的编号容器
     * @param[in] coarseLevel_ 粗网格层级
     * @param[out] finalDecomp 聚合后细网格所属粗网格单元编号
     * @param[out] nCoarseCells 粗网格单元总数
     */
	void Agglomerate(Mesh* mesh, const std::vector<int> &wallPatchIDList_, const int &coarseLevel_, std::vector<int> &finalDecomp, int& nCoarseCells);

private:
	//初始化细网格控制体
	void InitCV();

	//开始聚合
	void Agglom(std::vector<int> &finalDecomp, int &nCoarseCells);

	//从物面（如没有就从所有边界）开始对网格单元进行层数标记
	void SetLayerNumber(int &startLayer);

	//设置种子单元
	int GetSeed();

	//设置单元聚合的优先顺序
	void InitFront(std::vector<int> &front, const int &startLayer);	

	//给定单元，返回与该单元聚合后的粗单元控制体
	ControlVolume AgglomCV(const int &fineID);

	//寻找未聚合的邻接的单元列表
	void FindAdjacientElement(const std::vector<int> &faceIDList, std::vector<int> &agglomID, std::vector<std::pair<int, int>> &agglomFaceID, std::vector<Scalar> &cArea);
		
	//比较并获得最佳质量的单元
	void GetBestElement(const ControlVolume &CVout, const std::vector<Scalar> &cArea, const std::vector<int> &agglomID, const int &index, Scalar &bestf, int &bestID);

	//计算两个控制体与几个边界相邻（剔除重复的）
	int CalculateBoundarySize(const ControlVolume &CV1, const int &fineID);

	//合并控制体
	ControlVolume FuseCV(const ControlVolume &CV1, const int &iCV, const int &maxSize);

	//聚合单元列表
	void FindNextID(const std::vector<int> &front, int &frocand);

	//建立控制体
	ControlVolume CreateCV(const int &fineID);

	//计算当地粗化率
	int CalculateCoarseRatio(const int &fineID);
	
	//结构网格是否按照2个聚合
	bool CalculateAgglomSizeForStruct(const ControlVolume &CVout, const int &ID);	

private:
    const int &nLayer; ///< 边界层法向聚合层数
    const int &coarseRationLayer; ///< 边界层法向聚合率
    int coarseRationTang; ///< 边界层内切向聚合率
    const int &minsize; ///< 最小聚合网格数量
    const int &maxsize; ///< 最大聚合网格数量
	const bool &structured; ///< 是否是结构网格拓扑
	
	Mesh *fineMesh; ///< 细网格指针

	int nBound; ///< 边界个数
	std::vector<int> wallPatchIDList; ///< 物面边界号的列表
	std::vector<int> fineCVBoundFlag; ///< <0该控制体在内部 >=0记录在fineCVBound中的位置
	std::vector<std::vector<int>> fineCVBound;
	std::vector<Scalar> fineCVAera;
	std::vector<bool> agglomFlag; ///< 细网格的聚合标志（true为已经被聚合）
	std::vector<int> bPrior; ///< 从物面起始的网格层数标识
    std::vector<bool> faceAdjacency; ///< 单元相邻标识
	std::vector<int> faceIntTemp; ///< 临时,用于面合并的快速查找
	int coarseLevel; ///< 粗网格层次

	int nDim; ///< 二维网格为2，三维网格为3
	int cRatio; ///< 整体网格单方向的粗化率

};

#endif // _meshProcess_agglomeration_AgglomerateSeed_
