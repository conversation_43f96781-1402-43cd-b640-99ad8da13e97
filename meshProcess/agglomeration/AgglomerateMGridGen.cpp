﻿#include "meshProcess/agglomeration/AgglomerateMGridGen.h"

AgglomerateMGridGen::AgglomerateMGridGen(const int &nLayer_, const int &coarseRationLayer_,
										 const int &coarseRationTang_,
										 const int &minsize_, const int &maxsize_)
	:nLayer(nLayer_), coarseRationLayer(coarseRationLayer_),
	 coarseRationTang(coarseRationTang_), minsize(minsize_), maxsize(maxsize_)
{}

void  AgglomerateMGridGen::Agglomerate(Mesh* mesh, const std::vector<int> &wallPatchIDList, const int &level, std::vector<int> &finalDecomp, int& nCoarseCells)
{
    Print("通过MGridGen方法生成网格聚合列表...");

    int nFineCells = mesh->GetElementNumberReal();
    int options[10];

    std::vector<Scalar> magSi(mesh->GetFaceNumber());
    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        magSi[i] = mesh->GetFace(i).GetArea();
    }

    // Set the sizes of the addressing and faceWeights arrays
    std::vector<int> cellCellOffsets(nFineCells + 1);
    // Here "2 * mesh->GetFaceNumber()" is faceOwnerNum + faceNeiNum
    std::vector<int> cellCells(2 * mesh->GetFaceNumber());
    std::vector<Scalar> faceWeights(2 * mesh->GetFaceNumber());

    // 用于法向聚合的
    std::vector<bool> faceAdjacency(mesh->GetFaceNumber(), true);
	if (level == 1) SetAdjacency(mesh, wallPatchIDList, coarseRationLayer, true, faceAdjacency);

    // Number of neighbours for each cell
    std::vector<int> nNbrs(nFineCells, 0);
    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        if (mesh->GetFace(i).GetNeighborID() != -1 &&
            faceAdjacency[i])
            nNbrs[mesh->GetFace(i).GetOwnerID()] += 1;
    }

    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        if (mesh->GetFace(i).GetNeighborID() != -1 &&
            faceAdjacency[i])   
            nNbrs[mesh->GetFace(i).GetNeighborID()] += 1;
    }

    cellCellOffsets[0] = 0;
    for (int celli = 0; celli < mesh->GetElementNumberReal(); celli++)
    {
        cellCellOffsets[celli + 1] = cellCellOffsets[celli] + nNbrs[celli];
    }

    // reset the whole list to use as counter
    for (int i = 0; i < nNbrs.size(); i++)   nNbrs[i] = 0;

    for (int facei = 0; facei < mesh->GetFaceNumber(); facei++)
    {
        const Face &CurFace = mesh->GetFace(facei);

        int own = CurFace.GetOwnerID();
        int nei = CurFace.GetNeighborID();

        if (nei != -1 && faceAdjacency[facei])
        {
            // l1 and l2 is cell idx in CSR format
            int l1 = cellCellOffsets[own] + nNbrs[own]++;
            int l2 = cellCellOffsets[nei] + nNbrs[nei]++;

            // cellCells size is 2.0 * faceSize, because face two direction should
            // be considered
            cellCells[l1] = nei;
            cellCells[l2] = own;

            faceWeights[l1] = magSi[facei];
            faceWeights[l2] = magSi[facei];
        }
    }

    std::vector<Scalar> v_vol(mesh->GetElementNumberReal());
    for (int i = 0; i < mesh->GetElementNumberReal(); i++)
    {
        v_vol[i] = mesh->GetElement(i).GetVolume();
    }

    std::vector<Scalar> magSb(mesh->GetElementNumberReal());
    for (int i = 0; i < mesh->GetFaceNumber(); i++)
    {
        const Face& CurFace = mesh->GetFace(i);

        int own = CurFace.GetOwnerID();
        int nei = CurFace.GetNeighborID();

        if (nei == -1)
        {
            magSb[own] += CurFace.GetArea();
        }
    }

    options[0] = 4;        //tCType
    options[1] = 6;        //tRType
    options[2] = 128;
    options[3] = mesh->GetMeshDimension();

    int nMoves = -1;

    std::vector<idxtype> finalAgglom(nFineCells);

    MGridGen
        (
        nFineCells,
        &cellCellOffsets[0],
        &v_vol[0],
        &magSb[0],

        &cellCells[0],
        &faceWeights[0],
        minsize,
        maxsize,

        &options[0],
        &nMoves,
        &nCoarseCells,
        &finalAgglom[0]
        );

    finalDecomp.assign(finalAgglom.begin(), finalAgglom.end());
    
}

void AgglomerateMGridGen::SetAdjacency(Mesh* mesh, const std::vector<int> &wallPatchIDList, const int &nNormal, const bool &red, std::vector<bool> &faceAdjacency)
{
	//faceAdjacency.resize(mesh->v_face.size(), true);

	for (int i = 0; i < wallPatchIDList.size(); ++i)
	{
		const int &patchID = wallPatchIDList[i];
		for (int j = 0; j < mesh->GetBoundaryFaceSize(patchID); ++j)
		{
			int faceID = mesh->GetBoundaryFaceID(patchID, j);
			int ownerID = mesh->GetFace(faceID).GetOwnerID();
			bool green = false;
			int oppositeFaceID = SetFaceAdjacency(mesh, ownerID, faceID, green, red, faceAdjacency);
			for (int k = 1; k <= nLayer; ++k)
			{
				if (oppositeFaceID < 0) //找不到对面
				{
					break;
				}
				else
				{
					faceID = oppositeFaceID;
					if (ownerID == mesh->GetFace(faceID).GetOwnerID())
					{
						if (mesh->GetFace(faceID).GetNeighborID() < 0) break;
						else ownerID = mesh->GetFace(faceID).GetNeighborID();
					}
					else
						ownerID = mesh->GetFace(faceID).GetOwnerID();
					if (k % nNormal == 0) green = true;
					else                  green = false;
				}
				oppositeFaceID = SetFaceAdjacency(mesh, ownerID, faceID, green, red, faceAdjacency);
			}
		}
	}
	return;
}

int AgglomerateMGridGen::SetFaceAdjacency(Mesh* mesh, const int &elementID, const int &objectFaceID, const bool &green, const bool &red, std::vector<bool> &faceAdjacency)
{
	if (green) faceAdjacency[objectFaceID] = !green;

	int oppositeFaceID = -1;
    const Face &objectFace = mesh->GetFace(objectFaceID);
	const Element &element = mesh->GetElement(elementID);
	for (int j = 0; j < element.GetFaceSize(); j++)
	{
		const int &faceID = element.GetFaceID(j);
		if (faceID != objectFaceID)
		{
            const Face &face = mesh->GetFace(faceID);
			bool adjoinFaceFlag = false; //连接面标志
			for (int k = 0; k < face.GetNodeSize(); k++)
			{
				for (int m = 0; m < objectFace.GetNodeSize(); m++)
				{
					if (objectFace.GetNodeID(m) == face.GetNodeID(k))
					{
						adjoinFaceFlag = true;
						faceAdjacency[faceID] = !red;
						break;
					}
				}
				if (adjoinFaceFlag) break;
			}
			if (!adjoinFaceFlag)
			{
				if (fabs(mesh->GetFace(objectFaceID).GetNormal() & mesh->GetFace(faceID).GetNormal()) < 0.9)
				{
					adjoinFaceFlag = true;
					faceAdjacency[faceID] = !red;
					continue;
				}

				oppositeFaceID = faceID;
			}
		}
	}
	return oppositeFaceID;
}
