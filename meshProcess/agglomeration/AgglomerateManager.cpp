﻿#include "meshProcess/agglomeration/AgglomerateManager.h"
#include "basic/mesh/SubMesh.h"
#include "meshProcess/meshConverter/DlgMesh.h"

AgglomerateManager::AgglomerateManager( const Preprocessor::AgglomerateType &type_, const int &nLayer_,
                                        const int &coarseRationNormal_, const int &coarseRationTangential_,
	                                    const int &minSize_, const int &maxSize_,
                                        const bool &singletonsRemovementFlag_, const bool &nodeCenter_,
                                        const bool &structured_ )
    : type(type_), nLayer(nLayer_), minSize(minSize_), maxSize(maxSize_), nodeCenter(nodeCenter_),
    coarseRationNormal(coarseRationNormal_), coarseRationTangential(coarseRationTangential_),
    singletonsRemovementFlag(singletonsRemovementFlag_), structured(structured_)
{
}

AgglomerateManager::~AgglomerateManager()
{}

void AgglomerateManager::CreateMultiGrid(Mesh* fineMesh, MultiGrid* multiGrid, const std::vector<int> &wallList, const int &level)
{
    //step0: 准备工作
	int nCoarseCells = 0;
    std::vector<int> fineToCoarseMap;
    Print("\t生成粗细网格映射关系...");

	//step1: 生成粗细网格映射关系
	if (fineMesh->multiStructuredBlock.GetBlockSize() > 0 && JudgeDimension(fineMesh)) //结构网格并满足要求，按结构网格聚合
	{
		AgglomerateStructuredMesh p_Agglomerate;
		p_Agglomerate.Agglomerate(fineMesh, multiGrid, level, fineToCoarseMap, nCoarseCells);
	}
	else //按非结构网格聚合
	{
		if (structured) WarningContinue("注意：网格为结构形式，按非结构聚合...");
		
		if (type == Preprocessor::AgglomerateType::MGRIDGEN)
		{
			AgglomerateMGridGen p_Agglomerate(nLayer, coarseRationNormal, coarseRationTangential, minSize, maxSize);
			p_Agglomerate.Agglomerate(fineMesh, wallList, level, fineToCoarseMap, nCoarseCells);
		}
		else if (type == Preprocessor::AgglomerateType::SEED)
		{
			// 结构形式网格，法向和切向聚合率强制为2，同时，边界层层数足够大
			const int coarseRationNormalReal = structured ? 2 : coarseRationNormal;
			const int coarseRationTangentialReal = structured ? 2 : coarseRationTangential;
			const int nLayerReal = structured ? 100000000 : nLayer;
			AgglomerateSeed p_Agglomerate(nLayerReal, coarseRationNormalReal, coarseRationTangentialReal, minSize, maxSize, structured);
			p_Agglomerate.Agglomerate(fineMesh, wallList, level, fineToCoarseMap, nCoarseCells);
		}
		else
		{
			FatalError("Agglomerate type is not supported. Please check type.\n");
		}		
	}	
    
	//step2: 生成粗网格与细网格对应关系
    Print("\t生成粗网格与细网格对应关系...");
    multiGrid->v_elemMap.clear();
    multiGrid->v_elemMap.resize(nCoarseCells, std::vector<int>{});
	for (int coarseID = 0; coarseID < nCoarseCells; coarseID++) multiGrid->v_elemMap[coarseID].reserve(maxSize);
    for (int fineID = 0; fineID < fineToCoarseMap.size(); fineID++)
        multiGrid->v_elemMap[fineToCoarseMap[fineID]].push_back(fineID);	
    
	//step3: 剔除孤立单元
	if(singletonsRemovementFlag)
	{
		Print("\t去除孤立单元...");
		this->RemoveSingletons(fineMesh, fineToCoarseMap, multiGrid->v_elemMap, nCoarseCells);
	}
	
	//step4: 生成粗网格
    Print("\t生成粗网格拓扑信息...");
	this->CreateMultiGridTopology(fineMesh, multiGrid, fineToCoarseMap, multiGrid->v_elemMap);
}

void AgglomerateManager::RemoveSingletons(Mesh* fineMesh,
                                          std::vector<int> &fineToCoarseMap,
                                          std::vector<std::vector<int>> &coarseToFineMap,
                                          int &nCoarseCells)
{
    // 1.生成粗网格单元的边界编号列表，内部单元为空
    std::vector<std::vector<int>> elementPatchIDList(nCoarseCells, std::vector<int>{});
    for (int patchID = 0; patchID < fineMesh->GetBoundarySize(); patchID++)
    {
        for (int index = 0; index < fineMesh->GetBoundaryFaceSize(patchID); index++)
        {
            const int &faceID = fineMesh->GetBoundaryFaceID(patchID, index);
            const int &ownerID = fineMesh->GetFace(faceID).GetOwnerID();
            const int &coarseID = fineToCoarseMap[ownerID];
            elementPatchIDList[coarseID].push_back(patchID);
        }
    }
    for (int coarseID = 0; coarseID < nCoarseCells; coarseID++)
        elementPatchIDList[coarseID] = GetNonRepeatedList(elementPatchIDList[coarseID]);

    // 2.遍历粗网格单元，判断孤立单元，并修改fineToCoarseMap和coarseToFineMap
    for (int coarseID = 0; coarseID < coarseToFineMap.size(); ++coarseID)
    {
        // 2.1只包含一个细网格的粗网格为孤立单元
        if (coarseToFineMap[coarseID].size() > 1) continue;

        // 2.2孤立单元所对应的细网格单元
        const int &elementIDFine = coarseToFineMap[coarseID][0];
        const Element &fineElement = fineMesh->GetElement(elementIDFine);

        const std::vector<int> &patchIDListSingle = elementPatchIDList[coarseID];
        
        // 2.3获取相邻粗网格单元编号列表
        std::vector<int> coarseIDAdjoinList, coarseIDAdjoinListAll;
        for (int k = 0; k < fineElement.GetFaceSize(); ++k)
        {
            const int &faceID = fineElement.GetFaceID(k);
            int adjacentID = fineMesh->GetFace(faceID).GetNeighborID();
            if (adjacentID == elementIDFine) adjacentID = fineMesh->GetFace(faceID).GetOwnerID();

            if (adjacentID != -1)
            {
                const int &coarseIDAdjacent = fineToCoarseMap[adjacentID];

                // 优先选择不增加原始粗网格边界条件数量的相邻粗单元
                bool noFoundFlag = false;
                for (int m = 0; m < patchIDListSingle.size(); m++)
                {
                    const std::vector<int> &patchIDListAdjoin = elementPatchIDList[coarseIDAdjacent];
                    if (std::find(patchIDListAdjoin.begin(), patchIDListAdjoin.end(), patchIDListSingle[m]) == patchIDListAdjoin.end())
                    {
                        noFoundFlag = true;
                        break;
                    }
                }
                if (!noFoundFlag) coarseIDAdjoinList.push_back(coarseIDAdjacent);
                coarseIDAdjoinListAll.push_back(coarseIDAdjacent);
            }
        }
        if (coarseIDAdjoinList.empty()) coarseIDAdjoinList = coarseIDAdjoinListAll;
        coarseIDAdjoinList = GetNonRepeatedList(coarseIDAdjoinList);

        // 2.4待合并相邻粗网格单元编号和查找标识
        int coarseIDAdjoin0 = -1;
        bool foundFlag = false;

        // 2.5周围只有一个相邻粗网格单元时，只能选这个粗单元
        if (coarseIDAdjoinList.size() == 1)
        {
            coarseIDAdjoin0 = coarseIDAdjoinList[0];
            foundFlag = true;
        }

        // 2.6找聚合率最小的相邻粗单元
        if (!foundFlag)
        {
            coarseIDAdjoin0 = coarseIDAdjoinList[0];
            int coarseRationMin = coarseToFineMap[coarseIDAdjoinList[0]].size();
            for (int m = 1; m < coarseIDAdjoinList.size(); ++m)
            {
                const int &coarseIDAdjoin = coarseIDAdjoinList[m];
                const int currentRation = coarseToFineMap[coarseIDAdjoin].size();
                if (currentRation != coarseRationMin)
                {
                    foundFlag = true;
                    if (currentRation < coarseRationMin)
                    {
                        coarseRationMin = currentRation;
                        coarseIDAdjoin0 = coarseIDAdjoin;
                    }
                }
            }
            
            if (foundFlag)
            {
                int count = 0;
                for (int m = 0; m < coarseIDAdjoinList.size(); ++m)
                {
                    const int &coarseIDAdjoin = coarseIDAdjoinList[m];
                    const int currentRation = coarseToFineMap[coarseIDAdjoin].size();
                    if (coarseRationMin == currentRation) count++;
                    else coarseIDAdjoinList[m] = -1;
                }
                foundFlag = (count == 1);
            }
        }

        // 2.7如果聚合率都相同，则寻找体积最小的相邻粗单元
        if (!foundFlag)
        {
            Scalar volumeMin = INF;
            for (int m = 0; m < coarseIDAdjoinList.size(); ++m)
            {
                if (coarseIDAdjoinList[m] == -1) continue;
                Scalar volumeTemp = Scalar0;
                const int coarseIDAdjoin = coarseIDAdjoinList[m];
                for (int n = 0; n < coarseToFineMap[coarseIDAdjoin].size(); ++n)
                {
                    const int &fineID = coarseToFineMap[coarseIDAdjoin][n];
                    volumeTemp += fineMesh->GetElement(fineID).GetVolume();
                }
                if (volumeTemp < volumeMin)
                {
                    volumeMin = volumeTemp;
                    coarseIDAdjoin0 = coarseIDAdjoin;
                }
            }
        }

        // 2.8修改粗细网格对应关系
        coarseToFineMap[coarseIDAdjoin0].push_back(elementIDFine);
        fineToCoarseMap[elementIDFine] = coarseIDAdjoin0;
        coarseToFineMap[coarseID].clear();
    }
	
    // 3.修改粗细网格对应关系
    nCoarseCells = 0;
    for (int coarseID = 0; coarseID < coarseToFineMap.size(); ++coarseID)
    {
        if (coarseToFineMap[coarseID].size() == 0) continue;
        for (int index = 0; index < coarseToFineMap[coarseID].size(); ++index)
        {
            const int &fineID = coarseToFineMap[coarseID][index];
            fineToCoarseMap[fineID] = nCoarseCells;
        }
        coarseToFineMap[coarseID].clear();
        nCoarseCells++;
    }
    coarseToFineMap.resize(nCoarseCells);
    for (int fineID = 0; fineID < fineToCoarseMap.size(); fineID++)
    {
        const int &coarseID = fineToCoarseMap[fineID];
        coarseToFineMap[coarseID].push_back(fineID);
    }
}

void AgglomerateManager::CreateMultiGridTopology(Mesh *fineMesh, MultiGrid *coarseMesh, const std::vector<int> &fineToCoarseMap, std::vector<std::vector<int>> &coarseToFineMap)
{
	//step1:生成面信息
	this->CreateMultiGridFace(fineMesh, coarseMesh, fineToCoarseMap, coarseToFineMap);

    //step2: 修改单元的其他信息
	for (int coarseID = 0; coarseID < coarseMesh->v_elem.size(); ++coarseID)
    {
        coarseMesh->v_elem[coarseID].est_shapeType = (coarseMesh->md_meshDim == Mesh::md2D) ? Element::estPolygon : Element::estPolyhedron;
        coarseMesh->v_elem[coarseID].volume = Scalar0;
        coarseMesh->v_elem[coarseID].center = Vector0;
    }

	//step3: 计算粗网格体积和体心
	for (int coarseID = 0; coarseID < coarseMesh->v_elem.size(); ++coarseID)
	{
		// 体积和体心计算
		for (int index = 0; index < (int)coarseMesh->v_elemMap[coarseID].size(); ++index)
		{
			const int &fineID = coarseMesh->v_elemMap[coarseID][index];
			coarseMesh->v_elem[coarseID].volume += fineMesh->v_elem[fineID].volume;
			coarseMesh->v_elem[coarseID].center += fineMesh->v_elem[fineID].center * fineMesh->v_elem[fineID].volume;
		}
		coarseMesh->v_elem[coarseID].center /= coarseMesh->v_elem[coarseID].volume;
	}

    // step4: 细网格为对偶网格时，修改粗网格边界单元体心
    if (nodeCenter)
    {
        for (int patchID = 0; patchID < (int)coarseMesh->vv_boundaryFaceID.size(); patchID++)
        {
            for (int index = 0; index < (int)coarseMesh->vv_boundaryFaceID[patchID].size(); index++)
            {
                const int &faceID = coarseMesh->vv_boundaryFaceID[patchID][index];
                const int &coarseID = coarseMesh->v_face[faceID].GetOwnerID();

                const int fineSize = (int)coarseMesh->v_elemMap[coarseID].size();

                std::vector<int> boundaryFaceSizeList(fineSize, 0);
                int boundaryFaceSizeMax = 0;
                for (int index1 = 0; index1 < fineSize; ++index1)
                {
                    const int &fineID = coarseMesh->v_elemMap[coarseID][index1];
                    for (int j = 0; j < fineMesh->v_elem[fineID].GetFaceSize(); j++)
                    {
                        const int &faceID = fineMesh->v_elem[fineID].v_faceID[j];
                        if (fineMesh->v_face[faceID].n_neighbor == -1) boundaryFaceSizeList[index1]++;
                    }
                    if (boundaryFaceSizeList[index1] > boundaryFaceSizeMax) boundaryFaceSizeMax = boundaryFaceSizeList[index1];
                }

                Scalar volumeSum = Scalar0;
                coarseMesh->v_elem[coarseID].center = Vector0;
                for (int index1 = 0; index1 < fineSize; ++index1)
                {
                    if (boundaryFaceSizeList[index1] == boundaryFaceSizeMax)
                    {
                        const int &fineID = coarseMesh->v_elemMap[coarseID][index1];
                        volumeSum += fineMesh->v_elem[fineID].volume;
                        coarseMesh->v_elem[coarseID].center += fineMesh->v_elem[fineID].center * fineMesh->v_elem[fineID].volume;
                    }
                }
                coarseMesh->v_elem[coarseID].center /= volumeSum;
            }
        }
    }

    // 粗网格单元和面的v_nodeID为空	
	coarseMesh->n_nodeNum = coarseMesh->v_node.size();
	coarseMesh->n_elemNum = coarseMesh->v_elem.size();
    coarseMesh->n_elemNum_all = coarseMesh->n_elemNum;
}

void AgglomerateManager::CreateMultiGridFace(Mesh *fineMesh, MultiGrid *coarseMesh, const std::vector<int> &fineToCoarseMap, std::vector<std::vector<int>> &coarseToFineMap)
{
    //step0:准备工作
	coarseMesh->md_meshDim = fineMesh->md_meshDim;
	if (coarseMesh->md_meshDim == Mesh::md2D)      coarseMesh->est_shapeType = Element::estPolygon;
	else if (coarseMesh->md_meshDim == Mesh::md3D) coarseMesh->est_shapeType = Element::estPolyhedron;
	const int coarseElementSize = coarseMesh->v_elemMap.size();
	coarseMesh->v_elem.resize(coarseElementSize);
	coarseMesh->vv_boundaryFaceID.resize(fineMesh->GetBoundarySize());
    coarseMesh->v_boundaryName = fineMesh->v_boundaryName;
	
	//step1:建立待合并的面列表
	std::vector<std::vector<std::pair<int,int>>> faceIDMap;
    faceIDMap.reserve(coarseMesh->GetFaceNumber()); //此处要修改
    
	//step1.1:物理边界面循环，填充faceIDMap，边界面必须在前
	for (int i = 0; i < fineMesh->GetBoundarySize(); i++)
    {
        std::map<std::pair<int, int>, int> map;
		for (int j = 0; j < fineMesh->GetBoundaryFaceSize(i); j++)
		{
			const int &faceIDOldFine = fineMesh->GetBoundaryFaceID(i, j);
			const int &ownerIDCoarse = fineToCoarseMap[fineMesh->v_face[faceIDOldFine].n_owner];
			std::pair<int, int> p1{ ownerIDCoarse, i};            
            
            std::map<std::pair<int, int>, int>::iterator it = map.find(p1);
            if(it != map.end()) //找到了
            {
                const int &faceIDNew = it->second;
				faceIDMap[faceIDNew].push_back(std::pair<int, int>{faceIDOldFine, i});
            }
            else
            {
                int faceIDNew = faceIDMap.size();
                map.insert(std::make_pair(p1, faceIDNew));
                faceIDMap.push_back(std::vector<std::pair<int,int>>{std::pair<int,int>{faceIDOldFine, i}});
            }
        }
    }

    //step1.2:内部面循环，填充faceIDMap
	std::vector<std::map<std::pair<int, int>, int>> mapVector(coarseElementSize);
	for (int faceIDOldFine = 0; faceIDOldFine < fineMesh->GetFaceNumber(); faceIDOldFine++)
	{
		const Face &fineFace = fineMesh->v_face[faceIDOldFine];
		const int &ownerIDFine = fineFace.GetOwnerID();
		const int &neighIDFine = fineFace.GetNeighborID();
		if (neighIDFine < 0) continue; //边界面后面处理

		const int &ownerIDCoarse = fineToCoarseMap[ownerIDFine];
		const int &neighIDCoarse = fineToCoarseMap[neighIDFine];
		if (ownerIDCoarse == neighIDCoarse) continue; //聚合后消失的面跳过

		const int pos = (ownerIDCoarse + neighIDCoarse) / 2;

		std::pair<int, int> p1{ ownerIDCoarse, neighIDCoarse };
        
        auto it = mapVector[pos].find(p1);
        if(it != mapVector[pos].end()) //找到了
        {
            const int &faceIDNew = it->second;
			faceIDMap[faceIDNew].push_back(std::pair<int, int>{faceIDOldFine, -1});
        }
        else
        {
			std::pair<int, int> p2{ neighIDCoarse, ownerIDCoarse };
            it = mapVector[pos].find(p2);
            if(it != mapVector[pos].end()) //找到了
            {
                const int &faceIDNew = it->second;
				faceIDMap[faceIDNew].push_back(std::pair<int, int>{faceIDOldFine, -1});
            }
            else
            {
                int faceIDNew = faceIDMap.size();
                mapVector[pos].insert(std::make_pair(p1, faceIDNew));
				faceIDMap.push_back(std::vector<std::pair<int, int>>{std::pair<int, int>{faceIDOldFine, -1}});
            }
        }
    }
	std::vector<std::map<std::pair<int, int>, int>>().swap(mapVector); //释放空间
    
    //step2: 重新计算大面信息
	coarseMesh->v_face.reserve(faceIDMap.size());
	int countFace = 0; //统计应合并大面却保留碎面的大面数量
    for (int i = 0; i < faceIDMap.size(); ++i)
    {
		const int smallFaceSize = faceIDMap[i].size();
		if (smallFaceSize == 0) continue;
		std::vector<int> smallFaceIDList(smallFaceSize);
		for (int k = 0; k < smallFaceSize; k++)
			smallFaceIDList[k] = faceIDMap[i][k].first;
		
		Face bigFace;
		const Scalar ratio = MergeSmallFace(fineMesh, smallFaceIDList, bigFace, fineToCoarseMap);

		if (ratio > SMALL) //按大面处理
		{
			coarseMesh->v_face.push_back(bigFace);
			const int bigFaceID = coarseMesh->v_face.size() - 1;
			coarseMesh->v_elem[bigFace.n_owner].v_faceID.push_back(bigFaceID);
			if (bigFace.n_neighbor >= 0) // 内部面
			{
				coarseMesh->v_elem[bigFace.n_neighbor].v_faceID.push_back(bigFaceID);
			}
			else // 物理边界面
			{
				const int &patchID = faceIDMap[i][0].second;
				coarseMesh->vv_boundaryFaceID[patchID].push_back(bigFaceID);				
			}
		}
		else//该面不合并，按碎面处理
		{
			countFace++;
			if (countFace <= 5)
				Print("\tFace " + ToString(countFace) + " , center = " + ToString(bigFace.GetCenter()));

			for (int k = 0; k < smallFaceSize; ++k)
			{
				const Face &tempFace = fineMesh->GetFace(smallFaceIDList[k]);

				coarseMesh->v_face.push_back(tempFace);
				const int tempFaceID = coarseMesh->v_face.size() - 1;
				const int &coarseOwner = fineToCoarseMap[tempFace.n_owner];
				coarseMesh->v_elem[coarseOwner].v_faceID.push_back(tempFaceID);
				coarseMesh->v_face[tempFaceID].n_owner = coarseOwner;
				coarseMesh->v_face[tempFaceID].v_nodeID.clear();

				if (tempFace.n_neighbor >= 0) // 内部面
				{
					const int &coarseNeigh = fineToCoarseMap[tempFace.n_neighbor];
					coarseMesh->v_elem[coarseNeigh].v_faceID.push_back(tempFaceID);
					coarseMesh->v_face[tempFaceID].n_neighbor = coarseNeigh;

				}
				else // 物理边界面
				{					
					const int &patchID = faceIDMap[i][0].second;
					coarseMesh->vv_boundaryFaceID[patchID].push_back(tempFaceID);
					coarseMesh->v_face[tempFaceID].n_neighbor = -1;
				}
			}
		}
    }
	coarseMesh->n_faceNum = coarseMesh->v_face.size();
	Print("\t应合并大面却保留碎面的大面数量: size = " + ToString(countFace));
	
	//step3: 检查单元的面构成	
	int count = 0;
	for (int i = 0; i < coarseMesh->v_elem.size(); ++i)
	{
		if (coarseMesh->v_elem[i].v_faceID.size() < 2) count++;
	}
	if (count > 0)
		FatalError("AgglomerateManager::CreateMultiGridFace: 粗网格单元面构成错误， 数量为 " + ToString(count));
}

Scalar AgglomerateManager::MergeSmallFace(Mesh *fineMesh, std::vector<int> &smallFaceIDList, Face &bigFace, const std::vector<int> &fineToCoarseMap)
{
    std::vector<bool> sideFlag(smallFaceIDList.size(), true);
    for (int k = 0; k < smallFaceIDList.size(); k++)
    {
        const Face &tempFace = fineMesh->GetFace(smallFaceIDList[k]);
        if (tempFace.n_neighbor == -1) continue;
        const int &ownerIDCoarse = fineToCoarseMap[tempFace.n_owner];
        const int &neighIDCoarse = fineToCoarseMap[tempFace.n_neighbor];
        if (ownerIDCoarse > neighIDCoarse) sideFlag[k] = false;
    }

	Scalar sumMag = Scalar0;
    for (int k = 0; k < smallFaceIDList.size(); k++)
    {
        const Face &tempFace = fineMesh->GetFace(smallFaceIDList[k]);
        if(k == 0)
        {
            bigFace.areaMag = tempFace.areaMag;
            bigFace.center = tempFace.center * tempFace.areaMag;
			sumMag = tempFace.areaMag;
            if (sideFlag[k])
            {
                bigFace.n_owner = fineToCoarseMap[tempFace.n_owner];
                //边界面n_neighbor为-1，不需要修改
                if (tempFace.n_neighbor >= 0)
                    bigFace.n_neighbor = fineToCoarseMap[tempFace.n_neighbor];
                bigFace.normal = tempFace.normal * tempFace.areaMag;
            }
            else
            {
                bigFace.n_owner = fineToCoarseMap[tempFace.n_neighbor];
                bigFace.n_neighbor = fineToCoarseMap[tempFace.n_owner];
                bigFace.normal = -tempFace.normal * tempFace.areaMag;				
            }
        }
        else
        {
            bigFace.center += tempFace.center * tempFace.areaMag; // 面心坐标面积加权和
            bigFace.areaMag += tempFace.areaMag; // 面积大小总和
			sumMag += tempFace.areaMag;
            if (sideFlag[k]) bigFace.normal += tempFace.areaMag * tempFace.normal; // 面积矢量和
            else             bigFace.normal -= tempFace.areaMag * tempFace.normal; // 面积矢量和
        }
	}
    bigFace.center /= Max(bigFace.areaMag, SMALL);
    bigFace.areaMag = bigFace.normal.Mag();
	if (bigFace.areaMag > SMALL) bigFace.normal /= bigFace.areaMag;
	else
	{
		bigFace.areaMag = Scalar0;
		bigFace.normal = fineMesh->GetFace(smallFaceIDList[0]).normal;
	}

	return bigFace.areaMag / Max(sumMag, SMALL);
}

bool AgglomerateManager::JudgeAdjacent(const Face &face1, const Face &face2)
{
    if(face1.GetNodeSize() == 0 || face2.GetNodeSize() == 0) return true;

    for (int j = 0; j < face1.GetNodeSize(); j++)
    {
        for (int k = 0; k < face2.GetNodeSize(); k++)
        {
            if(face1.v_nodeID[j]==face2.v_nodeID[k]) return true;
        }
    }

    return false;
}

bool AgglomerateManager::JudgeDimension(Mesh *mesh)
{
	if (mesh->multiStructuredBlock.GetBlockSize() == 0) return false;
	
	MultiStructuredBlock &multiBlockFine = mesh->multiStructuredBlock;
	for (int n = 0; n < multiBlockFine.GetBlockSize(); ++n)
	{
		const Block &block = multiBlockFine.GetBlock(n);
		if (block.cellI % 2 != 0) return false;
		if (block.cellJ % 2 != 0) return false;
		if (mesh->GetMeshDimension() == Mesh::MeshDim::md3D && block.cellK % 2 != 0) return false;
	}

	return true;
}