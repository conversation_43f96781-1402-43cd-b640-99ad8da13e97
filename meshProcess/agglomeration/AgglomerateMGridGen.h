﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file AgglomerateMGridGen.h
//! <AUTHOR>
//! @brief 用于多重网格聚合的类(采用外部库MGridGen方式)
//! @date 2022-01-10
//
//------------------------------修改日志----------------------------------------
// 2022-01-10 李艳亮、乔龙
//    说明：建立
//
//
//------------------------------修改日志----------------------------------------

#ifndef _meshProcess_agglomeration_AgglomerateMGridGen_
#define _meshProcess_agglomeration_AgglomerateMGridGen_

#include "basic/mesh/MultiGrid.h"

#include <mgridgen.h>

/**
 * @brief 基于MGridGen的网格聚合类
 * 
 */
class AgglomerateMGridGen
{
public:
    /**
     * @brief 创建基于MGridGen的网格聚合对象
     * 
     * @param[in] nLayer 边界层法向聚合层数
     * @param[in] coarseRationLayer 边界层法向聚合率
     * @param[in] coarseRationTang 边界层切向聚合率
     * @param[in] minsize 最小聚合网格数量
     * @param[in] maxsize 最大聚合网格数量
     */
    AgglomerateMGridGen(const int &nLayer, const int &coarseRationLayer, const int &coarseRationTang,
                        const int &minsize, const int &maxsize);

    ~AgglomerateMGridGen(){}

    /**
     * @brief 调用MGridGen生成网格聚合列表
     * 
     * @param[in] mesh 细网格
     * @param[in] wallPatchIDList 壁面边界的编号容器
     * @param[in] level 粗网格层级
     * @param[out] finalDecomp 聚合后细网格所属粗网格单元编号
     * @param[out] nCoarseCells 粗网格单元总数
     */
    void Agglomerate(Mesh* mesh, const std::vector<int> &wallPatchIDList, const int &level, std::vector<int> &finalDecomp, int& nCoarseCells);

private:
    /**
     * @brief 边界层法向聚合时标记边界面的平行面以及垂直面
     * 
     * @param[in] mesh 细网格
     * @param[in] wallPatchIDList 壁面边界的编号容器
	 * @param[in] nNormal 并行面截断的间隔层数
	 * @param[in] red 垂直面截断标识(true为该面截断)
     * @param[out] faceAdjacency 是否可以合并的标识容器，边界面垂直面为false
     */
    void SetAdjacency(Mesh* mesh,
                      const std::vector<int> &wallPatchIDList,
					  const int &nNormal,
					  const bool &red,
                      std::vector<bool> &faceAdjacency);

    /**
     * @brief 寻找给定单元给定面的平行面并将其他面标记为垂直面
     * 
     * @param[in] mesh 细网格
     * @param[in] elementID 单元编号
     * @param[in] objectFaceID 标记的目标面
     * @param[in] green 平行面标识(true为该面截断)
	 * @param[in] red 垂直面标识(true为该面截断)
     * @param[out] faceAdjacency 是否可以合并的标识容器，边界面垂直面为false
     * @return int 平行面编号
     */
    int SetFaceAdjacency(Mesh* mesh,
                         const int &elementID,
                         const int &objectFaceID,
                         const bool &green,
						 const bool &red,
                         std::vector<bool> &faceAdjacency);
    
private:
    const int &nLayer; ///< 边界层法向聚合层数
    const int &coarseRationLayer; ///< 边界层法向聚合率
    const int &coarseRationTang; ///< 边界层内切向聚合率
    const int &minsize; ///< 最小聚合网格数量
    const int &maxsize; ///< 最大聚合网格数量
};

#endif // _meshProcess_agglomeration_AgglomerateMGridGen_
