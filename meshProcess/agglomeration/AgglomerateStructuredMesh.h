﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file AgglomerateStructuredMesh.h
//! <AUTHOR>
//! @brief 结构网格聚合类
//! @date 2024-06-05
//
//------------------------------修改日志----------------------------------------
// 2024-06-05 李艳亮、乔龙
//    说明：建立
//
//
//------------------------------修改日志----------------------------------------

#ifndef _meshProcess_agglomeration_AgglomerateStructuredMesh_
#define _meshProcess_agglomeration_AgglomerateStructuredMesh_

#include "basic/mesh/MultiGrid.h"

/**
 * @brief  结构网格聚合类
 * 
 */
class AgglomerateStructuredMesh
{
public:
    /**
     * @brief 创建基于结构网格的聚合对象
     * 
     */
	AgglomerateStructuredMesh(){};

	~AgglomerateStructuredMesh(){};

    /**
     * @brief 生成网格聚合列表
     * 
     * @param[in] fineMesh_ 细网格
     * @param[in,out] coarseMesh_ 粗网格
     * @param[in] coarseLevel 粗网格层级
     * @param[out] finalDecomp 聚合后细网格所属粗网格单元编号
     * @param[out] nCoarseCells 粗网格单元总数
     */
	void Agglomerate(Mesh *fineMesh_, Mesh *coarseMesh_, const int &coarseLevel, std::vector<int> &finalDecomp, int& nCoarseCells);

private:
	/**
	* @brief 建立粗网格的多块结构信息
	*
	* @param[out] nCoarseCells 粗网格单元总数
	* @param[in] cRatio 聚合率
	*/
	void BuildMultiStructuredBlock(int &nCoarseCells, const int &cRatio);

private:
	Mesh *fineMesh; ///< 细网格指针
	Mesh *coarseMesh; ///< 粗网格指针
};

#endif // _meshProcess_agglomeration_AgglomerateStructuredMesh_
