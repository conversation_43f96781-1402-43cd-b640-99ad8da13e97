# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

variables:
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_STRATEGY: fetch # fetch, clone, 每次测试时的代码获取策略，fetch是基于本地代码进行拉取，clone是获取全新代码

stages: # List of stages for jobs, and their order of execution
  - build
  - test
#  - deploy
  - update_version
  - release # List of stages for jobs, and their order of execution
#  - build_arm
#  - test_arm
#  - deploy
#  - release_arm

# x86
build-job: # This job runs in the build stage, which runs first.
  stage: build
  rules:
    - when: always
  script:
    - echo "CMake Configuration......"
    - rm -f ./build
    - cmake -S ./ -B build
    - cd ./build
    - make -j20
    #- make install PREFIX=Release
    - cd ../
  artifacts: #构建完成后保留的产物
    paths:
      - "bin"
    expire_in: 30 days

unit-test-job:
  stage: test
  rules:
    - when: always
  script:
    - echo "Regression Testing......"
    - current_path=$(echo "$(pwd)" | sed  's/\//\\\//g')
    - cd ./feilianRegressionTest
    - module load mpi/intel-oneapi 
    - python3 test_1.py test
    - python3 test_1.py setref
    - python3 test_1.py clean

# unit-test-job:   # This job runs in the test stage.
#   stage: test    # It only starts when the job in the build stage completes successfully.
#   script:
#     - echo "Running unit tests..."

# lint-test-job:   # This job also runs in the test stage.
#   stage: test    # It can run at the same time as unit-test-job (in parallel).
#   script:
#     - echo "Linting code... This will take about 10 seconds."
#     - sleep 1
#     - echo "No lint issues found."

# deploy-job:      # This job runs in the deploy stage.
#   stage: deploy  # It only runs when *both* jobs in the test stage complete successfully.
#   only:
#     - develop
#   environment: production
#   script:
#     - git fetch origin
#     - git pull origin develop
#     - git remote set-url origin *******************************************************/feilian/feilian.git
#     - git config --local user.name qiaolong 
#     - git config --local user.email <EMAIL>
#     - echo "Update version......"
#     - sh ./update_version.sh
#    # - git push origin HEAD:develop
#     #- echo "This code do not need to be deployed..."


update_version:
  stage: update_version
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" && $CI_COMMIT_MESSAGE !~ /update version/
      when: always

  script:
    - git config --local user.email "<EMAIL>"
    - git config --local user.name "feilianMaintainer"
    - git checkout $CI_COMMIT_REF_NAME
    - current_version=$(cat VERSION)
    - new_version=$(echo $current_version | awk -F. '{OFS="."; $4+=1; print $0}')
    - echo -n $new_version > VERSION
    - git add VERSION
    - git commit -m "update version to $new_version [skip ci]"
    - git config pull.rebase true  #防止在pull的时候产生merge提交
    - git pull
    - git push "http://feilianMaintainer:**************************@************/feilian/feilian" HEAD:$CI_COMMIT_REF_NAME

release-job:
  stage: release
  rules:
    - if: $CI_COMMIT_TAG #只有创建TAG时才执行
  script:
    - cd feilian-specialmodule
    - git tag $CI_COMMIT_TAG #specialModule与基础模块保持相同版本标签
      #先手动修改CHANGELOG.md，说明本次版本更新的内容
    - export EXTRA_DESCRIPTION=$(changelog)
  release:
    name: "Release $CI_COMMIT_TAG"
    description: "$EXTRA_DESCRIPTION"
    tag_name: "$CI_COMMIT_TAG"
    ref: "$CI_COMMIT_TAG"
## arm
#build-job-arm:       # This job runs in the build stage, which runs first.
#  stage: build_arm
#  tags:
#    - kunpeng
#  script:
#    - echo "CMake Configuration......"
#    - cmake -S ./ -B build
#    - cd ./build
#    - make -j20
# #   - make install
#    - cd ../
#  artifacts: #构建完成后保留的产物
#    paths:
#      - "bin"
#    expire_in: 30 days
#
#unit-test-job-arm:
#   stage: test_arm
#   tags:
#    - kunpeng
#   script:
#    - echo "Testing......"
#    - current_path=$(echo "$(pwd)" | sed  's/\//\\\//g')
#    - cd ./testCases
#    - sed -i "s/MYTEST   =.*/MYTEST   = $current_path\/testCases/g" DEFINES
#   # - sed -i "s/FLBIN   =.*/FLBIN   = $current_path\/bin/g" DEFINES
#    - make test
#    - make summary
#
#release-job-arm:
#  stage: release_arm
#  tags:
#    - kunpeng
#  rules:
#    - if: $CI_COMMIT_TAG   #只有创建TAG时才执行
#  script:
#    - cd feilian-specialmodule
#    - git tag $CI_COMMIT_TAG   #specialModule与基础模块保持相同版本标签
#     #先手动修改CHANGELOG.md，说明本次版本更新的内容
#    - export EXTRA_DESCRIPTION=$(changelog)
#  release:
#    name: "Release $CI_COMMIT_TAG"
#    description: "$EXTRA_DESCRIPTION"
#    tag_name: "$CI_COMMIT_TAG"
#    ref: "$CI_COMMIT_TAG"
