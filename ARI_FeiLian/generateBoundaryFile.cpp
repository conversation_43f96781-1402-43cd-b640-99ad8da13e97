﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file generateBoundaryFile.cpp
//! <AUTHOR>
//! @brief 边界条件参数文件生成工具.
//! @date 2021-08-07
//
//------------------------------修改日志----------------------------------------
// 2021-08-07 乔龙
//     说明：建立
//
//------------------------------------------------------------------------------

#include "basic/configure/Configure.h"
#include "meshProcess/meshConverter/MeshConvertManager.h"

void GenerateBoundaryFile(Configure::Configure &flowConfigure, const std::vector<std::vector<std::string>> &boundaryNameVector);
int main(int argc, char **argv)
{
	if (argc <= 1)
	{
		FatalError("参数错误");
		return -1;
	}
	
    // 输出软件相关信息
    SetInfoFile("AriCFD_GenerateBoundaryFile.info");
    PrintTitleInfo(" ARI-GenerateBoundaryFile ");
    PrintSystemTime();
    
    // 读入参数文件
    Configure::Configure configure;
    configure.ReadCaseXml(argv[1]);
    configure.PrintInformation();
    
    const auto &meshNumber = configure.GetMeshParameters().meshNumber;
	const auto &meshDim = configure.GetMeshParameters().dimension;
    std::vector<Mesh *> meshVector(meshNumber);
    std::vector<std::vector<std::string>> boundaryNameVector(meshNumber);
    for (int i = 0; i < meshNumber; i++)
    {
        meshVector[i] = new Mesh;

        const auto &meshPath = configure.GetMeshParameters().meshPath[i];
        const auto &fileName = configure.GetMeshParameters().fileName[i];
        const auto &meshType = configure.GetMeshParameters().meshType[i];
        if (meshType != Preprocessor::MeshType::ADLG && meshType != Preprocessor::MeshType::BDLG)
        {
            MeshConvertManager meshConvertManager(meshPath + fileName, meshType, meshDim, meshVector[i]);
            meshConvertManager.ReadMesh(false);
            CheckStatus(8001);

            boundaryNameVector[i] = meshConvertManager.GetBoundaryName();
        }
        else
        {
            bool binary = (meshType == Preprocessor::MeshType::BDLG);
            DlgMesh dlgMesh(meshPath + fileName, meshDim, meshVector[i], binary);
            dlgMesh.ReadMesh();
            CheckStatus(8002);

            boundaryNameVector[i] = dlgMesh.GetBoundaryName();
        }
    }

    // 与用户交互，生成边界条件
    Print("生成边界信息...");
    GenerateBoundaryFile(configure, boundaryNameVector);

    Print("已生成边界条件参数文件！");
    for (int i = 0; i < meshNumber; i++)
    {
        meshVector[i]->ClearMesh();
        if (meshVector[i] != nullptr) { delete meshVector[i]; meshVector[i] = nullptr; }
    }

    CloseInfoFile();
    
    return 0;
}

void GenerateBoundaryFile(Configure::Configure &configure, const std::vector<std::vector<std::string>> &boundaryNameVector)
{
    const auto &fileName = configure.GetMeshParameters().fileName;

    // 用户交互界面
    int meshNumber = boundaryNameVector.size();
    std::vector<Configure::BoundaryStruct> globalBoundary;
    for (int zoneID = 0; zoneID < meshNumber; zoneID++)
    {
        const auto &boundaryName = boundaryNameVector[zoneID];
        const int boundaryNumber = boundaryName.size();
        const int globalIDBegin = globalBoundary.size();

        std::cout << "\n文件" << fileName[zoneID] << "所包含边界名称列表:" << std::endl;
        for (int i = 0; i < boundaryNumber; i++)
        {
            globalBoundary.push_back(Configure::BoundaryStruct());
            const int globalID = globalBoundary.size() - 1;
            globalBoundary[globalID].globalID = globalID;
            globalBoundary[globalID].name = boundaryName[i];

            std::cout << std::setw(10) << "#" << globalID << ": " << boundaryName[i] << std::endl;
        }

        std::cout << "\n可选边界条件类型列表:" << std::endl;
        std::cout << std::setw(10) << "ID" << "  boundary_type" << std::endl;
        const auto &map = Configure::boundaryTypeReverseMap;
        for (auto iterator = map.begin(); iterator != map.end(); iterator++)
        {
            std::cout << std::setw(10) << iterator->first << ": " << iterator->second << std::endl;
        }
        std::cout << std::endl;

        bool selectTypeFlag = true;
        while (selectTypeFlag)
        {
            for (int i = 0; i < boundaryNumber; i++)
            {
                int globalID = globalIDBegin + i;

                int boundayTypeID = -1;
                std::string input;
                bool setFlag = false;
                while (!setFlag)
                {
                    std::cout << "边界#" << globalID << ": " << globalBoundary[globalID].name << "的类型是（ID）: ";
                    std::cin.clear();
                    std::cin >> input;
                    std::cin.ignore();

                    input.erase(std::remove(input.begin(), input.end(), '\n'), input.end());
                    input.erase(std::remove(input.begin(), input.end(), ' '), input.end());
                    std::stringstream sstream(input);
                    if (sstream >> boundayTypeID)
                    {
                        globalBoundary[globalID].type = (Boundary::Type)boundayTypeID;
                        if (map.find(globalBoundary[globalID].type) != map.end()) setFlag = true;
                    }

                    if (!setFlag) Print("边界设置不合理，请重新设置边界条件类型!");
                }

                std::ostringstream outString;
                outString
                    << "(" << globalID << ") "
                    << globalBoundary[globalID].name << " :\n\tindx = "
                    << boundayTypeID << "\n\ttype = "
                    << (Configure::boundaryTypeReverseMap.find(globalBoundary[globalID].type))->second << std::endl;
                PrintFile(outString.str());

                std::vector<std::string> boundaryProperty = (Configure::boundaryPropertyMap.find(globalBoundary[globalID].type))->second;
                int boundaryPropertySize = (int)boundaryProperty.size();
                if (boundaryPropertySize > 0)
                {
                    globalBoundary[globalID].value.resize(boundaryPropertySize);

                    for (int j = 0; j < boundaryPropertySize; j++)
                    {
                        char c[100];
                        std::cout << "\t" << boundaryProperty[j] << ": ";
                        std::cin.get(c, 100);
                        globalBoundary[globalID].value[j] = c;
                        std::cin.ignore();

                        outString.str("");
                        outString << "\t" << boundaryProperty[j] << ": " << globalBoundary[globalID].value[j] << std::endl;
                        PrintFile(outString.str());
                    }
                }
            }

            std::cout << "是否重新设置边界条件(N/Y): ";
            char flag = std::cin.get();
            if (!(flag == 'Y' || flag == 'y' )) selectTypeFlag = false;

            if (selectTypeFlag) PrintFile("重新设置边界条件.");
            else                PrintFile("确认边界条件设置.");
        }
    }

    // 生成边界条件文件
    std::fstream boundaryConditionFile;
    boundaryConditionFile.open("boundary.xml", std::ios::out);
    boundaryConditionFile << "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" << std::endl;
    int k = 0;
    for (int zoneID = 0; zoneID < meshNumber; zoneID++)
    {
        const int boundaryNumber = boundaryNameVector[zoneID].size();
        
        if(zoneID==0) boundaryConditionFile << "<boundaryConditions>" << std::endl;
        else          boundaryConditionFile << "<boundaryConditions" + ToString(zoneID) + ">" << std::endl;
        boundaryConditionFile << "\t" << "<number>" << boundaryNumber << "</number>" << std::endl;

        for (int i = 0; i < boundaryNumber; i++)
        {
            std::string globalBoundaryName = globalBoundary[k].name;
            if (globalBoundaryName.rfind("(") != globalBoundaryName.npos)
                globalBoundaryName = globalBoundaryName.substr(0, globalBoundaryName.rfind("("));

            boundaryConditionFile << "\t" << "<boundary" << i << ">" << std::endl;
            boundaryConditionFile << "\t\t" << "<name>"
                << globalBoundaryName
                << "</name>" << std::endl;

            boundaryConditionFile << "\t\t" << "<type>"
                << (Configure::boundaryTypeReverseMap.find(globalBoundary[k].type))->second
                << "</type>" << std::endl;

            std::vector<std::string> boundaryProperty = (Configure::boundaryPropertyMap.find(globalBoundary[k].type))->second;
            int boundaryPropertySize = (int)boundaryProperty.size();
            if (boundaryPropertySize > 0)
            {
                for (int j = 0; j < boundaryPropertySize; j++)
                {
                    boundaryConditionFile << "\t\t" << "<" << boundaryProperty[j] << ">"
                        << globalBoundary[k].value[j]
                        << "</" << boundaryProperty[j] << ">" << std::endl;
                }
            }
            boundaryConditionFile << "\t" << "</boundary" << i << ">" << std::endl;

            ++k;
        }

        if(zoneID==0) boundaryConditionFile << "</boundaryConditions>" << std::endl << std::endl;
        else          boundaryConditionFile << "</boundaryConditions" + ToString(zoneID) + ">" << std::endl << std::endl;
    }

    boundaryConditionFile.close();
}
