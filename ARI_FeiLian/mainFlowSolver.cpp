﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file mainFlowSolver.cpp
//! <AUTHOR>
//! @brief 流场解算器主流程.
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释
//
// 2021-03-29 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#include "sourceFlow/flowSolver/OuterLoop.h"

int main(int argc, char **argv)
{
	if (argc <= 1)
	{
		FatalError("参数错误");
		return -1;
	}

    // step0:建立并行环境
    InitializeMPI(argc, argv);

#if defined(_EnablePETSC_)
    char help[] = "FVM with PETSc \n";
	PetscInitialize(&argc, &argv, NULL, help);
#endif

    // step1:输出软件相关信息
    SetInfoFile("AriCFD_Solver.info");
    if (GetMPIRank() == 0)
    {
        PrintTitleInfo(" ARI_FeiLian ");
        PrintSystemTime();
    }

    if (GetMPIRank() == 0) Print("Read control file ...");

    // step2:读入控制参数文件
    Configure::Flow::FlowConfigure flowConfigure; //定义参数文件对象
    flowConfigure.ReadCaseXml(argv[1]); //读取参数文件
    flowConfigure.ReadBoundaryXml(); //读取边界参数文件
    if (GetMPIRank() == 0) //打印计算参数
    {
        flowConfigure.PrintInformation();
        flowConfigure.PrintReferenceValues();
    }
    
    // step3:读网格
    if (GetMPIRank() == 0) Print("\nRead mesh file ...");
    const std::string meshName = flowConfigure.GetPreprocess().outputPath + flowConfigure.GetCaseName();
    SubMesh localMesh(meshName,
                      flowConfigure.GetAcceleration().multigridSolver.level,
                      flowConfigure.GetPreprocess().binaryFileFlag);
    CheckStatus(9400);

    // 更新当地边界信息
    flowConfigure.UpdateLocalBoundary(&localMesh);

    // 网格参数预计算
    std::vector<std::vector<int>> symmetryPatchID(localMesh.GetTotalLevel());
    for (int level = 0; level < localMesh.GetTotalLevel(); ++level)
    {
        for (int patchID = 0; patchID < localMesh.GetMultiGrid(level)->GetBoundarySize(); ++patchID)
            if (flowConfigure.JudgeSymmetryLocal(level, patchID)) symmetryPatchID[level].push_back(patchID);
    }
    const bool &dualMeshFlag = flowConfigure.GetPreprocess().dualMeshFlag;
    localMesh.PreCalculate(dualMeshFlag, symmetryPatchID);
    CheckStatus(9500);

    // step4:流场计算
    OuterLoop outerLoop(&localMesh, flowConfigure); //创建流场解算器对象
	CheckStatus(2000);

    // 流场初始化
    outerLoop.Initialize();// 流场初始化
    CheckStatus(2100);

    // 流场迭代求解外循环
    outerLoop->Solve();

    // step5:计算完成
    if (GetMPIRank() == 0) Print("\nEnd to iterate successfully!");
    CloseInfoFile();

    FinalizeMPI();

    return 0;
}
