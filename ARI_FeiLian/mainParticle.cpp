﻿#include "feilian-specialmodule/particle/solver/DEMSolver.h"
#include "basic/geometry/Geometry.h"

int main(int argc, char **argv)
{
    // 建立并行环境
    InitializeMPI(argc, argv);
    omp_set_num_threads(1);

    // 输出软件相关信息
	SetInfoFile("AriCFD_DEM_Solver.info");
	if (GetMPIRank() == 0)
	{
        PrintTitleInfo(" ARI_DEM ");
		PrintSystemTime();
	}

    if (GetMPIRank() == 0) Print("读取参数文件...", 1);
    Configure::Particle::ParticleConfigure DEMConfigure;
    DEMConfigure.ReadParameter(argv[1]);
    SetInfoLevel(DEMConfigure.control.outputLevelFile, DEMConfigure.control.outputLevelScreen);
    
    Print("设置几何边界...", 1);
    Geometry::Geometry *geom = new Geometry::Geometry();
	const auto &geomParam = DEMConfigure.GetGeometryParameters();
	geom->SetDimension(geomParam.dimension);
	for (int i = 0; i < geomParam.cylinders.size(); i++)
	{
		const auto &param = geomParam.cylinders[i];
		geom->AddCylinder(Geometry::CylinderWall(param.radius0, param.radius1, Geometry::Line(param.axisP0, param.axisP1), param.wallNumber));
	}
	for (int i = 0; i < geomParam.planes.size(); i++)
	{
		geom->AddPlaneWall(geomParam.planes[i].nodes);
	}
    Print( "几何边界包括碎面数量为" + ToString( geom->GetPlaneWallNumber() ), 2);
    
    Print("创建颗粒求解器...", 1);
	Particle::DEMSolver DEMSolver(DEMConfigure, geom, {Boundary::Type::WALL});
    
    Print("初始化求解器...", 1);
    DEMSolver.Initialize();

    Print("开始计算...", 1);
	DEMSolver.Solve();

    Print("结束计算!", 1);

    FinalizeMPI();
    
    return 0;
}