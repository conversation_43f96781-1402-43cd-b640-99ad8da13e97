﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file flowPreprocessor_AeroDynamic.cpp
//! <AUTHOR>
//! @brief 动气弹前处理功能.
//! @date 2024-06-17
//
//
//------------------------------------------------------------------------------

#include "basic/postTools/Tecplot.h"
#include "basic/configure/Configure.h"
#include "basic/postTools/BasePost.h"
#include "meshProcess/meshConverter/MeshConvertManager.h"
#include "feilian-specialmodule/meshAeroStatic/A_MeshDeform.h"


std::vector<int> WriteBoundaryID(std::vector<std::string> &wallBoundaryname,std::vector<int> &wallBoundaryID)
{

    int n = wallBoundaryname.size();
    std::vector<int> wallBoundaryOut;

    std::cout << "\n 请选择需要输出的物面(0:不输出; 1:输出):" << std::endl;
    std::cout << "\n ******" << std::endl;
    for (int i = 0; i < n; i++)
    {
        std::cout << "物面边界: " << wallBoundaryname[i] << " 是否输出(0/1): ";
        char flag = std::cin.get();
        if ( flag == '1' ) wallBoundaryOut.push_back(wallBoundaryID[i]);
        std::cin.ignore();
    }

    return wallBoundaryOut;
}


int CopyFile(std::string SourceFile, std::string Newfile)
{

    std::ifstream in;
    std::ofstream out;
    try{
        in.open(SourceFile,std::ios::binary);
        if(in.fail()){
            std::cout<<"Fail to open the source file;"<<std::endl;
            in.close();
            out.close();
            return 0;
        }
        out.open(Newfile,std::ios::binary);
        if(out.fail()){
            std::cout<<"Fail to open the new file;"<<std::endl;
                        in.close();
                        out.close();
                        return 0;
        }
        out<<in.rdbuf();
        out.close();
        in.close();
        return 1;
    }
    catch(std::exception &e){
    }

    return 0;
}


void cgns2cgns(const std::string& input_fullfilename, std::vector<Node> &v_globalNode)
{
    int fileID,baseID=1,cellDim;
    if (cg_open(input_fullfilename.c_str(), CG_MODE_MODIFY, &fileID)) cg_error_exit();
    //确定网格维度
    char basename [33];
    int physDim;
    if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim)) cg_error_exit();
    //读取点数
    cgsize_t isize[3][1];
    char zonename[33];
    int zoneID=1;
    if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0])) cg_error_exit();
    int nodeSize = (int)isize[0][0];

    
    //if( nodeSize != v_globalNode.size()) Print("\t错误 ....\n");
    
    
    //修改坐标
    cgsize_t irmin = 1, irmax = nodeSize;
    Scalar *coor = new Scalar[nodeSize];
    Scalar *coor_x = new Scalar[nodeSize];
    Scalar *coor_y = new Scalar[nodeSize];
    Scalar *coor_z = new Scalar[nodeSize];
    char coordname_x[33],coordname_y[33],coordname_z[33];
    DataType_t dataType;
    int coodID;
    //X坐标
    if (cg_coord_info(fileID, baseID, zoneID, 1, &dataType, coordname_x)) cg_error_exit();
    if (cg_coord_read(fileID, baseID, zoneID, coordname_x, dataType, &irmin, &irmax, coor)) cg_error_exit();

//  for (int i = 0; i < nodeSize; i++) coor[i] =v_globalNode[i].X();
    
    for (int i = 0; i < v_globalNode.size(); i++)
    {
            coor_x[i] = v_globalNode[i].X();
            coor_y[i] = v_globalNode[i].Y();
           if (physDim == 3) {coor_z[i] = v_globalNode[i].Z(); }
    } 
    
    if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_x, coor_x,&coodID)) cg_error_exit();
    //Y坐标
    if (cg_coord_info(fileID, baseID, zoneID, 2, &dataType, coordname_y)) cg_error_exit();
    if (cg_coord_read(fileID, baseID, zoneID, coordname_y, dataType, &irmin, &irmax, coor)) cg_error_exit();

    //for (int i = 0; i < nodeSize; i++) coor[i] = v_globalNode[i].Y();
        if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_y, coor_y,&coodID)) cg_error_exit();
    //Z坐标
    if (physDim == 3)
    {
        if (cg_coord_info(fileID, baseID, zoneID, 3, &dataType, coordname_z)) cg_error_exit();
        if (cg_coord_read(fileID, baseID, zoneID, coordname_z, dataType, &irmin, &irmax, coor)) cg_error_exit();
            if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_z, coor_z,&coodID)) cg_error_exit();          
    }
    
    if (cg_close(fileID)) cg_error_exit();
}





int ReadNodeNumber(std::string &line)
{

    std::vector<int> n_node;
    int len_s = line.size();

    int n_i = 0, n_j = 0;
	for (int k = 0; k < len_s; k++)
	{
		if (line[k] == 'i' && line[k+1] == '=')
			n_i = k;
		if (line[k] == 'j' && line[k + 1] == '=')
			n_j = k;		
	}

	int i = 0;
	int j = 0;
    int num = 0;
    while (i < len_s)
	{
		if (line[i] >= '0' && line[i] <= '9')
		{
			j = i;
			int len = 0;
			while (line[i] >= '0' && line[i] <= '9')
			{
				i++;
				len++;
			}

            if (j> n_i && j< n_j)
            {
			    std::string s0 = line.substr(j, len);
			    std::stringstream s1(s0);
			    s1 >> num;
            }

	    }
		else{
			i++;
		}
	}
          
    return num;
}

/*
std::vector<Node> ReadNode(std::string &name)
{

    std::vector<Node> n_node;
    int len_s = line.size();
	int i = 0;
	int j = 0;
    while (i < len_s)
	{
		if (line[i] >= '0' && line[i] <= '9')
		{
			j = i;
			int len = 0;
			while (line[i] >= '0' && line[i] <= '9')
			{
				i++;
				len++;
			}
			std::string s0 = line.substr(j, len);
			int num = 0;
			std::stringstream s1(s0);
			s1 >> num;
			 n_node.push_back(num);
	    }
		else{
			i++;
		}
	}
          
    return n_node[0];
}*/


void RebuildNode(std::vector<Vector> &v_wallNode,std::vector<Vector> &v_GlobalNode,std::vector<Vector> &v_weight, int n_model, Scalar R)
{
	std::vector<Node> Delta;

	Node Delta_xyz;
	Scalar yita;
	std::fstream file;
    std::vector<Node> v_GlobalNode_deform;	
     std::vector<Node> v_GlobalNode_new;	
	int n = v_GlobalNode.size();
	int n1 = v_wallNode.size();
    Delta.resize(n1);
    v_GlobalNode_deform.resize(n);
    v_GlobalNode_new.resize(n);

    std::vector<Scalar> fai;
    int j, k;

    Print("\n R  = " + std::to_string((long double)R) );
 
    ARI_OMP(parallel for schedule(static) private(Delta_xyz, yita, fai, j, k))
    for (int i = 0; i < n; i++)
    {
		Delta_xyz =Vector0;
		yita = 0.0;
		const Node nodeTemp1 = v_GlobalNode[i];	
		fai.resize(n1);
		for ( j = 0; j < v_wallNode.size(); j++)
		{
			const Node nodeTemp2 = v_wallNode[j];  
                                  
			yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2)
				+ pow((nodeTemp2.Y() - nodeTemp1.Y()), 2)
				+ pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R;

			if (yita > 1.0)
			{
				fai[j] = 0.0;				
			}
			else
			{
				fai[j] = pow((1 - yita), 4)*(4 * yita + 1);		
			}

		}

		for ( k = 0; k < v_wallNode.size(); k++)
		{
			Delta_xyz.SetX( Delta_xyz.X() + v_weight[k].X()*fai[k] );
			Delta_xyz.SetY( Delta_xyz.Y() + v_weight[k].Y()*fai[k] );
			Delta_xyz.SetZ( Delta_xyz.Z() + v_weight[k].Z()*fai[k] );
		}
	
		v_GlobalNode_deform[i] = Delta_xyz;
        fai.clear();    
    
    }	

    file.open("Global_mesh_mode"+  std::to_string((long  long int)n_model+1)  + ".dat", std::fstream::out);
    file << "VARIABLES = " << "\"dx\"," << "\"dy\"";    
    file << ",\"dz\","<< "\"id\""<<std::endl;  
    for ( k = 0; k < n; k++)
	   {
            file << v_GlobalNode_deform[k].X() << " " <<  v_GlobalNode_deform[k].Y() << " "<< v_GlobalNode_deform[k].Z() << " "  << k << std::endl;  
            v_GlobalNode_new[k].SetX(v_GlobalNode_deform[k].X() + v_GlobalNode[k].X());
            v_GlobalNode_new[k].SetY(v_GlobalNode_deform[k].Y() + v_GlobalNode[k].Y());
            v_GlobalNode_new[k].SetZ(v_GlobalNode_deform[k].Z() + v_GlobalNode[k].Z());      
       }
	file.close();	

    std::string oldFile="./agard4456_scale.cgns";
    std::string newFile="./agard4456_scale_new_model"+   std::to_string((long  long int)n_model)     +".cgns";


    CopyFile(oldFile,newFile);        
    cgns2cgns(newFile,v_GlobalNode_new);   
    



}

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
mpi::communicator mpi_world;
#endif 



int main(int argc, char **argv)
{
    if (argc <= 1) FatalError("参数错误");

    // 建立并行环境
    InitializeMPI(argc, argv);

    // 读入求解参数文件
    Configure::Configure configure;
    configure.ReadCaseXml(argv[1]);
    configure.ReadBoundaryXml(); // 读入边界条件参数文件

    SubMesh *globalMesh = new SubMesh();
    
    const auto &meshDim = configure.GetMeshParameters().dimension;
    const auto &meshPath = configure.GetMeshParameters().meshPath[0];
    const auto &fileName = configure.GetMeshParameters().fileName[0];
    const auto &meshType = configure.GetMeshParameters().meshType[0];
    const auto &meshTransform = configure.GetMeshParameters().meshTransform;

    MeshConvertManager meshConvertManager(meshPath + fileName, meshType, meshDim, globalMesh, meshTransform);		
    meshConvertManager.ReadMesh(true);
    meshConvertManager.BuildTopology();

    std::fstream file;
    file.open("foo_body1_mode1.dat", std::fstream::in);
    if (!file) {
        
        Post::Position postPosition = Post::Position::CELL_CENTER;
        Post::Tecplot *postPointer = new Post::Tecplot(globalMesh, false, postPosition);

        std::vector<std::string> wallBoundaryname;
        std::vector<int> wallBoundaryID;
        int n = globalMesh->GetBoundarySize();
        for (int globalID = 0; globalID < n; globalID++)
        {
            if(configure.GetGlobalBoundary(globalID).type >= Boundary::Type::WALL_ADIABATIC){
                wallBoundaryname.push_back( configure.GetGlobalBoundary(globalID).name);
                wallBoundaryID.push_back(globalID);
            }
        }
   
        std::vector<int> wallBoundaryOut =  WriteBoundaryID(wallBoundaryname,wallBoundaryID);
        postPointer->SetCaseName( configure.GetCaseName());
       // postPointer->WriteBoundaryFile_AeroDynamic(wallBoundaryOut);
    }
    else
    {
        
        int n_model = 8;
        std::vector<Node> v_wallNode;	
        std::vector<std::vector<Node>> v_wallNode_deform;	
        std::vector<std::vector<Vector>> v_weight; 
        v_weight.resize(n_model);

        if (GetMPIRank() == 0) 
        {
       
            v_wallNode_deform.resize(n_model);           
            std::string line;
            for (int i = 0; i < 3; i++) getline(file, line);

            int ncfd =  ReadNodeNumber(line);
            Print("\n ncfd  = " + std::to_string((long long int)ncfd));
            v_wallNode.resize(ncfd);
            for (int i = 0; i < n_model; i++) {
                v_weight[i].resize(ncfd);
                v_wallNode_deform[i].resize(ncfd);
            }
            file.close();
        
            int id;
            std::vector<Scalar> temp;       
            temp.resize(6);
            
            int scale = 10;
            for (int i = 7; i < n_model; i++) 
            {
                int idt = i+1;
                std::string name = "foo_body1_mode" + std::to_string((long  long int)idt) + ".dat";
                file.open(name, std::fstream::in);
                for (int k = 0; k < 3; k++)  getline(file, line);           
                for (int j = 0; j < ncfd; j++) 
                {
                    file>> temp[0] >> temp[1] >>temp[2] >> id >>temp[3] >>temp[4] >>temp[5];

                   // if(i == 0) 
                    //{
                        v_wallNode[j].SetX(temp[0]);
                        v_wallNode[j].SetY(temp[1]);
                        v_wallNode[j].SetZ(temp[2]);
                   // }

                    if(idt > 6) scale = 80;

                    v_wallNode_deform[i][j].SetX(temp[0]+temp[3]/scale);
                    v_wallNode_deform[i][j].SetY(temp[1]+temp[4]/scale);
                    v_wallNode_deform[i][j].SetZ(temp[2]+temp[5]/scale);
                }
                file.close();
            }          
           
            for (int i = 1; i < GetMPISize(); i++) 
            {
                mpi_world.send(i, 60, v_wallNode);
                mpi_world.send(i, 70, v_wallNode_deform);
            }    
        }
        else{

            mpi_world.recv(0, 60, v_wallNode);
            mpi_world.recv(0, 70, v_wallNode_deform);

        }
        


        MPIBarrier();    
        std::vector<Scalar>  R;  
        R.resize(n_model);
        R[0] = 72;  
        for (int i = 1; i < n_model; i++)  R[i] = 72;
        
        for (int i = 7; i < n_model; i++) 
        {
            MeshDeform meshdeform(v_wallNode,v_wallNode_deform[i],R[i]);            
	        meshdeform.Process();              
	        v_weight[i] = meshdeform.GetV_weight();
        }


        MPIBarrier();
        int n = globalMesh->GetNodeNumber();
        std::vector<Node> v_GlobalNode;	
        v_GlobalNode.resize(n);
        for (int i = 0; i < n; i++) v_GlobalNode[i] = globalMesh->GetNode(i);

        if(GetMPIRank() == 0)
        {

            for (int i = 7; i < n_model; i++) 
            {
                
                RebuildNode(v_wallNode, v_GlobalNode,v_weight[i], i,R[i]);
            }
             


        }
           
               
    }

    FinalizeMPI();
    return 0;
}


