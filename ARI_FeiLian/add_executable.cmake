#-----------------------------------------------------------------------------
# 设置所有可执行文件的编译及安装
#-----------------------------------------------------------------------------

# 设置可执行文件所需头文件路径
include_directories(${ARI_SRCDIR})

# 添加可执行文件
add_executable (${APP_NAME} ${APP_NAME}.cpp)

# 准备可执行文件所需依赖库，若依赖库没有生成，则等待生成后再执行
add_dependencies (${APP_NAME} ${LINK_LIBS})

# 为可执行文件链接依赖库
target_link_libraries (${APP_NAME} ${LINK_LIBS})

# 设置可执行文件在工程中所属的文件夹
set_target_properties(${APP_NAME} PROPERTIES FOLDER ${EXECUTABLE_FOLDER})

# 设置可执行文件的安装路径
install(TARGETS ${APP_NAME} RUNTIME DESTINATION ${ARI_INSTALL_PREFIX}/bin)
