﻿#include "meshProcess/wallDistance/WallDistanceManager.h"
#include "feilian-specialmodule/particle/solver/CFDDEMSolver.h"
#include "meshProcess/meshProcess/MeshProcess.h"

#if defined(_BasePlatformWinddows_)
#include <sys/stat.h>
#else
#include <sys/stat.h>
#include <unistd.h>
#endif

int main(int argc, char **argv)
{
    
    if (argc <= 1) FatalError("参数错误");

    // 建立并行环境
    InitializeMPI(argc, argv);

#if defined(_EnablePETSC_)
    char help[] = "FVM with PETSc \n";
	PetscInitialize(&argc, &argv, NULL, help);
#endif

    // 输出软件相关信息
    SetInfoFile("AriCFD_CFDDEM_Solver.info");
    if (GetMPIRank() == 0)
    {
        PrintTitleInfo(" ARI-CFDDEM ");
        PrintSystemTime();
    }

    if (GetMPIRank() == 0) Print("读取控制参数...");

    // 读取流场计算参数
    Configure::Flow::FlowConfigure flowConfigure;
    flowConfigure.ReadCaseXml(argv[1]);
    flowConfigure.ReadBoundaryXml();

    // 读取颗粒计算参数
    Configure::Particle::ParticleConfigure DEMConfigure;
    DEMConfigure.ReadParameter(argv[1]);
    SetInfoLevel(DEMConfigure.control.outputLevelFile, DEMConfigure.control.outputLevelScreen);
    
    // 打印计算参数
    if(GetMPIRank()==0) flowConfigure.PrintInformation();
    
	SubMesh *localMesh = nullptr;

	std::string preprocessPath = flowConfigure.GetPreprocess().outputPath;
	struct stat buffer;
    bool exist = stat(preprocessPath.c_str(), &buffer) == 0;
    MPIBarrier();

	if (false) // 路径存在
	{
		// 读网格
        if (GetMPIRank() == 0) Print("\n读取网格...");
		localMesh = new SubMesh(preprocessPath + flowConfigure.GetCaseName(),
			flowConfigure.GetAcceleration().multigridSolver.level,
			flowConfigure.GetPreprocess().binaryFileFlag);
	}
	else
	{
		// 设置前处理的线程数
		flowConfigure.SetOpenMPThread();

		// 定义前处理对象
		MeshProcess meshProcess(flowConfigure);
		CheckStatus(1000);

		// 开始前处理
		localMesh = new SubMesh;
		meshProcess.PreProcess( localMesh );

		// 设置前处理的线程数
		flowConfigure.SetOpenMPThread(1);

	}
	CheckStatus(9400);

    // 更新当地边界信息
    flowConfigure.UpdateLocalBoundary(localMesh);

    // 网格参数预计算
    const int nLevel = Min(localMesh->GetTotalLevel(), flowConfigure.GetAcceleration().multigridSolver.level);
    std::vector<std::vector<int>> symmetryPatchID(nLevel);
    for (int level = 0; level < nLevel; ++level)
    {
        for (int patchID = 0; patchID < localMesh->GetMultiGrid(level)->GetBoundarySize(); ++patchID)
            if (flowConfigure.JudgeSymmetryLocal(level, patchID)) symmetryPatchID[level].push_back(patchID);
    }
    const bool &dualMeshFlag = flowConfigure.GetPreprocess().dualMeshFlag;
    localMesh->PreCalculate(dualMeshFlag, symmetryPatchID);
    CheckStatus(9500);

    //计算壁面距离
    const auto &wallDistanceMethod = flowConfigure.GetPreprocess().wallDistanceMethod;
    if (wallDistanceMethod != Turbulence::WallDistance::NONE_WALL_DISTANCE)
    {
        if (GetMPIRank() == 0) Print("\n计算壁面距离...");
        WallDistanceManager wallDistanceManager(localMesh, nLevel, wallDistanceMethod, dualMeshFlag);
        
        std::vector<int> wallPatchIDList;
        for (int patchID = 0; patchID < localMesh->GetBoundarySize(); ++patchID)
            if (flowConfigure.JudgeWallLocal(0, patchID)) wallPatchIDList.push_back(patchID);
        wallDistanceManager.Calculate(wallPatchIDList);
        CheckStatus(9600);
    }

    // 创建流场解算器对象
    CFDDEMSolver *solver = new CFDDEMSolver(localMesh, flowConfigure, DEMConfigure);
    CheckStatus(5000);

    // 流场初始化
    solver->Initialize();
    CheckStatus(5100);

    // 流场迭代求解外循环
    solver->Solve();

    // 删除求解器
    delete solver;
    solver = nullptr;

    // 删除网格
    localMesh->ClearMesh();
    delete localMesh;
    localMesh = nullptr;

    // 计算完成
    if (GetMPIRank() == 0) Print("\n计算结束!");
    CloseInfoFile();

    FinalizeMPI();

    return 0;

}