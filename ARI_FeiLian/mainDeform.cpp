﻿////////////////////////////////////////////////////////////////////////////////
//////--------------------------------ARI-CFD---------------------------------////
//////------------------------中国航空工业空气动力研究院------------------------////
//////////////////////////////////////////////////////////////////////////////////
////
////! @file mainDeform.cpp
////! <AUTHOR>
////! @brief 网格变形，并输出到cgns
////! @date 2023-04-10
////


#include "meshProcess/meshConverter/CgnsMesh.h"
#include "feilian-specialmodule/meshDeform/MeshDeform.h"
#include "meshProcess/meshConverter/MeshConvertManager.h"
#include "basic/configure/ConfigureMacro.h"
#include "basic/configure/ConfigureMacro.hxx"
#include "basic/mesh/MeshSupport.h" 
#include "basic/configure/PropertyTree.h"


#include <string>
#include <iostream>


#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

int CopyFile(const char* SourceFile,const char* Newfile)
{

    std::ifstream in;
    std::ofstream out;
    try{
        in.open(SourceFile,std::ios::binary);
        if(in.fail()){
            std::cout<<"Fail to open the source file;"<<std::endl;
            in.close();
            out.close();
            return 0;
        }
        out.open(Newfile,std::ios::binary);
        if(out.fail()){
            std::cout<<"Fail to open the new file;"<<std::endl;
                        in.close();
                        out.close();
                        return 0;
        }
        out<<in.rdbuf();
        out.close();
        in.close();
        return 1;
    }
    catch(std::exception &e){
    }

    return 0;
}


void cgns2cgns(const std::string& input_fullfilename, std::vector<Node> &v_globalNode)
{
    int fileID,baseID=1,cellDim;
    if (cg_open(input_fullfilename.c_str(), CG_MODE_MODIFY, &fileID)) cg_error_exit();
    //确定网格维度
    char basename [33];
    int physDim;
    if (cg_base_read(fileID, baseID, basename, &cellDim, &physDim)) cg_error_exit();
    //读取点数
    cgsize_t isize[3][1];
    char zonename[33];
    int zoneID=1;
    if (cg_zone_read(fileID, baseID, zoneID, zonename, isize[0])) cg_error_exit();
    int nodeSize = (int)isize[0][0];

    
    //if( nodeSize != v_globalNode.size()) Print("\t错误 ....\n");
    
    
    //修改坐标
    cgsize_t irmin = 1, irmax = nodeSize;
    Scalar *coor = new Scalar[nodeSize];
    Scalar *coor_x = new Scalar[nodeSize];
    Scalar *coor_y = new Scalar[nodeSize];
    Scalar *coor_z = new Scalar[nodeSize];
    char coordname_x[33],coordname_y[33],coordname_z[33];
    DataType_t dataType;
    int coodID;
    //X坐标
    if (cg_coord_info(fileID, baseID, zoneID, 1, &dataType, coordname_x)) cg_error_exit();
    if (cg_coord_read(fileID, baseID, zoneID, coordname_x, dataType, &irmin, &irmax, coor)) cg_error_exit();

//  for (int i = 0; i < nodeSize; i++) coor[i] =v_globalNode[i].X();
    
    for (int i = 0; i < v_globalNode.size(); i++)
    {
            coor_x[i] = v_globalNode[i].X();
            coor_y[i] = v_globalNode[i].Y();
           if (physDim == 3) {coor_z[i] = v_globalNode[i].Z(); }
    } 
    
    if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_x, coor_x,&coodID)) cg_error_exit();
    //Y坐标
    if (cg_coord_info(fileID, baseID, zoneID, 2, &dataType, coordname_y)) cg_error_exit();
    if (cg_coord_read(fileID, baseID, zoneID, coordname_y, dataType, &irmin, &irmax, coor)) cg_error_exit();

    //for (int i = 0; i < nodeSize; i++) coor[i] = v_globalNode[i].Y();
        if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_y, coor_y,&coodID)) cg_error_exit();
    //Z坐标
    if (physDim == 3)
    {
        if (cg_coord_info(fileID, baseID, zoneID, 3, &dataType, coordname_z)) cg_error_exit();
        if (cg_coord_read(fileID, baseID, zoneID, coordname_z, dataType, &irmin, &irmax, coor)) cg_error_exit();
            if (cg_coord_write(fileID, baseID, zoneID, dataType,coordname_z, coor_z,&coodID)) cg_error_exit();          
    }
    
    if (cg_close(fileID)) cg_error_exit();
}


void ReadBoundaryXml(std::vector<std::string> &globalBoundaryName,std::vector<Boundary::Type> &globalBoundaryType)
{
     PropertyTree boundaryTree("boundary.xml");    
     
     // 边界信息节点
     std::string nodeName = "boundaryConditions";
     PropertyTree boundaryNode(boundaryTree, nodeName);
     
     // 读取边界数量
     int boundaryNumber = 0;
     ReadNodeValue(boundaryNode, std::string("number"), boundaryNumber);

     // 读取边界信息
     for (int i = 0; i < boundaryNumber; i++)
     {
         std::string stringTemp;
         PropertyTree boundaryNodeTemp(boundaryNode, "boundary" + ToString(i));

         ReadNodeValue(boundaryNodeTemp, std::string("name"), stringTemp, false);
         globalBoundaryName.push_back(stringTemp);

         ReadNodeValue(boundaryNodeTemp, std::string("type"), stringTemp);
         globalBoundaryName.push_back(stringTemp);
         globalBoundaryType.push_back(Configure::boundaryTypeMap.find(stringTemp)->second);
      }
}

int main(int argc,char **argv)
{
     SetInfoFile("AriCFD_MeshDeform.info"); 
     omp_set_num_threads(1);
     
#if defined(_BaseParallelMPI_)
    boost::mpi::environment env(argc, argv);
#endif     

    int nPart = GetMPISize();

    // 输出软件相关信息及读入网格、查找物面点
    std::vector<Node> v_wallNode;
    std::vector<Node> v_subProNode;
    bool meshDim_2D = true;

    const char *oldFile = argv[1];
    const char *newFile = argv[2];

    Print("\n GetMPIRank() = " + std::to_string((long long int)nPart));

    Print(std::string(70, '-'));
    Print(std::string(28, '-') + "(ARI_FeiLian)" + std::string(28, '-'));
    Print(std::string(70, '-'));
    PrintSystemTime();

    // 由globalMesh读入全局网格
    const Preprocessor::MeshType meshType = Preprocessor::MeshType::CGNS;
    SubMesh *globalMesh = new SubMesh();
    Configure::MeshTransformStruct meshTransform;

    double sca =1;
    meshTransform.scale.SetX(sca);
    meshTransform.scale.SetY(sca);
    meshTransform.scale.SetZ(sca);

    // 构建拓扑关系
    Mesh::MeshDim meshDim = Mesh::MeshDim::md3D;
    MeshConvertManager meshConvertManager(oldFile, meshType, meshDim, globalMesh, meshTransform);
    meshConvertManager.ReadMesh(true);
    meshConvertManager.BuildTopology();
    meshConvertManager.BuildBoundaryTopology();

    if( GetMPIRank() == 0) Print("\n 输出.....测试  ");

    // 物面边界判定以及查找物面点
    std::vector<std::vector<int>> v_wallNodeID_temp;
    std::vector<int> v_wallBoundaryID;

    std::vector<std::string> globalBoundaryName;    ///< 全局边界名称容器
    std::vector<Boundary::Type> globalBoundaryType; ///< 全局边界类型容器

    ReadBoundaryXml(globalBoundaryName, globalBoundaryType);

    int n = globalMesh->GetBoundarySize();
    for (int i = 0; i < n; i++)
    {
        if (globalBoundaryType[i] > Boundary::Type::WALL)
        {
            v_wallBoundaryID.push_back(i);
        }
    }

    int n_wall = v_wallBoundaryID.size();
    v_wallNodeID_temp.resize(n_wall);

    std::vector<int> v_wallNodeID;

    for (int i = 0; i < n_wall; i++)
    {
        int id = v_wallBoundaryID[i];
        globalMesh->PopulateBoundaryNodeID(id, v_wallNodeID_temp[i]);
        for (int j = 0; j < v_wallNodeID_temp[i].size(); j++)
        {
            v_wallNodeID.push_back(v_wallNodeID_temp[i][j]);
        }
    }

    v_wallNodeID = GetNonRepeatedList(v_wallNodeID);

    for (int i = 0; i < v_wallNodeID.size(); i++)
    {
        v_wallNode.push_back(globalMesh->GetNode(v_wallNodeID[i]));
    }

      if( GetMPIRank() == 0) Print("\n v_wallNode.size() = " + std::to_string((long long int)v_wallNode.size()));
    std::vector<Node> v_wallDeformNode;
    v_wallDeformNode.resize(v_wallNode.size());

    //判断物面点是否输出
    std::string name = "wall_deform.dat";
    std::fstream file1;
    file1.open(name,std::fstream::in);
    if(file1.is_open()){
        for (int i = 0; i < v_wallNode.size(); i++)
        {
            double temp[3];
            file1>>temp[0]>>temp[1]>>temp[2];
            v_wallDeformNode[i].SetX(temp[0]);
            v_wallDeformNode[i].SetY(temp[1]);
            v_wallDeformNode[i].SetZ(temp[2]);

        }
    }else{
        std::fstream file;
        file.open("wall.dat",std::fstream::out);
        for (int i = 0; i < v_wallNode.size(); i++)
        {     
            file<<v_wallNode[i].X()<<"  "<<v_wallNode[i].Y()<<"  "<<v_wallNode[i].Z()<<std::endl;
        }
        exit(1);
    }

    // 读取全局网格点
    std::vector<Node> v_globalNode;
    int n_tem = globalMesh->GetNodeNumber();
    v_globalNode.resize(n_tem);

    for (int i = 0; i < n_tem; i++)
    {
        v_globalNode[i] = globalMesh->GetNode(i);
    }
    if( GetMPIRank() == 0)    Print("\n v_globalNode.size() = " + std::to_string((long long int)v_globalNode.size()));
#if defined(_BaseParallelMPI_)
    boost::mpi::communicator mpiWorld;
    mpiWorld.barrier();
#endif

    // 径向基函数变形
    Scalar R = 2386;
    MeshDeform meshDeform(v_wallNode, v_wallDeformNode, R);
    meshDeform.Process();
    
    if (GetMPIRank() == 0)
    {
        meshDeform.RebuildNode(v_globalNode);
        Print("\n 计算结束   ");
        CopyFile(oldFile, newFile);
        cgns2cgns(newFile, v_globalNode);
    }

    // 计算完成
    CloseInfoFile();

    FinalizeMPI();

    return 0;
}
