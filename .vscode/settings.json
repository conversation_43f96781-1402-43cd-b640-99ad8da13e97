{"files.associations": {"iosfwd": "cpp", "sstream": "cpp", "algorithm": "cpp", "memory": "cpp", "numeric": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory_resource": "cpp", "optional": "cpp", "ratio": "cpp", "set": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "*.ipp": "cpp", "strstream": "cpp", "codecvt": "cpp", "complex": "cpp", "csignal": "cpp", "forward_list": "cpp", "unordered_set": "cpp", "random": "cpp", "hash_map": "cpp", "slist": "cpp", "future": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "valarray": "cpp", "filesystem": "cpp", "variant": "cpp", "regex": "cpp", "rope": "cpp"}}