{
    "tasks": [
        {
            "label": "cmake",
            "options": {
                "cwd":"${workspaceFolder}/build"
            },
            "type":"shell",
            "command":"cmake",
            "args": [
                ".."
            ]
        },

        {
            "label": "make",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "type":"shell",
            "command":"make -j20", // >log.make 2>&1",
            "dependsOn":[
                "cmake"
            ],
        },

        {
            "label": "makeInstall",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "type":"shell",
            "command":"make install", // >log.make 2>&1",
            "dependsOn":[
                "make"
            ],
        },

        {
            "label": "testClean",
            "options": {
                "cwd": "${workspaceFolder}/testCases"
            },
            "type":"shell",
            "command":"make allclean",
            "dependsOn":[
                "makeInstall"
            ],
        },
        
        {
            "label": "test",
            "options": {
                "cwd": "${workspaceFolder}/testCases"
            },
            "type":"shell",
            "command":"make test",
            "dependsOn":[
                "testClean"
            ],
        },
        
        {
            "label": "testSummary",
            "options": {
                "cwd": "${workspaceFolder}/testCases"
            },
            "type":"shell",
            "command":"make summary",
            "dependsOn":[
                "test"
            ],
        },

        {
            "label": "onlyMake",
            "options": {
                "cwd": "${workspaceFolder}/build"
            },
            "type":"shell",
            "command":"make -j40", // >log.make 2>&1",
            "dependsOn":[
                "cmake"
            ],
        }
        
    ],
    "version": "2.0.0"
}