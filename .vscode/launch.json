{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "g++",
            "type": "cppdbg",
            "request": "launch",
            //"program": "${workspaceFolder}/bin/mainFlowSolver",
            "args": [
            //     "${workspaceFolder}/testCases/TestCases_1/Steady_Turbulent-P-L1/WORK/Steady_Turbulent-P-L1_DUAL1_SST.xml"
            ],
            "stopAtEntry": false,
            //"cwd": "${workspaceFolder}/testCases/TestCases_1/Steady_Turbulent-P-L1",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "miDebuggerPath": "/usr/bin/gdb",
            "setupCommands": [
                {
                    "description": "为 gdb 启用整齐打印",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "testSummary"
            //"preLaunchTask": "makeInstall"
            //"preLaunchTask": "make"
        },

        //启动并行计算
        {
            "name": "mpi run mainAeroStatic",
            "type": "cppdbg",
            "request": "launch",
            "program": "/home/<USER>/intel/oneapi/mpi/2021.1.1/bin/mpiexec",
            "args": ["-np","8","${workspaceFolder}/bin/mainAeroStatic","default.xml"],
            "stopAtEntry": false,
            "cwd": "/home/<USER>/guochengpeng/wing_with_control/wing_with_control_1",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "miDebuggerPath": "/usr/bin/gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                },
                {
                    "description": "Increase max number of elements",
                    "text": "set print elements 0",
                    "ignoreFailures": false
                }
            ],
        //   "preLaunchTask": "onlyMake"
        },

        //并行调试计算程序
        {
            "name": "attachProcess",
            "type": "cppdbg",
            "request": "attach",
            "processId":"${command:pickProcess}",
            "program": "${workspaceFolder}/bin/mainAeroStatic",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
        },
    ],

    "compounds": [
        {
            "name": "debug mpi mainFlowSolver",
            "configurations": ["mpi run mainAeroStatic","attachProcess"],
            "order": "sequential",
            // "stopAll": true,
        }
    ]
}