﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file NacelleExhaust.h
//! <AUTHOR>
//! @brief 发动机短舱出口边界（计算域的入口边界）
//! @date  2023-2-4
//
//------------------------------修改日志----------------------------------------
//
// 2023-2-4 李艳亮 
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_NacelleExhaust_
#define _sourceFlow_boundaryCondition_NacelleExhaust_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 发动机短舱出口边界条件类
 * 
 */
class NacelleExhaust : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] totalPressure 总压
     * @param[in] totalTemperature 总温
     */
    NacelleExhaust(const int &boundaryPatchID, Package::FlowPackage &data,
        const Scalar &totalPressure, const Scalar &totalTemperature);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 边界条件更新
     * 
     */
    void UpdateBoundaryCondition();

    /**
    * @brief 计算出口流量
    *
    */
    Scalar CalculateMassFlow();

private:
    Scalar pTotal; ///< 总压
    Scalar TTotal; ///< 总温
    const Scalar &R; ///< 气体常数
    const Scalar gammag; ///< (gamma -1)/gamma
};

} // namespace Flow
} // namespace Boundary

#endif
