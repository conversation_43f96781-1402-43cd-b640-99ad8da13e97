﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file InflowTotalCondition.h
//! <AUTHOR>
//! @brief InflowTotalCondition为总条件入口类
//! @date  2021-3-30
//
//------------------------------修改日志----------------------------------------
//
// 2021-03-30 李艳亮
// 说明：添加注释并规范化
// 
//------------------------------------------------------------------------------
#ifndef  _sourceFlow_boundaryCondition_InflowTotalCondition_
#define  _sourceFlow_boundaryCondition_InflowTotalCondition_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 总条件入口边界条件类
 * 
 */
class InflowTotalCondition : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，创建远场边界条件对象
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] mach 马赫数
     * @param[in] totalpressure 总压
     * @param[in] totalTemperature 总温
     * @param[in] alpha_ 迎角（度）
     * @param[in] beta_ 侧滑角（度）
     * @param[in] precondition_ 低速预处理对象
     */
    InflowTotalCondition(const int &boundaryPatchID,
                         Package::FlowPackage &data,
                         const Scalar &mach,
                         const Scalar &totalpressure,
                         const Scalar &totalTemperature,
                         const Scalar &alpha_,
                         const Scalar &beta_,
                         Flux::Flow::Precondition::Precondition *precondition_);

	/**
	* @brief 初始化
	*
	*/
	void Initialize();

	/**
	* @brief 远场边界条件具体实现
	*
	*/
	void UpdateBoundaryCondition();

private:
	void UpdateBoundaryConditionNonPrecondition();
	void UpdateBoundaryConditionPrecondition();

private:
	Flux::Flow::Precondition::Precondition *precondition; ///< 预处理指针

	Scalar pTotal; ///< 外场总压
	Scalar TTotal; ///< 外场总温
	Vector velocity_inf; ///< 外场速度
	Scalar soundSpeed_inf; ///< 外场音速
	Scalar turbulentViscosityRatio; ///< 自由流湍流与层流的粘性比

	const Scalar &gamma; ///< 气体比热容比\gamma
	const Scalar &gamma1; ///< gamma - 1
	Scalar gammaDivGamma1; ///< gamma / (gamma - 1)
    
};
} // namespace Flow
} // namespace Boundary

#endif
