﻿#include "sourceFlow/boundaryCondition/WallRiblets.h"
// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{
// 绝热壁面边界条件
WallRiblets::WallRiblets(const int &boundaryPatchID, Package::FlowPackage &data, const Scalar &s_)
	:
	Wall(boundaryPatchID, data), s(s_)
{
	initialized = false;
	if (muLaminar == nullptr) FatalError("WallRiblets::WallRiblets： 该边界仅支持粘性流动...");
	if (flowPackage.GetFlowConfigure().GetAcceleration().multigridSolver.level != 1) FatalError("WallRiblets::WallRiblets： 该边界不支持多重网格...");
	if (flowPackage.GetFlowConfigure().GetControl().initialization.type != Initialization::Type::RESTART) FatalError("WallRiblets::WallRiblets： 该边界仅支持续算...");
}

void WallRiblets::Initialize()
{	
	const Scalar uInf = flowPackage.GetFlowConfigure().GetFlowReference().velocity.Mag();
	const Scalar muInf = flowPackage.GetFlowConfigure().GetFlowReference().muLaminar;
	const Scalar rhoInf = flowPackage.GetFlowConfigure().GetFlowReference().density;

	const Scalar oneThird = 1.0 / 3.0;
	Vector dU, tauWall;
	Scalar distance, muWall, uTau, sPlus, DR, ux, uy, uz;

	UWall.resize(boundaryFaceSize);

	const auto *muTurbulent = flowPackage.GetField().muTurbulent;
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighborID = mesh->GetFace(faceID).GetNeighborID();

		const Face &face = mesh->GetFace(faceID);
		const Vector &faceNormal = face.GetNormal();

		if (nodeCenter)
		{
			const int &innerElementID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, j);
			distance = (mesh->GetElement(innerElementID).GetCenter() - face.GetCenter()).Mag();
			dU = U.GetValue(innerElementID) - 0.5 * (U.GetValue(ownerID) + U.GetValue(neighborID));
			muWall = 0.5 * (muLaminar->GetValue(ownerID) + muLaminar->GetValue(innerElementID));
			if (muTurbulent != nullptr) muWall += 0.5 * (muTurbulent->GetValue(ownerID) + muTurbulent->GetValue(innerElementID));

		}
		else
		{
			distance = (mesh->GetElement(ownerID).GetCenter() - face.GetCenter()).Mag();
			dU = U.GetValue(ownerID) - 0.5 * (U.GetValue(ownerID) + U.GetValue(neighborID));
			muWall = 0.5 * (muLaminar->GetValue(ownerID) + muLaminar->GetValue(neighborID));
			if (muTurbulent != nullptr) muWall += 0.5 * (muTurbulent->GetValue(ownerID) + muTurbulent->GetValue(neighborID));

		}

		tauWall = -muWall * (dU + oneThird * (dU & faceNormal) * faceNormal) / (distance + SMALL);
		uTau = sqrt(tauWall.Mag() / rhoInf);
		sPlus = (rhoInf * uTau * s) / muInf;
		DR = 0.0564 * sPlus*sPlus - 1.3073 * sPlus + 0.08;

		ux = uInf * (1 - sqrt(1 / (1 + DR / 100))) * (tauWall.X() / tauWall.Mag());
		uy = uInf * (1 - sqrt(1 / (1 + DR / 100))) * (tauWall.Y() / tauWall.Mag());
		uz = uInf * (1 - sqrt(1 / (1 + DR / 100))) * (tauWall.Z() / tauWall.Mag());

		UWall[j] = Vector(ux, uy, uz);
		
	}

	this->BoundFromElement(rho);
	this->BoundFromElement(p);
	this->BoundFromElement(T);
	this->SetVelocityAndMu();
}

void WallRiblets::UpdateBoundaryCondition()
{
	this->BoundFromElement(rho);
	this->BoundFromElement(p);
	this->BoundFromElement(T);

	// 更新速度

	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		if (nodeCenter)
		{
			U.SetValue(ownerID, UWall[j]);
			U.SetValue(neighID, UWall[j]);
		}
		else
		{
			U.SetValue(neighID, 2.0 * UWall[j] - U.GetValue(ownerID));
		}
	}

	return;
}

void WallRiblets::AddConvectiveResidual()
{
    this->AddMRFConvectiveResidualAverage();
}

void WallRiblets::AddDiffusiveResidual()
{
	if (!nodeCenter) this->AddDiffusiveResidualCellCenter();

	return;
}

void WallRiblets::UpdateBoundaryResidual()
{
	if (!nodeCenter) return;

	if (muLaminar != nullptr) this->UpdateBoundaryResidualStatic();
	else                      this->UpdateBoundaryResidualSlipping();
}

}// namespace Flow
}// namespace Boundary
