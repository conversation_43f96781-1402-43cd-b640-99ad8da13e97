﻿#include "sourceFlow/boundaryCondition/NacelleExhaust.h"

namespace Boundary
{
namespace Flow
{

NacelleExhaust::NacelleExhaust(const int &boundaryPatchID, Package::FlowPackage &data,
    const Scalar &totalPressure, const Scalar &totalTemperature)
    :
    ExternalBoundary(boundaryPatchID, data),
    pTotal(totalPressure),
    TTotal(totalTemperature),
    R(data.GetMaterialNumber().R),
    gammag(data.GetMaterialNumber().gamma1 / data.GetMaterialNumber().gamma)    
{
}

void NacelleExhaust::Initialize()
{
    this->UpdateBoundaryCondition();
}

void NacelleExhaust::UpdateBoundaryCondition()
{
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();    

        Scalar pb = p.GetValue(ownerID); // 压强外插得到
        if (nodeCenter)  // 对偶网格边界压强取内点值
        {
            const int &innerElementID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, j);
            pb = p.GetValue(innerElementID);
        }

        const Scalar temp = pow(pTotal / pb, gammag);
        const Scalar Tb = TTotal / temp;
        const Scalar rhob = pb / Tb /R;
        const Scalar UbMag = sqrt(2.0 * R * (TTotal - Tb) / gammag);
        const Vector Ub = (-UbMag) * mesh->GetFace(faceID).GetNormal(); //速度方向朝向内

        if (nodeCenter)
        {
            rho.SetValue(ownerID, rhob);
            U.SetValue(ownerID, Ub);
            p.SetValue(ownerID, pb);
            T.SetValue(ownerID, Tb);
            
            rho.SetValue(neighID, rhob);
            U.SetValue(neighID, Ub);
            p.SetValue(neighID, pb);
            T.SetValue(neighID, Tb);
        }
        else
        {
            rho.SetValue(neighID, 2.0 * rhob - rho.GetValue(ownerID));
            U.SetValue(neighID, 2.0 * Ub - U.GetValue(ownerID));
            p.SetValue(neighID, pb);
            T.SetValue(neighID, 2.0 * Tb - T.GetValue(ownerID));
        }
    }
}

Scalar NacelleExhaust::CalculateMassFlow()
{
    Scalar massFlow = Scalar0;

    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();

        const Scalar &pb = p.GetValue(ownerID); // 压强外插得到        
        const Scalar temp = pow(pTotal / pb, gammag);
        const Scalar Tb = TTotal / temp;
        const Scalar UbMag = sqrt(2.0 * R * (TTotal - Tb) / gammag);

        massFlow += UbMag * mesh->GetFace(faceID).GetArea();
    }

    return massFlow;
}

}// namespace Flow
}// namespace Boundary
