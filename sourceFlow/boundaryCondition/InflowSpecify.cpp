﻿#include "sourceFlow/boundaryCondition/InflowSpecify.h"

namespace Boundary
{
namespace Flow
{

InflowSpecify::InflowSpecify(const int &boundaryPatchID, Package::FlowPackage &data, 
    const Scalar &density,    const Vector &velocity,    const Scalar &pressure)
    :
    FlowBoundary(boundaryPatchID, data)
{       
    this->rhoSpecified = density;
    this->velocitySpecified = velocity;
    this->pressureSpecified = pressure;
    this->temperatureSpecified = material.GetTemperature(pressureSpecified, rhoSpecified);

    //计算远湍流粘性系数
    turbulentViscosityRatio =  data.GetFlowConfigure().GetFlowReference().turbulentViscosityRatio;
}

void InflowSpecify::Initialize()
{
    this->UpdateBoundaryCondition();
    
    return;
}

void InflowSpecify::UpdateBoundaryCondition()
{
    // 指定原始变量(rho, u, v, w, p)边界上所有面心的密度、速度、压强直接取指定的对应值
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        if (!nodeCenter)
        {
            rho.SetValue(ownerID, rhoSpecified);
            U.SetValue(ownerID, velocitySpecified);
            p.SetValue(ownerID, pressureSpecified);
            T.SetValue(ownerID, temperatureSpecified);
            
            rho.SetValue(neighID, rhoSpecified);
            U.SetValue(neighID, velocitySpecified);
            p.SetValue(neighID, pressureSpecified);
            T.SetValue(neighID, temperatureSpecified);
        }
        else
        {
            rho.SetValue(neighID, 2 * rhoSpecified - rho.GetValue(ownerID));
            U.SetValue(neighID, 2 * velocitySpecified - U.GetValue(ownerID));
            p.SetValue(neighID, 2 * pressureSpecified - p.GetValue(ownerID));
            T.SetValue(neighID, 2 * temperatureSpecified - T.GetValue(ownerID));
        }
    }

    return;
}

void InflowSpecify::AddDiffusiveResidual()
{
    if(!nodeCenter) this->AddDiffusiveResidualCellCenter();

    return;
}

void InflowSpecify::AddConvectiveResidual()
{
	if (!nodeCenter)
	{
		if (jacobian) this->AddConvectiveResidualReflected();
		else          this->AddConvectiveResidualAverage();
	}
    this->AddMRFConvectiveResidualAverage();
    
    return;
}

void InflowSpecify::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;

    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();

        residualMass.SetValue(ownerID, Scalar0);
        residualMomentum.SetValue(ownerID, Vector0);
        residualEnergy.SetValue(ownerID, Scalar0);
    }
}

}// namespace Flow
}// namespace Boundary
