﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FarField.h
//! <AUTHOR> 尹强
//! @brief FarField为远场边界条件类,具体类
//!           含以下四类：
//!           亚音速入流：采用Riemann 无反射边界条件
//!           亚音速出流：采用Riemann 无反射边界条件
//!           超音速入流：取自由来流值
//!           超音速出流：取相邻内场值
//! @date  2021-3-30
//
//------------------------------修改日志----------------------------------------
//
// 2021-03-30 尹强 
// 说明：添加注释并规范化
// 
//------------------------------------------------------------------------------
#ifndef  _sourceFlow_boundaryCondition_FarField_
#define  _sourceFlow_boundaryCondition_FarField_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 远场边界条件类
 * 
 */
class FarField : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，创建远场边界条件对象
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] density 远场密度
     * @param[in] Velocity 远场速度矢量
     * @param[in] pressure 远场压强
     * @param[in] precondition_ 低速预处理对象
     */
    FarField(const int &boundaryPatchID,
             Package::FlowPackage &data,
             const Scalar &density,
             const Vector &velocity,
             const Scalar &pressure,
             Flux::Flow::Precondition::Precondition *precondition_);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 远场边界条件具体实现
     * 
     */
    void UpdateBoundaryCondition();

private:
    /**
     * @brief 更新边界不考虑低速预处理
     * 
     */
    void UpdateBoundaryConditionNonPrecondition0();
    
    /**
     * @brief 更新边界不考虑低速预处理
     * 
     */
    void UpdateBoundaryConditionNonPrecondition();

    /**
     * @brief 更新边界考虑低速预处理
     * 
     */
    void UpdateBoundaryConditionPrecondition();

protected:
    Flux::Flow::Precondition::Precondition *precondition; ///< 预处理指针

    Scalar rho_inf; ///< 外场密度
    Vector velocity_inf; ///< 外场速度
    Scalar Pressure_inf; ///< 外场压强
    Scalar soundSpeed_inf; ///< 外场音速
    Scalar turbulentViscosityRatio; ///< 自由流湍流与层流的粘性比

    const Scalar &gamma; ///< 气体比热容比\gamma
    const Scalar &gamma1; ///< \gamma - 1
};
} // namespace Flow
} // namespace Boundary

#endif
