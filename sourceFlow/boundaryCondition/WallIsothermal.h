﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallIsothermal.h
//! <AUTHOR>
//! @brief WallIsothermal为等温壁面边界条件类
//! @date  2022-4-7
//
//------------------------------修改日志----------------------------------------
//
// 2022-4-7 乔龙
// 说明：建立
// 
//------------------------------------------------------------------------------
#ifndef _sourceFlow_boundaryCondition_WallIsothermal_
#define _sourceFlow_boundaryCondition_WallIsothermal_

#include "sourceFlow/boundaryCondition/Wall.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 等温壁面边界条件类
 * 
 */
class WallIsothermal :public Wall
{
public:
    /**
     * @brief 构造函数，初始化等温壁面边界条件
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] wallTemperature_ 壁面温度
     */
    WallIsothermal(const int &boundaryPatchID, Package::FlowPackage &data, const Scalar &wallTemperature_);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 等温壁面边界条件具体实现函数
     * 
     */
    void UpdateBoundaryCondition();    
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();
    
private:
    const Scalar wallTemperature; ///< 等温壁面温度值
    const Scalar &R; ///< 理想气体常数
};

} // namespace Flow
} // namespace Boundary


#endif
