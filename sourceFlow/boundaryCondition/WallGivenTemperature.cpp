﻿//------------------------------修改日志----------------------------------------
//
// 2024-3-21 YQ
// 说明：建立
// 
//------------------------------------------------------------------------------
#include <vector>
#include <iostream>
#include <fstream>
#include <string>

#include "Eigen/Dense"

#include "sourceFlow/boundaryCondition/WallGivenTemperature.h"

#ifdef RBF_YQ
	#include "basic/common/RBF_interpolation.hpp"
#else
	#include "basic/common/InterpolationTools.h"
#endif

// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{
	
WallGivenTemperature::WallGivenTemperature(const int& boundaryPatchID, Package::FlowPackage& data, const std::string& fileName_)
	: Wall(boundaryPatchID, data), fileName(fileName_), R(flowPackage.GetMaterialNumber().R)
{
}

void WallGivenTemperature::Initialize()
{
	// 初始化给定分布热通量壁面边界条件
	this->UpdateBoundaryCondition();
}

void WallGivenTemperature::UpdateBoundaryCondition()
{
	this->ReadFile();
	this->InterpolateTemperature();

	this->BoundFromElement(rho);
	this->SetVelocityAndMu();

	// 遍历等温壁面边界上的每一个面心，更新速度矢量
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		Scalar wallTemperature = TInterp[j];
		const Scalar soundSpeedTemp = material.GetSoundSpeed(wallTemperature);

		if (nodeCenter)
		{
			T.SetValue(ownerID, wallTemperature);
			T.SetValue(neighID, wallTemperature);

			p.SetValue(ownerID, R * rho.GetValue(ownerID) * wallTemperature);
			p.SetValue(neighID, p.GetValue(ownerID));
		}
		else
		{
			T.SetValue(neighID, 2.0 * wallTemperature - T.GetValue(ownerID));
			p.SetValue(neighID, 2 * R * rho.GetValue(ownerID) * wallTemperature - p.GetValue(ownerID));
		}
	}

	return;
}

void WallGivenTemperature::UpdateBoundaryResidual()
{
	if (!nodeCenter) return;

	if (muLaminar != nullptr) this->UpdateBoundaryResidualStatic();
	else                      this->UpdateBoundaryResidualSlipping();
}

void WallGivenTemperature::InterpolateTemperature()
{
#ifdef RBF_YQ
	int DataNum = coorSrc.size();
	MatrixXd mSrcCoor(DataNum, 3);
	MatrixXd mSrcT(DataNum, 1);
	for (int idata = 0; idata < DataNum; idata++)
	{
		mSrcCoor.row(idata) << coorSrc[idata][0], coorSrc[idata][1], coorSrc[idata][2];
		mSrcT.row(idata) << TSrc[idata];
	}
	//cout << "mSrcCoor" << endl<< mSrcCoor << endl;
	//cout << "mSrcT" << endl << mSrcT << endl;
	MatrixXd rbfcoeff;
	rbfcoeff = rbfcreate(mSrcCoor, mSrcT, rbfphi_multiquadrics, 0.4444, 0);
	MatrixXd mInterpCoor(boundaryFaceSize, 3);
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		const int faceID = mesh->GetBoundaryFaceID(boundaryPatchID, j);
		const Vector faceCentCoor = mesh->GetFace(faceID).GetCenter();
		mInterpCoor.row(j) << faceCentCoor.X(), faceCentCoor.Y(), faceCentCoor.Z();
	}
	//cout << "mInterpCoor" << endl << mInterpCoor << endl;
	MatrixXd mInterpT;
	mInterpT = rbfinterp(mSrcCoor, rbfcoeff, mInterpCoor, rbfphi_multiquadrics, 0.4444);
	//cout << "mInterpT" << endl << mInterpT << endl;
	TInterp.resize(boundaryFaceSize, 0);
	for (int j = 0; j < boundaryFaceSize; ++j)
		TInterp[j] = mInterpT[j];
#else
	// TODO:没考虑并行和插值选点，并行计算太耗时
	int DataNum = coorSrc.size();
	std::vector<Vector> rPos(DataNum, Vector0);
	TInterp.resize(boundaryFaceSize, 0);
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		const int faceID = mesh->GetBoundaryFaceID(boundaryPatchID, j);
		const Vector faceCentCoor = mesh->GetFace(faceID).GetCenter();
		for (int idata = 0; idata < DataNum; idata++)
		{
			rPos[idata] = Vector(coorSrc[idata][0], coorSrc[idata][1], coorSrc[idata][2]) - faceCentCoor;
		}
		TInterp[j] = RBFInterpolation<Scalar>(rPos, TSrc);
	}
#endif
}

void WallGivenTemperature::AddConvectiveResidual()
{
	if (implicitFlag)
	{
		if (!muLaminar) this->AddConvectiveResidualSlip();
		else if (!nodeCenter) this->AddConvectiveResidualNoSlip();
	}
	else
	{
		this->AddConvectiveResidualAverage();
	}

	this->AddMRFConvectiveResidualAverage();
}

void WallGivenTemperature::AddDiffusiveResidual()
{
	if (!nodeCenter)
	{
		this->AddDiffusiveResidualCellCenter();
		return;
	}

	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
		const Face &face = mesh->GetFace(faceID);
		const int &ownerID = face.GetOwnerID();
		const int &innerID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, index);
		const Scalar metric = face.GetArea() / distance[faceID];

		// 计算面通量
		const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(innerID) + flowPackage.CalculateKappa(ownerID));
		const Scalar dT = T.GetValue(ownerID) - T.GetValue(innerID);
		const Scalar energyFlux = kappaFace * dT * metric;
		residualEnergy.AddValue(ownerID, -energyFlux);
	}

	return;
}

void WallGivenTemperature::ReadFile()
{
	//假定文件格式如下
	// NP   #热通量点数
	// x1 y1 z1 T1 # 第一点热通量
	// x2 y2 z2 T2 # 第2点热通量
	// ......
	// xNP yNP zNP TNP # 第NP点热通量

	std::ifstream fin(fileName);

	if (!fin.is_open())
		FatalError("WallGivenTemperature::ReadFile: " + fileName + " in not opened...");

	//string temp;

	int Num;
	fin >> Num;
	//		cout << Num << endl;
	coorSrc.resize(Num, std::vector<double>{0., 0., 0.});
	TSrc.resize(Num, 0.);

	for (int irec = 0; irec < Num; irec++)
	{
		fin >> coorSrc[irec][0] >> coorSrc[irec][1] >> coorSrc[irec][2] >> TSrc[irec];
	}
}

}// namespace Flow
}// namespace Boundary