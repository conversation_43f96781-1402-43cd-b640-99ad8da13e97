﻿#include "sourceFlow/boundaryCondition/WallSlipping.h"

namespace Boundary
{
namespace Flow
{

WallSlipping::WallSlipping(const int &boundaryPatchID, Package::FlowPackage &data)
    :
    Wall(boundaryPatchID, data)
{
}

void WallSlipping::Initialize()
{
    this->UpdateBoundaryCondition();
}

void WallSlipping::UpdateBoundaryCondition()
{
    this->BoundFromElement(rho);
    this->BoundFromElement(p);
    this->BoundFromElement(T);

    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &UOwner = U.GetValue(ownerID);
        const Vector faceU = UOwner - (UOwner & faceNormal) * faceNormal;
        U.SetValue(neighID, 2.0 * faceU - UOwner);
    }

    return;
}

void WallSlipping::AddDiffusiveResidual()
{
    if (!nodeCenter) this->AddDiffusiveResidualCellCenter();
    else             this->AddDiffusiveResidualNodeCenter();
}

void WallSlipping::AddConvectiveResidual()
{
    if (implicitFlag) this->AddConvectiveResidualReflected();
    else              this->AddConvectiveResidualAverage();
    
    this->AddMRFConvectiveResidualAverage();
}

void WallSlipping::UpdateBoundaryResidual()
{
    return;
}

}// namespace Flow
}// namespace Boundary
