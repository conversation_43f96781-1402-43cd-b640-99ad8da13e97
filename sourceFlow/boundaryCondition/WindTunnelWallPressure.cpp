﻿#include "sourceFlow/boundaryCondition/WindTunnelWallPressure.h"

namespace Boundary
{
namespace Flow
{

	WindTunnelWallPressure::WindTunnelWallPressure(const int &boundaryPatchID, 
		                                           Package::FlowPackage &data, 
												   const std::string &fileName_, 
												   Flux::Flow::Precondition::Precondition *precondition_)
    :
    FlowBoundary(boundaryPatchID, data),
	fileName(fileName_),
	precondition(precondition_)
{
	//std::cout<<"boundaryPatchID"<<boundaryPatchID;  //添加编译测试      
	//std::cout<<"fileName"<<fileName;  //添加编译测试
	this->rho_inf = data.GetFlowConfigure().GetFlowReference().density;
	this->velocity_inf = data.GetFlowConfigure().GetFlowReference().velocity;
	this->Pressure_inf = data.GetFlowConfigure().GetFlowReference().pressure;
	this->soundSpeed_inf = material.GetSoundSpeed(Pressure_inf, rho_inf);
	turbulentViscosityRatio = data.GetFlowConfigure().GetFlowReference().turbulentViscosityRatio;
}

void WindTunnelWallPressure::Initialize()
{
    this->ReadFile();
    this->CalculatePressure();
    this->UpdateBoundaryCondition();
    //std::cout<<"boundaryPatchIDE"<<boundaryPatchID;  //添加编译测试     
    //std::cout<<fileName<<"EE";  //添加编译测试   
}

void WindTunnelWallPressure::UpdateBoundaryCondition()
{
	if (precondition != nullptr) this->UpdateBoundaryConditionP();
	else                         this->UpdateBoundaryConditionNonP();
}

void WindTunnelWallPressure::UpdateBoundaryConditionNonP()
{
	// 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();
		const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();

		// 取得特定面心相邻体心物理量
		const Scalar &rhoOwner = rho.GetValue(ownerID);
		const Vector &UOwner = U.GetValue(ownerID);
		const Scalar &pOwner = p.GetValue(ownerID);
		const Scalar TOwner = material.GetTemperature(pOwner, rhoOwner);
		const Scalar muOwner = material.Mu(TOwner);
		const Scalar aOwner = material.GetSoundSpeed(pOwner, rhoOwner);

		// 计算特征值
		const Scalar UNormal = UOwner & faceNormal;
		const Scalar eigenValue1 = UNormal;
		const Scalar eigenValue2 = UNormal + aOwner;
		const Scalar eigenValue3 = UNormal - soundSpeed_inf;

		const Scalar ROC = this->rho_inf*this->soundSpeed_inf;
		const Scalar C2 = this->soundSpeed_inf*this->soundSpeed_inf;
		const Scalar UInfNormal = velocity_inf & faceNormal;

		// 计算特征量
		Vector WW1; Scalar WW4, WW5;
		if (eigenValue1 > 0.0) WW1 = (C2 * rhoOwner - pOwner) * faceNormal + ROC * (UOwner ^ faceNormal);
		else                   WW1 = (C2 * rho_inf - Pressure_inf) * faceNormal + ROC* (velocity_inf ^ faceNormal);
		if (eigenValue2 > 0.0) WW4 = 0.5 * ROC * UNormal + 0.5 * pOwner;
		else                   WW4 = 0.5 * ROC * UInfNormal + 0.5 * Pressure_inf;
		if (eigenValue3 > 0.0) WW5 = -0.5 * ROC * UNormal + 0.5 * pOwner;
		else                   WW5 = -0.5 * ROC * UInfNormal + 0.5 * Pressure_inf;

		// 计算基本量
		const Scalar rhob = ((WW1 & faceNormal) + WW4 + WW5) / C2;
		const Vector Ub = ((faceNormal ^ WW1) + faceNormal *(WW4 - WW5)) / ROC;
		const Scalar pb = pbList[j];
		const Scalar Tb = material.GetTemperature(pb, rhob);

		rho.SetValue(neighID, 2.0 * rhob - rhoOwner);
		U.SetValue(neighID, 2.0 * Ub - UOwner);
		p.SetValue(neighID, 2.0 * pb - pOwner);
		T.SetValue(neighID, 2.0 * Tb - TOwner);
	}

	return;
}

void WindTunnelWallPressure::UpdateBoundaryConditionP()
{
	const Scalar K2 = 4.0;
	const Scalar gamma = 1.4;
	const Scalar gamma1 = 0.4;

	// 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceID(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		// 代码处理成流入为正向
		const Vector faceNormal = -1.0 * mesh->GetFace(faceID).GetNormal();

		// 2.1 设置自由来流值法向速度和面单元的局部量
		Scalar U2Inf, C2Inf, Ma2Inf, UnormalInf, betaInf;
		C2Inf = soundSpeed_inf * soundSpeed_inf;
		U2Inf = velocity_inf & velocity_inf;
		UnormalInf = velocity_inf & faceNormal;
		Ma2Inf = (velocity_inf & velocity_inf) / C2Inf;

		betaInf = Min(Max(U2Inf, K2*U2Inf), C2Inf);

		Scalar rhoFace, TFace, U2Face, C2Face, Ma2Face, pFace, betaFace, UnormalFace;
		Vector UFace;
		rhoFace = rho.GetValue(ownerID);
		UFace = U.GetValue(ownerID);
		pFace = p.GetValue(ownerID);
		TFace = T.GetValue(ownerID);
		UnormalFace = UFace & faceNormal;
		U2Face = UFace & UFace;
		C2Face = material.GetSoundSpeed(TFace) * material.GetSoundSpeed(TFace);
		Ma2Face = U2Face / C2Face;
		betaFace = Min(Max(U2Face, K2*U2Inf), C2Face);

		// 2.2 计算黎曼条件中相关的分量
		Scalar zInf, z1Inf, fInf, zFace, fFace;
		zInf = 1 + betaInf / C2Inf;
		z1Inf = 1 - betaInf / C2Inf;
		fInf = sqrt(abs(zInf*zInf*UnormalInf*UnormalInf + 4 * (1 - Ma2Inf)*betaInf));
		zFace = 1 + betaFace / C2Face;
		// Scalar z1Face = 1 - betaFace / C2Face;
		fFace = sqrt(abs(zFace*zFace*UnormalFace*UnormalFace + 4 * (1 - Ma2Face)*betaFace));

		// 2.3 前向（入流）特征值方向的判断和处理
		Scalar eigenvalue4, fact4Inf;
		eigenvalue4 = 0.5 * (zInf*UnormalInf + fInf);
		fact4Inf = 0.5 * (z1Inf*UnormalInf - fInf) / (rho_inf*betaInf);
		// Scalar fact4Face = 0.5 * (z1Face*UnormalFace - fFace) / (rhoFace*betaFace);
		Scalar R4;
		// 超音速出流, R4来源于内部流场
		if (eigenvalue4 < 0.0)
		{
			R4 = UnormalFace - pFace*fact4Inf;
		}
		// 其余情况，R4来源于边界远场值
		else
		{
			R4 = UnormalInf - Pressure_inf*fact4Inf;
		}

		// 2.4 后向（出流）特征值方向的判断和处理
		Scalar eigenvalue5, fact5Inf;
		eigenvalue5 = 0.5 * (UnormalFace*zFace - fFace);
		fact5Inf = 0.5 * (UnormalInf*z1Inf + fInf) / (rho_inf*betaInf);
		// Scalar fact5Face = 0.5 * (UnormalFace*z1Face + fFace) / (rhoFace*betaFace);
		Scalar R5;
		// 超音速入流, R5来源于边界远场值
		if (eigenvalue5 > 0.0)
		{
			R5 = UnormalInf - Pressure_inf*fact5Inf;
		}
		// 其余情况，R5来源于内部流场
		else
		{
			R5 = UnormalFace - pFace*fact5Inf;
		}

		// 2.5 应用平均化处理法向速度和音速
		Scalar pSpec, UnornalTemp;
		UnornalTemp = (fact5Inf*R4 - fact4Inf*R5) / (fact5Inf - fact4Inf);
		//pSpec = (R4 - R5) / (fact5Inf - fact4Inf);
		pSpec = pbList[j];

		// 2.6 确定切向速度
		Vector UTang;
		Scalar SSpec;
		if (UnornalTemp > 0.0)
			// 入流边界，远场自由流的切向速度、熵
		{
			UTang = velocity_inf + (UnornalTemp - UnormalInf)*faceNormal;
			SSpec = Pressure_inf / pow(rho_inf, gamma);
		}
		// 出流边界，切向速度和熵外插
		else
		{
			UTang = UFace + (UnornalTemp - UnormalFace) * faceNormal;
			SSpec = pFace / pow(rhoFace, gamma);
		}

		// 2.7 确定密度和能量
		Scalar rhoSpec;
		rhoSpec = pow(pSpec / SSpec, 1.0 / gamma);
		Scalar Tspec = material.GetTemperature(pSpec, rhoSpec);

		// 2.8 计算得到远场边界处的流动变量值后，进行虚单元的变量更新  
		if (nodeCenter)
		{
			rho.SetValue(ownerID, rhoSpec);
			rho.SetValue(neighID, rhoSpec);
			U.SetValue(ownerID, UTang);
			U.SetValue(neighID, UTang);
			p.SetValue(ownerID, pSpec);
			p.SetValue(neighID, pSpec);
			T.SetValue(ownerID, Tspec);
			T.SetValue(neighID, Tspec);
		}
		else
		{
			rho.SetValue(neighID, 2 * rhoSpec - rhoFace);
			U.SetValue(neighID, 2 * UTang - UFace);
			p.SetValue(neighID, 2 * pSpec - pFace);
			T.SetValue(neighID, 2 * Tspec - material.GetTemperature(pFace, rhoFace));
		}


	}

	return;
}

void WindTunnelWallPressure::AddDiffusiveResidual()
{
    if(!nodeCenter) this->AddDiffusiveResidualCellCenter();

    return;
}

void WindTunnelWallPressure::AddConvectiveResidual()
{
	if (jacobian) this->AddConvectiveResidualReflected();
	else          this->AddConvectiveResidualAverage();
}

void WindTunnelWallPressure::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;
    
    //对于对偶网格，压强已经在UpdateBoundaryCondition每次重置，因此不需要处理能量残值
    return;
}

void WindTunnelWallPressure::CalculatePressure()
{    
    pbList.resize(boundaryFaceSize);
    const Scalar &pRef = flowPackage.GetFlowConfigure().GetFlowReference().pressure;
    
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const Vector &center = mesh->GetFace(faceID).GetCenter();

        //计算该位置X平面与所有测压带交点处压强值列表
        std::vector<Scalar> pList(bandList.size());
        std::vector<Scalar> coorList(bandList.size());
        	
        //std::cout<<"bandList.size()="<<bandList.size()<<"\n";// 添加编译测试    

        for (int m = 0; m < bandList.size(); ++m)
        {
            pList[m] = CalculateSPLine(center.X(), bandList[m].coorX, bandList[m].pressure);
            pList[m] = pList[m]*pRef;//程序修改//添加编译测试     
            //std::cout<<"x坐标="<<center.X()<<"\n";// 添加编译测试    
            //std::cout<<"pList[m]="<<pList[m]<<"\n"; //添加编译测试    
            if (yFlag) coorList[m] = bandList[m].coorZ;
            else       coorList[m] = bandList[m].coorY;
            //std::cout<<"coorList[m]="<<coorList[m]<<"\n"; //添加编译测试   
        }

        //计算该位置处压强
        Scalar coor = center.Y();
        if (yFlag) coor = center.Z();
        
        //std::cout<<"coor="<<coor<<"\n"; //添加编译测试    
        
        pbList[j] = CalculateLagrange(coor, coorList, pList);
        
        //std::cout<<"pbList[j]="<<pbList[j]<<"\n"; //添加编译测试    
    }

    // 清空相关容器，释放空间
    for (int m = 0; m < bandList.size(); ++m)
    {
        std::vector<Scalar>().swap(bandList[m].coorX);
        std::vector<Scalar>().swap(bandList[m].pressure);
    }
    std::vector<Band>().swap(bandList);
    //std::cout<<"CalculatePressure OK"; //添加编译测试    
}

Scalar WindTunnelWallPressure::CalculateSPLine(const Scalar &pos, const std::vector<Scalar> &posList, const std::vector<Scalar> &valueList)
{
    const int size = posList.size();
    std::vector<Scalar> h(size), dely(size);
    std::vector<Scalar> h1(size), deltaValue(size), b(size), DELSQY(size);
    std::vector<Scalar> s2(size), s3(size), c(size);
    
    for (int i = 0; i < size - 1; ++i)
    {
        h[i] = posList[i + 1] - posList[i];
        dely[i] = (valueList[i + 1] - valueList[i]) / h[i];
    }

    for (int i = 1; i < size - 1; ++i)
    {
        h1[i] = h[i - 1] + h[i];
        b[i] = 0.5 * h[i - 1] / h1[i];
        DELSQY[i] = (dely[i] - dely[i - 1]) / h1[i];
        s2[i] = 2.0 * DELSQY[i];
        c[i] = 3.0 * DELSQY[i];
    }
    
    s2[0] = 0.0;
    s2[size - 1] = 0.0;
    
    for (int i = 1; i < size - 1; ++i)
        s2[i] += (c[i] - b[i] * s2[i - 1] - (0.5 - b[i])*s2[i + 1] - s2[i]) * 1.0717968;

    for (int i = 0; i < size - 1; ++i)
        s3[i] = (s2[i + 1] - s2[i]) / h[i];
        
    //std::cout<<"s3[i]"<<s3[i]<<"\n"; //添加编译测试    
    //std::cout<<"posList[0]="<<posList[0]<<"\n";//添加编译测试    
    //std::cout<<"posList[size-1]="<<posList[size-1]<<"\n";//添加编译测试    	
    int j = 0;
    if(pos <= posList[0])
    {
    	j = 0;
    }
    else
    {
    	if (pos >= posList[size-1])
    	{
    		j = size - 2;
    	}
    	else
    	{
    		for (int i = 1; i < size; ++i)
    		{
    			if(pos < posList[i])
    		  {
    			   j=i-1;
    			   //std::cout<<"j="<<j<<"\n"; //添加编译测试    
    			   break;
    		  }
    		  else
    		  {
    		  	if(pos == posList[i])
    		  	{
    		  		j=i;
    		  		break;
    		  	}
    		  }
    		}
    	}
      //std::cout<<"j_1="<<j<<"\n"; //添加编译测试    
    }

    //std::cout<<"j_2="<<j<<"\n"; //添加编译测试    
    //std::cout<<"pos="<<pos<<"\n";//添加编译测试    
    //std::cout<<"s3[j]="<<s3[j]<<"\n";//添加编译测试    
    	
    Scalar ht1 = pos - posList[j];
    Scalar ht2 = pos - posList[j + 1];
    Scalar DELSQS = (2.0*s2[j] + s2[j + 1] + ht1 * s3[j]) / 6.0;
    
    //std::cout<<"ht1"<<ht1<<"\n"; //添加编译测试    
    //std::cout<<"ht2"<<ht2<<"\n"; //添加编译测试    
    //std::cout<<"DELSQS"<<DELSQS<<"\n"; //添加编译测试      
    //std::cout<<"posList[i]="<<posList[i]<<"\n"; //添加编译测试   
    //std::cout<<"posList[i+1]="<<posList[i+1]<<"\n"; //添加编译测试   
    		  
    return    valueList[j] + ht1 * dely[j] + ht1 * ht2 * DELSQS;
}

Scalar WindTunnelWallPressure::CalculateLagrange(const Scalar &x0, const std::vector<Scalar> &lx, const std::vector<Scalar> &ly)
{
    const int size = lx.size();
    Scalar lagrange = 0;

    for (int i = 0; i < size; ++i)
    {
        Scalar coff = 1.0;
        for (int j = 0; j < size; ++j)
            if (i != j) coff *= ((x0 - lx[j]) / (lx[i] - lx[j]));
        
        lagrange += coff*ly[i];
    }

    return lagrange;
}

void WindTunnelWallPressure::ReadFile()
{
    //打开文件
    //std::cout<<"boundaryPatchIDD"<<boundaryPatchID;  //添加编译测试    
    //std::cout<<fileName<<"DD";  //添加编译测试      
    std::fstream file; 
    //std::cout<<fileName<<"BB";  //添加编译测试        
    //file.open(fileName, std::fstream::in); 版本1
    file.open(fileName, std::ios::in);
    if (!file.is_open())
        FatalError("WindTunnelWallPressure::ReadFile: " + fileName +"BB"+ " in not opened...");
        
    std::string s;// 用于跳过注释行

    //读入测压带条数、所在洞壁位置
    file >> s >> s;
    int sizeBand;
    file >> sizeBand >> s;
    bandList.resize(sizeBand);
    if (s == "Y" || s == "y") yFlag = true;
    else yFlag = false;

    //文件提供的坐标位置、 压强等是否需要缩放
    file >> s >> s;
    Scalar coorRatio, pressureRatio;
    file >> coorRatio >> pressureRatio;
   // std::cout<<"coorRatio="<<coorRatio<<"\n";  //添加编译测试   

    //读入每个测压带基本数据
    for (int m = 0; m < sizeBand; ++m)
    {
        // 探针数量
        file >> s >> s >> s;
        int sizeProbe;
        Scalar coor;
        file >> sizeProbe;
        bandList[m].coorX.resize(sizeProbe);
        bandList[m].pressure.resize(sizeProbe);

        // 测压带坐标
        file >> coor;
        coor *= coorRatio;
        //std::cout<<"coor="<<coor<<"\n";  //添加编译测试   
        if (yFlag)
        {
            bandList[m].coorY = INF;
            bandList[m].coorZ = coor;
        }
        else
        {
            bandList[m].coorY = coor;
            bandList[m].coorZ = INF;
        }    
        
        // x坐标和压强值
        file >> s >> s; //添加编译测试   
        for (int i = 0; i < sizeProbe; ++i)
        {
            file >> bandList[m].coorX[i] >> bandList[m].pressure[i];
            bandList[m].coorX[i] *= coorRatio;
            bandList[m].pressure[i] *= pressureRatio;
            //std::cout<<"bandList[m].pressure[i]="<<bandList[m].pressure[i]<<"\n"; //添加编译测试    
        }
            
    }

    file.close();
    //std::cout<<"ReadFile OK"; //添加编译测试    
}

}// namespace Flow
}// namespace Boundary
