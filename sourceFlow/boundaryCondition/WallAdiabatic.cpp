﻿#include "sourceFlow/boundaryCondition/WallAdiabatic.h"
// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{
// 绝热壁面边界条件
WallAdiabatic::WallAdiabatic(const int &boundaryPatchID, Package::FlowPackage &data)
    :
    Wall(boundaryPatchID, data)
{
}

void WallAdiabatic::Initialize()
{
    this->UpdateBoundaryCondition();
}

void WallAdiabatic::UpdateBoundaryCondition()
{
    this->BoundFromElement(rho);
    this->BoundFromElement(p);
    this->BoundFromElement(T);
    this->BoundFromElement(A);
    this->SetVelocityAndMu();

    return;
}

void WallAdiabatic::AddDiffusiveResidual()
{
    if (!nodeCenter)
    {
        if (muLaminar) this->AddDiffusiveResidualCellCenterNoSlip();
        else           this->AddDiffusiveResidualCellCenterSlip();
    }

    if (nodeCenter && !muLaminar) this->AddDiffusiveResidualNodeCenter();

    return;
}

void WallAdiabatic::AddConvectiveResidual()
{
    if (implicitFlag)
    {
        if (!muLaminar) this->AddConvectiveResidualSlip();
        else if (!nodeCenter) this->AddConvectiveResidualNoSlip();
    }
    else
    {
        this->AddConvectiveResidualAverage();
    }
    
    this->AddMRFConvectiveResidualAverage();
}

void WallAdiabatic::UpdateBoundaryResidual()
{
    if (nodeCenter && muLaminar) this->UpdateBoundaryResidualStatic();
}

}// namespace Flow
}// namespace Boundary
