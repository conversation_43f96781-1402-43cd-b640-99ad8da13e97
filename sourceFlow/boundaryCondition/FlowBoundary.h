﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowBoundary.h
//! <AUTHOR> 尹强
//! @brief FlowBoundary为相关边界条件基类,抽象类
//! @date 2021-03-30
//
//------------------------------修改日志----------------------------------------
//
// 2021-03-30 尹强 
// 说明：添加注释并规范化
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_FlowBoundary_
#define _sourceFlow_boundaryCondition_FlowBoundary_

#include "sourceFlow/package/FlowPackage.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/InviscidFluxScheme.h"
#include "sourceFlow/fluxScheme/inviscidFluxScheme/RoeScheme.h"
#include "sourceFlow/fluxScheme/viscousFluxScheme/ViscousFluxScheme.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流动控制方程相关边界条件基类
 * 
 */
class FlowBoundary
{
public:
    /**
     * @brief 构造函数，创建流场边界条件对象
     * 
     * @param[in] boundaryPatchID_ 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     */
    FlowBoundary(const int &boundaryPatchID_, Package::FlowPackage &data);
    
    /**
     * @brief 析构函数
     * 
     */
    virtual ~FlowBoundary();
    
    /**
     * @brief 纯虚函数，初始化边界值
     * 
     */
    virtual void Initialize() = 0;

    /**
     * @brief 纯虚函数，更新边界条件
     * 
     */
    virtual void UpdateBoundaryCondition() = 0;
    
    /**
     * @brief 计算边界粘性残值
     * 
     */
    virtual void AddDiffusiveResidual() = 0;

    /**
     * @brief 计算边界对流残值
     * 
     */
    virtual void AddConvectiveResidual() = 0;

    /**
     * @brief 纯虚函数，更新边界残值(用于对偶网格)
     * 
     */
    virtual void UpdateBoundaryResidual() = 0;

    /**
     * @brief 将边界处的值复制到虚拟单元处
     * 
     * @tparam Type 
     * @param[in, out] phi 物理场，例如密度、压强、速度等
     */
    template<class Type>
    void BoundFromElement(ElementField<Type> &phi);

    /**
     * @brief 将边界处法向分量置零
     * 
     * @tparam Type 
     * @param[in, out] phi 梯度场，例如密度、压强、速度等梯度
     */
    template<class Type>
    void SetBoundaryNormalZero(ElementField<Type> *phi);

    /**
     * @brief 设置边界条件调用的通量离散格式
     *
     */
    void SetFluxScheme(Flux::Flow::Inviscid::InviscidFluxScheme *inviscidFluxScheme_, Flux::Flow::Viscous::ViscousFluxScheme *viscousFluxScheme_);

    /**
     * @brief 更新边界面相邻单元的音速、总焓和层流粘度
     *
     */
    void UpdateExtras();

#if defined(_EnableMultiSpecies_)
    /**
     * @brief 计算多组分对流平均残值
     * 
     */
    void AddConvectiveResidualMultiSpecies();

    /**
     * @brief 计算多组分扩散残值
     * 
     */
    void AddDiffusiveResidualMultiSpecies();
#endif

protected:

    /**
     * @brief 计算边界粘性残值，原始网格采用虚单元与边界单元的平均值计算
     * 
     */
    void AddDiffusiveResidualCellCenter();

    /**
     * @brief 计算无滑移边界粘性残值
     * 
     */
    void AddDiffusiveResidualCellCenterNoSlip();
    
    /**
     * @brief 计算滑移边界粘性残值
     * 
     */
    void AddDiffusiveResidualCellCenterSlip();

    /**
     * @brief 计算边界粘性残值，对偶网格采用内部单元与边界单元的平均值计算
     * 
     */
    void AddDiffusiveResidualNodeCenter();

    /**
     * @brief 计算边界对流残值，采用虚单元与边界单元的平均值计算
     * 
     */
    void AddConvectiveResidualAverage();

    /**
     * @brief 计算边界对流项的通量修正
     * 
     */
    void AddMRFConvectiveResidualAverage();
    
    /**
     * @brief 计算外部边界对流残值，采用迎风格式计算
     * 
     */
    void AddConvectiveResidualExternal();

    /**
     * @brief 计算镜面边界对流残值，采用迎风格式计算
     * 
     */
    void AddConvectiveResidualReflected();

    /**
     * @brief 计算无滑移边界对流残值，采用迎风格式计算
     * 
     */
    void AddConvectiveResidualNoSlip();
    
    /**
     * @brief 计算滑移边界对流残值，采用迎风格式计算
     * 
     */
    void AddConvectiveResidualSlip();

protected:
    Package::FlowPackage &flowPackage; ///< 流场包
    Mesh *mesh; ///< 网格指针
    const Material::Flow::Materials &material; ///< 材料对象

    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度
    ElementField<Scalar> &H; ///< 总焓
    ElementField<Scalar> &A; ///< 当地声速
    ElementField<Scalar> *muLaminar; ///< 层流粘性系数
    
    ElementField<Scalar> &residualMass; ///< 质量残值
    ElementField<Vector> &residualMomentum; ///< 动量残值
    ElementField<Scalar> &residualEnergy; ///< 能量残值

    ElementField<Vector> *gradientRho; ///< 密度梯度
    ElementField<Tensor> *gradientU; ///< 速度梯度
    ElementField<Vector> *gradientP; ///< 压强梯度
    ElementField<Vector> *gradientT; ///< 温度梯度
    
    const int boundaryPatchID; ///< 边界编号
    const int boundaryFaceSize; ///< 边界面数量
    const bool nodeCenter; ///< 边界数据存储在节点标识
    
	const std::vector<Scalar> &distance; ///< 当地网格体心连线距离

#if defined(_EnableMultiSpecies_)
	const int speciesSize; ///< 组分方程数量
	const std::vector<ElementField<Scalar> *> &massFraction; ///< 组分指针容器
	const std::vector<ElementField<Scalar> *> &residualMassFraction; ///< 组分残值场指针容器
    std::vector<ElementField<Vector> *> gradientMassFraction; ///< 组分场梯度
#endif

    Flux::Flow::NSFaceFlux faceFlux; ///< 面通量
    Flux::Flow::NSFaceValue faceValue; ///< 左右面心值
    Flux::Flow::Inviscid::InviscidFluxScheme *inviscidFluxScheme; ///< 无粘通量指针
    Flux::Flow::Viscous::ViscousFluxScheme *viscousFluxScheme; ///< 粘性通量指针

	BlockSparseMatrix *jacobian; ///< 隐式方法的jacobian
    const bool &updateJacobian; ///< Jacobian是否更新标识

    bool implicitFlag; ///< 隐式标识
    unsigned short nDim, nVar;
    Matrix Jacobian_i;
};
} // namespace Flow
} // namespace Boundary

#endif
