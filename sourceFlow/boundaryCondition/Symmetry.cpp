﻿#include "sourceFlow/boundaryCondition/Symmetry.h"

namespace Boundary
{
namespace Flow
{
Symmetry::Symmetry(const int &boundaryPatchID, Package::FlowPackage &data)
    :
    FlowBoundary(boundaryPatchID, data)
{
}

void Symmetry::Initialize()
{
    this->UpdateBoundaryCondition();
}

void Symmetry::UpdateBoundaryCondition()
{
    this->BoundFromElement(rho);
    this->BoundFromElement(p);
    this->BoundFromElement(T);

    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Vector &UOwner = U.GetValue(ownerID);
        const Vector faceU = UOwner - (UOwner & faceNormal) * faceNormal;
        U.SetValue(neighID, 2.0 * faceU - UOwner);
    }

    return;
}

void Symmetry::AddDiffusiveResidual()
{
    if (!nodeCenter)
    {
        this->AddDiffusiveResidualCellCenter();
        // this->AddDiffusiveResidualCellCenterSlip();
    }
    else
    {
        this->AddDiffusiveResidualNodeCenter();
    }
}

void Symmetry::AddConvectiveResidual()
{
    if (implicitFlag) this->AddConvectiveResidualReflected();
    else              this->AddConvectiveResidualAverage();
    
    this->AddMRFConvectiveResidualAverage();
}

void Symmetry::UpdateBoundaryResidual()
{
    return;
}

}// namespace Flow
}// namespace Boundary
