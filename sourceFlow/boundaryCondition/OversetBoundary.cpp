﻿#include "sourceFlow/boundaryCondition/OversetBoundary.h"

namespace Boundary
{
namespace Flow
{

OversetBoundary::OversetBoundary(Package::FlowPackage &data)
    :
    flowPackage(data), mesh(data.GetMeshStruct().mesh),
    material(data.GetMaterial()),
    rho(*data.GetField().density),
    U(*data.GetField().velocity),
    p(*data.GetField().pressure),
    T(*data.GetField().temperature),
    A(*data.GetField().soundSpeed),
    H(*data.GetField().enthalpy),
    muLaminar(data.GetField().muLaminar),
    residualMass(*data.GetResidualField().residualMass),
    residualMomentum(*data.GetResidualField().residualMomentum),
    residualEnergy(*data.GetResidualField().residualEnergy),
    nodeCenter(data.GetFlowConfigure().GetPreprocess().dualMeshFlag)
{
#if defined(_EnableOverset_)
    oversetMesh = nullptr;
	// 在最细网格层启动重叠网格模块，ToDO：粗网格层如何处理需进一步研究
    if (data.GetMeshStruct().level == 0)
    {
	    oversetMesh = new OversetMesh(data);
        upToDate = false; // 重叠网格状态初始为未更新
    }
#endif
}

OversetBoundary::~OversetBoundary()
{
}

void OversetBoundary::Initialize()
{
#if defined(_EnableOverset_)
    if (oversetMesh && upToDate == false)
    {
        oversetMesh->InitOGA();
        upToDate = true;
    }
#endif
}

void OversetBoundary::UpdateBoundaryCondition()
{
#if defined(_EnableOverset_)
    rho.SetGhostlValueOverset();
    U.SetGhostlValueOverset();
    p.SetGhostlValueOverset();
    
    const std::vector<std::vector<Acceptor>> &acceptorList = mesh->GetOversetRegion().GetAcceptorList();
    for (int i = 0; i < acceptorList.size(); ++i)
    {
        for (int j = 0; j < acceptorList[i].size(); j++)
        {
            const int &elemID = acceptorList[i][j].GetAcceptorID();

            const Scalar &pTemp = p.GetValue(elemID);
            const Scalar &rhoTemp = rho.GetValue(elemID);
            const Scalar TTemp = material.GetTemperature(pTemp, rhoTemp);
            T.SetValue(elemID, TTemp);
            A.SetValue(neighborID, material.GetSoundSpeed(TTemp));
            H.SetValue(neighborID, material.Enthalpy(TTemp));
            if (muLaminar != nullptr) muLaminar->SetValue(neighborID, material.Mu(TTemp));
        }
    }
#endif
    return;
}

void OversetBoundary::UpdateBoundaryResidual()
{
    if(!nodeCenter) return;
    return;
}

}// namespace Flow
}// namespace Boundary
