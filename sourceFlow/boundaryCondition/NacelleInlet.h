﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file NacelleInlet.h
//! <AUTHOR>
//! @brief 发动机短舱入口边界（计算域的出口边界）
//! @date  2023-2-4
//
//------------------------------修改日志----------------------------------------
//
// 2023-2-4 李艳亮、乔龙
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_NacelleInlet_
#define _sourceFlow_boundaryCondition_NacelleInlet_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 发动机短舱入口边界条件类
 * 
 */
class NacelleInlet : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] eps_ 捕获流量比(当<=0时，由massFlow计算得到eps,当>0时直接使用)
     * @param[in] massFlow_ 入口质量流量rho*A*U（当=0时，由程序自动匹配匹配边界couplePatchIDGlobal计算，当>0时直接使用）
     * @param[in] couplePatchIDGlobal_ 配对出口边界编号（当>=0时用于获取massFlow，当<0时无效）
     * @param[in] area 该边界的面积
     */
    NacelleInlet(const int &boundaryPatchID, Package::FlowPackage &data,
        const Scalar &eps_, const Scalar &massFlow_, const int &couplePatchIDGlobal_,
        Scalar &area);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 边界条件更新
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 获取匹配边界全局编号
     * 
     */
    const int GetcouplePatchIDGlobal()const {return this->boundaryPatchID;}

private:
    /**
    * @brief 迭代计算入口的La数
    *
    * @param[in] LaRef 参考LAVAL数
    */
    Scalar CalculateLaFan(const Scalar &LaRef);

    /**
    * @brief 计算参考La数
    *
    */
    Scalar CalculateLaRef();

    /**
    * @brief 计算捕获流量比率
    *
    */
    void CalculateEps();

    /**
    * @brief 计算入口面积
    *
    */
    Scalar CalculateArea();

    /**
    * @brief 更新入口处物理量
    *
    * @param[in] LaFan 入口的LAVAL数
    */
    void UpdateValue(const Scalar &LaFan);
    
private:
    int couplePatchIDGlobal; ///< 配对的另一个边界编号（当<0时不需要配对使用）
    Scalar eps; ///< 捕获流量比率
    const Scalar &massFlow; ///< 入口质量流量（常引用,数据源在边界管理器中）

    Scalar LaFree; ///< 参考点的LAVAL数
    Scalar rhoFree; ///< 参考点的密度
    Scalar pFree; ///< 参考点的压强
    Scalar cFree; ///< 参考点的音速
    Scalar machFree; ///< 参考点的马赫数
    
    Scalar pb; ///< 入口的压强
    Scalar &areaFan; ///< 入口的面积

    const Scalar &gamma; ///< gamma
    Scalar gamma1p; ///< gamma + 1.0
    Scalar gamma1m; ///< gamma - 1.0;
    Scalar gam1; ///< gamma1m / gamma1p
    Scalar div; ///< 1.0 - gam1
    Scalar expNum; ///< 1.0 / gamma1m
    Scalar gam2; ///< (2.0 - gamma) / gamma1m

    bool updateMassFlowFlag; ///< 是否每次更新massFlow
};

} // namespace Flow
} // namespace Boundary


#endif
