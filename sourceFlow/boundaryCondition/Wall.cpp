﻿#include "sourceFlow/boundaryCondition/Wall.h"

namespace Boundary
{
namespace Flow
{
Wall::Wall(const int &boundaryPatchID, Package::FlowPackage &data)
    :
    FlowBoundary(boundaryPatchID, data)
{
}

void Wall::SetVelocityAndMu()
{    
    //更新层流粘性系数
    if (muLaminar != nullptr) this->BoundFromElement(*muLaminar);

    // 更新速度和湍流粘性系数
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        //更新速度
        if (muLaminar != nullptr) //无滑移边界
        {
            if (nodeCenter)
            {
                U.SetValue(ownerID, Vector0);
                U.SetValue(neighID, Vector0);
            }
            else
            {
                U.SetValue(neighID, -U.GetValue(ownerID));
            }
        }
        else //滑移边界
        {
            const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
            const Vector &UOwner = U.GetValue(ownerID);
            const Vector faceU = UOwner - (UOwner & faceNormal) * faceNormal;
            U.SetValue(neighID, 2.0 * faceU - UOwner);
        }        
    }

    return;
}

void Wall::UpdateBoundaryResidualSlipping()
{
	return;
}

void Wall::UpdateBoundaryResidualStatic()
{
    if (!nodeCenter) return;

    bool dim3 = mesh->GetMeshDimension() == Mesh::MeshDim::md3D;
    
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        residualMomentum.SetValue(ownerID, Vector0);

        if (jacobian && updateJacobian)
        {
            jacobian->DeleteValsRowi(ownerID, 1);
            jacobian->DeleteValsRowi(ownerID, 2);
            if (dim3) jacobian->DeleteValsRowi(ownerID, 3);
        }
    }
}

}// namespace Flow
}// namespace Boundary
