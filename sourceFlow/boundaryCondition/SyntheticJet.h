﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SyntheticJet.h
//! <AUTHOR>
//! @brief 合成射流边界条件（非定常计算）
//! @date  2023-2-6
//
//------------------------------修改日志----------------------------------------
//
// 2023-2-6 李艳亮、乔龙
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_SyntheticJet_
#define _sourceFlow_boundaryCondition_SyntheticJet_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 合成射流边界条件类
 * 
 */
class SyntheticJet: public FlowBoundary
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] direction_ 速度方向
     * @param[in] velocityMean_ 时均速度
     * @param[in] velocityAmplitude_ 速度振幅
     * @param[in] frequency_ 频率
     * @param[in] theta_ 初始相位角(度)
     */
    SyntheticJet(const int &boundaryPatchID, Package::FlowPackage &data,
                 const Vector &direction_,
                 const Scalar &velocityMean_,
                 const Scalar &velocityAmplitude_,
                 const Scalar &frequency_,
                 const Scalar &theta_);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 边界条件更新
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();
    
private:
    Vector direction; ///< 速度方向（其模近似为0则自动选择面负法向）
    bool directionNormFlag; ///< 速度方向是否自动选择负法向标志，true为选择负法向
    Scalar velocityMean; ///< 时均速度
    Scalar velocityAmplitude; ///< 速度振幅
    Scalar frequency; ///< 频率    
    Scalar omega; ///< 速度变化圆频率 omega = 2*PI*frequency 
    Scalar theta; ///< 初始相位（输入角度，程序转化为弧度）

    const Scalar &currentTime;  ///< 当前物理时间
	const Scalar &startTime;  ///< 起始物理时间
};

} // namespace Flow
} // namespace Boundary


#endif
