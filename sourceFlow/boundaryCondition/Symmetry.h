﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Symmetry.h
//! <AUTHOR>
//! @brief Symmetry为对称面边界条件类
//! @date  2021-3-30
//
//------------------------------修改日志----------------------------------------
//
// 2021-03-30 尹强
// 说明：添加注释并规范化
//
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_Symmetry_
#define _sourceFlow_boundaryCondition_Symmetry_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 对称边界条件类
 * 
 */
class Symmetry :public FlowBoundary
{
public:
    /**
     * @brief 构造函数，初始化对称面边界条件
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     */
    Symmetry(const int &boundaryPatchID, Package::FlowPackage &data);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 对称面边界条件具体实现函数
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 对称面边界残值更新
     * 
     */
    void UpdateBoundaryResidual();
    
};

} // namespace Flow
} // namespace Boundary
#endif
