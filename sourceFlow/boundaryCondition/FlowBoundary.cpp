﻿#include "sourceFlow/boundaryCondition/FlowBoundary.h"

namespace Boundary
{
namespace Flow
{
FlowBoundary::FlowBoundary(const int &boundaryPatchID_, Package::FlowPackage &data)
    :
    flowPackage(data),
    mesh(data.GetMeshStruct().mesh),
    material(data.GetMaterial()),
    boundaryPatchID(boundaryPatchID_),
    boundaryFaceSize(mesh->GetBoundaryFaceNumberInDomain(boundaryPatchID)),
    rho(*data.GetField().density),
    U(*data.GetField().velocity),
    p(*data.GetField().pressure),
    T(*data.GetField().temperature),
    H(*data.GetField().enthalpy),
    A(*data.GetField().soundSpeed),
    muLaminar(data.GetField().muLaminar),
    residualMass(*data.GetResidualField().residualMass),
    residualMomentum(*data.GetResidualField().residualMomentum),
    residualEnergy(*data.GetResidualField().residualEnergy),
#if defined(_EnableMultiSpecies_)
	massFraction(data.GetField().massFraction),
	residualMassFraction(data.GetResidualField().residualMassFraction),
    gradientMassFraction(data.GetGradientField().gradientMassFraction),
    speciesSize(data.GetMultiSpeciesStatus().speciesSize),
#endif
    nodeCenter(data.GetFlowConfigure().GetPreprocess().dualMeshFlag),
	distance(data.GetExtraInfo().distance),
	jacobian(data.GetImplicitSolver().jacobian),
    updateJacobian(data.GetImplicitSolver().updateJacobian),
    inviscidFluxScheme(nullptr), viscousFluxScheme(nullptr)
{
    gradientRho = data.GetGradientField().gradientRho;
    gradientU = data.GetGradientField().gradientU;
    gradientP = data.GetGradientField().gradientP;
    gradientT = data.GetGradientField().gradientT;

    implicitFlag = (jacobian != nullptr);
    nDim = data.GetMeshStruct().mesh->GetMeshDimension();
    nVar = nDim + 2;
	Jacobian_i.Resize(nVar, nVar);
    
#if defined(_EnableMultiSpecies_)
    faceFlux.massFractionFlux.resize(speciesSize);
    faceValue.massFractionLeft.resize(speciesSize);
    faceValue.massFractionRight.resize(speciesSize);
#endif
}

FlowBoundary::~FlowBoundary()
{
    if (implicitFlag)
    {
        const int currentLevel = flowPackage.GetMeshStruct().level;
        if (flowPackage.GetFlowConfigure().GetFluxScheme(currentLevel).inviscid == Flux::Flow::Inviscid::CENTRAL)
        {
            if (inviscidFluxScheme != nullptr)
            {
                delete inviscidFluxScheme;
                inviscidFluxScheme = nullptr;
            }
        }
    }
}

void FlowBoundary::AddDiffusiveResidualCellCenter()
{
    Flux::Flow::NSFaceFlux faceFlux;

	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
	    const Vector &faceNormal = face.GetNormal();
	    const Scalar metric = face.GetArea() / distance[faceID];

	    // 计算面心值
	    const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));

        // 计算差量
        const Scalar muFace = 0.5 * (flowPackage.CalculateMu(ownerID) + flowPackage.CalculateMu(neighID));
	    const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(ownerID) + flowPackage.CalculateKappa(neighID));
	    const Vector dU = U.GetValue(neighID) - U.GetValue(ownerID);
	    const Scalar dT = T.GetValue(neighID) - T.GetValue(ownerID);
	    const Scalar UNorm = (dU & faceNormal) / 3.0;
        
	    // 计算面通量
	    faceFlux.massFlux = 0.0;
	    faceFlux.momentumFlux = muFace * (dU + UNorm * faceNormal) * metric;
	    faceFlux.energyFlux = (faceFlux.momentumFlux & UFace) + kappaFace * dT * metric;

        if (updateJacobian) viscousFluxScheme->CalculateViscousJacobian(faceID, muFace, kappaFace, UFace, faceFlux);

	    // 将面通量累加到残值中
        residualMass.AddValue(ownerID, -faceFlux.massFlux);
        residualMomentum.AddValue(ownerID,-faceFlux.momentumFlux);
        residualEnergy.AddValue(ownerID, -faceFlux.energyFlux);
    }
}

void FlowBoundary::AddDiffusiveResidualCellCenterNoSlip()
{
    const int nDim = flowPackage.GetMeshStruct().mesh->GetMeshDimension();
    const int lastIndex = nDim + 1;

	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
	    const Vector &faceNormal = face.GetNormal();
	    const Scalar metric = face.GetArea() / distance[faceID];

        // 计算差量
        const Scalar muFace = 0.5 * (flowPackage.CalculateMu(ownerID) + flowPackage.CalculateMu(neighID));
	    const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(ownerID) + flowPackage.CalculateKappa(neighID));
	    const Vector dU = U.GetValue(neighID) - U.GetValue(ownerID);
	    const Scalar dT = T.GetValue(neighID) - T.GetValue(ownerID);
	    const Scalar UNorm = (dU & faceNormal) / 3.0;
        
	    // 计算面通量
	    const Scalar massFlux = 0.0;
	    const Vector momentumFlux = muFace * (dU + UNorm * faceNormal) * metric;
	    const Scalar energyFlux = kappaFace * dT * metric;

	    // 将面通量累加到残值中
        residualMass.AddValue(ownerID, -massFlux);
        residualMomentum.AddValue(ownerID,-momentumFlux);
        residualEnergy.AddValue(ownerID, -energyFlux);

        if (updateJacobian)
        {
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();

            Jacobian_i.SetZero();
            if (distance[faceID] > 0.0)
            {
                const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
                const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));

                const Scalar xi = muFace / rhoFace * metric;
                const Tensor t0 = -xi * (Tensor1 + (face.GetNormal() * face.GetNormal()) / 3.0);

                const Scalar &R = flowPackage.GetMaterialNumber().R;
                const Scalar &gamma1 = flowPackage.GetMaterialNumber().gamma1;
                const Scalar phi = kappaFace * metric * gamma1 / (rhoFace * R);

                Jacobian_i(1, 1) = t0.XX();
                Jacobian_i(1, 2) = t0.XY();
                Jacobian_i(2, 1) = t0.YX();
                Jacobian_i(2, 2) = t0.YY();
                Jacobian_i(lastIndex, 0) = phi * pFace / (rhoFace * gamma1);
                Jacobian_i(lastIndex, lastIndex) = -phi;

                if (nDim == 3)
                {
                    Jacobian_i(1, 3) = t0.XZ();
                    Jacobian_i(2, 3) = t0.YZ();
                    Jacobian_i(3, 1) = t0.ZX();
                    Jacobian_i(3, 2) = t0.ZY();
                    Jacobian_i(3, 3) = t0.ZZ();
                }
            }

            jacobian->AddBlock2Diag(ownerID, -Jacobian_i);
        }
    }
}

void FlowBoundary::AddDiffusiveResidualCellCenterSlip()
{
    const int nDim = flowPackage.GetMeshStruct().mesh->GetMeshDimension();
    const int lastIndex = nDim + 1;

	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
	    const Vector &faceNormal = face.GetNormal();
	    const Scalar metric = face.GetArea() / distance[faceID];

	    // 计算面通量
        const Scalar muFace = 0.5 * (flowPackage.CalculateMu(ownerID) + flowPackage.CalculateMu(neighID));
        const Scalar UNormal = U.GetValue(ownerID) & faceNormal;
        const Scalar coef = -8.0 / 3.0 * muFace * metric;
	    const Scalar massFlux = 0.0;
	    const Vector momentumFlux = coef * UNormal * faceNormal;
	    const Scalar energyFlux = coef * UNormal * UNormal;

	    // 将面通量累加到残值中
        residualMass.AddValue(ownerID, -massFlux);
        residualMomentum.AddValue(ownerID,-momentumFlux);
        residualEnergy.AddValue(ownerID, -energyFlux);

        if (updateJacobian)
        {
            Jacobian_i.SetZero();
            if (distance[faceID] > 0.0)
            {
                const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
                const Scalar temp0 = -coef / rhoFace * UNormal;
                const Tensor temp1 = coef / rhoFace * faceNormal * faceNormal;
                const Scalar temp2 = 2.0 * temp0;

                Jacobian_i(1, 0) = temp0 * faceNormal.X();
                Jacobian_i(1, 1) = temp1.XX();
                Jacobian_i(1, 2) = temp1.XY();

                Jacobian_i(2, 0) = temp0 * faceNormal.Y();
                Jacobian_i(2, 1) = temp1.YX();
                Jacobian_i(2, 2) = temp1.YY();

                Jacobian_i(lastIndex, 0) = -temp2 * UNormal;
                Jacobian_i(lastIndex, 1) = temp2 * faceNormal.X();
                Jacobian_i(lastIndex, 2) = temp2 * faceNormal.Y();

                if (nDim == 3)
                {
                    Jacobian_i(1, 3) = temp1.XZ();
                    Jacobian_i(2, 3) = temp1.YZ();
                    Jacobian_i(lastIndex, 3) = temp2 * faceNormal.Z();

                    Jacobian_i(3, 0) = temp0 * faceNormal.Z();
                    Jacobian_i(3, 1) = temp1.ZX();
                    Jacobian_i(3, 2) = temp1.ZY();
                    Jacobian_i(3, 3) = temp1.ZZ();
                }
            }

            jacobian->AddBlock2Diag(ownerID, -Jacobian_i);
        }
    }
}

void FlowBoundary::AddDiffusiveResidualNodeCenter()
{
	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
	    const Vector &faceNormal = face.GetNormal();
	    const Scalar metric = face.GetArea() / distance[faceID];

	    // 计算面心值
	    const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));

        // 计算差量
        const int &innerID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, index);
        const Scalar muFace = 0.5 * (flowPackage.CalculateMu(innerID) + flowPackage.CalculateMu(ownerID));
	    const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(innerID) + flowPackage.CalculateKappa(ownerID));
	    const Vector dU = U.GetValue(ownerID) - U.GetValue(innerID);
	    const Scalar dT = T.GetValue(ownerID) - T.GetValue(innerID);
	    const Scalar UNorm = (dU & faceNormal) / 3.0;
        
	    // 计算面通量
	    const Scalar massFlux = 0.0;
	    const Vector momentumFlux = muFace * (dU + UNorm * faceNormal) * metric;
	    const Scalar energyFlux = (momentumFlux & UFace) + kappaFace * dT * metric;

	    // 将面通量累加到残值中
        residualMass.AddValue(ownerID, -massFlux);
        residualMomentum.AddValue(ownerID,-momentumFlux);
        residualEnergy.AddValue(ownerID, -energyFlux);
    }
}

void FlowBoundary::AddConvectiveResidualAverage()
{
    const Scalar gamma1 = flowPackage.GetMaterialNumber().gamma1;
	const Scalar gamma1Inv = 1.0 / gamma1;

	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
		const Face &face = mesh->GetFace(faceID);
		const int &ownerID = face.GetOwnerID();
		const int &neighID = face.GetNeighborID();

		// 计算面平均值
		const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
		const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
		const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
        const Scalar HFace = 0.5 * (H.GetValue(ownerID) + H.GetValue(neighID));
		const Scalar rhoEFace = rhoFace * HFace - pFace + 0.5 * rhoFace * (UFace & UFace);
		
		// 计算面通量
		const Vector faceArea = face.GetNormal() * face.GetArea();
		const Scalar UFlux = UFace & faceArea;
		const Scalar massFlux = rhoFace * UFlux;
		const Vector momentumFlux = massFlux * UFace + pFace * faceArea;
		const Scalar energyFlux = (rhoEFace + pFace) * UFlux;
		
		//累加残值
		residualMass.AddValue(ownerID, massFlux);
		residualMomentum.AddValue(ownerID, momentumFlux);
		residualEnergy.AddValue(ownerID, energyFlux);
#if defined(_EnableMultiSpecies_)
        for (int m = 0; m < speciesSize; ++m)
        {
            const Scalar fluxPhi = massFlux * 0.5 * (massFraction[m]->GetValue(ownerID) + massFraction[m]->GetValue(neighID));
            residualMassFraction[m]->AddValue(ownerID, fluxPhi);
        }
#endif

        if (updateJacobian)
        {
	    	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	    	const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        	inviscidFluxScheme->GetInviscidProjJac(UFace, rhoEFace / rhoFace, faceArea, 0.5, Jacobian_i);
            jacobian->AddBlock2Diag(ownerID, Jacobian_i);
        }
	}
}

void FlowBoundary::AddConvectiveResidualExternal()
{
    const int nDim = mesh->GetMeshDimension();
    const int lastIndex = nDim + 1;

    const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(boundaryPatchID);
    for (int index = 0; index < faceSize; ++index)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Scalar &faceArea = face.GetArea();
        const Vector &faceNormal = face.GetNormal();

        if (nodeCenter)
        {
            // 获取左右面心值
            faceValue.rhoLeft = rho.GetValue(ownerID);
            faceValue.ULeft = U.GetValue(ownerID);
            faceValue.pLeft = p.GetValue(ownerID);
            faceValue.rhoRight = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
            faceValue.URight = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
            faceValue.pRight = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));

            // 计算面通量
            faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

            // 累加残值
            residualMass.AddValue(ownerID, faceFlux.massFlux);
            residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
            residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
        }
        else
        {
            /*
            // 获取左右面心值
            faceValue.rhoLeft = rho.GetValue(ownerID);
            faceValue.ULeft = U.GetValue(ownerID);
            faceValue.pLeft = p.GetValue(ownerID);
            faceValue.rhoRight = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
            faceValue.URight = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
            faceValue.pRight = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));

            // 计算面通量
            faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

            // 累加残值
            residualMass.AddValue(ownerID, faceFlux.massFlux);
            residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
            residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
            
            continue;
            */

            const Scalar gamma = flowPackage.GetMaterialNumber().gamma;
            const Scalar gamma1 = flowPackage.GetMaterialNumber().gamma1;
            auto farValue = flowPackage.GetFlowConfigure().GetFlowReference();
            const Scalar ro0 = farValue.density;
            const Vector uu0 = farValue.velocity;
            const Scalar pp0 = farValue.pressure;

            // 面心值
            const Scalar rol = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
            const Vector uul = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
            const Scalar ppl = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));

            const Scalar ccl = sqrt(gamma * ppl / rol);
            const Scalar qnl = uul & faceNormal;
            const Scalar xma = qnl / ccl;

            // 超音速入流
            if (xma <= -1.0)
            {
                const Vector &UFace = uu0;
                const Scalar &rhoFace = ro0;
                const Scalar &pFace = pp0;
                const Scalar rhoEFace = pp0 / gamma1 + 0.5 * ro0 * (uu0 & uu0);

		        // 计算面通量
		        const Vector faceArea = face.GetNormal() * face.GetArea();
		        const Scalar UFlux = UFace & faceArea;
		        const Scalar massFlux = rhoFace * UFlux;
		        const Vector momentumFlux = massFlux * UFace + pFace * faceArea;
		        const Scalar energyFlux = (rhoEFace + pFace) * UFlux;

		        //累加残值	
		        residualMass.AddValue(ownerID, massFlux);
		        residualMomentum.AddValue(ownerID, momentumFlux);
		        residualEnergy.AddValue(ownerID, energyFlux);

                // Jacobian贡献为零
            }
            // 亚音速入流
            else if (xma > -1.0 && xma < 0.0)
            {
                faceValue.rhoLeft = rol;
                faceValue.ULeft = uul;
                faceValue.pLeft = ppl;
                faceValue.rhoRight = ro0;
                faceValue.URight = uu0;
                faceValue.pRight = pp0;

                // 计算面通量
                faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

                // 累加残值
                residualMass.AddValue(ownerID, faceFlux.massFlux);
                residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
                residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
            }
            // 亚音速出流
            else if (xma >= 0.0 && xma < 1.0)
            {
                const Vector &UFace = uul;
                const Scalar &rhoFace = rol;
                const Scalar &pFace = pp0;
                const Scalar rhoHFace = 0.5 * rol * (uul & uul) + pp0 * gamma / gamma1;

		        // 计算面通量
		        const Vector faceArea = face.GetNormal() * face.GetArea();
		        const Scalar UFlux = UFace & faceArea;
		        const Scalar massFlux = rhoFace * UFlux;
		        const Vector momentumFlux = massFlux * UFace + pFace * faceArea;
		        const Scalar energyFlux = rhoHFace * UFlux;

		        //累加残值	
		        residualMass.AddValue(ownerID, massFlux);
		        residualMomentum.AddValue(ownerID, momentumFlux);
		        residualEnergy.AddValue(ownerID, energyFlux);

                if (updateJacobian)
                {
                    Jacobian_i.SetZero();

                    // 动量方程对守恒量的导数
                    const Vector temp0 = - UFace * UFlux;
                    const Tensor temp1 = UFace * faceArea;
                    const Scalar temp2 = UFlux;
                    const Scalar temp4 = rhoHFace / rhoFace;
                    
                    if (nDim == 3)
                    {
                        // 质量通量对守恒量的导数
                        Jacobian_i(0, 0) = 0.0;
                        Jacobian_i(0, 1) = faceArea.X();
                        Jacobian_i(0, 2) = faceArea.Y();
                        Jacobian_i(0, 3) = faceArea.Z();
                        Jacobian_i(0, lastIndex) = 0.0;

                        // 动量通量对守恒量的导数
                        Jacobian_i(1, 0) = temp0.X();
                        Jacobian_i(2, 0) = temp0.Y();
                        Jacobian_i(3, 0) = temp0.Z();
                        Jacobian_i(1, 1) = temp1.XX(); Jacobian_i(1, 2) = temp1.XY();  Jacobian_i(1, 3) = temp1.XZ();
                        Jacobian_i(2, 1) = temp1.YX(); Jacobian_i(2, 2) = temp1.YY();  Jacobian_i(2, 3) = temp1.YZ();
                        Jacobian_i(3, 1) = temp1.ZX(); Jacobian_i(3, 2) = temp1.ZY();  Jacobian_i(3, 3) = temp1.ZZ();
                        Jacobian_i(1, 1) += temp2;
                        Jacobian_i(2, 2) += temp2;
                        Jacobian_i(3, 3) += temp2;
                        Jacobian_i(1, lastIndex) = 0.0;
                        Jacobian_i(2, lastIndex) = 0.0;
                        Jacobian_i(3, lastIndex) = 0.0;
                        
                        // 能量通量对守恒量的导数
                        Jacobian_i(lastIndex, 0) = -(0.5 * (UFace & UFace) + temp4) * UFlux;
                        Jacobian_i(lastIndex, 1) = -temp0.X() + temp4 * faceArea.X();
                        Jacobian_i(lastIndex, 2) = -temp0.Y() + temp4 * faceArea.Y();
                        Jacobian_i(lastIndex, 3) = -temp0.Z() + temp4 * faceArea.Z();
                        Jacobian_i(lastIndex, lastIndex) = 0.0;
                    }
                    else
                    {
                        // 质量通量对守恒量的导数
                        Jacobian_i(0, 0) = 0.0;
                        Jacobian_i(0, 1) = faceArea.X();
                        Jacobian_i(0, 2) = faceArea.Y();
                        Jacobian_i(0, lastIndex) = 0.0;

                        // 动量通量对守恒量的导数
                        Jacobian_i(1, 0) = temp0.X();
                        Jacobian_i(2, 0) = temp0.Y();
                        Jacobian_i(1, 1) = temp1.XX(); Jacobian_i(1, 2) = temp1.XY();
                        Jacobian_i(2, 1) = temp1.YX(); Jacobian_i(2, 2) = temp1.YY();
                        Jacobian_i(1, 1) += temp2;
                        Jacobian_i(2, 2) += temp2;
                        Jacobian_i(1, lastIndex) = 0.0;
                        Jacobian_i(2, lastIndex) = 0.0;
                        Jacobian_i(3, lastIndex) = 0.0;
                        
                        // 能量通量对守恒量的导数
                        Jacobian_i(lastIndex, 0) = -(0.5 * (UFace & UFace) + temp4) * UFlux;
                        Jacobian_i(lastIndex, 1) = -temp0.X() + temp4 * faceArea.X();
                        Jacobian_i(lastIndex, 2) = -temp0.Y() + temp4 * faceArea.Y();
                        Jacobian_i(lastIndex, lastIndex) = 0.0;
                    }
                    jacobian->AddBlock2Diag(ownerID, Jacobian_i);
                }
            }
            // 超音速出流
            else if (xma >= 1.0)
            {
                const Vector &UFace = uul;
                const Scalar &rhoFace = rol;
                const Scalar &pFace = ppl;
                const Scalar rhoEFace = ppl / gamma1 + 0.5 * rol * (uul & uul);

		        // 计算面通量
		        const Vector faceArea = face.GetNormal() * face.GetArea();
		        const Scalar UFlux = UFace & faceArea;
		        const Scalar massFlux = rhoFace * UFlux;
		        const Vector momentumFlux = massFlux * UFace + pFace * faceArea;
		        const Scalar energyFlux = (rhoEFace + ppl) * UFlux;

		        //累加残值	
		        residualMass.AddValue(ownerID, massFlux);
		        residualMomentum.AddValue(ownerID, momentumFlux);
		        residualEnergy.AddValue(ownerID, energyFlux);

                if (updateJacobian)
                {
                    inviscidFluxScheme->GetInviscidProjJac(UFace, rhoEFace / rhoFace, faceArea, 1.0, Jacobian_i);
                    jacobian->AddBlock2Diag(ownerID, Jacobian_i);
                }
            }
        }
    }
}

void FlowBoundary::AddConvectiveResidualReflected()
{
    const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(boundaryPatchID);
    for (int index = 0; index < faceSize; ++index)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        // 获取左右面心值
        if (nodeCenter)
        {
            faceValue.rhoLeft = rho.GetValue(ownerID);
            faceValue.ULeft = U.GetValue(ownerID);
            faceValue.pLeft = p.GetValue(ownerID);
            faceValue.rhoRight = rho.GetValue(neighID);
            faceValue.URight = U.GetValue(neighID);
            faceValue.pRight = p.GetValue(neighID);
        }
        else
        {
            faceValue = inviscidFluxScheme->GetFaceLeftRightValue(faceID);
            faceValue.rhoRight = faceValue.rhoLeft;
            faceValue.URight = faceValue.ULeft - 2.0 * (faceValue.ULeft & face.GetNormal()) * face.GetNormal();
            faceValue.pRight = faceValue.pLeft;
        }
        faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

        // 累加残值
        residualMass.AddValue(ownerID, faceFlux.massFlux);
        residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
        residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
    }
}

void FlowBoundary::AddConvectiveResidualSlip()
{
    if (implicitFlag) Jacobian_i.SetZero();

    const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(boundaryPatchID);
    for (int index = 0; index < faceSize; ++index)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        // 获取左右面心值
        if (nodeCenter)
        {
            // 获取左右面心值
            faceValue.rhoLeft = rho.GetValue(ownerID);
            faceValue.ULeft = U.GetValue(ownerID);
            faceValue.pLeft = p.GetValue(ownerID);
            faceValue.rhoRight = rho.GetValue(neighID);
            faceValue.URight = U.GetValue(neighID);
            faceValue.pRight = p.GetValue(neighID);

            // 计算面通量
            faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

            // 累加残值
            residualMass.AddValue(ownerID, faceFlux.massFlux);
            residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
            residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
        }
        else
        {
            /*
            // 获取左右面心值
            // faceValue = inviscidFluxScheme->GetFaceLeftRightValue(faceID);
            faceValue.rhoLeft = rho.GetValue(ownerID);
            faceValue.ULeft = U.GetValue(ownerID);
            faceValue.pLeft = p.GetValue(ownerID);
            faceValue.rhoRight = faceValue.rhoLeft;
            faceValue.URight = faceValue.ULeft;
            faceValue.pRight = faceValue.pLeft;
            faceValue.ULeft -= (faceValue.ULeft & face.GetNormal()) * face.GetNormal();
            faceValue.URight = (faceValue.URight & face.GetNormal()) * face.GetNormal();

            // 计算面通量
            faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

            // 累加残值
            residualMass.AddValue(ownerID, faceFlux.massFlux);
            residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
            residualEnergy.AddValue(ownerID, faceFlux.energyFlux);

            continue;
            */

            // 累加残值
            const Vector faceArea = face.GetNormal() * face.GetArea();
            const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
            residualMomentum.AddValue(ownerID, pFace * faceArea);

            if (updateJacobian)
            {
                /*
                 dF / dW = | dMass / dRho, dMass / dRhoU, dMass / drhoE | = |       0.0,        0.0,        0.0 |
                           | dMomX / dRho, dMomX / dRhoU, dMomX / drhoE |   | Sx dPdRho, Sx dPdRhoU, Sx dPdRhoE |
                           | dMomY / dRho, dMomY / dRhoU, dMomY / drhoE |   | Sy dPdRho, Sy dPdRhoU, Sy dPdRhoE |
                           | dMomZ / dRho, dMomZ / dRhoU, dMomZ / drhoE |   | Sz dPdRho, Sz dPdRhoU, Sz dPdRhoE |
                           | dEneg / dRho, dEneg / dRhoU, dEneg / drhoE |   |       0.0,        0.0,        0.0 |
                */

                const int nDim = mesh->GetMeshDimension();
                const int lastIndex = nDim + 1;
                const Scalar scale = 0.5;
                const Scalar gamma1 = flowPackage.GetMaterialNumber().gamma1;
                const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
                const Scalar dPdRho = scale * gamma1 * 0.5 * (UFace & UFace);
                const Vector dPdRhoU = -scale * gamma1 * UFace;
                const Scalar dPdRhoE = scale * gamma1;

                Jacobian_i(1, 0) = dPdRho * faceArea.X();
                Jacobian_i(1, 1) = dPdRhoU.X() * faceArea.X();
                Jacobian_i(1, 2) = dPdRhoU.Y() * faceArea.X();
                Jacobian_i(1, lastIndex) = dPdRhoE * faceArea.X();

                Jacobian_i(2, 0) = dPdRho * faceArea.Y();
                Jacobian_i(2, 1) = dPdRhoU.X() * faceArea.Y();
                Jacobian_i(2, 2) = dPdRhoU.Y() * faceArea.Y();
                Jacobian_i(2, lastIndex) = dPdRhoE * faceArea.Y();

                if (nDim == 3)
                {
                    Jacobian_i(3, 0) = dPdRho * faceArea.Z();
                    Jacobian_i(3, 1) = dPdRhoU.X() * faceArea.Z();
                    Jacobian_i(3, 2) = dPdRhoU.Y() * faceArea.Z();
                    Jacobian_i(3, lastIndex) = dPdRhoE * faceArea.Z();
                    
                    Jacobian_i(1, 3) = dPdRhoU.Z() * faceArea.X();
                    Jacobian_i(2, 3) = dPdRhoU.Z() * faceArea.Y();
                    Jacobian_i(3, 3) = dPdRhoU.Z() * faceArea.Z();
                }

                jacobian->AddBlock2Diag(ownerID, Jacobian_i);
            }
        }
    }
}

void FlowBoundary::AddConvectiveResidualNoSlip()
{
    if (implicitFlag) Jacobian_i.SetZero();

    const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(boundaryPatchID);
    for (int index = 0; index < faceSize; ++index)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        // 获取左右面心值
        if (nodeCenter)
        {
            faceValue.rhoLeft = rho.GetValue(ownerID);
            faceValue.ULeft = U.GetValue(ownerID);
            faceValue.pLeft = p.GetValue(ownerID);
            faceValue.rhoRight = rho.GetValue(neighID);
            faceValue.URight = U.GetValue(neighID);
            faceValue.pRight = p.GetValue(neighID);

            // 计算面通量
            faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);

            // 累加残值
            residualMass.AddValue(ownerID, faceFlux.massFlux);
            residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
            residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
        }
        else
        {
            /*
            faceValue = inviscidFluxScheme->GetFaceLeftRightValue(faceID);
            faceValue.ULeft = Vector0;
            faceValue.URight = Vector0;
            
            // 计算面通量
            faceFlux = inviscidFluxScheme->FaceFluxCalculate(faceID, faceValue);
    
            // 累加残值
            residualMass.AddValue(ownerID, faceFlux.massFlux);
            residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
            residualEnergy.AddValue(ownerID, faceFlux.energyFlux);

            continue;
            */
            
            // 累加残值
            const Vector faceArea = face.GetNormal() * face.GetArea();
            const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
            residualMomentum.AddValue(ownerID, pFace * faceArea);

            if (updateJacobian)
            {
                /*
                 dF / dW = | dMass / dRho, dMass / dRhoU, dMass / drhoE | = |       0.0,        0.0,        0.0 |
                           | dMomX / dRho, dMomX / dRhoU, dMomX / drhoE |   | Sx dPdRho, Sx dPdRhoU, Sx dPdRhoE |
                           | dMomY / dRho, dMomY / dRhoU, dMomY / drhoE |   | Sy dPdRho, Sy dPdRhoU, Sy dPdRhoE |
                           | dMomZ / dRho, dMomZ / dRhoU, dMomZ / drhoE |   | Sz dPdRho, Sz dPdRhoU, Sz dPdRhoE |
                           | dEneg / dRho, dEneg / dRhoU, dEneg / drhoE |   |       0.0,        0.0,        0.0 |
                */

                const int nDim = mesh->GetMeshDimension();
                const int lastIndex = nDim + 1;
                const Scalar scale = 0.5;
                const Scalar gamma1 = flowPackage.GetMaterialNumber().gamma1;
                const Scalar dPdRhoE = scale * gamma1;
                Jacobian_i(1, lastIndex) = dPdRhoE * faceArea.X();
                Jacobian_i(2, lastIndex) = dPdRhoE * faceArea.Y();
                if (nDim == 3) Jacobian_i(3, lastIndex) = dPdRhoE * faceArea.Z();

                jacobian->AddBlock2Diag(ownerID, Jacobian_i);
            }
        }
    }
}

void FlowBoundary::AddMRFConvectiveResidualAverage()
{
	const std::shared_ptr<MRFZONE>mrf_tem = flowPackage.GetMRF();
	if(mrf_tem)
	{
		Boundary::Type boundary_type = flowPackage.GetFlowConfigure().GetLocalBoundary(0, boundaryPatchID).type;
		const Scalar gamma1 = flowPackage.GetMaterialNumber().gamma1;
		const Scalar gamma1Inv = 1.0 / gamma1;
		if(boundary_type != 202)
		{
			for (int index = 0; index < boundaryFaceSize; ++index)
			{
				// 得到面相关信息
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
				const Face &face = mesh->GetFace(faceID);
				const int &ownerID = face.GetOwnerID();
				const int &neighID = face.GetNeighborID();
				const Vector &faceArea = face.GetNormal() * face.GetArea();

				// 计算面平均值
				const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
				const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
				const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
    			const Scalar rhoEFace = pFace * gamma1Inv + 0.5 * rhoFace * (UFace & UFace);

				Vector omegar;
				omegar = mrf_tem->oumu ^ (face.GetCenter() - mrf_tem->origin);
				
				// 计算面通量
				residualMass.AddValue(ownerID, -rhoFace*(omegar & faceArea));
				residualMomentum.AddValue(ownerID, -rhoFace*(omegar & faceArea)*UFace);
				residualEnergy.AddValue(ownerID, -rhoEFace*(omegar & faceArea));
			}
		}
	}
}

template void FlowBoundary::BoundFromElement(ElementField<Scalar> &phi);
template void FlowBoundary::BoundFromElement(ElementField<Vector> &phi);
template<class Type>
void FlowBoundary::BoundFromElement(ElementField<Type> &phi)
{
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        
        phi.SetValue(neighID, phi.GetValue(ownerID));
    }
}

template void FlowBoundary::SetBoundaryNormalZero(ElementField<Vector> *phi);
template void FlowBoundary::SetBoundaryNormalZero(ElementField<Tensor> *phi);
template<class Type>
void FlowBoundary::SetBoundaryNormalZero(ElementField<Type> *phi)
{
    if (!nodeCenter || phi == nullptr) return;
	
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const Vector &faceNormal = face.GetNormal();
        
        const Type &value = phi->GetValue(ownerID);
        phi->SetValue(ownerID, value - ( faceNormal * Dot(faceNormal, value)) );
    }
}

void FlowBoundary::SetFluxScheme( Flux::Flow::Inviscid::InviscidFluxScheme *inviscidFluxScheme_,
                                  Flux::Flow::Viscous::ViscousFluxScheme *viscousFluxScheme_ )
{
    if (implicitFlag)
    {
        const int currentLevel = flowPackage.GetMeshStruct().level;
        if (flowPackage.GetFlowConfigure().GetFluxScheme(currentLevel).inviscid == Flux::Flow::Inviscid::CENTRAL)
        {
            inviscidFluxScheme = new Flux::Flow::Inviscid::RoeScheme(flowPackage, nullptr, nullptr);
        }
        else
        {
            inviscidFluxScheme = inviscidFluxScheme_;
        }
        viscousFluxScheme = viscousFluxScheme_;
    }
}

void FlowBoundary::UpdateExtras()
{
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();
        
        const Scalar TOwner = T.GetValue(ownerID);
        const Scalar TNeigh = T.GetValue(neighID);
        const Scalar TFace = 0.5 * (TOwner + TNeigh);

        A.SetValue(ownerID, material.GetSoundSpeed(TOwner));
        A.SetValue(neighID, 2.0 * material.GetSoundSpeed(TFace) - A.GetValue(ownerID));
        H.SetValue(ownerID, material.Enthalpy(TOwner));
        H.SetValue(neighID, 2.0 * material.Enthalpy(TFace) - H.GetValue(ownerID));
        
        if (muLaminar != nullptr)
        {
            muLaminar->SetValue(ownerID, material.Mu(TOwner));
            muLaminar->SetValue(neighID, 2.0 * material.Mu(TFace) - muLaminar->GetValue(ownerID));
        }
        
#if defined(_EnableMultiSpecies_)
		for (int m = 0; m < speciesSize; ++m)
			massFraction[m]->SetValue(neighID, massFraction[m]->GetValue(ownerID));
#endif
    }
}

#if defined(_EnableMultiSpecies_)
void FlowBoundary::AddConvectiveResidualMultiSpecies()
{
	// 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
			const Face &face = mesh->GetFace(faceID);
			const int &ownerID = face.GetOwnerID();
			const int &neighID = face.GetNeighborID();

			const Vector UFace = U.GetValue(ownerID) + U.GetValue(neighID);
			const Scalar rhoFace = rho.GetValue(ownerID) + rho.GetValue(neighID);
			const Scalar rhoUFlux = 0.25 * rhoFace * (UFace & face.GetNormal()) * face.GetArea();
			for (int m = 0; m < speciesSize; ++m)
			{
				const Scalar fluxPhi = rhoUFlux * 0.5 * (massFraction[m]->GetValue(ownerID) + massFraction[m]->GetValue(neighID));
				residualMassFraction[m]->AddValue(ownerID, fluxPhi);
			}
		}
	}
}

void FlowBoundary::AddDiffusiveResidualMultiSpecies()
{
    if (nodeCenter) return;

	// 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
			const Face &face = mesh->GetFace(faceID);
			const int &ownerID = face.GetOwnerID();
			const int &neighID = face.GetNeighborID();

			const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
			const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
			const Scalar TFace = 0.5 * (T.GetValue(ownerID) + T.GetValue(neighID));
            
			std::vector<Scalar> massFractionFace(speciesSize);
			for (int k = 0; k < speciesSize; k++)
				massFractionFace[k] = 0.5 * (massFraction[k]->GetValue(ownerID) + massFraction[k]->GetValue(neighID));
            
			const Scalar metric = face.GetArea() / distance[faceID];

			// 计算面通量, 不计算切向部分的贡献
			Scalar energyFlux = Scalar0;
			for (int m = 0; m < speciesSize; ++m)
			{
				const Scalar diffCoeff = material.Diffusion(TFace, pFace, massFractionFace, m);

				const Scalar deltaPhi = massFraction[m]->GetValue(neighID) - massFraction[m]->GetValue(ownerID);
				const Scalar fluxPhiLeft = rhoFace * diffCoeff * metric * deltaPhi;
				residualMassFraction[m]->AddValue(ownerID, -fluxPhiLeft); //扩散项对于残差是负号

				const Scalar &enthalpySpecies = material.EnthalpyPureSpecies(TFace, m);
				energyFlux = energyFlux + enthalpySpecies * fluxPhiLeft;
			}
			residualEnergy.AddValue(ownerID, -energyFlux);
		}
	}
}
#endif

}// namespace Flow
}// namespace Boundary
