﻿//------------------------------修改日志----------------------------------------
//
// 2024-3-21 YQ
// 说明：建立
// 
//------------------------------------------------------------------------------
#include <vector>
#include <iostream>
#include <fstream>
#include <string>

#include "Eigen/Dense"

#include "sourceFlow/boundaryCondition/WallGivenHeatFlux.h"

#ifdef RBF_YQ
#include "basic/common/RBF_interpolation.hpp"
#else
#include "basic/common/InterpolationTools.h"
#endif


// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{

WallGivenHeatFlux::WallGivenHeatFlux(const int& boundaryPatchID, Package::FlowPackage& data, const std::string& fileName_)
: <PERSON>(boundaryPatchID, data), 
	fileName(fileName_), 
	R(data.GetMaterialNumber().R),
	PrL(data.GetMaterialNumber().PrandtlLaminar),
	PrT(data.GetMaterialNumber().PrandtlTurbulent),
	Cp(data.GetMaterialNumber().Cp)
{
}

void WallGivenHeatFlux::Initialize()
{
	// 初始化给定分布热通量壁面边界条件
	this->UpdateBoundaryCondition();
}

void WallGivenHeatFlux::UpdateBoundaryCondition()
{
	//this->BoundFromElement(rho);
	this->BoundFromElement(p);
	//this->BoundFromElement(T);
	this->SetVelocityAndMu();

	this->ReadFile();
	this->InterpolateHeatFlux();

	const auto *muTurbulent = flowPackage.GetField().muTurbulent;
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		const int faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int neighID = mesh->GetFace(faceID).GetNeighborID();

		Scalar kappa = Cp * muLaminar->GetValue(ownerID) / PrL;
		if (muTurbulent) kappa += Cp * muTurbulent->GetValue(ownerID) / PrT;
		Vector wallNormVector = mesh->GetElement(ownerID).GetCenter() - mesh->GetFace(faceID).GetCenter();
		Scalar wallDist = wallNormVector.Mag();
		Scalar wallTemperature = T.GetValue(ownerID) - HeatFluxInterp[faceID] * wallDist / kappa;
		const Scalar soundSpeedTemp = material.GetSoundSpeed(wallTemperature);
		if (nodeCenter)
		{
			T.SetValue(ownerID, wallTemperature);
			T.SetValue(neighID, wallTemperature);

			p.SetValue(ownerID, R * rho.GetValue(ownerID) * wallTemperature);
			p.SetValue(neighID, p.GetValue(ownerID));
		}
		else
		{
			T.SetValue(neighID, 2.0 * wallTemperature - T.GetValue(ownerID));
			p.SetValue(neighID, 2 * R * rho.GetValue(ownerID) * wallTemperature - p.GetValue(ownerID));
		}
	}
	return;
}

void WallGivenHeatFlux::AddConvectiveResidual()
{
    if (implicitFlag)
    {
        if (!muLaminar) this->AddConvectiveResidualSlip();
        else if (!nodeCenter) this->AddConvectiveResidualNoSlip();
    }
    else
    {
        this->AddConvectiveResidualAverage();
    }

    this->AddMRFConvectiveResidualAverage();
}

void WallGivenHeatFlux::AddDiffusiveResidual()
{
	if (!nodeCenter)
	{
		this->AddDiffusiveResidualCellCenter();
		return;
	}

	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
		const Face &face = mesh->GetFace(faceID);
		const int &ownerID = face.GetOwnerID();
		const int &innerID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, index);
		const Scalar metric = face.GetArea() / distance[faceID];

		// 计算面通量
		const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(innerID) + flowPackage.CalculateKappa(ownerID));
		const Scalar dT = T.GetValue(ownerID) - T.GetValue(innerID);
		const Scalar energyFlux = kappaFace * dT * metric;
		residualEnergy.AddValue(ownerID, -energyFlux);
	}

	return;
}

void WallGivenHeatFlux::UpdateBoundaryResidual()
{
	if (!nodeCenter) return;

	if (muLaminar != nullptr) this->UpdateBoundaryResidualStatic();
	else                      this->UpdateBoundaryResidualSlipping();
}

void WallGivenHeatFlux::InterpolateHeatFlux()
{
#ifdef RBF_YQ
	int DataNum = coorSrc.size();
	MatrixXd mSrcCoor(DataNum, 3);
	MatrixXd mSrcHF(DataNum, 1);
	for (int idata = 0; idata < DataNum; idata++)
	{
		mSrcCoor.row(idata) << coorSrc[idata][0], coorSrc[idata][1], coorSrc[idata][2];
		mSrcHF.row(idata) << HeatSrc[idata];
	}
	//cout << "mSrcCoor" << endl<< mSrcCoor << endl;
	//cout << "mSrcHF" << endl<< mSrcHF << endl;
	MatrixXd rbfcoeff;
	rbfcoeff = rbfcreate(mSrcCoor, mSrcHF, rbfphi_multiquadrics, 0.4444, 0);
	MatrixXd mInterpCoor(boundaryFaceSize, 3);
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		const int faceID = mesh->GetBoundaryFaceID(boundaryPatchID, j);
		const Vector faceCentCoor = mesh->GetFace(faceID).GetCenter();
		mInterpCoor.row(j) << faceCentCoor.X(), faceCentCoor.Y(), faceCentCoor.Z();
	}
	//cout << "mInterpCoor" << endl << mInterpCoor << endl;
	MatrixXd mInterpT;
	mInterpT = rbfinterp(mSrcCoor, rbfcoeff, mInterpCoor, rbfphi_multiquadrics, 0.4444);
	//cout << "mInterpT" << endl << mInterpT << endl;
	HeatFluxInterp.resize(boundaryFaceSize, 0);
	for (int j = 0; j < boundaryFaceSize; ++j) HeatFluxInterp[j] = mInterpT[j];
#else
	// TODO:没考虑并行和插值选点，并行计算太耗时
	int DataNum = coorSrc.size();
	std::vector<Vector> rPos(DataNum, Vector0);
	HeatFluxInterp.resize(boundaryFaceSize, 0);
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		const int faceID = mesh->GetBoundaryFaceID(boundaryPatchID, j);
		const Vector faceCentCoor = mesh->GetFace(faceID).GetCenter();
		for (int idata = 0; idata < DataNum; idata++)
		{
			rPos[idata] = Vector(coorSrc[idata][0], coorSrc[idata][1], coorSrc[idata][2]) - faceCentCoor;
		}
		HeatFluxInterp[j] = RBFInterpolation<Scalar>(rPos, HeatSrc);
	}
#endif
}

void WallGivenHeatFlux::ReadFile()
{
	//假定文件格式如下
	// NP   #热通量点数
	// x1 y1 z1 HF1 # 第一点热通量
	// x2 y2 z2 HF2 # 第2点热通量
	// ......
	// xNP yNP zNP HFNP # 第NP点热通量

	std::ifstream fin(fileName);

	if (!fin.is_open())
		FatalError("WallGivenHeatFlux::ReadFile: " + fileName + " in not opened...");

	int Num;
	fin >> Num;
	coorSrc.resize(Num, std::vector<double>{0., 0., 0.});
	HeatSrc.resize(Num, 0.);

	for (int irec = 0; irec < Num; irec++)
	{
		fin >> coorSrc[irec][0] >> coorSrc[irec][1] >> coorSrc[irec][2] >> HeatSrc[irec];
	}
}

}// namespace Flow
}// namespace Boundary