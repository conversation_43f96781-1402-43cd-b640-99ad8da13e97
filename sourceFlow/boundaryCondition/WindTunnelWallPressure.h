﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WindTunnelWallPressure.h
//! <AUTHOR>
//! @brief 风洞洞壁的壁压边界条件
//! @date  2023-2-21
//
//------------------------------修改日志----------------------------------------
//
// 2023-2-21 王祥云、李艳亮
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_WindTunnelWallPressure_
#define _sourceFlow_boundaryCondition_WindTunnelWallPressure_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"
#include "sourceFlow/boundaryCondition/FarField.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 风洞洞壁的壁压边界条件类
 * 
 */
	class WindTunnelWallPressure : public FlowBoundary
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] fileName_ 壁压信息文件
     */
	WindTunnelWallPressure(const int &boundaryPatchID, Package::FlowPackage &data, const std::string &fileName_, Flux::Flow::Precondition::Precondition *precondition_);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 边界条件更新
     * 
     */
    void UpdateBoundaryCondition();

	void UpdateBoundaryConditionNonP();

	void UpdateBoundaryConditionP();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();
    
    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();

private:
    /**
    * @brief 插值计算边界面心处的压强值
    *
    */
    void CalculatePressure();

    /**
    * @brief 针对给定测压带，计算给定X坐标处压强
    *
    * @param[in] pos X坐标
    * @param[in] posList 测压带上探针坐标列表
    * @param[in] valueList 测压带上探针压强列表
    */
    Scalar CalculateSPLine(const Scalar &pos, const std::vector<Scalar> &posList, const std::vector<Scalar> &valueList);

    /**
    * @brief 利用不同测压带截面，插值计算压强值
    *
    * @param[in] coor Y坐标或Z坐标
    * @param[in] lx 截面坐标列表
    * @param[in] ly 截面压强列表
    */
    Scalar CalculateLagrange(const Scalar &coor, const std::vector<Scalar> &lx, const std::vector<Scalar> &ly);
    
    /**
    * @brief 读用户指定的壁压文件
    *
    */
    void ReadFile();

protected:
	Flux::Flow::Precondition::Precondition *precondition; ///< 预处理指针
    
private:
    struct Band ///< 测压管数据信息
    {
        std::vector<Scalar> coorX; ///< 每个探针的x坐标值
        std::vector<Scalar> pressure; ///< 每个探针的压强值        
        Scalar coorY; ///< yFlag为false时当前测压管的y坐标
        Scalar coorZ; ///< yFlag为true时当前测压管的z坐标
    };

    std::vector<Band> bandList; ///< 测压管容器
    bool yFlag; ///< 所有测压管的y坐标是否相同,ture为y坐标相同,false为z坐标相同
    //const std::string &fileName; ///< 测压数据文件<版本1
    const std::string fileName; ///< 测压数据文件

    std::vector<Scalar> pbList; ///< 边界面压强容器    

    Scalar rho_inf; ///< 外场密度
    Vector velocity_inf; ///< 外场速度
    Scalar Pressure_inf; ///< 外场压强
    Scalar soundSpeed_inf; ///< 外场音速
    Scalar turbulentViscosityRatio; ///< 自由流湍流与层流的粘性比
};

} // namespace Flow
} // namespace Boundary


#endif
