﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MassFlow.h
//! <AUTHOR>
//! @brief MassFlow为质量流量入口/出口类
//! @date  2024-5-31
//
//------------------------------修改日志----------------------------------------
//
// 2024-5-31 YQ
// 说明：新建
// 
//------------------------------------------------------------------------------
#ifndef  _sourceFlow_boundaryCondition_MassFlow_
#define  _sourceFlow_boundaryCondition_MassFlow_

#include "sourceFlow/boundaryCondition/ExternalBoundary.h"
#include "sourceFlow/fluxScheme/precondition/Precondition.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 总条件入口边界条件类
 * 
 */
class MassFlow : public ExternalBoundary
{
public:
    /**
     * @brief 构造函数，创建质量流量入口边界条件对象
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
	 * @param[in] massFlowRate_ 目标质量流率
     * @param[in] massFlowRate_ 实际质量流率
     * @param[in] alpha_ 迎角（度）
     * @param[in] beta_ 侧滑角（度）
     * @param[in] precondition_ 低速预处理对象
     */
    MassFlow(const int &boundaryPatchID,
                         Package::FlowPackage &data,
                         const Scalar &massFlowRateTarget_,
						 Scalar &massFlowRate_,
                         Flux::Flow::Precondition::Precondition *precondition_);

	/**
	* @brief 初始化
	*
	*/
	void Initialize();

	/**
	* @brief 质量流量入口边界条件更新
	*
	*/
	void UpdateBoundaryCondition();
	/**
	* @brief 每时间步计算实际质量流量，计算时密度和速度从内场外推
	*
	*/
	Scalar CalculateMassFlowRate();

private:
	/**
	* @brief 质量流量入口边界具体实现
	*
	*/
	void UpdateBoundaryConditionNonPrecondition();
	/**
	* @brief 质量流量入口边界条件,防止用户指定预处理
	*
	*/
	void UpdateBoundaryConditionPrecondition();


private:
	Flux::Flow::Precondition::Precondition *precondition; ///< 预处理指针

	Scalar massFlowRateTarget; ///< 该边界处的目标质量流率  rho * Vn * A,带正负号
	Scalar &massFlowRate; ///< 该边界处每时间步计算得到的质量流率  rho * Vn * A（数据源在边界管理器中,依靠FlowBoundaryManager::CalculateMassFlowRate(const bool &initializeFlag)计算）
	//Scalar alpha; ///< 该边界处气流的迎角
	//Scalar beta; ///< 该边界处气流的侧滑角

	//Scalar turbulentViscosityRatio; ///< 自由流湍流与层流的粘性比

	const Scalar &gamma; ///< 气体比热容比\gamma
	const Scalar &gamma1; ///< gamma - 1
	Scalar gammaDivGamma1; ///< gamma / (gamma - 1)
    
};
} // namespace Flow
} // namespace Boundary

#endif
