﻿#include "sourceFlow/boundaryCondition/WallIsothermal.h"
// 边界条件命名空间
namespace Boundary
{
// 流动控制方程边界条件命名空间
namespace Flow
{
// 等温壁面边界条件
WallIsothermal::WallIsothermal(const int &boundaryPatchID, Package::FlowPackage &data, const Scalar &wallTemperature_)
    :
    Wall(boundaryPatchID, data), wallTemperature(wallTemperature_), R(flowPackage.GetMaterialNumber().R)
{
}

void WallIsothermal::Initialize()
{
    // 初始化等温壁面边界条件
    this->UpdateBoundaryCondition();
}

// 更新等温壁面边界条件    
void WallIsothermal::UpdateBoundaryCondition()
{    
    this->BoundFromElement(rho);
    this->SetVelocityAndMu();

    const Scalar soundSpeedTemp = material.GetSoundSpeed(wallTemperature);

    // 遍历等温壁面边界上的每一个面心，更新速度矢量
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        // 几何信息
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();        

        if(nodeCenter)
        {
            T.SetValue(ownerID, wallTemperature);
            T.SetValue(neighID, wallTemperature);

            p.SetValue(ownerID, R * rho.GetValue(ownerID) * wallTemperature);
            p.SetValue(neighID, p.GetValue(ownerID));
        }
        else
        {
            T.SetValue(neighID, 2.0 * wallTemperature - T.GetValue(ownerID));
            p.SetValue(neighID, 2 * R * rho.GetValue(ownerID) * wallTemperature - p.GetValue(ownerID));
        }
    }

    return;
}

void WallIsothermal::AddConvectiveResidual()
{
    this->AddMRFConvectiveResidualAverage();
}

void WallIsothermal::AddDiffusiveResidual()
{
    if(!nodeCenter)
    {
        this->AddDiffusiveResidualCellCenter();
        return;
    }
    
	for (int index = 0; index < boundaryFaceSize; ++index)
	{
		// 得到面相关信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &innerID = mesh->GetInnerElementIDForBoundaryElement(boundaryPatchID, index);
	    const Scalar metric = face.GetArea() / distance[faceID];

	    // 计算面通量
	    const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(innerID) + flowPackage.CalculateKappa(ownerID));
	    const Scalar dT = T.GetValue(ownerID) - T.GetValue(innerID);
	    const Scalar energyFlux = kappaFace * dT * metric;
        residualEnergy.AddValue(ownerID, -energyFlux);
    }

    return;
}

void WallIsothermal::UpdateBoundaryResidual()
{
    if (!nodeCenter) return;

    if (muLaminar != nullptr) this->UpdateBoundaryResidualStatic();
    else                      this->UpdateBoundaryResidualSlipping();
}

}// namespace Flow
}// namespace Boundary
