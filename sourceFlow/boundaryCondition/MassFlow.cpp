﻿#include "sourceFlow/boundaryCondition/MassFlow.h"

namespace Boundary
{
namespace Flow
{

MassFlow::MassFlow(const int &boundaryPatchID,
	Package::FlowPackage &data,
	const Scalar &massFlowRateTarget_,
	Scalar &massFlowRate_,
	Flux::Flow::Precondition::Precondition *precondition_)
	:
	ExternalBoundary(boundaryPatchID, data),
	massFlowRateTarget(massFlowRateTarget_),
	massFlowRate(massFlowRate_),
	precondition(precondition_),
	gamma(data.GetMaterialNumber().gamma),
	gamma1(data.GetMaterialNumber().gamma1),
	gammaDivGamma1(gamma / gamma1)
{
}

void MassFlow::Initialize()
{
	this->UpdateBoundaryCondition();
}

void MassFlow::UpdateBoundaryCondition()
{
	if (precondition) FatalError("MassFlowCondition::UpdateBoundaryCondition: Precondition in not supported");
	else              this->UpdateBoundaryConditionNonPrecondition();
}

void MassFlow::UpdateBoundaryConditionNonPrecondition()
{
	// 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();
		const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();

		// 取得特定面心相邻体心物理量
		const Scalar &rhoOwner = rho.GetValue(ownerID);
		const Vector &UOwner = U.GetValue(ownerID);
		const Scalar &pOwner = p.GetValue(ownerID);		
		const Scalar &TOwner = T.GetValue(ownerID);
		const Scalar aOwner = material.GetSoundSpeed(pOwner, rhoOwner);

		//massFlowRate = CalculateMassFlowRate();
		//每个时间步时由FlowBoundaryManager::CalculateMassFlowRate(const bool &initializeFlag)计算
		if (std::abs(massFlowRate) < SMALL)
			FatalError("sourceFlow/boundaryCondition/MassFlow.h: fail to initialize or update massFlowRate ");
		const Scalar ratioMass = Max(Min(massFlowRateTarget / massFlowRate, 1.05), 0.95);

		// 计算边界面的速度
		const Vector UFace = UOwner * ratioMass;

		// 计算边界面的温度、压强、密度
        const Scalar TFace = TOwner + 0.5 * gamma1 / gamma / flowPackage.GetMaterialNumber().R * (UOwner & UOwner) * (1 - ratioMass * ratioMass);  // 从能量(总焓 cp*T+0.5*v*v)守恒推得该关系式
		const Scalar pFace = pOwner * pow(TFace / TOwner,gammaDivGamma1);     // 从等熵关系推得该关系式
		const Scalar rhoFace = pFace / flowPackage.GetMaterialNumber().R / TFace;  // 热力学关系式

		// 计算边界面的层流和湍流粘性系数
		const Scalar freeStreamMuLaminar = material.Mu(TFace);
		const Scalar freeStreamMuTurbulent = freeStreamMuLaminar * flowPackage.GetFlowConfigure().GetFlowReference().turbulentViscosityRatio;
		
		// 更新虚单元值
		if (nodeCenter)
		{
			rho.SetValue(neighID, rhoFace);
			U.SetValue(neighID, UFace);
			p.SetValue(neighID, pFace);
			T.SetValue(neighID, TFace);
			rho.SetValue(ownerID, rhoFace);
			U.SetValue(ownerID, UFace);
			p.SetValue(ownerID, pFace);
			T.SetValue(ownerID, TFace);
		}
		else
		{
			rho.SetValue(neighID, 2.0 * rhoFace - rhoOwner);
			U.SetValue(neighID, 2.0 * UFace - UOwner);
			p.SetValue(neighID, 2.0 * pFace - pOwner);
			T.SetValue(neighID, 2.0 * TFace - TOwner);
		}
	}

	return;
}

void MassFlow::UpdateBoundaryConditionPrecondition()
{
}

Scalar MassFlow::CalculateMassFlowRate()
{
	Scalar massFlow_subMesh = Scalar0;

	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();

		const Vector &UOwner = U.GetValue(ownerID);// 速度外插得到      
		const Scalar &RhoOwner = rho.GetValue(ownerID);// 密度外插得到
		massFlow_subMesh += RhoOwner * (UOwner & mesh->GetFace(faceID).GetNormal())*mesh->GetFace(faceID).GetArea();
	}

	return massFlow_subMesh;
}

}// namespace Flow
}// namespace Boundary