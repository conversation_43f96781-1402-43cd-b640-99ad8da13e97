﻿#include "sourceFlow/boundaryCondition/NacelleInlet.h"

namespace Boundary
{
namespace Flow
{

NacelleInlet::NacelleInlet(const int &boundaryPatchID, Package::FlowPackage &data,
    const Scalar &eps_, const Scalar &massFlow_, const int &couplePatchIDGlobal_, Scalar &area)
    :
    ExternalBoundary(boundaryPatchID, data),
    eps(eps_), massFlow(massFlow_), couplePatchIDGlobal(couplePatchIDGlobal_), areaFan(area),
    gamma(data.GetMaterialNumber().gamma)
{
    //参数检查
    if (couplePatchIDGlobal >= 0)
    {
        //Print("NacelleInlet::NacelleInlet: 采用自动流量匹配模式...");
        updateMassFlowFlag = true;
    }
    else
    {
        updateMassFlowFlag = false;
        if (eps > SMALL)
        {
            //Print("NacelleInlet::NacelleInlet: 采用给定捕获流量比率模式...");
        }
        else
        {
            if (massFlow > SMALL)
            {
                //Print("NacelleInlet::NacelleInlet: 采用给定流量模式...");
            }                
            else
            {
                FatalError("NacelleInlet::NacelleInlet: 参数不正确，无法采用该边界...");
            }
        }        
    }

    //部分常数计算
    gamma1p = gamma + 1.0;
    gamma1m = gamma - 1.0;
    gam1 = gamma1m / gamma1p;
    div = 1.0 - gam1;
    expNum = 1.0 / gamma1m;
    gam2 = (2.0 - gamma) / gamma1m;

    //参考点参数
    pFree = flowPackage.GetFlowConfigure().GetFlowReference().pressure;
    cFree = flowPackage.GetFlowConfigure().GetFlowReference().sound;
    machFree = flowPackage.GetFlowConfigure().GetFlowReference().mach;
    rhoFree = flowPackage.GetFlowConfigure().GetFlowReference().density;
    const Scalar machFree2 = machFree * machFree;
    LaFree = sqrt(gamma1p * machFree2 / (gamma1m * machFree2 + 2.0));    

    //计算初始值
    areaFan = this->CalculateArea();    
}

void NacelleInlet::Initialize()
{
    if (eps < SMALL) CalculateEps();
    UpdateValue(CalculateLaFan(CalculateLaRef()));
    
    this->UpdateBoundaryCondition();
}

void NacelleInlet::UpdateBoundaryCondition()
{
    if (updateMassFlowFlag)
    {
        CalculateEps();
        UpdateValue(CalculateLaFan(CalculateLaRef()));
    }

    // 密度、速度边界值来自相邻单元，压强不变
    this->BoundFromElement(rho);
    this->BoundFromElement(U);

    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        if (nodeCenter)
        {
            p.SetValue(ownerID, pb);
            p.SetValue(neighID, pb);

            T.SetValue(ownerID, material.GetTemperature(p.GetValue(ownerID), rho.GetValue(ownerID)));
            T.SetValue(neighID, material.GetTemperature(p.GetValue(neighID), rho.GetValue(neighID)));
        }
        else
        {
            p.SetValue(neighID, 2.0 * pb - p.GetValue(ownerID));
            const Scalar Tb = material.GetTemperature(pb, rho.GetValue(ownerID));
            T.SetValue(neighID, 2 * Tb - T.GetValue(ownerID));
        }
    }
}

Scalar NacelleInlet::CalculateLaFan(const Scalar &LaRef)
{
    Scalar LaFan = Scalar0; //迭代初始值
    int num = 0; //记录迭代次数
    Scalar delta = INF; //Lafan的差量值    

    while (num++ < 1000000 && fabs(delta) > SMALL)
    {
        Scalar temp = 1.0 - (gamma1m / gamma1p) * LaFan * LaFan;
        Scalar LaEst = LaFan * pow(temp / div, expNum);

        delta = LaEst - LaRef;

        Scalar La1 = 1.0 - gam1 * LaEst * LaEst;
        Scalar La2 = La1 / gam1;
        Scalar dLa = pow(La1, gam2) * La2 / pow(div, expNum);

        LaFan -= (0.875 * delta / dLa);
        if (LaFan < 0.0) LaFan = 0.0;
    }

    return LaFan;
}

Scalar NacelleInlet::CalculateLaRef()
{
    const Scalar up = 1.0 - gam1 * LaFree * LaFree;
    const Scalar criticalAreaRatio = LaFree * pow(up / div, expNum);
    return eps * criticalAreaRatio;
}

void NacelleInlet::CalculateEps()
{
    eps = massFlow / (areaFan * machFree * sqrt(gamma * pFree * rhoFree));
}

Scalar NacelleInlet::CalculateArea()
{
    Scalar area = Scalar0;
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        area += mesh->GetFace(faceID).GetArea();
    }
    return area;
}

void NacelleInlet::UpdateValue(const Scalar &LaFan)
{
    const Scalar coef = gamma / gamma1m;
    const Scalar coefFree = 1.0 + 0.5 * gamma1m * machFree * machFree;
    
    // const Scalar cTotalFree = cFree * sqrt(coefFree);
    const Scalar pTotalFree = pFree * pow(coefFree, coef);

    const Scalar LaFan2 = LaFan * LaFan;
    const Scalar MFan2 = 2.0 * LaFan2 / (gamma1p - gamma1m * LaFan2);
    const Scalar coefFan = 1.0 + 0.5 * gamma1m * MFan2;

    // const Scalar cFan = cTotalFree / sqrt(coefFan);
    pb = pTotalFree / pow(coefFan, coef);
}

}// namespace Flow
}// namespace Boundary
