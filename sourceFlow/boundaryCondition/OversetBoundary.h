﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetBoundary.h
//! <AUTHOR>
//! @brief OversetBoundary为重叠网格边界
//! @date  2023-2-8
//
//------------------------------修改日志----------------------------------------
//
// 2023-2-8 曾凯 
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_OversetBoundary_
#define _sourceFlow_boundaryCondition_OversetBoundary_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

#if defined(_EnableOverset_)
#include "feilian-specialmodule/oversetMesh/OversetMesh.h"
#endif

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 重叠网格边界条件类
 * 
 */
class OversetBoundary
{
public:
    /**
     * @brief 构造函数，创建重叠网格边界条件
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     */
    OversetBoundary(Package::FlowPackage &data);

    /**
    * @brief 析构函数
    *
    */
    ~OversetBoundary();

    /**
    * @brief 初始化
    *
    */
    void Initialize();

    /**
     * @brief 更新重叠网格界面边界条件
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 更新更新边界虚单元残值
     * 
     */
    void UpdateBoundaryResidual();


private:
#if defined(_EnableOverset_)
    OversetMesh *oversetMesh;
    bool upToDate; // 重叠网格的装配状态是否更新至最新：初始为未更新，重叠模块更新后会修改为已更新；网格运动或其他情况引起运动后再改为未更新，
#endif

    Mesh *mesh; ///< 网格指针
    const Material::Flow::Materials &material; ///< 材料对象
    Package::FlowPackage &flowPackage; ///< 流场包

    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度
    ElementField<Scalar> &A; ///< 音速
    ElementField<Scalar> &H; ///< 总焓
    ElementField<Scalar> *muLaminar; ///< 层流粘性系数
    
    ElementField<Scalar> &residualMass; ///< 质量残值
    ElementField<Vector> &residualMomentum; ///< 动量残值
    ElementField<Scalar> &residualEnergy; ///< 能量残值

    const bool &nodeCenter; ///< 边界数据存储在节点标识
};

} // namespace Flow
} // namespace Boundary

#endif
