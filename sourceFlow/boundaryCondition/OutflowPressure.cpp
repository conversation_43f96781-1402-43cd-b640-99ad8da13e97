﻿#include "sourceFlow/boundaryCondition/OutflowPressure.h"

namespace Boundary
{
namespace Flow
{

OutflowPressure::OutflowPressure(const int &boundaryPatchID, Package::FlowPackage &data, const Scalar &pOut_)
    :
    ExternalBoundary(boundaryPatchID, data), pOut(pOut_)
{
}

void OutflowPressure::Initialize()
{
    this->UpdateBoundaryCondition();
}

void OutflowPressure::UpdateBoundaryCondition()
{
    // 密度、速度边界值来自相邻单元，压强不变
    this->BoundFromElement(rho);
    this->BoundFromElement(U);
    
    // 更新边界温度
    for (int j = 0; j < boundaryFaceSize; ++j)
    {
        const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        const Scalar Tb = material.GetTemperature(pOut, rho.GetValue(ownerID));
        p.SetValue(neighID, 2.0 * pOut - p.Get<PERSON>alue(ownerID));
        T.SetValue(neighID, 2 * Tb - T.GetValue(ownerID));
    }
}

}// namespace Flow
}// namespace Boundary