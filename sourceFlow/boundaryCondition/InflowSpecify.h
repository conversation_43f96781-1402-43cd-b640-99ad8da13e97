﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file InflowSpecify.h
//! <AUTHOR> 尹强
//! @brief InflowSpecify为指定原始变量(rho,u,v,w,p)边界条件类
//! @date  2021-3-30
//
//------------------------------修改日志----------------------------------------
//
// 2021-03-30 尹强 
// 说明：添加注释并规范化
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_InflowSpecify_
#define _sourceFlow_boundaryCondition_InflowSpecify_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 指定原始变量(rho,u,v,w,p)边界条件类
 * 
 */
class InflowSpecify :public FlowBoundary
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] density 给定密度
     * @param[in] Velocity 给定速度矢量
     * @param[in] pressure 给定压强
     */
    InflowSpecify(const int &boundaryPatchID, Package::FlowPackage &data, 
        const Scalar &density, const Vector &velocity, const Scalar &pressure);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();

    /**
     * @brief 边界条件具体实现，负责更新（实际不进行任何操作，初始化后不需要更新）
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();
    
    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();
    
private:
    Scalar rhoSpecified; ///< 指定的密度
    Vector velocitySpecified; ///< 指定的速度
    Scalar pressureSpecified; ///< 指定的压强
    Scalar temperatureSpecified; ///< 根据指定压强和密度计算得到的温度

    Scalar turbulentViscosityRatio; ///< 自由流湍流与层流的粘性比
};

} // namespace Flow
} // namespace Boundary


#endif
