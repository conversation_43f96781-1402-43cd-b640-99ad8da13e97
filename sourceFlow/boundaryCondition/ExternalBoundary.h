﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ExternalBoundary.h
//! <AUTHOR>
//! @brief 外部边界（远场、压力出口等边界的积累）
//! @date  2024-1-25
//
//------------------------------修改日志----------------------------------------
//
// 2024-1-25 李艳亮、乔龙
// 说明：建立
// 
//------------------------------------------------------------------------------

#ifndef _sourceFlow_boundaryCondition_ExternalBoundary_
#define _sourceFlow_boundaryCondition_ExternalBoundary_

#include "sourceFlow/boundaryCondition/FlowBoundary.h"

/**
 * @brief 边界条件命名空间
 * 
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 外部边界条件类
 * 
 */
class ExternalBoundary : public FlowBoundary
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     */
    ExternalBoundary(const int &boundaryPatchID, Package::FlowPackage &data);

    /**
     * @brief 初始化
     * 
     */
    virtual void Initialize() = 0;

    /**
     * @brief 边界条件更新
     * 
     */
    virtual void UpdateBoundaryCondition() = 0;

    /**
     * @brief 计算边界粘性残值
     * 
     */
    void AddDiffusiveResidual();

    /**
     * @brief 计算边界对流残值
     * 
     */
    void AddConvectiveResidual();

    /**
     * @brief 边界残值更新
     * 
     */
    void UpdateBoundaryResidual();

};

} // namespace Flow
} // namespace Boundary


#endif
