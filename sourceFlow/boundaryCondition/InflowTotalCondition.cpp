﻿#include "sourceFlow/boundaryCondition/InflowTotalCondition.h"

namespace Boundary
{
namespace Flow
{

InflowTotalCondition::InflowTotalCondition(const int &boundaryPatchID,
                                           Package::FlowPackage &data,
                                           const Scalar &mach,
                                           const Scalar &totalpressure,
                                           const Scalar &totalTemperature,
                                           const Scalar &alpha_,
                                           const Scalar &beta_,
                                           Flux::Flow::Precondition::Precondition *precondition_)
    :
	ExternalBoundary(boundaryPatchID, data),
	precondition(precondition_),
	pTotal(totalpressure),
	TTotal(totalTemperature),
	gamma(data.GetMaterialNumber().gamma),
	gamma1(data.GetMaterialNumber().gamma1)
{
    const Scalar &pRef = flowPackage.GetFlowConfigure().GetFlowReference().pressure;
    const Scalar &TRef = flowPackage.GetFlowConfigure().GetFlowReference().temperature;
    const Scalar alpha = alpha_ * PI / 180.0;
    const Scalar beta = beta_ * PI / 180.0;

    Scalar cofficient = 1.0 + 0.5 * gamma1 * mach * mach;
	gammaDivGamma1 = gamma / gamma1;    
	Scalar pStatic = pTotal / pow(cofficient, gammaDivGamma1);
	Scalar TStatic = TTotal / cofficient;
    Scalar Umag = mach * material.GetSoundSpeed(TStatic);

    Scalar Ux, Uy, Uz;
    if (flowPackage.GetMeshStruct().dim2)
    {
        Ux = Umag * cos(alpha);
        Uy = Umag * sin(alpha);
        Uz = 0.0;
    }
    else
    {
        Ux = Umag * cos(beta) * cos(alpha);
        Uy = Umag * sin(beta);
        Uz = Umag * cos(beta) * sin(alpha);
    }

    this->velocity_inf = Vector(Ux, Uy, Uz);
	Scalar rhoInflow = pStatic / (TStatic * flowPackage.GetMaterialNumber().R);
	this->soundSpeed_inf = material.GetSoundSpeed(pStatic, rhoInflow);
	turbulentViscosityRatio = data.GetFlowConfigure().GetFlowReference().turbulentViscosityRatio;
}


void InflowTotalCondition::Initialize()
{
	this->UpdateBoundaryCondition();
}

void InflowTotalCondition::UpdateBoundaryCondition()
{
	if (precondition) FatalError("nflowTotalCondition::UpdateBoundaryCondition: Precondition in not supported");
	else              this->UpdateBoundaryConditionNonPrecondition();
}

void InflowTotalCondition::UpdateBoundaryConditionNonPrecondition()
{
	// 遍历远场边界上的每一个面心，更新密度、速度矢量、压强
	for (int j = 0; j < boundaryFaceSize; ++j)
	{
		// 几何信息
		const int &faceID = mesh->GetBoundaryFaceIDInDomain(boundaryPatchID, j);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();
		const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();

		// 取得特定面心相邻体心物理量
		const Scalar &rhoOwner = rho.GetValue(ownerID);
		const Vector &UOwner = U.GetValue(ownerID);
		const Scalar &pOwner = p.GetValue(ownerID);		
		const Scalar &TOwner = T.GetValue(ownerID);
		const Scalar aOwner = material.GetSoundSpeed(pOwner, rhoOwner);

		// 计算不变量及边界面音速
		const Scalar UNormalOwner = UOwner & faceNormal;		
		const Scalar ROwner = UNormalOwner + 2 * aOwner / gamma1;

		const Scalar UNormal_inf = velocity_inf & faceNormal;
		const Scalar R_inf = UNormal_inf - 2 * soundSpeed_inf / gamma1;

		const Scalar VMagFace = (R_inf + ROwner) * 0.5;
		const Scalar soundFace = 0.25 * gamma1 * (ROwner - R_inf);

		// 计算边界面速度
		Vector UFace;
		if (VMagFace > 0) UFace = UOwner + (VMagFace - UNormalOwner) * faceNormal; //出流边界条件		
		else              UFace = velocity_inf + (VMagFace - UNormal_inf) * faceNormal; //入流边界条件		

		// 计算边界面的压强、密度、温度
		const Scalar soundFace2 = soundFace * soundFace;
		const Scalar machLocal2 = (UFace & UFace) / soundFace2;
		const Scalar cofficient = 1.0 + 0.5 * gamma1 * machLocal2;
		const Scalar pFace = pTotal / pow(cofficient, gammaDivGamma1);
		const Scalar rhoFace = gamma * pFace / soundFace2;
		const Scalar TFace = material.GetTemperature(pFace, rhoFace);		

		// 更新边界值
		rho.SetValue(neighID, 2.0 * rhoFace - rhoOwner);
		U.SetValue(neighID, 2.0 * UFace - UOwner);
		p.SetValue(neighID, 2.0 * pFace - pOwner);
		T.SetValue(neighID, 2.0 * TFace - TOwner);
	}

	return;
}

void InflowTotalCondition::UpdateBoundaryConditionPrecondition()
{
}

}// namespace Flow
}// namespace Boundary