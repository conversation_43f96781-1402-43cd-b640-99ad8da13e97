﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file WallGivenHeatFlux.h
//! <AUTHOR>
//! @brief 结冰/换热-给定壁面热通量分布的壁边界条件
//! @date  2024-3-21
//
//------------------------------修改日志----------------------------------------
//
// 2024-3-21 YQ
// 说明：建立
// 
//------------------------------------------------------------------------------
#ifndef _sourceFlow_boundaryCondition_WallGivenHeatFlux_
#define _sourceFlow_boundaryCondition_WallGivenHeatFlux_

#include "sourceFlow/boundaryCondition/Wall.h"

/**
 * @brief 边界条件命名空间
 *
 */
namespace Boundary
{
/**
 * @brief 流场边界条件命名空间
 *
 */
namespace Flow
{
/**
 * @brief 风洞洞壁的壁压边界条件类
 *
 */
class WallGivenHeatFlux : public Wall
{
public:
    /**
     * @brief 构造函数，初始化指定原始变量
     *
     * @param[in] boundaryPatchID 边界patch的编号
     * @param[in, out] data 包含各类场的数据包
     * @param[in] fileName_ 热通量信息文件
     */
    WallGivenHeatFlux(const int &boundaryPatchID, Package::FlowPackage &data, const std::string &fileName_);

    /**
     * @brief 初始化
     *
     */
    void Initialize();

    /**
     * @brief 边界条件更新
     *
     */
    void UpdateBoundaryCondition();

    /**
	 * @brief 计算边界对流残值
	 *
	 */
    void AddConvectiveResidual();

    /**
	 * @brief 计算边界粘性残值
	 *
	 */
    void AddDiffusiveResidual();

    /**
	 * @brief 边界残值更新
	 *
	 */
    void UpdateBoundaryResidual();

private:
    /**
     * @brief 插值计算边界面心处的热通量值
     *
     */
    void InterpolateHeatFlux();

    /**
     * @brief 读用户指定的热通量文件
     *
     */
    void ReadFile();

private:
    std::vector<std::vector<Scalar>> coorSrc; ///< 每个输入热通量的点的坐标
    std::vector<Scalar> HeatSrc;              ///< 每个输入点的热通量值
    std::vector<Scalar> HeatFluxInterp;       ///< 每个插值点的热通量值

    const std::string &fileName; ///< 热通量数据文件
    const Scalar &R;             ///< 理想气体常数
    const Scalar PrL;            ///< 层流普朗特数
    const Scalar PrT;            ///< 湍流普朗特数
    const Scalar &Cp;            ///< 等压比热
};

} // namespace Flow
} // namespace Boundary
#endif
