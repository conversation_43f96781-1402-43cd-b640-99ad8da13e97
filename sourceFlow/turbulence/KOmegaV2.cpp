﻿#include "sourceFlow/turbulence/KOmegaV2.h"

namespace Turbulence
{

	KOmegaV2::KOmegaV2(Package::FlowPackage &flowPackage)
		:
		BaseTurbulence(flowPackage),
		k(*GetTurbulencePointer(FlowMacro::Scalar::K)),
		omega(*GetTurbulencePointer(FlowMacro::Scalar::OMEGA)),
		V2Bar(*GetTurbulencePointer(FlowMacro::Scalar::V2BAR)),
		residualK(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_K)),
		residualOmega(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_OMEGA)),
		residualV2Bar(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_V2BAR)),
		gradientK(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_K)),
		gradientOmega(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_OMEGA)),
		gradientV2Bar(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_V2BAR)),
        alphaT(flowPackage.GetField().alphaT),

        A0(4.04),As(2.12),Anu(3.8),Abp(0.2),Anat(200.0),Ats(200.0),
		Cbp(1.5),Cnc(0.1),Cnat(1450.0),Cint(0.95),Cts(1000.0),Crnat(0.02),
		C11(3.4e-7),C12(1.e-10),
		Cr(3.20),Css(3.0),Ctaul(4360.0),COmega1(0.44),COmega2(0.92),
		ComegaR(1.15),Clambda(2.495),
		sigmaK(1.0),sigmaOmega1(1.17),sigmaOmega2(1.856),

		two3(2.0 / 3.0),
		CINTinverse(1.0/0.95),

		betaK(0.09),

		a1(0.31), a2(0.23),
		Csst(2.5)
	{
		// 获取远场值
		const std::vector<Scalar> &farValue = turbulenceBoundary.GetFarValue();

		for (int m = 0; m < turbulenceSize; ++m)
		{
			if (turbulenceMacro[m] == FlowMacro::Scalar::K) kFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::OMEGA) omegaFree = farValue[m];
		}

		omegaLimit = 1.e-2 * omegaFree;
		muTSmall = new ElementField<Scalar>(mesh, "muTSmall");
		muTSmall->Initialize(0.0);
	}

	KOmegaV2::~KOmegaV2()
	{
	}

	void KOmegaV2::CalculateMuTurbulent()
	{
		//计算mut
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &rhoI = rho.GetValue(elementID);
			const Scalar &kI = k.GetValue(elementID);
			const Scalar &wI = omega.GetValue(elementID);
			const Scalar &v2barI = V2Bar.GetValue(elementID);
			const Scalar &d = Max(mesh->GetNearWallDistance(elementID),SMALL);
			const Scalar &muL = muLaminar.GetValue(elementID);
			const Scalar &nuL = muL / rhoI;
			const Scalar &S_ = Max( STensorMag(elementID), SMALL );
			const Scalar &vor = Max( OmegaTensorMag(elementID), SMALL);

			// calculate v^2_small and v^2_large
			const Scalar ffsPower = Css * nuL * vor / v2barI;
			const Scalar fss = exp(-ffsPower * ffsPower);

			const Scalar lambdaT = sqrt(v2barI) / wI;
			const Scalar lambdaEff = Min(Clambda * d,lambdaT);
			const Scalar fW = pow(lambdaEff / lambdaT, two3);

			const Scalar v2barISmall = fss * fW * v2barI;
			const Scalar v2barILarge = v2barI - v2barISmall;

			const Scalar ReTsqrt = fW * sqrt(v2barI / nuL / wI);
			const Scalar fnu = 1.0 - exp(-ReTsqrt / Anu);
			const Scalar fINT = Min(v2barI / kI * CINTinverse, 1.0);
			const Scalar Cmu = 1.0 / (A0 + As * S_ / wI);
			const Scalar alphaTTemp = rhoI * fnu * sqrt(v2barISmall) * lambdaEff;
			const Scalar muTSmallI = fINT * Cmu * alphaTTemp;

			const Scalar STMag2 = S_ * S_;
			const Scalar p_v2_prod = muTSmallI * STMag2;
			const Scalar LambdaSST = 1.0 / (1.0 + exp(-10.0 * (p_v2_prod / (rhoI*wI*v2barI)-Csst)));
			const Scalar f2 = F2(rhoI, muL, kI, wI, d);
			const Scalar muTSmallITemp = Min(a1, a2 / LambdaSST) * rhoI * v2barI / vor / f2;
			const Scalar muTSmallLimited = Min(muTSmallI, muTSmallITemp);
			//const Scalar muTSmallLimited = muTSmallI;


			const Scalar vor2 = vor * vor;
			const Scalar lambdaEff2 = lambdaEff * lambdaEff;
			const Scalar ftaul = 1.0 - exp( -Ctaul * v2barILarge / ( lambdaEff2 * vor2 ) ); 
			const Scalar ReOmega = d * d * vor / nuL;
			Scalar betaTS = Max(ReOmega - Cts, 0.0);
			betaTS = 1.0 - exp( -betaTS * betaTS / Ats );
			const Scalar dEff = lambdaEff / Clambda;
			const Scalar dEff2 = dEff * dEff;
			const Scalar dEff4 = dEff2 * dEff2;
			const Scalar muTLargeTemp1 = rhoI * vor / nuL * ( ftaul  * C11 * lambdaEff2 * sqrt(v2barILarge) * lambdaEff + betaTS * C12 * vor * dEff4 );
			const Scalar muTLargeTemp2 = rhoI * (kI - v2barISmall) / S_ * 0.5;
			const Scalar muTLargeI = Min(muTLargeTemp1, muTLargeTemp2);

			muTSmall->SetValue(elementID, muTSmallLimited);
			alphaT->SetValue(elementID, alphaTTemp);
			muTurbulent->SetValue(elementID, muTLargeI + muTSmallLimited);
		}

		return;
	}

	void KOmegaV2::PreCalculate()
	{
	}

	void KOmegaV2::AddSourceResidual()
	{
		if (currentLevel > 0) return;
		// 计算源项
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);

			const Scalar &rhoI = rho.GetValue(elementID);
		    const Scalar muTurblentI = muTurbulent->GetValue(elementID);
			const Scalar &muTSmallI = muTSmall->GetValue(elementID);
			const Scalar &muLaminarI = muLaminar.GetValue(elementID);
			const Scalar &nuL = muLaminarI / rhoI;
		    const Scalar &kI = k.GetValue(elementID);
		    const Scalar &wI = omega.GetValue(elementID);
			const Scalar &v2barI = V2Bar.GetValue(elementID);
		    const Scalar wInverse = 1.0 / wI; 
			//const Scalar &alphaTI = alphaT->GetValue(elementID);
			const Scalar &d = Max( mesh->GetNearWallDistance(elementID), SMALL );
			const Scalar &vor = Max( OmegaTensorMag(elementID), SMALL);

		    //2.计算交叉源项

		    const Scalar rhoW = rhoI * wI;

		    //3.计算产生源项

			// source for k equation
			const Scalar STMag = STensorMag(elementID);
			const Scalar STMag2 = STMag * STMag;
			const Scalar gradKMag = gradientK->GetValue(elementID).Mag();
			const Scalar Dk = 0.5 * muLaminarI * gradKMag * gradKMag / kI;
			const Scalar p_k_prod = muTurblentI * STMag2;
		    const Scalar p_k_dest = Min(kI,v2barI) * rhoW + Dk;

			// source for omega equation

			const Scalar ffsPower = Css * nuL * vor / v2barI;
			const Scalar fss = exp(-ffsPower * ffsPower);
			const Scalar lambdaT = sqrt(v2barI) / wI;
			const Scalar lambdaEff = Min(Clambda * d, lambdaT);
			const Scalar fW = pow(lambdaEff / lambdaT, two3);

			const Scalar psiBP = Max(v2barI / nuL / vor - Cbp, 0.0);
			const Scalar betaBP = 1.0 - exp(-psiBP / Abp);
			const Scalar Rbp = Cr * betaBP * (kI - v2barI) * wI / fW;
			const Scalar ReOmega = d * d * vor / nuL;
			const Scalar fNAT = 1.0 - exp(- Cnc * sqrt(kI) * d / nuL);
			const Scalar psiNAT = Max(ReOmega - Cnat / fNAT,0.0);
			const Scalar betaNAT = 1.0 - exp(-psiNAT / Anat);
			const Scalar Rnat = Crnat * betaNAT * (kI - v2barI) * vor;

		    const Scalar rd = 1.0 / Max(d, SMALL);
		    const Scalar rd2 = rd * rd;
		    const Scalar cross = gradientK->GetValue(elementID) & gradientOmega->GetValue(elementID);
		    const Scalar CD_kw = Max(2.0 * rhoI * sigmaOmega2 * cross / wI, 1.0E-10);
			const Scalar F1Temp2 = 4.0 * rhoI * sigmaOmega2 * kI / CD_kw * rd2;
			const Scalar F1Temp1 = Max(v2barI / wI * rd, 500.0 * nuL * betaK / wI);
			const Scalar F1Temp = Min(F1Temp1, F1Temp2);
			const Scalar F1 = tanh(F1Temp * F1Temp * F1Temp * F1Temp);
			const Scalar F1star = 1.0 - (1.0 - F1) * fss;
		    const Scalar p_w_Cross = 2.0 * betaK * (1.0 - F1star) * rhoI * sigmaOmega2 * wInverse * cross;

			// source for v2bar
			//const Scalar fINT = Min(v2barI / kI * CINTinverse, 1.0);
			//const Scalar Cmu = 1.0 / (A0 + As * STMag / wI);
			//const Scalar muTSmallI = fINT * Cmu * alphaTI;

			const Scalar gradv2barMag = gradientV2Bar->GetValue(elementID).Mag();
			const Scalar Dv2bar = 0.5 * muLaminarI * gradv2barMag * gradv2barMag / v2barI;
			const Scalar p_v2_prod = muTSmallI * STMag2;
			const Scalar p_v2_trans= rhoI * (Rbp + Rnat);
			const Scalar p_v2_dest = rhoW * v2barI + Dv2bar;

			const Scalar LambdaSST = 1.0 / (1.0 + exp(-10.0 * (p_v2_prod / (rhoW * v2barI) - Csst)));
			const Scalar fNETemp = Max(300.0 * ReOmega * LambdaSST, 1.0);
			const Scalar fNE = Min(fNETemp, 3.3);
		    const Scalar p_w_prod = COmega1 * wI / v2barI * muTSmallI * STMag2;
			const Scalar p_w_trans = rhoW * (ComegaR / fW - 1.0) / v2barI * (Rbp + Rnat);
			const Scalar p_w_dest = fNE * COmega2 * rhoW * wI * fW * fW;

		    // 修正源项
		    const Scalar &volume = mesh->GetElement(elementID).GetVolume();
		    const Scalar p_k_source  = volume * (p_k_prod - p_k_dest);
		    const Scalar p_w_source  = volume * (p_w_prod - p_w_dest + p_w_trans + p_w_Cross);
		    const Scalar p_v2_source = volume * (p_v2_prod - p_v2_dest + p_v2_trans);
		
		    residualK.AddValue(elementID, -p_k_source);
		    residualOmega.AddValue(elementID, -p_w_source);
		    residualV2Bar.AddValue(elementID, -p_v2_source);
		}

		return;
	}

	int KOmegaV2::CheckAndLimit()
	{
		////参考了Overset的rudnik limiter
		//const Scalar scoeff = 0.5 * sqrt(3.0);
		const Scalar limitCoff = 1.e-5;
		const Scalar kMin = limitCoff * kFree;
		const Scalar wMin = limitCoff * omegaFree;

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
		  	const Scalar &kI = k.GetValue(elementID);
		  	Scalar kLimited = Max(kI, kMin);
		  	k.SetValue(elementID, kLimited);

		  	//const Scalar Strain = STensorMag(elementID);
		  	//const Scalar wMin = scoeff * Strain;
		  	const Scalar &wI = omega.GetValue(elementID);
		  	Scalar wLimited = Max(wI, wMin);
		  	omega.SetValue(elementID, wLimited);

			const Scalar &v2barI = V2Bar.GetValue(elementID);
			Scalar v2barLimited = Max(v2barI, kLimited);
			V2Bar.SetValue(elementID, v2barLimited);
		}

		int elementID = -1;
		elementID = Max(elementID, k.CheckAndLimit(0.0, INF));
		elementID = Max(elementID, omega.CheckAndLimit(0.0, INF));
		elementID = Max(elementID, V2Bar.CheckAndLimit(0.0, INF));
		return elementID;
	}

    Scalar KOmegaV2::CalculateAlphaT(const int &elementID)
	{
		const Scalar &v2barI = V2Bar.GetValue(elementID);
		const Scalar &wI = omega.GetValue(elementID);
		const Scalar &d = Max( mesh->GetNearWallDistance(elementID), SMALL );
		const Scalar &rhoI = rho.GetValue(elementID);
		const Scalar &muLaminarI = muLaminar.GetValue(elementID);
		const Scalar &nuL = muLaminarI / rhoI;
		const Scalar &vor = Max( OmegaTensorMag(elementID), SMALL);

		const Scalar lambdaT = sqrt(v2barI) / wI;
		const Scalar lambdaEff = Min(Clambda * d,lambdaT);
		const Scalar fW = pow(lambdaEff / lambdaT, two3);
		const Scalar ffsPower = Css * nuL * vor / v2barI;
		const Scalar fss = exp(-ffsPower * ffsPower);
		const Scalar v2barISmall = fss * fW * v2barI;
		const Scalar ReTsqrt = fW * sqrt(v2barI / nuL / wI);
		const Scalar fnu = 1.0 - exp(-ReTsqrt / Anu);
		Scalar alphaT = rhoI * fnu * betaK * sqrt(v2barISmall) * lambdaEff;

		return alphaT;
	}

	std::vector<std::pair<Scalar, Scalar>> KOmegaV2::CalculateGammaFace(const int &faceID)
	{
		//得到面相关信息
		const int &leftID = mesh->GetFace(faceID).GetOwnerID();
		const int &rightID = mesh->GetFace(faceID).GetNeighborID();

        std::vector<std::pair<Scalar, Scalar>> gamma(3);
		//计算面心值
		const Scalar muLaminarFace = 0.5 * (muLaminar.GetValue(leftID) + muLaminar.GetValue(rightID));
		const Scalar alphaTFace = 0.045 * ( alphaT->GetValue(leftID) + alphaT->GetValue(rightID)) ;

		gamma[0].first = muLaminarFace + alphaTFace;
		gamma[0].second = gamma[0].first;

		gamma[1].first = muLaminarFace + alphaTFace / sigmaOmega1;
		gamma[1].second = gamma[1].first;

		gamma[2].first = gamma[0].first;
		gamma[2].second = gamma[0].second;

		return gamma;
	}

	Scalar KOmegaV2::F1(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d, const Scalar &cross, const Scalar &omegaLimit0)
	{
		const Scalar rd = 1.0 / Max(d, SMALL);
		const Scalar rd2 = rd * rd;
		const Scalar CD_kw = Max(2.0 * rho * sigmaOmega2 * cross / Max(w, omegaLimit0), 1.0E-20);
		Scalar temp = Max(sqrt(k) / 0.09 * rd, 500 * muL / rho * rd2);
		const Scalar arg1 = Min(temp / Max(w, SMALL), 4.0 * rho * sigmaOmega2 * k / CD_kw * rd2);

		return tanh(arg1 * arg1 * arg1 *arg1);
	}

	Scalar KOmegaV2::F2(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d)
	{
		const Scalar rw = 1.0 / Max(w, SMALL);
		const Scalar rd = 1.0 / Max(d, SMALL);
		const Scalar temp1 = 2.0 / 0.09 * sqrt(k);
		const Scalar temp2 = 500 * muL / rho * rd;
		const Scalar arg2 = Max(temp1, temp2) * rd * rw;

		return tanh(arg2 * arg2);
	}

}//namespace Turbulence
