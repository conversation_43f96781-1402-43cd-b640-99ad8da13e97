﻿#include "sourceFlow/turbulence/MenterSSTML.h"

namespace Turbulence
{

	MenterSSTML::MenterSSTML(Package::FlowPackage &flowPackage)
		:
		MenterSST(flowPackage),
		k(*GetTurbulencePointer(FlowMacro::Scalar::K)),
		omega(*GetTurbulencePointer(FlowMacro::Scalar::OMEGA)),
		residualK(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_K)),
		residualOmega(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_OMEGA)),
		gradientK(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_K)),
		gradientOmega(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_OMEGA)),
		fieldF1(nullptr),
		betaK(0.09), kappa(0.41), a1(0.31),
		sigmaK1(0.85), sigmaOmega1(0.5), COmega1(0.55317),
		sigmaK2(1.0), sigmaOmega2(0.856), COmega2(0.4403547), two3(2.0 / 3.0),
		betaOmega1(0.075), betaOmega2(0.0828), predictStep(-1)
	{
#if defined(_EnableMLTurbModel_)
		MLTurbModel = nullptr;
        if(currentLevel==0)
        {
            //const char* model_path = "rf_model.onnx";
            std::string model_path = flowPackage.GetFlowConfigure().GetModel().modelPath;
			const int IntraOpNumThreads = flowPackage.GetFlowConfigure().GetModel().IntraOpNumThreads; 
			const int InterOpNumThreads = flowPackage.GetFlowConfigure().GetModel().InterOpNumThreads;
			const int numFeatures = 6;

			MLTurbModel = new mlTurbModel(model_path, IntraOpNumThreads, InterOpNumThreads, numFeatures);
        }
#endif
        predictInterval = flowPackage.GetFlowConfigure().GetModel().predictInterval;
		mlSolverLevel = flowPackage.GetFlowConfigure().GetAcceleration().multigridSolver.level;
		predictInterval *= mlSolverLevel;

		// 获取远场值
		const std::vector<Scalar> &farValue = turbulenceBoundary.GetFarValue();

		for (int m = 0; m < turbulenceSize; ++m)
		{
			if (turbulenceMacro[m] == FlowMacro::Scalar::K) kFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::OMEGA) omegaFree = farValue[m];
		}

		rhoFree = flowPackage.GetFlowConfigure().GetFlowReference().density;
		muFree = flowPackage.GetFlowConfigure().GetFlowReference().muLaminar;
		aFree = flowPackage.GetFlowConfigure().GetFlowReference().sound;
		Mach = flowPackage.GetFlowConfigure().GetFlowReference().mach;
		Reynolds = flowPackage.GetFlowConfigure().GetFlowReference().Reynolds;
		UFreeMag = flowPackage.GetFlowConfigure().GetFlowReference().velocity.Mag();
		UFreeMag2 = UFreeMag * UFreeMag;
		UFreeMag4 = UFreeMag2 * UFreeMag2;

		muTurbulentRANS = new ElementField<Scalar>(mesh, "muTRANS");
		muTurbulentRANS->Initialize(0.0);

		//如果采用gamma-ReTheta模型，omega下界设为SMALL，因为转捩模型中的CheckAndLimit函数已经对omega进行了限制
		if (flowPackage.GetFlowConfigure().GetModel().type == Turbulence::Model::MENTER_SST_GAMMARE)
		{
		    omegaLimit = SMALL;
		}
		else
		{
		    omegaLimit = SMALL;
		    //omegaLimit = 1.e-2 * omegaFree;
		}
	}

	MenterSSTML::~MenterSSTML()
	{
	}

	void MenterSSTML::CalculateMuTurbulent()
	{
		//计算mut
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &rhoI = rho.GetValue(elementID);
			const Scalar &kI = k.GetValue(elementID);
			const Scalar &wI = omega.GetValue(elementID);
			const Scalar &d = mesh->GetNearWallDistance(elementID);
			const Scalar &muL = muLaminar.GetValue(elementID);
			const Scalar f2 = F2(rhoI, muL, kI, wI, d);

			// Menter SST 1994
			Tensor omegaI = gradientU.GetValue(elementID).AntiSymm();
			Scalar S_ = sqrt(2.0 * (omegaI && omegaI));

			// UNSMB 
			// Menter SST 2003
			//const Tensor &gradUI = gradientU.GetValue(elementID);
			//Tensor symmetricTensor = gradUI.Symm(true);
			//Scalar S_ = sqrt(2.0 * symmetricTensor && symmetricTensor);

			const Scalar wTemp = Max(wI, omegaLimit);
			const Scalar temp = Max(a1 * wTemp, f2 * S_);
			const Scalar muTurbulentTemp = rhoI * kI * a1 / temp;
			//muTurbulent->SetValue(elementID, muTurbulentTemp);
			muTurbulentRANS->SetValue(elementID, muTurbulentTemp);
		}
		muTurbulentRANS->SetGhostlValueParallel();

		if (fieldF1 != nullptr)
		{
			flowPackage.FreeTempField(*fieldF1);
			fieldF1 = nullptr;
		}

		if( currentLevel==0 && predictStep % predictInterval == 0) 
		{
			this->CalculateMuTML();
		}
		predictStep += 1;

		return;
	}
		
	void MenterSSTML::CalculateMuTML()
	{
		const int elementNumber = mesh->GetElementNumberInDomain();
#if defined(_EnableMLTurbModel_)
	    std::vector<float> features;
	    for (int index = 0; index < elementNumber; ++index)
	    {
	    	const int &elementID = mesh->GetElementIDInDomain(index);
	    	const Scalar &rhoI = rho.GetValue(elementID);
	    	const Scalar &kI = k.GetValue(elementID);
	    	const Scalar &wI = omega.GetValue(elementID);
	    	const Scalar &muL = muLaminar.GetValue(elementID);
	    	const Scalar &muT = muTurbulentRANS->GetValue(elementID);
	    	const Vector &UI = U.GetValue(elementID);

	    	//const Scalar epsilonNondim = Max(wI, 9297.6) * kI * 0.09 * muFree / rhoFree / UFreeMag4;
	    	const Scalar epsilonNondim = Max(wI, omegaFree) * kI * 0.09 * muFree / rhoFree / UFreeMag4;
	    	const Scalar kNondim = kI / UFreeMag2;

	    	const Scalar turbscale = epsilonNondim/0.09/kNondim*Reynolds;

	    	const Tensor gradUNondim = gradientU.GetValue(elementID) / UFreeMag; // / aFree / Mach;
	    	const Tensor STensor = gradUNondim.Symm(true);
	    	const Scalar Sfeature = NormalizeTensor(STensor, turbscale);

	    	const Tensor WTensor = gradUNondim.AntiSymm(true);
	    	const Scalar Wfeature = NormalizeTensor(WTensor, turbscale);

	    	const Scalar vorNondim = OmegaTensorMag(elementID) / UFreeMag;// / aFree / Mach; //feature

	        const Scalar lrans = Max(mesh->GetNearWallDistance(elementID), SMALL);
	        const Scalar rdw2 = 1.0 / (lrans * lrans);
	        const Tensor &gradUI = gradientU.GetValue(elementID);
	        const Scalar gradUM = sqrt(gradUI && gradUI);
	        const Scalar rd = 8.0 * (muL + muT) / (gradUM + SMALL) * rdw2 * 5.948839976 / rhoI;
	        const Scalar fd = 1 - tanh(rd * rd * rd);

	    	const Scalar tkOverk =  2.0 * kI / UI.Mag() / UI.Mag();
	    	const Scalar Xi = muT / muL;

	    	features.push_back( static_cast<float>(Sfeature) );
	    	features.push_back( static_cast<float>(Wfeature) );
	    	features.push_back( static_cast<float>(vorNondim / (abs(vorNondim) + epsilonNondim / kNondim * Reynolds)) );
	    	features.push_back( static_cast<float>(fd) ); 
	    	features.push_back( static_cast<float>(1.0 / (1.0 + 1.0 / tkOverk)) );
	    	features.push_back( static_cast<float>(1.0 / (1.0 + 1.0 / Xi)) ); 
	    }

        //auto start = std::chrono::high_resolution_clock::now();  // 记录开始时间

	    std::vector<float> mutPredict = MLTurbModel->predictor(features);
#endif

        //auto end = std::chrono::high_resolution_clock::now();  // 记录结束时间
        //auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start) / 1000.0;  // 计算时间差
        //std::cout << "CPU time used: " << duration.count() << " ms\n";  // 输出运行时间

	    for (int index = 0; index < elementNumber; ++index)
	    {
	    	const int &elementID = mesh->GetElementIDInDomain(index);
	    	Scalar muT = muTurbulentRANS->GetValue(elementID);
#if defined(_EnableMLTurbModel_)
	    	muT = exp(mutPredict[elementID]) * muT;
#endif
	    	muTurbulent->SetValue(elementID, muT);
	    }
    }


    Scalar MenterSSTML::NormalizeTensor(const Tensor &SWTensor, const Scalar turbScale)
	{
			const Scalar SWMag = sqrt( SWTensor && SWTensor );
			const Tensor SWTensorNormalised = SWTensor / (SWMag + turbScale);
			const Tensor SWTensorNormalisedSym = SWTensorNormalised.Transpose();
			Scalar SWfeature = SWTensorNormalised && SWTensorNormalisedSym;

			return SWfeature;
	}

	void MenterSSTML::PreCalculate()
	{
		// 仅细网格涉及该项
		//if (currentLevel > 0) return;

		if (fieldF1 == nullptr) fieldF1 = &flowPackage.GetTempElementField("F1", Scalar0);

		//计算调和函数F1场
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar &rhoI = rho.GetValue(elementID);
			const Scalar &muLaminarI = muLaminar.GetValue(elementID);
			const Scalar &kI = k.GetValue(elementID);
			const Scalar &wI = omega.GetValue(elementID);
			const Scalar &d = mesh->GetNearWallDistance(elementID);
			const Scalar cross = gradientK->GetValue(elementID) & gradientOmega->GetValue(elementID);

		        //SST模型的F1函数
			Scalar f1 = F1(rhoI, muLaminarI, kI, wI, d, cross, omegaLimit);

		        //gamma-ReTheta模型需要修改F1函数
			if (flowPackage.GetFlowConfigure().GetModel().type == Turbulence::Model::MENTER_SST_GAMMARE)
			{
				const Scalar Ry = rhoI * d *sqrt(kI) / muLaminarI;
				const Scalar F3 = exp(-pow(Ry / 120.0, 8));
				f1 = Max(f1, F3);
			}

			fieldF1->SetValue(elementID, f1);
		}

		fieldF1->SetGhostlValueBoundary();
		fieldF1->SetGhostlValueOverset();
		fieldF1->SetGhostlValueParallel();

		return;
	}

	void MenterSSTML::AddSourceResidual()
	{
		// 计算湍流源项
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			AddSourceResidual(elementID, 1.0, 1.0);
		}

		return;
	}

	void MenterSSTML::AddSourceResidual(const int &elementID, const Scalar &produceK, const Scalar &ldOverlr)
	{
		const Scalar &rhoI = rho.GetValue(elementID);
		//const Scalar muTurblentI = muTurbulent->GetValue(elementID);
		const Scalar muTurblentI = muTurbulentRANS->GetValue(elementID);
		const Scalar &kI = k.GetValue(elementID);
		const Scalar &wI = omega.GetValue(elementID);
		//const Scalar wInverse = 1.0 / Max(wI, omegaLimit); // 用作除数需要采用此项
		const Scalar wInverse = 1.0 / wI; // 用作除数需要采用此项

		//1.获得调和函数及相关系数
		const Scalar &f1 = fieldF1->GetValue(elementID);
		const Scalar mf1 = 1.0 - f1;
		const Scalar COmega = f1 * COmega1 + mf1 * COmega2;
		const Scalar betaW = f1 * betaOmega1 + mf1 * betaOmega2;

		//2.计算交叉源项
		Scalar p_w_Cross = Scalar0;
		if (!crossDiffusionFlag)
		{
			const Scalar cross = gradientK->GetValue(elementID) & gradientOmega->GetValue(elementID);
			p_w_Cross = 2.0 * mf1 * cross  * rhoI * sigmaOmega2 * wInverse;
		}

		const Scalar rhoK = rhoI * kI;
		const Scalar rhoW = rhoI * wI;
		const Scalar muTInverse = 1.0 / (muTurblentI + SMALL);

		//3.计算产生源项
		Scalar p_k_prod = Scalar0, p_w_prod = Scalar0;
		Scalar produceK_dest = Min(Max(produceK, 0.1), 1.0);
		Scalar lrOverld = 1.0 / ldOverlr;

		Scalar p_k_dest = betaK * rhoK * wI * produceK_dest * lrOverld;
		Scalar p_w_dest = betaW * rhoW * wI;
		if (currentLevel == 0)
		{
			const Scalar Vorticity = this->OmegaTensorMag(elementID);
			p_k_prod = Min(muTurblentI * Vorticity * Vorticity, 20.0 * p_k_dest);
			p_w_prod = rhoI * muTInverse * p_k_prod * COmega;
			//const Tensor &gradUI = gradientU.GetValue(elementID);
			//Tensor tau = flowPackage.CalculateTau(muTurblentI, gradUI);
			//tau.AddDiag(-two3 * rhoK);
			//Scalar tauS = Max(tau && gradUI, SMALL);

			//UNSMB的生成项和破坏项
			//Scalar produceKLimit = rhoK * sqrt(tauS * muTInverse);
			//p_k_prod = Min(tauS, Min(produceKLimit, INF)) * produceK;

			//p_w_prod = rhoI * tauS * muTInverse;
			//Scalar produceWLimit = rhoK * sqrt(rhoI * p_w_prod) * muTInverse;
			//p_w_prod = Min(p_w_prod, produceWLimit) * COmega;

			//Menter SST 2003模型的生成项和破坏项
			//p_k_prod = Min(tauS * produceK, 20.0 * p_k_dest);
			//p_w_prod = rhoI * muTInverse * tauS * COmega;
		}

		// 修正源项

		const Scalar &volume = mesh->GetElement(elementID).GetVolume();
		//用于Menter SST 2003
		const Scalar p_k_source = volume * (p_k_prod - p_k_dest);
		const Scalar p_w_source = volume * (p_w_prod - p_w_dest + p_w_Cross);
		
		//用于UNSMB
		//const Scalar p_k_source = volume * (p_k_prod * produceK - betaK * rhoK * wI * produceK_dest);
		//const Scalar p_w_source = volume * (p_w_prod - betaW * rhoW * wI + p_w_Cross);
		residualK.AddValue(elementID, -p_k_source);
		residualOmega.AddValue(elementID, -p_w_source);

		// 点隐式Jacobian
		const Scalar jacobianK = -betaK * wI * volume * produceK_dest;
		const Scalar jacobianW = (-2.0 * betaW * wI - Max(0.0, p_w_Cross) / rhoI * wInverse) * volume;
		jacobianTurbulence[0]->SetValue(elementID, jacobianK);
		jacobianTurbulence[1]->SetValue(elementID, jacobianW);
	}

	int MenterSSTML::CheckAndLimit()
	{
		const Scalar limitCoff = 1.e-5;
		const Scalar kMin = SMALL;// limitCoff * kFree;
		const Scalar wMin = SMALL;// limitCoff * omegaFree;

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
		  	const Scalar wLimited = Max(omega.GetValue(elementID), wMin);
		  	omega.SetValue(elementID, wLimited);
		}

		int elementID = -1;
		elementID = Max(elementID, k.CheckAndLimit(0.0, INF));
		elementID = Max(elementID, omega.CheckAndLimit(0.0, INF));
		return elementID;
	}

	Scalar MenterSSTML::F1(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d, const Scalar &cross, const Scalar &omegaLimit0)
	{
		const Scalar rd = 1.0 / Max(d, SMALL);
		const Scalar rd2 = rd * rd;
		const Scalar CD_kw = Max(2.0 * rho * sigmaOmega2 * cross / Max(w, omegaLimit0), 1.0E-20);
		Scalar temp = Max(sqrt(k) / 0.09 * rd, 500 * muL / rho * rd2);
		const Scalar arg1 = Min(temp / Max(w, SMALL), 4.0 * rho * sigmaOmega2 * k / CD_kw * rd2);

		return tanh(arg1 * arg1 * arg1 *arg1);
	}

	Scalar MenterSSTML::F2(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d)
	{
		const Scalar rw = 1.0 / Max(w, SMALL);
		const Scalar rd = 1.0 / Max(d, SMALL);
		const Scalar temp1 = 2.0 / 0.09 * sqrt(k);
		const Scalar temp2 = 500 * muL / rho * rd;
		const Scalar arg2 = Max(temp1, temp2) * rd * rw;

		return tanh(arg2 * arg2);
	}

std::vector<std::pair<Scalar, Scalar>> MenterSSTML::CalculateGammaFace(const int &faceID)
{
	//得到面相关信息
	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	const int &neighID = mesh->GetFace(faceID).GetNeighborID();

	//计算面心值	
	const Scalar muLaminarFace = FieldManipulation::InnerfaceValue(faceID, muLaminar);
	//const Scalar muTurbulentFace = FieldManipulation::InnerfaceValue(faceID, *muTurbulent);
	const Scalar muTurbulentFace = FieldManipulation::InnerfaceValue(faceID, *muTurbulentRANS);
	const Scalar &kLeft = k.GetValue(ownerID);
	const Scalar &kRight = k.GetValue(neighID);
	const Scalar kFace = 0.5 * (kLeft + kRight);

	//利用调和函数计算相关系数
	const Scalar f1Face = 0.5 * (fieldF1->GetValue(ownerID) + fieldF1->GetValue(neighID));
	const Scalar mf1 = 1.0 - f1Face;
	const Scalar sigmaK = f1Face * sigmaK1 + mf1 * sigmaK2;
	const Scalar sigmaW = f1Face * sigmaOmega1 + mf1 * sigmaOmega2;

    //计算面的扩散项通量
    std::vector<std::pair<Scalar, Scalar>> gamma(2);
    gamma[0].first = muLaminarFace + sigmaK * muTurbulentFace; //K方程的扩散系数
    gamma[1].first = muLaminarFace + sigmaW * muTurbulentFace; //w方程的扩散系数

    gamma[0].second = gamma[0].first;
    gamma[1].second = gamma[1].first;

    if (crossDiffusionFlag) //加入w方程的交叉项扩散系数
    {
        const Scalar cLeft = 2.0 * sigmaOmega2 * (1.0 - fieldF1->GetValue(ownerID))
                           * rho.GetValue(ownerID) / Max(omega.GetValue(ownerID), omegaLimit);
        const Scalar cRight = 2.0 * sigmaOmega2 * (1.0 - fieldF1->GetValue(neighID))
                            * rho.GetValue(neighID) / Max(omega.GetValue(neighID), omegaLimit);
        gamma[1].first += cLeft * (kFace - kLeft);
        gamma[1].second += cRight * (kFace - kRight);
    }

    return gamma;
}

	void MenterSSTML::InitializeTurbulentField()
	{
		ElementField<Vector> Ucorrected = U;

		Scalar freeStreamRe = flowPackage.GetFlowConfigure().GetFlowReference().Reynolds;
		Vector freeStreamVelocity = flowPackage.GetFlowConfigure().GetFlowReference().velocity;
		Scalar freeStreamDensity = flowPackage.GetFlowConfigure().GetFlowReference().density;
		Scalar freeStreamMuLaminar = flowPackage.GetFlowConfigure().GetFlowReference().muLaminar;
		Scalar cfTheory = 0.058 * pow(freeStreamRe, -0.2);
		Scalar UMag = freeStreamVelocity.Mag();
		Scalar tauWall = 0.5 * cfTheory * freeStreamDensity * UMag * UMag;
		Scalar Utau = sqrt(tauWall / freeStreamDensity);
		Scalar blThickness = Scalar0;

		const Scalar Cmu = 0.09;
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);

			const Scalar &d = mesh->GetNearWallDistance(elementID);

			Scalar yPlusTemp = Utau * d / freeStreamMuLaminar * freeStreamDensity;
			Scalar yPlus = Max(yPlusTemp, exp(-5.0));

			Scalar UMagInner = Utau / 0.41 * (log(yPlus) + 5.0);
			Scalar UMagTemp = Min(UMag, UMagInner);
			if (UMagTemp < UMag) blThickness = d;

			Scalar Ux = UMagTemp * freeStreamVelocity.X() / UMag;
			Scalar Uy = UMagTemp * freeStreamVelocity.Y() / UMag;
			Scalar Uz = UMagTemp * freeStreamVelocity.Z() / UMag;

			Vector UcorrectedLocal;
			UcorrectedLocal.SetX(Ux);
			UcorrectedLocal.SetY(Uy);
			UcorrectedLocal.SetZ(Uz);

			Ucorrected.SetValue(elementID, UcorrectedLocal);
		}

		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);

			Scalar d = mesh->GetNearWallDistance(elementID);
			d = Max(d, SMALL);
			Scalar lmix = Min(0.41 * d, 0.09 * blThickness);
			Scalar UMag = Ucorrected.GetValue(elementID).Mag();
			Scalar kTemp = kFree;
			Scalar epsilonFree = kFree * omegaFree;
			Scalar epsilonTemp = epsilonFree;
			if (d < blThickness)
			{
				kTemp = pow(UMag / d, 2.0) * lmix * lmix / sqrt(Cmu) + kFree * d / blThickness;
				epsilonTemp = pow(Cmu, 0.75) * pow(kTemp, 1.5) / lmix + epsilonFree * d / blThickness;
			}

			k.SetValue(elementID, kTemp);
			omega.SetValue(elementID, epsilonTemp / kTemp);
		}

		return;
	}
}//namespace Turbulence
