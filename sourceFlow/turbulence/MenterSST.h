﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MenterSST.h
//! <AUTHOR>
//! @brief 湍流类：MenterSST
//! @date 2021-04-05
//
//------------------------------修改日志----------------------------------------
// 2021-04-05 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_MenterSST_
#define _sourceFlow_turbulence_MenterSST_

#include "sourceFlow/turbulence/BaseTurbulence.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流MenterSST类
 * 
 */
class  MenterSST: public BaseTurbulence
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
    MenterSST(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
    ~MenterSST();

    /**
     * @brief 计算湍流粘性系数
     * 
     */
    void CalculateMuTurbulent();

    /**
     * @brief 各项残值计算前的准备工作
     * 
     */
    void PreCalculate();
    
    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();
    
    /**
     * @brief 湍流量限制
     * 
     * @return int 
     */
    int CheckAndLimit();  

    /**
     * @brief 湍流场初始化
     * 
     */
    void InitializeTurbulentField();

protected:
    /**
     * @brief 计算扩散项面心处扩散系数
     * 
     * @param[in] faceID 面编号
     * @return std::vector<std::pair<Scalar, Scalar>> 
     */
    std::vector<std::pair<Scalar, Scalar>> CalculateGammaFace(const int &faceID);

	/**
	* @brief 累加源项通量残差
	*
	* @param[in] elementID 单元编号
	* @param[in] produceK 控制湍动能生成的系数
	*/
	void AddSourceResidual(const int &elementID, const Scalar &produceK = 1.0, const Scalar &ldOverlr = 1.0);

private:
    /**
     * @brief SST模型的调和函数F1
     * 
     * @param[in] rho 密度
     * @param[in] muL 层流粘性系数
     * @param[in] k 湍动能
     * @param[in] w 比耗散率
     * @param[in] d 壁面距离
     * @param[in] cross 交叉项
     * @param[in] omegaLimit0 w的下限
     * @return Scalar 
     */
    Scalar F1(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d, const Scalar &cross, const Scalar &omegaLimit0);

    /**
     * @brief SST模型的调和函数F2
     * 
     * @param[in] rho 密度
     * @param[in] muL 层流粘性系数
     * @param[in] k 湍动能
     * @param[in] w 比耗散率
     * @param[in] d 壁面距离
     * @return Scalar 
     */
    Scalar F2(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d);
    
protected:
    ElementField<Scalar> &k; ///< 湍动能
    ElementField<Scalar> &omega; ///< 比耗散率
	ElementField<Scalar> *fieldF1; ///< 调和函数F1场


private:
    ElementField<Vector> *gradientK; ///< 湍动能梯度
    ElementField<Vector> *gradientOmega; ///< 比耗散率梯度
    ElementField<Scalar> &residualK; ///< 湍动能残值
    ElementField<Scalar> &residualOmega; ///< 比耗散率残值

    //ElementField<Scalar> *fieldF1; ///< 调和函数F1场

    const Scalar sigmaK1; ///< 内层模型参数sigmaK1
    const Scalar sigmaOmega1; ///< 内层模型参数sigmaOmega1
    const Scalar COmega1; ///< 内层模型参数COmega1
    const Scalar betaOmega1; ///< 内层模型参数betaOmega1
    
    const Scalar sigmaK2; ///< 外层模型参数sigmaK2
    const Scalar sigmaOmega2; ///< 外层模型参数sigmaOmega2
    const Scalar COmega2; ///< 外层模型参数COmega2
    const Scalar betaOmega2; ///< 外层模型参数betaOmega2
    
    const Scalar betaK; ///< 参数betaK
    const Scalar kappa; ///< 参数kappa
    const Scalar a1; ///< 参数a1
    const Scalar two3; ///< 常数2.0/3.0
    
    Scalar omegaFree; ///< 远场比耗散率
    Scalar kFree; ///< 远场湍动能
    Scalar omegaLimit; ///< 远场比耗散率的0.01倍（用于除数）

    Matrix Jacobian_i;
};

} // namespace Turbulence
#endif 
