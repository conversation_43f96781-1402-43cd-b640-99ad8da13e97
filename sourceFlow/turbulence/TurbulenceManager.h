﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file TurbulenceManager.h
//! <AUTHOR>
//! @brief 湍流管理类，用于管理各类湍流模型。
//! @date 2021-04-05
//
//------------------------------修改日志----------------------------------------
// 2021-04-05 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_TurbulenceManager_
#define _sourceFlow_turbulence_TurbulenceManager_

#include "sourceFlow/turbulence/BaseTurbulence.h"
#include "sourceFlow/turbulence/SpalartAllmaras.h"
#include "sourceFlow/turbulence/SpalartAllmarasBC.h"
#include "sourceFlow/turbulence/MenterSST.h"
#include "sourceFlow/turbulence/KEpsilon.h"
#include "sourceFlow/turbulence/KOmegaV2.h"
#include "sourceFlow/turbulence/SpalartAllmarasDES.h"
#include "sourceFlow/turbulence/SpalartAllmarasDDES.h"
#include "sourceFlow/turbulence/MenterSSTGammaRe.h"
#include "sourceFlow/turbulence/MenterSSTDES.h"
#include "sourceFlow/turbulence/MenterSSTDDES.h"
#include "sourceFlow/turbulence/MenterSSTML.h"


/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流管理类
 * 
 */
class  TurbulenceManager
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场包
     */
    TurbulenceManager(Package::FlowPackage &flowPackage_);

    /**
     * @brief 析构函数
     * 
     */
    ~TurbulenceManager();

    /**
     * @brief 计算湍流粘性系数
     * 
     */
    void CalculateMuTurbulent();

    /**
     * @brief 湍流量残差置零
     * 
     */
    void SetResidualZero();    

    /**
     * @brief 初始化
     * 
     * @param[in] initialType 初始化类型
     */
    void Initialize(const Initialization::Type &initialType);

    /**
     * @brief 计算湍流量体心场梯度
     * 
     */
    void CalculateGradient();

    /**
     * @brief 累加对流通量残差
     * 
     */
    void AddConvectiveAverageResidual();

    /**
    * @brief 累加对流耗散残差
    *
    */
    void AddConvectiveDissipationResidual();

    /**
     * @brief 累加扩散通量残差
     * 
     */
    void AddDiffusiveResidual();

    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();

    /**
     * @brief 湍流量限制
     * 
     * @return int 
     */
    int CheckAndLimit();
    
    /**
    * @brief 初始化湍流边界条件
    *
    */
    void InitializeBoundary();

    /**
     * @brief 更新湍流边界条件
     * 
     */
    void UpdateBoundaryCondition();
    
    /**
     * @brief 更新边界湍流残值
     * 
     */
    void UpdateBoundaryResidual();

private:
    /**
     * @brief 确定湍流模型的指针并开辟相关物理场
     * 
     */
    void SetTurbulencePointer();

private:
    Package::FlowPackage &flowPackage; ///< 包含流场和残差场的数据包
    const Configure::Flow::FlowConfigure &flowConfigure; ///< 流动及计算参数

    BaseTurbulence *turbulence; ///< 湍流模型对象
    const Turbulence::Model &turbulenceType; ///< 湍流类型

	const FieldManipulation::GradientScheme &gradientScheme;  ///< 梯度的计算方法
	const bool &nodeCenter; ///< 是否对偶网格

	const std::vector<ElementField<Scalar> *> &turbulenceVector; ///< 湍流场指针容器
	const std::vector<ElementField<Vector> *> &turbulenceGradientVector; ///< 湍流梯度场指针容器
};

} // namespace Turbulence
#endif 