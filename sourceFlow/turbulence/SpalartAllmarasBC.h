﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//

//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_SpalartAllmarasBC_
#define _sourceFlow_turbulence_SpalartAllmarasBC_

#include "sourceFlow/turbulence/SpalartAllmaras.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流SA-DES类(DES97)
 * 
 */
class  SpalartAllmarasBC : public SpalartAllmaras
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
	SpalartAllmarasBC(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
	~SpalartAllmarasBC();

	/**
	* @brief 累加源项通量残差
	*
	*/
	void AddSourceResidual();


private:
    //ElementField<Scalar> &nut; ///< 湍流量nut
    //ElementField<Vector> *gradientNut; ///< 湍流量nut梯度场
    //ElementField<Scalar> &residualNut; ///< 湍流量nut残值场
    //ElementField<Scalar> *edgeMax;     ///< 网格单元中最大边长的体场
	//ElementField<Scalar> *ldOverlr;     ///< 网格单元中最大边长的体场
	//ElementField<Scalar> deltaDES;     ///< 网格单元中最大边长的体场


    //const Scalar Cb1; ///< SA模型的专有常数Cb1
    //const Scalar Cb2; ///< SA模型的专有常数Cb2
    //const Scalar Cnu1; ///< SA模型的专有常数Cnu1
    //const Scalar Cnu2; ///< SA模型的专有常数Cnu2
    //const Scalar Cnu3; ///< SA模型的专有常数Cnu3
    //const Scalar sigma; ///< SA模型的专有常数sigma
    //const Scalar karmann; ///< SA模型的专有常数karmann
    //const Scalar Cw1; ///< SA模型的专有常数Cw1
    //const Scalar Cw2; ///< SA模型的专有常数Cw2
    //const Scalar Cw3; ///< SA模型的专有常数Cw3
    //const Scalar Ct1; ///< SA模型的专有常数Ct1
    //const Scalar Ct2; ///< SA模型的专有常数Ct2
    //const Scalar Ct3; ///< SA模型的专有常数Ct3
    //const Scalar Ct4; ///< SA模型的专有常数Ct4
    //
    //const Scalar Cv1p3; ///< SA模型局部量Cv1p3
    //const Scalar Cw3p6; ///< SA模型局部量Cw3p6
    //const Scalar kap2; ///< SA模型局部量kap2
    //const Scalar rkap2; ///< SA模型局部量rkap2
    //const Scalar rsigma; ///< SA模型局部量rsigma
    //const Scalar Cw1k; ///< SA模型局部量Cw1k
    //const Scalar Cb2s; ///< SA模型局部量Cb2s
    //const Scalar fwLimit; ///< SA模型局部量fwLimit
    //const Scalar fwstar; ///< SA模型局部量fwstar
    //const Scalar rlim; ///< SA模型局部量rlim

    // DES常数
    //const Scalar Cdes; //SA-DES模型中网格尺度的缩放系数

};

} // namespace Turbulence
#endif 