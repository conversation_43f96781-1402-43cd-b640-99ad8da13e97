﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file TurbulenceBoundary.h
//! <AUTHOR> 乔龙
//! @brief 湍流边界类
//! @date 2022-06-27
//
//------------------------------修改日志----------------------------------------
// 2022-06-27 李艳亮 乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_TurbulenceBoundary_
#define _sourceFlow_turbulence_TurbulenceBoundary_

#include "sourceFlow/package/FlowPackage.h"
#include "sourceFlow/configure/FlowConfigure.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流边界类
 * 
 */
class  TurbulenceBoundary
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场包
     */
    TurbulenceBoundary(Package::FlowPackage &flowPackage_);

    /**
     * @brief 初始化
     * 
     */
    void Initialize();    

    /**
     * @brief 更新湍流边界条件
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 更新边界湍流残值
     * 
     */
    void UpdateBoundaryResidual();

    /**
     * @brief 获取远场值
     * 
     * @return const std::vector<Scalar>& 
     */
    const std::vector<Scalar> &GetFarValue();

    /**
     * @brief 计算壁面函数
     * 
     * @param[in] elementID 单元编号
     * @return Scalar 
     */
    Scalar WallFunction(const int &elementID);

private:
    /**
     * @brief 计算来流的相关湍流量值
     * 
     */
    void CalculateFreeStreamValue();

    /**
    * @brief 更新物面边界处的湍流量
    *
    * @param[in] patchID 边界编号
    */
    void UpdateWallGeneral(const int &patchID);

    /**
    * @brief 更新湍流量全部来自内场
    *
    * @param[in] patchID 边界编号
    */
    void UpdateFromInnerField(const int &patchID);

    /**
    * @brief 根据速度方向更新湍流量（取内场或者参考值）
    *
    * @param[in] patchID 边界编号
    */
    void UpdateAccordingToVelocity(const int &patchID);
    
    /**
     * @brief 用远场参考值更新边界处的湍流量
     * 
     * @param[in] patchID 边界编号
     */
    void UpdateFromFreeStream(const int &patchID);

private:
    Package::FlowPackage &flowPackage; ///< 包含流场和残差场的数据包
    Mesh *mesh; ///< 网格指针
    
    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &muLaminar; ///< 层流粘性系数
    ElementField<Scalar> *muTurbulent; ///< 湍流粘性系数
    ElementField<Scalar> *alphaT; ///< 有效湍流粘性系数
    ElementField<Scalar> &residualEnergy; ///< 能量残值
    
	BlockSparseMatrix *jacobianTur; ///< 湍流Jacobian矩阵
    const bool &updateJacobian; ///< Jacobian是否更新标识

    const std::vector<ElementField<Scalar> *> &turbulenceVector; ///< 湍流场指针容器
	const std::vector<ElementField<Vector> *> &turbulenceGradientVector; ///< 湍流梯度场指针容器
    const std::vector<ElementField<Scalar> *> &turbulenceResidualVector; ///< 湍流残值场指针容器
    const std::vector<FlowMacro::Scalar> turbulenceMacro; ///< 湍流量数量
    std::vector<Scalar> &freeStreamTurbulenceValue; ///< 存放来流的相关湍流量值的容器
    
    std::vector<Boundary::Type> flowBoundaryType; ///< 边界条件类型容器
    
    Scalar freeStreamIntensity; ///< 自由流湍流度
    Scalar freeStreamTurbulentViscosityRatio; ///< 自由流湍流与层流的粘性比
    Scalar freeStreamDensity; ///< 自由流密度
    Scalar freeStreamTemperature; ///< 自由流温度
    Scalar freeStreamPressure; ///< 自由流压强
    Vector freeStreamVelocity; ///< 自由流速度
    Scalar freeStreamMuLaminar; ///< 自由流层流粘性系数
    Scalar freeStreamRe; ///< 自由流雷诺数
    Scalar freeStreamMa; ///< 自由流马赫数

    const int &level; ///< 当前网格层级
    const bool &nodeCenter; ///< 格点标识
    int turbulenceSize; ///< 湍流量数量
    bool kOmegaV2Flag; ///< kOmegaV2标识
};

} // namespace Turbulence
#endif 