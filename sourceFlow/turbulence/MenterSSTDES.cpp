﻿#include "sourceFlow/turbulence/MenterSSTDES.h"

namespace Turbulence
{

	MenterSSTDES::MenterSSTDES(Package::FlowPackage &flowPackage)
		:
		MenterSST(flowPackage),
		k(*GetTurbulencePointer(FlowMacro::Scalar::K)),
		omega(*GetTurbulencePointer(FlowMacro::Scalar::OMEGA))
	{
		const std::vector<Scalar> &farValue = turbulenceBoundary.GetFarValue();
		for (int m = 0; m < turbulenceSize; ++m)
		{
			if (turbulenceMacro[m] == FlowMacro::Scalar::K) kFree = farValue[m];
			if (turbulenceMacro[m] == FlowMacro::Scalar::OMEGA) omegaFree = farValue[m];
		}
		const Scalar lrans = sqrt(kFree) / (0.09 * omegaFree);

		Cdes = flowPackage.GetFlowConfigure().GetModel().DESconstant;
		ldOverlr = flowPackage.GetField().ldOverlr;
		CalculateFilterWidth();

		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar lrans = Max(mesh->GetNearWallDistance(elementID), SMALL);
			const Scalar deltaMaxTemp = deltaMax->GetValue(elementID);
			const Scalar ldes = Min(lrans, Cdes*deltaMaxTemp);

			ldOverlr->SetValue(elementID, ldes / lrans);
		}
	}

	MenterSSTDES::~MenterSSTDES()
	{
	}

	
	void MenterSSTDES::AddSourceResidual()
	{
		// 计算湍流源项
		const int elementNumber = mesh->GetElementNumberInDomain();
		for (int index = 0; index < elementNumber; ++index)
		{
			const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar deltaMaxTemp = deltaMax->GetValue(elementID);
			const Scalar &kI = k.GetValue(elementID);
			const Scalar &wI = omega.GetValue(elementID);
			const Scalar lrans = sqrt(kI) / (0.09 * wI);
			const Scalar ldes = Min(deltaMaxTemp, lrans);
			const Scalar ldOverlrTemp = ldes / lrans;
			ldOverlr->SetValue(elementID,  ldOverlrTemp);
			MenterSST::AddSourceResidual(elementID, 1.0, ldOverlrTemp);
		}

		return;
	}




}//namespace Turbulence
