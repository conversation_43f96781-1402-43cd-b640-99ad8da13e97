﻿#include "sourceFlow/turbulence/BaseTurbulence.h"

namespace Turbulence
{
BaseTurbulence::BaseTurbulence(Package::FlowPackage &flowPackage_)
    :
    flowPackage(flowPackage_), mesh(flowPackage_.GetMeshStruct().mesh),
    currentLevel(flowPackage_.GetMeshStruct().level),
    rho(*flowPackage_.GetField().density),
    U(*flowPackage_.GetField().velocity),
    p(*flowPackage_.GetField().pressure),
    T(*flowPackage_.GetField().temperature),
    residualEnergy(*flowPackage_.GetResidualField().residualEnergy),
    gradientU(*flowPackage_.GetGradientField().gradientU),
    muLaminar(*flowPackage_.GetField().muLaminar),
    muTurbulent(flowPackage_.GetField().muTurbulent),
    unsteadyFlag(flowPackage_.GetUnsteadyStatus().unsteadyFlag),
    turbulenceVector(flowPackage_.GetField().turbulence),
    turbulenceResidualVector(flowPackage_.GetResidualField().residualTurbulence),
    turbulenceGradientVector(flowPackage_.GetGradientField().gradientTurbulence),
    jacobianTurbulence(flowPackage_.GetField().jacobianTurbulence),
    timeImplicitFlag(flowPackage_.GetFlowConfigure().GetTimeScheme().innnerLoopType != Time::Scheme::RUNGE_KUTTA),
    nodeCenter(flowPackage_.GetFlowConfigure().GetPreprocess().dualMeshFlag),
    turbulenceMacro(flowPackage_.GetTurbulentStatus().variableMacro),
    turbulenceResidualMacro(flowPackage_.GetTurbulentStatus().residualMacro),
    turbulenceGradientMacro(flowPackage_.GetTurbulentStatus().gradientMacro),
    turbulenceBoundary(flowPackage_),
    crossDiffusionFlag(flowPackage_.GetFlowConfigure().GetModel().crossDiffusionFlag),
    distance(flowPackage_.GetExtraInfo().distance),
    distanceNormal(flowPackage_.GetExtraInfo().distanceNormal),
	jacobianTur(flowPackage.GetImplicitSolver().jacobianTur),
    updateJacobian(flowPackage.GetImplicitSolver().updateJacobian)
{
    turbulenceSize = turbulenceMacro.size();

    viscousScheme = flowPackage.GetFlowConfigure().GetFluxScheme(currentLevel).viscous;
    turbulenceOrder = flowPackage_.GetFlowConfigure().GetFluxScheme(currentLevel).turbulenceOrder;

    lowMachFlag = flowPackage.GetFlowConfigure().GetFlowReference().mach < 0.3;
    entropyFixFlag = flowPackage.GetFlowConfigure().GetAcceleration().entropyFixFlag;

	desFlag = flowPackage.GetTurbulentStatus().desFlag;

    Jacobian_i.Resize(turbulenceSize, turbulenceSize);
    Jacobian_j.Resize(turbulenceSize, turbulenceSize);

    implicitFlag = jacobianTur != nullptr;
}

BaseTurbulence::~BaseTurbulence()
{
}

void BaseTurbulence::SetResidualZero()
{
    for (int m = 0; m < turbulenceSize; ++m)
    {
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            turbulenceResidualVector[m]->SetValue(elementID, Scalar0);
        }
    }
    
    return;
}

void BaseTurbulence::BoundaryInitialize()
{
    turbulenceBoundary.Initialize();
}

void BaseTurbulence::UpdateBoundaryCondition()
{
    turbulenceBoundary.UpdateBoundaryCondition();
}

void BaseTurbulence::UpdateBoundaryResidual()
{
    turbulenceBoundary.UpdateBoundaryResidual();
}

void BaseTurbulence::AddConvectiveDissipationResidual()
{
    if (implicitFlag) return;

    std::vector<ElementField<Scalar> *> fields;
    
    if (nodeCenter)
    {
        for (int m = 0; m < turbulenceSize; ++m)
        {
            fields.push_back(&flowPackage.GetTempElementField("turbulence" + ToString(m), Scalar0));
            for (int i = 0; i < mesh->GetElementNumberAll(); ++i)
                fields[m]->SetValue(i, turbulenceVector[m]->GetValue(i));
        }

        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        {
            const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
            if (typeString > Boundary::Type::WALL)
            {
                for (int index = 0; index < mesh->GetBoundaryFaceSize(patchID); ++index)
                {
                    const int &faceID = mesh->GetBoundaryFaceID(patchID, index);
                    const Face &face = mesh->GetFace(faceID);
                    const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
                    const int &innerID = mesh->GetInnerElementIDForBoundaryElement(patchID, index);

                    for (int m = 0; m < turbulenceSize; ++m)
                        fields[m]->SetValue(ownerID, fields[m]->GetValue(innerID));
                }
            }
        }
    }
    else
    {
        fields = turbulenceVector;
    }
    
    // 计算单元周围最小值
    if (turbulenceOrder == Flux::ReconstructionOrder::SECOND)
    {
        localMinimum.resize(turbulenceSize);
        for (int m = 0; m < turbulenceSize; ++m)
        {
            localMinimum[m] = &flowPackage.GetTempElementField("localMinimum" + ToString(m), INF);
        }

        // 内部面循环
        const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
        for (int index = 0; index < innerFaceNumber; ++index)
        {
            // 得到面相关信息
            const int &faceID = mesh->GetInnerFaceIDInDomain(index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetNeighborID();

            // 计算最小值
            for (int m = 0; m < turbulenceSize; ++m)
            {
                localMinimum[m]->SetValue(ownerID, Min(localMinimum[m]->GetValue(ownerID), turbulenceVector[m]->GetValue(neighID)));
                localMinimum[m]->SetValue(neighID, Min(localMinimum[m]->GetValue(neighID), turbulenceVector[m]->GetValue(ownerID)));
            }
        }
    
        for (int m = 0; m < turbulenceSize; ++m) localMinimum[m]->SetGhostlValueParallel();
    }
    
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
        const Scalar &faceArea = mesh->GetFace(faceID).GetArea();
        const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
        const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
        Scalar lambda = fabs(UFace & faceNormal);

        // // 熵修正
        // if (entropyFixFlag)
        // {
        //     Scalar delta;
        //     if (lowMachFlag)
        //     {
        //         delta = Max(lambda, 0.1 * (lambda + UFace.Mag()));
        //     }
        //     else
        //     {
        //         const Scalar soundFace = 0.5 * (A.GetValue(ownerID) + A.GetValue(neighID));
        //         delta = Max(lambda, 0.1 * (lambda + soundFace));
        //     }
        //     lambda = 0.5 * (lambda * lambda + delta * delta) / (delta + SMALL);
        // }

        // 计算通量
        const Scalar flux = 0.5 * lambda * rhoFace * faceArea;
        for (int m = 0; m < turbulenceSize; ++m)
        {
            Scalar deltaPhi = fields[m]->GetValue(neighID) - fields[m]->GetValue(ownerID);
            if (turbulenceOrder == Flux::ReconstructionOrder::SECOND)
                deltaPhi -= CalculateCorrectDelta(faceID, m, fields);

            const Scalar fluxPhi = -flux * deltaPhi;
            turbulenceResidualVector[m]->AddValue(ownerID, fluxPhi);
            turbulenceResidualVector[m]->AddValue(neighID, -fluxPhi);
        }
    }

    if (nodeCenter)
    {
        for (int m = 0; m < turbulenceSize; ++m) flowPackage.FreeTempField(*fields[m]);
    }

    if (turbulenceOrder == Flux::ReconstructionOrder::SECOND)
    {
        for (int m = 0; m < turbulenceSize; ++m) flowPackage.FreeTempField(*localMinimum[m]);
        localMinimum.clear();
    }

    return;
}

void BaseTurbulence::AddConvectiveAverageResidual()
{
    if (implicitFlag) this->AddConvectiveAverageResidualImplicit();
    else              this->AddConvectiveAverageResidualExplicit();
}

void BaseTurbulence::AddConvectiveAverageResidualImplicit()
{
    if (jacobianTur && updateJacobian)
    {
        Jacobian_i.SetZero();
        Jacobian_j.SetZero();
    }

    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        const Vector faceArea = mesh->GetFace(faceID).GetNormal() * mesh->GetFace(faceID).GetArea();

        const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
        const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));

        const Scalar UFaceNormal = UFace & faceArea;

        // 一阶迎风
        Scalar a0 = Scalar0, a1 = Scalar0;
        if (UFaceNormal > 0) a0 = UFaceNormal;
        else                 a1 = UFaceNormal;

        for (int m = 0; m < turbulenceSize; ++m)
        {
            const Scalar fluxPhi = a0 * rho.GetValue(ownerID) * turbulenceVector[m]->GetValue(ownerID) + a1 * rho.GetValue(neighID) * turbulenceVector[m]->GetValue(neighID);
            turbulenceResidualVector[m]->AddValue(ownerID, fluxPhi);
            turbulenceResidualVector[m]->AddValue(neighID, -fluxPhi);
        }

        if (jacobianTur && updateJacobian)
        {
            for (int m = 0; m < turbulenceSize; ++m)
            {
                Jacobian_i(m, m) = a0;
                Jacobian_j(m, m) = a1;
            }
            jacobianTur->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
        }
    }

    // 边界面循环
    const int &boundarySize = mesh->GetBoundarySize();
    for (int patchID = 0; patchID < boundarySize; ++patchID)
    {
        const auto &bcType = flowPackage.GetFlowConfigure().GetLocalBoundary(currentLevel, patchID).type;
        if ( bcType == Boundary::Type::SYMMETRY || bcType > Boundary::Type::WALL ) continue;

        const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
        for (int index = 0; index < faceSize; ++index)
        {
            // 得到面相关信息
            const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();
            const Vector faceArea = mesh->GetFace(faceID).GetNormal() * mesh->GetFace(faceID).GetArea();

            const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
            const Scalar UFaceNormal = UFace & faceArea;

            // 一阶迎风
            if (UFaceNormal > 0)
            {
                const Scalar rhoTempOwner = rho.GetValue(ownerID);
                for (int m = 0; m < turbulenceSize; ++m)
                {
                    const Scalar fluxPhi = UFaceNormal * rhoTempOwner * turbulenceVector[m]->GetValue(ownerID);
                    turbulenceResidualVector[m]->AddValue(ownerID, fluxPhi);
                }

                if (jacobianTur && updateJacobian)
                {
                    for (int m = 0; m < turbulenceSize; ++m) Jacobian_i(m, m) = UFaceNormal;
                    jacobianTur->AddBlock2Diag(ownerID, Jacobian_i);
                }
            }
            else
            {
                const Scalar rhoTempNeigh = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
                for (int m = 0; m < turbulenceSize; ++m)
                {
                    const Scalar phiFace = 0.5 * (turbulenceVector[m]->GetValue(ownerID) + turbulenceVector[m]->GetValue(neighID));
                    const Scalar fluxPhi = UFaceNormal * rhoTempNeigh * phiFace;
                    turbulenceResidualVector[m]->AddValue(ownerID, fluxPhi);
                    
                    // phiFace为固定值，不需要求导，对jacobianTur的贡献为零
                }
            }
        }
    }

    return;
}

void BaseTurbulence::AddConvectiveAverageResidualExplicit()
{
    // 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();

            const Vector UFace = U.GetValue(ownerID) + U.GetValue(neighID);
            const Scalar rhoFace = rho.GetValue(ownerID) + rho.GetValue(neighID);
            const Scalar rhoUFlux = 0.25 * rhoFace * (UFace & face.GetNormal()) * face.GetArea();
            for (int m = 0; m < turbulenceSize; ++m)
            {
                const Scalar fluxPhi = rhoUFlux * 0.5 * ( turbulenceVector[m]->GetValue(ownerID)
                                                        + turbulenceVector[m]->GetValue(neighID) );
                turbulenceResidualVector[m]->AddValue(ownerID, fluxPhi);
            }
        }
    }

    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        // 计算通量
        const Vector UFace = U.GetValue(ownerID) + U.GetValue(neighID);
        const Scalar rhoFace = rho.GetValue(ownerID) + rho.GetValue(neighID);
        const Scalar rhoUFlux = 0.25 * rhoFace * (UFace & face.GetNormal()) * face.GetArea();
        for (int m = 0; m < turbulenceSize; ++m)
        {
            const Scalar fluxPhi = rhoUFlux * 0.5 * ( turbulenceVector[m]->GetValue(ownerID)
                                                    + turbulenceVector[m]->GetValue(neighID) );
            turbulenceResidualVector[m]->AddValue(ownerID, fluxPhi);
			turbulenceResidualVector[m]->AddValue(neighID, -fluxPhi);
        }
    }

    return;
}

void BaseTurbulence::AddDiffusiveResidual()
{
    if (implicitFlag) this->AddDiffusiveResidualImplicit();
    else              this->AddDiffusiveResidualExplicit();
}

void BaseTurbulence::AddDiffusiveResidualImplicit()
{
    if (jacobianTur && updateJacobian)
    {
        Jacobian_i.SetZero();
        Jacobian_j.SetZero();
    }

    const bool thinLayer = viscousScheme != Flux::Flow::Viscous::CENTRAL_FULL;

    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        const Vector &faceNorm = face.GetNormal();
        const Scalar &faceArea = face.GetArea();

        // 计算面心处扩散系数
        const std::vector<std::pair<Scalar, Scalar>> gammaFace = CalculateGammaFace(faceID);
        const Scalar metric = faceArea / distance[faceID];

        for (int m = 0; m < turbulenceSize; ++m)
        {
            // 1.计算正交部分通量贡献
            const Scalar deltaPhi = turbulenceVector[m]->GetValue(neighID) - turbulenceVector[m]->GetValue(ownerID);
            Scalar gradPhiFlux = metric * deltaPhi;

            // 2.计算切向部分通量贡献
            if (thinLayer)
            {
                const Vector gradFace = 0.5 * (turbulenceGradientVector[m]->GetValue(ownerID) + turbulenceGradientVector[m]->GetValue(neighID));
                const Vector gradFaceTang = gradFace - distanceNormal[faceID] * (distanceNormal[faceID] & gradFace);
                gradPhiFlux += faceArea * (faceNorm & gradFaceTang);
            }

            // 3.残值更新
            turbulenceResidualVector[m]->AddValue(ownerID, -gammaFace[m].first * gradPhiFlux);
            turbulenceResidualVector[m]->AddValue(neighID, gammaFace[m].second * gradPhiFlux);

            // 4.通量Jacobian计算
            if (jacobianTur && updateJacobian)
            {
                const Scalar temp = thinLayer ? metric : metric * (distanceNormal[faceID] & faceNorm);
                Jacobian_i(m, m) =  gammaFace[m].first * temp / rho.GetValue(ownerID);
                Jacobian_j(m, m) = -gammaFace[m].second * temp / rho.GetValue(neighID);
                if (turbulenceSize == 1)
                {
                    const Scalar sigmaInv = 1.5;
                    const Scalar jacobianTemp = 0.5 * gradPhiFlux * sigmaInv;
                    Jacobian_i(m, m) -= jacobianTemp / rho.GetValue(ownerID);
                    Jacobian_j(m, m) -= jacobianTemp / rho.GetValue(neighID);
                }
            }
        }

        if (jacobianTur && updateJacobian)
            jacobianTur->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
    }

    // 边界面循环
    if (!nodeCenter)
    {
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                // 得到面相关信息
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const int &ownerID = face.GetOwnerID();
                const int &neighID = face.GetNeighborID();

                // 计算面心处扩散系数
                const std::vector<std::pair<Scalar, Scalar>> gammaFace = CalculateGammaFace(faceID);
                const Scalar metric = face.GetArea() / distance[faceID];

                for (int m = 0; m < turbulenceSize; ++m)
                {
                    // 边界面切向通量贡献为零
                    const Scalar deltaPhi = turbulenceVector[m]->GetValue(neighID) - turbulenceVector[m]->GetValue(ownerID);
                    const Scalar gradPhiFlux = metric * deltaPhi;
                    turbulenceResidualVector[m]->AddValue(ownerID, -gammaFace[m].first * gradPhiFlux);

                    if (jacobianTur && updateJacobian)
                    {
                        Jacobian_i(m, m) = gammaFace[m].first * metric / rho.GetValue(ownerID);
                        if (turbulenceSize == 1)
                        {
                            const Scalar sigmaInv = 1.5;
                            const Scalar jacobianTemp = 0.5 * gradPhiFlux * sigmaInv;
                            Jacobian_i(m, m) -= jacobianTemp / rho.GetValue(ownerID);
                        }
                    }
                }

                if (jacobianTur && updateJacobian) jacobianTur->AddBlock2Diag(ownerID, Jacobian_i);
            }
        }
    }
	
    return;
}

void BaseTurbulence::AddDiffusiveResidualExplicit()
{
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        //计算面心处扩散系数
        const std::vector<std::pair<Scalar, Scalar>> gammaFace = CalculateGammaFace(faceID);
        const Scalar metric = face.GetArea() / distance[faceID];

        for (int m = 0; m < turbulenceSize; ++m)
        {
            // 1.计算正交部分的贡献
            const Scalar deltaPhi = turbulenceVector[m]->GetValue(neighID) - turbulenceVector[m]->GetValue(ownerID);
            Scalar fluxPhiLeft = gammaFace[m].first * metric * deltaPhi;
            Scalar fluxPhiRight = gammaFace[m].second * metric * deltaPhi;

            // 2.计算切向部分的贡献
            if (viscousScheme == Flux::Flow::Viscous::CENTRAL_FULL)
            {
                const Vector gradFace = 0.5 * ( turbulenceGradientVector[m]->GetValue(ownerID)
                                              + turbulenceGradientVector[m]->GetValue(neighID) );
                const Vector &direction = distanceNormal[faceID];
                const Vector gradFaceTang = gradFace - direction * (direction & gradFace);
                const Scalar temp = face.GetArea() * (face.GetNormal() & gradFaceTang);
                fluxPhiLeft += gammaFace[m].first * temp;
                fluxPhiRight += gammaFace[m].second * temp;
            }

            // 3.通量累加
            turbulenceResidualVector[m]->AddValue(ownerID, -fluxPhiLeft); //扩散项对于残差是负号
            turbulenceResidualVector[m]->AddValue(neighID, fluxPhiRight);
        }
    }

    // 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            const int &neighID = face.GetNeighborID();
            const Scalar metric = face.GetArea() / distance[faceID];

            // 计算面通量, 不计算切向部分的贡献
            if (nodeCenter)
            {
                // const int &innerID = mesh->GetInnerElementIDForBoundaryElement(patchID, index);
                // const auto gammaFace = CalculateGammaFace(faceID);
                // for (int m = 0; m < turbulenceSize; ++m)
                // {
                //     const Scalar deltaPhi = turbulenceVector[m]->GetValue(ownerID) - turbulenceVector[m]->GetValue(innerID);
                //     const Scalar fluxPhiLeft = gammaFace[m].first * metric * deltaPhi;
                //     turbulenceResidualVector[m]->AddValue(ownerID, -fluxPhiLeft); //扩散项对于残差是负号
                // }
            }
            else
            {
                const auto gammaFace = CalculateGammaFace(faceID);
                for (int m = 0; m < turbulenceSize; ++m)
                {
                    const Scalar deltaPhi = turbulenceVector[m]->GetValue(neighID) - turbulenceVector[m]->GetValue(ownerID);
                    const Scalar fluxPhiLeft = gammaFace[m].first * metric * deltaPhi;
                    turbulenceResidualVector[m]->AddValue(ownerID, -fluxPhiLeft); //扩散项对于残差是负号
                }
            }
        }
    }
    
    return;
}

void BaseTurbulence::CalculateFilterWidth()
{
	deltaMax = nullptr;

	if (desFlag)
	{
		bool filterVolume = flowPackage.GetFlowConfigure().GetModel().filterVolume;
		//deltaMax = flowPackage.GetField().deltaMax;
		deltaMax = new ElementField<Scalar>(mesh, Scalar0, "DELTAMAX");

		if (filterVolume)
		{
			//使用体积尺度计算DES
			const int elementNumber = mesh->GetElementNumberInDomain();
			for (int index = 0; index < elementNumber; ++index)
			{
				const int &elementID = mesh->GetElementIDInDomain(index);
				const Scalar &volume = mesh->GetElement(elementID).GetVolume();
				const Scalar delta_vol = pow(volume, 1 / 3.0);
				deltaMax->SetValue(elementID, delta_vol);
			}
		}
		else
		{
			// 使用单元边长尺度计算DES
			const int &boundarySize = mesh->GetBoundarySize();
			for (int patchID = 0; patchID < boundarySize; ++patchID)
			{
				const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
				for (int index = 0; index < faceSize; ++index)
				{
					const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
					const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
					const Vector &ownerElementCenter = mesh->GetElement(ownerID).GetCenter();

					const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
					const Scalar edgeLength = 2.0 * (ownerElementCenter - faceCenter).Mag();
					const Scalar ownEdgeMaxLength = deltaMax->GetValue(ownerID);
					if (edgeLength > ownEdgeMaxLength) deltaMax->SetValue(ownerID, edgeLength);
				}
			}

			const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
			for (int index = 0; index < innerFaceNumber; ++index)
			{
				const int &faceID = mesh->GetInnerFaceIDInDomain(index);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();

				const Vector &faceCenter = mesh->GetFace(faceID).GetCenter();
				const Vector &ownerElementCenter = mesh->GetElement(ownerID).GetCenter();
				const Vector &neighElemementCenter = mesh->GetElement(neighID).GetCenter();

				const Scalar ownerEdgeLenghtTemp = 2.0 * (ownerElementCenter - faceCenter).Mag();
				const Scalar neighEdgeLenghtTemp = 2.0 * (neighElemementCenter - faceCenter).Mag();

				const Scalar ownEdgeMaxLength = deltaMax->GetValue(ownerID);
				const Scalar neiEdgeMaxLength = deltaMax->GetValue(neighID);

				if (ownerEdgeLenghtTemp > ownEdgeMaxLength) deltaMax->SetValue(ownerID, ownerEdgeLenghtTemp);
				if (neighEdgeLenghtTemp > neiEdgeMaxLength) deltaMax->SetValue(neighID, neighEdgeLenghtTemp);
			}
		}
	}
	else
	{
		return;
	}
}

ElementField<Scalar>* BaseTurbulence::GetTurbulencePointer(const FlowMacro::Scalar &macro)
{
    for (int k= 0; k < turbulenceSize; ++k)
        if(turbulenceMacro[k] == macro) return turbulenceVector[k];
    
    return nullptr;
}

ElementField<Scalar>* BaseTurbulence::GetTurbulenceResidualPointer(const FlowMacro::Scalar &macro)
{
    for (int k= 0; k < turbulenceSize; ++k)
        if(turbulenceResidualMacro[k] == macro) return turbulenceResidualVector[k];
    
    return nullptr;
}

ElementField<Vector>* BaseTurbulence::GetTurbulenceGradientPointer(const FlowMacro::Vector &macro)
{
    for (int k= 0; k < turbulenceSize; ++k)
        if(turbulenceGradientMacro[k] == macro) return turbulenceGradientVector[k];
    
    return nullptr;
}

Scalar BaseTurbulence::CalculateCorrectDelta(const int &faceID, const int &turIndex, const std::vector<ElementField<Scalar> *> fields)
{
    // 得到面相关信息
    const Face &face = mesh->GetFace(faceID);
    const int &ownerID = face.GetOwnerID();
    const int &neighID = face.GetNeighborID();

    //比较最小值与当前值的差异
    if (localMinimum[turIndex]->GetValue(ownerID) > 2.0 * fields[turIndex]->GetValue(ownerID)) return 0.0;
    if (localMinimum[turIndex]->GetValue(neighID) > 2.0 * fields[turIndex]->GetValue(neighID)) return 0.0;
    
    //计算左右差量值    
    const Vector distanceVector = mesh->GetElement(neighID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
    const Scalar deltaLeft = distanceVector & turbulenceGradientVector[turIndex]->GetValue(ownerID);
    const Scalar deltaRight = distanceVector & turbulenceGradientVector[turIndex]->GetValue(neighID);

    const Scalar delta = fields[turIndex]->GetValue(neighID) - fields[turIndex]->GetValue(ownerID);
    const Scalar deltaLeft2 = 2.0 * deltaLeft - delta;
    const Scalar deltaRight2 = 2.0 * deltaRight - delta;

    const Scalar sgL = deltaLeft > 0.0 ? 1.0 : -1.0;
    const Scalar sgL2 = deltaLeft2 > 0.0 ? 1.0 : -1.0;
    const Scalar sgF = delta > 0.0 ? 1.0 : -1.0;
    const Scalar sgR = deltaRight > 0.0 ? 1.0 : -1.0;
    const Scalar sgR2 = deltaRight2 > 0.0 ? 1.0 : -1.0;

    const Scalar valueSign = Min(Min(Min(sgL * sgF, sgF * sgR), sgL2 * sgF), sgF * sgR2);
    const Scalar valueMin = Min(Min(Min(sgL * deltaLeft, sgR * deltaRight), sgL2 * deltaLeft2), sgR2 * deltaRight2);
    const Scalar del = 0.5 * (1.0 + valueSign) * sgL* valueMin;
    const Scalar sgDel = (del + delta) > 0.0 ? 1.0 : -1.0;
    return 2.0 * del * delta / (del + delta + SMALL * sgDel);
}


} //namespace Turbulence
