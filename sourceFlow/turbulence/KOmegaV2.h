﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file KOmegaV2.h
//! <AUTHOR>
//! @brief k-v2-omega SPF模型类
//! @date 2025-04-24
//
//------------------------------修改日志----------------------------------------
// 2025-04-24 钱琛庚
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_KOmegaV2_
#define _sourceFlow_turbulence_KOmegaV2_

#include "sourceFlow/turbulence/BaseTurbulence.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief k-v2-omega SPF模型
 * 
 */
class  KOmegaV2 : public BaseTurbulence
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
    KOmegaV2(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
    ~KOmegaV2();    

    /**
     * @brief 计算湍流粘性系数
     * 
     */
    void CalculateMuTurbulent();

    /**
     * @brief 各项残值计算前的准备工作
     * 
     */
    void PreCalculate();

    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();
    
    /**
     * @brief 湍流量限制
     * 
     * @return int 
     */
    int CheckAndLimit();    

    /**
     * @brief 湍流场初始化
     * 
     */
    void InitializeTurbulentField(){};

protected:
    /**
     * @brief 计算扩散项面心处扩散系数
     * 
     * @param[in] faceID 面编号
     * @return std::vector<std::pair<Scalar, Scalar>> 
     */
    std::vector<std::pair<Scalar, Scalar>> CalculateGammaFace(const int &faceID);


private:
    /**
     * @brief SST模型的调和函数F1
     * 
     * @param[in] rho 密度
     * @param[in] muL 层流粘性系数
     * @param[in] k 湍动能
     * @param[in] w 比耗散率
     * @param[in] d 壁面距离
     * @param[in] cross 交叉项
     * @param[in] omegaLimit0 w的下限
     * @return Scalar 
     */
    Scalar F1(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d, const Scalar &cross, const Scalar &omegaLimit0);

    /**
     * @brief SST模型的调和函数F2
     * 
     * @param[in] rho 密度
     * @param[in] muL 层流粘性系数
     * @param[in] k 湍动能
     * @param[in] w 比耗散率
     * @param[in] d 壁面距离
     * @return Scalar 
     */
    Scalar F2(const Scalar &rho, const Scalar &muL, const Scalar &k, const Scalar &w, const Scalar &d);

    /**
     * @brief 计算有效湍流黏性系数alpha_T
     * 
     * @param[in] elementID 单元编号
     * @return Scalar 
     */
    Scalar CalculateAlphaT(const int &elementID);

    ElementField<Scalar> &k; ///< 湍动能
    ElementField<Scalar> &omega; ///< 比耗散率
	//ElementField<Scalar> *fieldF1; ///< 调和函数F1场

	ElementField<Scalar> &V2Bar; ///< 全湍流脉动动能
	ElementField<Vector> *gradientV2Bar; ///< 全湍流脉动动能的梯度
	ElementField<Scalar> &residualV2Bar; ///< 全湍流脉动动能的残值

    ElementField<Vector> *gradientK; ///< 湍动能梯度
    ElementField<Vector> *gradientOmega; ///< 比耗散率梯度
    ElementField<Scalar> &residualK; ///< 湍动能残值
    ElementField<Scalar> &residualOmega; ///< 比耗散率残值

	ElementField<Scalar> *alphaT; ///< 有效湍流黏性系数
	ElementField<Scalar> *muTSmall; ///< 小尺度湍流黏性系数

    Scalar omegaFree; ///< 远场比耗散率
    Scalar kFree; ///< 远场湍动能
    Scalar omegaLimit;

    const Scalar betaK;

    const Scalar CINTinverse;
    const Scalar two3;

    const Scalar A0;
    const Scalar As;
    const Scalar Anu;
    const Scalar Abp;
    const Scalar Anat;

    const Scalar Ats;
    const Scalar Cbp;
    const Scalar Cnc;

    const Scalar Cnat;
    const Scalar Cint;
    const Scalar Cts;
    const Scalar Crnat;

    const Scalar C11;
    const Scalar C12;

    const Scalar Cr;
    const Scalar Css;
    const Scalar Ctaul;
    const Scalar COmega1;
    const Scalar COmega2;

    const Scalar ComegaR;
    const Scalar Clambda;
    const Scalar sigmaK;
    const Scalar sigmaOmega1;
    const Scalar sigmaOmega2;

    const Scalar Csst;
    const Scalar a1;
    const Scalar a2;

};

} // namespace Turbulence
#endif 
