﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MenterSST.h
//! <AUTHOR>
//! @brief 湍流类：MenterSST
//! @date 2021-04-05
//
//------------------------------修改日志----------------------------------------
// 2021-04-05 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_MenterSSTDDES_
#define _sourceFlow_turbulence_MenterSSTDDES_

#include "sourceFlow/turbulence/MenterSST.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流MenterSST_DES类
 * 
 */
class  MenterSSTDDES: public MenterSST
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage 流场包
     */
    MenterSSTDDES(Package::FlowPackage &flowPackage);

    /**
    * @brief 析构函数
    *
    */
    ~MenterSSTDDES();
    
    /**
     * @brief 累加源项通量残差
     * 
     */
    void AddSourceResidual();

    
protected:
    ElementField<Scalar> &k; ///< 湍动能
    ElementField<Scalar> &omega; ///< 比耗散率

private:
	ElementField<Scalar> *ldOverlr;     ///< 网格单元中最大边长的体场
	ElementField<Scalar> *shieldingFunction;     ///< DDES的延迟函数

	// DES常数
	Scalar Cdes; //SA-DES模型中网格尺度的缩放系数
	Scalar omegaFree; ///< 远场比耗散率
	Scalar kFree; ///< 远场湍动能

};

} // namespace Turbulence
#endif 