﻿#include "sourceFlow/turbulence/SpalartAllmarasDES.h"

namespace Turbulence
{

SpalartAllmarasDES::SpalartAllmarasDES(Package::FlowPackage &flowPackage)
    :SpalartAllmaras(flowPackage)

{
	Cdes = flowPackage.GetFlowConfigure().GetModel().DESconstant;
	ldOverlr = flowPackage.GetField().ldOverlr;
	CalculateFilterWidth();

	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar lrans = Max(mesh->GetNearWallDistance(elementID), SMALL);
		const Scalar deltaMaxTemp = deltaMax->GetValue(elementID);
		const Scalar ldes = Min(lrans, Cdes*deltaMaxTemp);

		ldOverlr->SetValue(elementID, ldes / lrans);
	}
}

SpalartAllmarasDES::~SpalartAllmarasDES()
{
}


void SpalartAllmarasDES::AddSourceResidual()
{
	// 仅细网格计算湍流方程的源项
	// if (currentLevel > 0) return;

	//源项残差按单元循环
	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar ldOverlrTemp = ldOverlr->GetValue(elementID);
		SpalartAllmaras::AddSourceResidual(elementID, ldOverlrTemp, 1.0);
	}
}


}//namespace Turbulence
