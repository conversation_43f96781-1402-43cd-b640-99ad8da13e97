﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file BaseTurbulence.h
//! <AUTHOR>
//! @brief 湍流基类，用于派生各种具体湍流模型类。
//! @date 2021-04-05
//
//------------------------------修改日志----------------------------------------
// 2021-04-05 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_turbulence_BaseTurbulence_
#define _sourceFlow_turbulence_BaseTurbulence_

#include "sourceFlow/configure/FlowConfigure.h"
#include "sourceFlow/package/FlowPackage.h"
#include "sourceFlow/turbulence/TurbulenceBoundary.h"

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流基类，用于派生各种具体湍流模型类(不能直接实例化)
 * 
 */
class  BaseTurbulence
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场包
     */
    BaseTurbulence(Package::FlowPackage &flowPackage_);

    /**
     * @brief 析构函数
     * 
     */
    virtual ~BaseTurbulence();

    /**
     * @brief 湍流量残差置零
     * 
     */
    void SetResidualZero();

    /**
     * @brief 湍流边界条件初始化
     * 
     */
    void BoundaryInitialize();

    /**
     * @brief 更新湍流边界条件
     * 
     */
    void UpdateBoundaryCondition();

    /**
     * @brief 更新边界处湍流残值（仅用于格点格式）
     * 
     */
    void UpdateBoundaryResidual();

    /**
     * @brief 累加对流通量残差
     * 
     */
    void AddConvectiveAverageResidual();
    
    /**
    * @brief 累加对流耗散残差
    *
    */
    void AddConvectiveDissipationResidual();

    /**
     * @brief 累加扩散通量残差
     * 
     */
    void AddDiffusiveResidual();

    void AddConvectiveAverageResidualImplicit();
    void AddConvectiveAverageResidualExplicit();

    void AddDiffusiveResidualImplicit();
    void AddDiffusiveResidualExplicit();

	/**
	* @brief 计算DES长度尺度
	*
	*/
	void CalculateFilterWidth();

    // *************以下虚函数需要具体湍流模型实现***********************************************
public: 
    /**
     * @brief 计算湍流粘性系数
     * 纯虚函数，在具体湍流模型中实现
     * 
     */
    virtual void CalculateMuTurbulent() = 0;

    /**
     * @brief 各项残值计算前的准备工作
     * 纯虚函数，在具体湍流模型中实现
     * 
     */
    virtual void PreCalculate() = 0;

    /**
     * @brief 累加源项通量残差
     * 纯虚函数，在具体湍流模型中实现
     * 
     */
    virtual void AddSourceResidual() = 0;

    /**
     * @brief 湍流量限制
     * 纯虚函数，在具体湍流模型中实现
     * 
     * @return int 
     */
    virtual int CheckAndLimit() = 0;

    /**
     * @brief 湍流场初始化
     * 纯虚函数，在具体湍流模型中实现
     * 
     * @return int 
     */
    virtual void InitializeTurbulentField() = 0;

protected:
    /**
     * @brief 计算扩散项面心处扩散系数
     * 
     * @param[in] faceID 面编号
     * @return std::vector<std::pair<Scalar, Scalar>>  
     */
    virtual std::vector<std::pair<Scalar, Scalar>>  CalculateGammaFace(const int &faceID) = 0;

    /**
     * @brief 获取湍流量指针
     * 
     * @param[in] macro 湍流量宏
     * @return ElementField<Scalar>* 
     */
    ElementField<Scalar>* GetTurbulencePointer(const FlowMacro::Scalar &macro);

    /**
     * @brief 获取湍流残值指针
     * 
     * @param[in] macro 湍流残值宏
     * @return ElementField<Scalar>* 
     */
    ElementField<Scalar>* GetTurbulenceResidualPointer(const FlowMacro::Scalar &macro);

    /**
     * @brief 获取湍流梯度场指针
     * 
     * @param[in] macro 湍流梯度宏
     * @return ElementField<Vector>* 
     */
    ElementField<Vector>* GetTurbulenceGradientPointer(const FlowMacro::Vector &macro);

	/**
	* @brief 计算应变率张量的模
	*
	* @param[in] elementID 单元号
	* @return Scalar
	*/
	Scalar STensorMag(const int &elementID)
	{
		const Tensor temp = gradientU.GetValue(elementID).Symm(true);
		return sqrt(2.0 * (temp && temp));
	}

	/**
	* @brief 计算旋度率张量的模
	*
	* @param[in] elementID 单元号
	* @return Scalar
	*/
	Scalar OmegaTensorMag(const int &elementID)
	{
		const Tensor temp = gradientU.GetValue(elementID).AntiSymm(true);
		return sqrt(2.0 * (temp && temp));
	}
private:
    /**
    * @brief 对流通量计算中，如果采用二阶精度，计算迎风各种人工耗散项所需的差量值
    *
    * @param[in] faceID 面编号
    * @param[in] turIndex 湍流量的序号
    * @param[in] fields 湍流量场
    * @return Scalar
    */
    Scalar CalculateCorrectDelta(const int &faceID, const int &turIndex, const std::vector<ElementField<Scalar> *> fields);

protected:
    Package::FlowPackage &flowPackage; ///< 包含流场和残差场的数据包
    TurbulenceBoundary turbulenceBoundary; ///< 湍流边界条件管理器对象
    Mesh *mesh; ///< 网格指针
    
    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强
    ElementField<Scalar> &T; ///< 温度
    ElementField<Scalar> &residualEnergy; ///< 主流的能量残差场
    ElementField<Tensor> &gradientU; ///< 速度梯度场
    ElementField<Scalar> &muLaminar; ///< 层流粘性系数
    ElementField<Scalar> *muTurbulent; ///< 湍流粘性系数
    
	BlockSparseMatrix *jacobianTur; ///< 湍流Jacobian矩阵
    const bool &updateJacobian; ///< Jacobian是否更新标识

    std::vector<ElementField<Scalar>*> jacobianTurbulence; ///< 当采用RK时：湍流的点隐式谱半径
                                                           ///< 当采用LU时：湍流的对角阵D中源项贡献部分
    const bool timeImplicitFlag; ///< 时间推进是否是隐式，true为隐式
    
    const std::vector<FlowMacro::Scalar> &turbulenceMacro; ///< 湍流量宏定义容器
    const std::vector<FlowMacro::Scalar> &turbulenceResidualMacro; ///< 湍流残值宏定义容器
    const std::vector<FlowMacro::Vector> &turbulenceGradientMacro; ///< 湍流梯度宏定义容器
    int turbulenceSize; ///< 湍流量数量
    bool crossDiffusionFlag; ///< 交叉项是否转换成扩散项来计算，true为采用扩散项形式来计算
    
    const int &currentLevel; ///< 当前网格层次
    const bool &unsteadyFlag; ///< 非定常标识，true为非定常
    const bool &nodeCenter; ///< 格点标识

	ElementField<Scalar> *deltaMax; ///< 最大边长或是体积三次方根，用于计算DES尺度
    
private:
    const std::vector<ElementField<Scalar> *> &turbulenceVector; ///< 湍流场指针容器
    const std::vector<ElementField<Scalar> *> &turbulenceResidualVector; ///< 湍流残值场指针容器
    const std::vector<ElementField<Vector> *> &turbulenceGradientVector; ///< 湍流梯度场指针容器

    std::vector<ElementField<Scalar> *> localMinimum; ///< 相邻单元最小值

    Flux::Flow::Viscous::Scheme viscousScheme; ///< 扩散项的计算方法
    const std::vector<Scalar> &distance; ///< 当地网格体心连线距离
    const std::vector<Vector> &distanceNormal; ///< 体心连线的单位化（owner指向neigher）

    Flux::ReconstructionOrder turbulenceOrder;  ///< 湍流对流项计算的重构阶数

    bool lowMachFlag; ///< 低速标识
    bool entropyFixFlag; ///< 熵修正标识

	bool desFlag; ///< DES标识
    
    Matrix Jacobian_i, Jacobian_j;

    bool implicitFlag; ///< 隐式标识
};

} // namespace Turbulence
#endif 