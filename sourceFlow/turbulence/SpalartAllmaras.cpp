﻿#include "sourceFlow/turbulence/SpalartAllmaras.h"

namespace Turbulence
{

SpalartAllmaras::SpalartAllmaras(Package::FlowPackage &flowPackage)
    :BaseTurbulence(flowPackage),
    nut(*GetTurbulencePointer(FlowMacro::Scalar::NUT)),
    residualNut(*GetTurbulenceResidualPointer(FlowMacro::Scalar::RESIDUAL_NUT)),
    gradientNut(GetTurbulenceGradientPointer(FlowMacro::Vector::GRADIENT_NUT)),
    <PERSON>b1(0.1355), <PERSON>b2(0.622), <PERSON><PERSON>1(7.1), <PERSON><PERSON>2(5.0), <PERSON>nu3(0.9), sigma(2.0 / 3.0),
    karmann(0.41), rsigma(1.0 / sigma), kap2(karmann * karmann), rkap2(1.0 / kap2),
    Cw1(Cb1 / karmann / karmann + (1.0 + Cb2) / sigma), Cw1k(Cw1*kap2),
    Cw2(0.3), Cw3(2.0),
    Ct1(1.0), Ct2(2.0), Ct3(1.2), Ct4(0.5), Cv1p3(pow(Cnu1, 3.0)), Cw3p6(pow(Cw3, 6.0)),
    Cb2s(Cb2 * rsigma), fwstar(0.424), rlim(10.0),
    fwLimit(exp(0.166666666666666 * log(1.0 + Cw3p6)))
{
	Jacobian_i.Resize(1, 1); Jacobian_i.SetZero();
    if (jacobianTur) crossDiffusionFlag = false;
}

SpalartAllmaras::~SpalartAllmaras()
{
}

int SpalartAllmaras::CheckAndLimit()
{
    return nut.CheckAndLimit(0, INF);
}

void SpalartAllmaras::CalculateMuTurbulent()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar &muLaminarTemp = muLaminar.GetValue(elementID);
        const Scalar &rhoTemp = rho.GetValue(elementID);
        const Scalar &nutTemp = nut.GetValue(elementID);
        const Scalar muTurbulentTemp = rhoTemp * nutTemp;
        const Scalar chi = muTurbulentTemp / muLaminarTemp;
        const Scalar chi3 = chi * chi * chi;
        const Scalar fnu1 = chi3 / (chi3 + Cv1p3);
        muTurbulent->SetValue(elementID, fnu1 * muTurbulentTemp);
    }

    return;
}

void SpalartAllmaras::AddSourceResidual()
{
	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		AddSourceResidual(elementID, 1.0);
	}

	return;
}

void SpalartAllmaras::AddSourceResidual(const int &elementID, const Scalar &ldOverlr, const Scalar &gammaBC)
{
	const Scalar &rhoI = rho.GetValue(elementID);
    const Scalar &nutI = nut.GetValue(elementID);
    const Scalar &muI = muLaminar.GetValue(elementID);

    const Scalar chi = rhoI * nutI / muI;
    const Scalar chi3 = chi * chi * chi;
    const Scalar fv1 = chi3 / (chi3 + Cv1p3);
    const Scalar chifv1 = 1.0 / (1.0 + chi * fv1);
    const Scalar fv2 = 1.0 - chi * chifv1;

    const Scalar &distance = mesh->GetNearWallDistance(elementID);
    const Scalar y = Max(distance, SMALL) * ldOverlr;
    const Scalar y2 = y * y;
    const Scalar kappaY2 = kap2 * y2;

    // 湍流源项生成项计算
    const Tensor omega = gradientU.GetValue(elementID).AntiSymm();
    const Scalar S = sqrt(2.0 * (omega && omega));
    const Scalar STilda = S + nutI * fv2 / kappaY2;
    const Scalar production = Cb1 * STilda * nutI * gammaBC;

    // 湍流源项壁面破坏项计算
    const Scalar r = Min(10.0, nutI / Max(STilda * kappaY2, SMALL));
    const Scalar powr6 = pow(r, 6);
    const Scalar g = r + Cw2 * (powr6 - r);
    const Scalar powg6 = pow(g, 6);
    const Scalar lim_g = (1.0 + Cw3p6) / (powg6 + Cw3p6);
    const Scalar powlimg = pow(lim_g, 1.0 / 6.0);
    const Scalar fw = g * powlimg;
    const Scalar wall_dest = - Cw1 * fw * nutI * nutI / y2;

    // 湍流源项耗散项计算
    Scalar cross = Scalar0;
    if (!crossDiffusionFlag)
    {
        const Vector &gradNutI = gradientNut->GetValue(elementID);
        cross = Cb2 * rsigma * (gradNutI & gradNutI);
    }

    // 累加残差
    const Scalar source = rhoI * (production + wall_dest + cross);
    const Scalar &volume = mesh->GetElement(elementID).GetVolume();
    residualNut.AddValue(elementID, -source * volume);

    // 源项Jacobian
    if (updateJacobian)
    {
        const Scalar dchi = rhoI / muI;
        const Scalar dchi3 = 3.0 * chi * chi * dchi;
        const Scalar dfv1 = (dchi3  - fv1 * dchi3) / (chi3 + Cv1p3);
        const Scalar dfv2 = (chi * chi * dfv1 - dchi) * (chifv1 * chifv1);
        const Scalar dSTilda = (nutI * dfv2 + fv2) / kappaY2;
        const Scalar dprod = (Cb1 * dSTilda * nutI + Cb1 * STilda) * gammaBC;

        const Scalar dr = 1.0 / Max(STilda * kappaY2, SMALL) - nutI / (STilda * kappaY2 * STilda * kappaY2) * dSTilda * kappaY2;
        const Scalar dg = dr + Cw2 * (6.0 * powr6 / r - 1) * dr;
        const Scalar dlim_g =  -(1.0 + Cw3p6) / ( (powg6 + Cw3p6) * (powg6 + Cw3p6)) * 6.0 * powg6 / g * dg;
        const Scalar dfw = dg * powlimg + g / 6.0 * powlimg / lim_g * dlim_g;
        const Scalar ddest = - Cw1 / y2 * (dfw * nutI + 2.0 * fw) * nutI;

   	    Jacobian_i(0, 0) = (dprod + ddest) * volume;
        jacobianTur->AddBlock2Diag(elementID, -Jacobian_i);
    }
    else
    {  
        // 负源项的贡献
        const Scalar jacobianI = -2.0 * Cw1 * fw * nutI * volume / y2;
        jacobianTurbulence[0]->SetValue(elementID, jacobianI);
    }
}

std::vector<std::pair<Scalar, Scalar>> SpalartAllmaras::CalculateGammaFace(const int &faceID)
{
    //得到面相关信息
    const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
    const int &neighID = mesh->GetFace(faceID).GetNeighborID();

    std::vector<std::pair<Scalar, Scalar>> gamma(1);

    //计算面心值
    Scalar muFace;
    if (mesh->JudgeBoundaryFace(faceID))
    {
        const Scalar muLaminarFace = 0.5 * (muLaminar.GetValue(ownerID) + muLaminar.GetValue(neighID));
        const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
        const Scalar nutFace = 0.5 * (nut.GetValue(ownerID) + nut.GetValue(neighID));
        muFace = muLaminarFace + rhoFace * nutFace;
        gamma[0].first = muFace / sigma;
        if (crossDiffusionFlag)
        {
            gamma[0].first *= (1.0 + Cb2);
            gamma[0].first -= Cb2 * muFace / sigma;
        }
        gamma[0].second = gamma[0].first;
    }
    else
    {
        const Scalar muLeft = muLaminar.GetValue(ownerID) + rho.GetValue(ownerID) * nut.GetValue(ownerID);
        const Scalar muRight = muLaminar.GetValue(neighID) + rho.GetValue(neighID) * nut.GetValue(neighID);
        muFace = 0.5 * (muLeft + muRight);
        gamma[0].first = muFace / sigma;
        gamma[0].second = gamma[0].first;
        if (crossDiffusionFlag)
        {
            gamma[0].first *= (1.0 + Cb2);
            gamma[0].second *= (1.0 + Cb2);
            gamma[0].first -= Cb2 * muLeft / sigma;
            gamma[0].second -= Cb2 * muRight / sigma;
        }
    }

    return gamma;
}

void SpalartAllmaras::InitializeTurbulentField()
{
    return;
    
	ElementField<Vector> Ucorrected = U;

	Scalar freeStreamRe = flowPackage.GetFlowConfigure().GetFlowReference().Reynolds;
	Vector freeStreamVelocity = flowPackage.GetFlowConfigure().GetFlowReference().velocity;
	Scalar freeStreamDensity = flowPackage.GetFlowConfigure().GetFlowReference().density;
	Scalar freeStreamMuLaminar = flowPackage.GetFlowConfigure().GetFlowReference().muLaminar;
	Scalar cfTheory = 0.058 * pow(freeStreamRe, -0.2);
	Scalar UMag = freeStreamVelocity.Mag();
	Scalar tauWall = 0.5 * cfTheory * freeStreamDensity * UMag * UMag;
	Scalar Utau = sqrt(tauWall / freeStreamDensity);
	Scalar blThickness = Scalar0;

	const Scalar Cmu = 0.09;
	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);

		const Scalar &d = mesh->GetNearWallDistance(elementID);

		Scalar yPlusTemp = Utau * d / freeStreamMuLaminar * freeStreamDensity;
		Scalar yPlus = Max(yPlusTemp, exp(-5.0));

		Scalar UMagInner = Utau / 0.41 * (log(yPlus) + 5.0);
		Scalar UMagTemp = Min(UMag, UMagInner);
		if (UMagTemp < UMag) blThickness = d;

		Scalar Ux = UMagTemp * freeStreamVelocity.X() / UMag;
		Scalar Uy = UMagTemp * freeStreamVelocity.Y() / UMag;
		Scalar Uz = UMagTemp * freeStreamVelocity.Z() / UMag;

		Vector UcorrectedLocal;
		UcorrectedLocal.SetX(Ux);
		UcorrectedLocal.SetY(Uy);
		UcorrectedLocal.SetZ(Uz);

		Ucorrected.SetValue(elementID, UcorrectedLocal);
	}

	Scalar nutTemp = 50.0 * freeStreamMuLaminar / freeStreamDensity;
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);

		Scalar d = mesh->GetNearWallDistance(elementID);
		d = Max(d, SMALL);

		if (d < blThickness)
		{
		        nut.SetValue(elementID, nutTemp);
		}
	}

	return;
}

}//namespace Turbulence
