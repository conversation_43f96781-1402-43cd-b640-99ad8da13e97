﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file flowForceNondimensionalize.cpp
//! <AUTHOR>
//! @brief 力与力矩无量纲化无量纲化处理
//! @date 2024-12-01
//
//------------------------------修改日志----------------------------------------
// 2024-12-01 吴奥奇
//     说明：创建与编写
//------------------------------------------------------------------------------

#ifndef _sourceFlow_postprocessor_Postprocessor_
#define _sourceFlow_postprocessor_Postprocessor_

#include "sourceFlow/configure/FlowConfigure.h"


/**
* @brief 无量纲化力与力矩
* 主要用于无量纲化力与力矩计算
*
*/
class  ForceNondimensionalize
{
public:
	/**
	* @brief 构造函数，创建流场后处理对象
	*
	* @param[in] flowConfigure_ 流场参数
	*/
	ForceNondimensionalize(Configure::Flow::FlowConfigure &flowConfigure_);

	/**
	* @brief 析构函数
	*
	*/
	~ForceNondimensionalize();

	// 用于定位
	enum ForceType
	{
		FORCE_PRESSURE = 0, ///< 压力
		FORCE_VISCOUS = 1, ///< 粘性力
		MOMENT_PRESSURE = 2, ///< 压力贡献的力矩
		MOMENT_VISCOUS = 3, ///< 粘性立贡献的力矩
		AREA_SUM = 4 ///< 面积分
	};

	/**
	* @brief 后处理力数据函数
	*
	*/
	void ProcessForce();

	/**
	* @brief 读取有量刚力数据
	*
	*/
	void ReadDimensionalForce();

	/**
	* @brief 无量纲化处理
	*
	*/
	void Nondimensionalize();

	/**
	* @brief 输出无量纲化力数据
	*
	*/
	void WriteNondimensionalForce();

private:
	Configure::Flow::FlowConfigure &flowConfigure; ///< 用户各种设置参数

	std::string resultPath; ///< 结果路径

	std::string resultName;
	std::vector<std::string> globalBoundaryName;
	std::vector<bool> globalWallFlag; ///< 全局所有边界是否为物面，true为物面
	int dimension;

	//三层容器，第一层为迭代步数，第二层为边界ID，第三层为力分量
	std::vector<std::vector<std::vector<Vector>>> forceDimensionalAll;
	std::vector<std::vector<std::vector<Vector>>> forceNonDimensionalAll;

	//两层容器，第一层为迭代步数，第二层为选择的边界累加后的无量纲力与力矩的和
	std::vector<std::vector<Vector>> forceNonDimensionalSum;

	//参考量以及累加的边界ID
	Vector lengthReference;
	Vector SReference;
	Vector momentCenter;
	Scalar alpha;
	Scalar beta;
	Scalar densityReference;
	Scalar velocityReference;
	Scalar pressureReference;
	std::vector<int> boundaryIDList;

	// 转换矩阵
	Matrix matrix;
};

#endif 