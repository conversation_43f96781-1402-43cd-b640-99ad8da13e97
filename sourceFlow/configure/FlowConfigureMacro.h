﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowConfigureMacro.h
//! <AUTHOR>
//! @brief 流场解算器相关枚举
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-31 乔龙
//     说明：添加注释
//
// 2021-03-29 乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_configure_FlowConfigureMacro_
#define _sourceFlow_configure_FlowConfigureMacro_

/**
 * @brief 参数命名空间
 * 
 */
namespace Configure
{
/**
 * @brief 流场参数命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 参考量类型枚举
 * 
 */
enum ReferenceType
{
    MACH_REYNOLDS = 0, ///< 来流参考量选用Mach数和Reynolds数
    MACH_DENSITY, ///< 来流参考量选用Mach数和密度
	MACH_ALTITUDE, ///< 来流参考量选用Mach数和飞行高度
    VELOCITY_PRESSURE ///< 来流参考量选用速度和压力
};
}
}

/**
 * @brief 材料命名空间
 * 
 */
namespace Material
{
/**
 * @brief 流体材料命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 密度类型枚举
 * 
 */
enum DensityType
{
    IDEAL_GAS = 0, ///< 理想气体
	MULTISPECIES_GAS, ///< 多组分气体
	REACTIVE_MULTISPECIES_GAS ///< 带化学反应的多组分气体
};

/**
 * @brief 粘性系数类型枚举
 * 
 */
enum ViscosityType
{
    CONSTANT = 0, ///< 常数
    SUTHERLAND, ///< Sutherland公式
	MIXTURE_AVERAGE ///< 用于多组分计算
};

/**
 * @brief 定压比热容类型枚举
 * 
 */
enum CpType
{
    cptConstant = 0, ///< 常数
    cptPiecewiseLinear, ///< 分段线性
    cptKineticTheory, ///< 动理论
	cpNASAPolynomial ///< NASA多项式
};

/**
 * @brief 导热系数类型枚举
 * 
 */
enum LambdaType
{
    ltConstant = 0 ///< 常数
};
}
}

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief 无粘通量计算格式枚举
 * 
 */
enum Scheme
{
    CENTRAL = 0, ///< 中心格式
    LAX_FRIEDRICHS, ///< LaxFriedrichs格式
    ROE, ///< Roe格式
    VANLEER, ///< VanLeer格式
    ROE_MOD, ///< 修改版Roe格式
    AUSM, ///< AUSM格式
    AUSMPWP, ///< AUSMPWP格式
    AUSMDV, ///< AUSMDV格式
    HLLC ///< HLLC格式
};
} // namespace Inviscid

/**
 * @brief 粘性通量命名空间
 * 
 */
namespace Viscous
{
/**
 * @brief 粘性通量计算格式枚举
 * 
 */
enum Scheme
{
    NONE_VISCOUS = 0, ///< 不计算粘性    
    CENTRAL_DISTANCE, ///< 采用距离方法计算（简化方法，需网格正交性较好）
    CENTRAL_FULL      ///< 采用全NS方程计算方法（对面心梯度进行修正）
};

} // namespace Viscous

/**
 * @brief 源项通量计算命名空间
 * 
 */
namespace Source
{
/**
 * @brief 源项通量计算格式枚举
 * 
 */
enum Scheme
{
    NONE_SOURCE = 0, ///< 不计算源项
    EXPLICIT, ///< 显式计算源项
    IMPLICIT ///< 隐式计算源项
};
} // namespace Source

/**
 * @brief 通量限制器命名空间
 * 
 */
namespace Limiter
{
/**
 * @brief 通量限制器格式枚举
 * 
 */
enum Scheme
{
    NONE_LIMITER = 0, ///< 不计算限制器
    MINMOD, ///< 采用MinMod计算限制器
    VANLEER, ///< 采用VanLeer计算限制器
    VENKATAKRISHNAN ///< 采用Venkatakrishnan计算限制器
};
} // namespace Limiter

} // namespace Flow

/**
 * @brief 面通量重构格式枚举
 * 
 */
enum ReconstructionOrder
{
    FIRST = 0, ///< 采用一阶重构（不重构）
    SECOND ///< 采用二阶重构（带限制器的线性重构）
};

} // namespace Flux

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 非定常类型枚举
 * 
 */
enum UnsteadyType
{
    STEADY = 0, ///< 定常计算
    DUAL_TIME_STEP, ///< 双时间步非定常计算
    GLOBAL_TIME_STEP ///< 全局时间步长非定常计算
};

/**
 * @brief 非定常计算时间项离散阶数枚举
 * 
 */
enum UnsteadyOrder
{
    FIRST = 0, ///< 时间项采用一阶离散
    SECOND ///< 时间项采用二阶离散
};

/**
 * @brief 时间推进类型枚举
 * 
 */
enum Scheme
{
    RUNGE_KUTTA = 0, ///< 采用Runge-Kutta显式计算
    LUSGS, ///< 采用LUSGS方法隐式计算
    Jacobian, ///< 采用Jacobian方法隐式计算
    CG, ///< 采用共轭梯度法隐式计算
    BiCG, ///< 采用双共轭梯度法隐式计算
    GMRES, ///< 采用GMRES方法隐式计算
    DPLUR
};

/**
 * @brief Runge-Kutta类型枚举
 * 
 */
enum RungeKuttaType
{
    STANDARD = 0, ///< 粘性项及耗散项每步都重新计算
    HYBRID ///< 粘性项及耗散项仅在第一步计算
};

/**
 * @brief 线性求解器预处理类型枚举
 * 
 */
enum LinearSolverPreconditionerType
{
    JACOBI = 0, ///< 采用雅可比预处理
    ILU ///< 采用ILU0预处理
};
} // namespace Time

/**
 * @brief 湍流命名空间
 * 
 */
namespace Turbulence
{
/**
 * @brief 湍流模型枚举
 * 
 */
enum Model
{
    INVISCID = 0, ///< 无粘计算
    LAMINAR, ///< 层流计算
    SPALART_ALLMARAS, ///< Spalart-Allmaras湍流模型
	SPALART_ALLMARAS_BC, ///< Spalart-Allmaras-BC转捩模型
    MENTER_SST, ///< Menter-SST湍流模型
	MENTER_SST_GAMMARE, ///< 基于Menter-SST的转捩模型
    MENTER_BSL, ///< Menter-BSL湍流模型
    MENTER_SST_ML, ///< Menter-SST 机器学习增强湍流模型
    K_EPSILON, ///< k-epsilon湍流模型
    K_OMEGA, ///< k-omega湍流模型
    K_OMEGA_V2, ///< k-omega-v2 SPF 模型
    SPALART_ALLMARAS_DES,   ///SA-DES湍流模型，DES-1997
    SPALART_ALLMARAS_DDES,  ///SA-DDES湍流模型，DDES-2006
    SPALART_ALLMARAS_IDDES, ///SA-IDDES湍流模型，IDDES-2008
    MENTER_SST_DES,   ///SA-DES湍流模型
    MENTER_SST_DDES,  ///SA-DDES湍流模型
    LES_SGS  ///LES-SGS湍流模型
};

/**
 * @brief 壁面函数计算方法枚举
 * 
 */
enum WallFunction
{
    NONE_WALL_FUNCTION = 0, ///< 不采用壁面函数
    ENHANCED ///< 采用增强壁面函数
};
} // namespace Turbulence

/**
 * @brief 初始化方法
 * 
 */
namespace Initialization
{
/**
 * @brief 初始化方法枚举
 * 
 */
enum Type
{
    REFERENCE = 0, ///< 采用参考量初始化
    STATIC_FLOW, ///< 采用静止流场初始化
    RESTART, ///< 续算采用计算结果初始化
    FILE, ///< 采用文件初始化
    NONE_INITIAL ///< 不初始化
};
}
#endif
