﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowGlobalDataMacro.h
//! <AUTHOR>
//! @brief 流场相关物理场枚举
//! @date 2021-03-31
//
//------------------------------修改日志----------------------------------------
// 2021-03-31 乔龙
//     说明：添加注释
//
// 2021-03-29 乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_configure_FlowGlobalDataMacro_
#define _sourceFlow_configure_FlowGlobalDataMacro_

/**
 * @brief 流场物理场宏
 * 
 */
namespace FlowMacro
{
/**
 * @brief 标量场宏定义枚举
 * 
 */
enum Int
{
    OversetElemType ///< 重叠网格单元类型场
};

enum Scalar
{
    RHO = 0, ///< 均流密度
    P, ///< 均流压强
    T, ///< 均流温度
    H, ///< 均流总焓
    A, ///< 当地声速
    MU_LAMINAR, ///< 均流层流动力学粘度
    MU_TURBULENT, ///< 均流湍流动力学粘度
    ALPHAT, ///< 有效湍流黏性系数

	MASSFRACTION, ///< 均流组分

    NUT, ///< 湍流运动学涡粘性系数
    K, ///< 湍动能
    EPSILON, ///< 湍流耗散率
    OMEGA, ///< 比湍流耗散率
    V2BAR, ///< 全湍流脉动动能

    RESIDUAL_MASS, ///< 质量残值
    RESIDUAL_ENERGY, ///< 能量残值

    // 湍流相关残差场
    RESIDUAL_NUT, ///< 湍流运动学涡粘性系数
    RESIDUAL_K, ///< 湍动能
    RESIDUAL_EPSILON, ///< 湍流耗散率
    RESIDUAL_OMEGA, ///< 比湍流耗散率
	RESIDUAL_GAMMA,  ///< 间歇因子
	RESIDUAL_ReThet,///< 动量Reynolds数
    RESIDUAL_V2BAR,///< 全湍流脉动动能
    
    RHO0, ///< 上一步密度旧值
    P0, ///< 上一步压强旧值
    T0, ///< 上一步温度旧值

    NUT0, ///< 湍流运动学涡粘性系数旧值
    K0, ///< 湍动能旧值
    E0, ///< 湍流耗散率旧值
    OMEGA0, ///< 比湍流耗散率旧值
    EPSILON0, ///< 湍流耗散率旧值
    V2BAR0, ///< 全湍流脉动动能旧值

    // 上上步物理场
    RHO00, ///< 上上步密度旧值
    P00, ///< 上上步压强旧值
    T00, ///< 上上步温度旧值

    NUT00, ///< 湍流运动学涡粘性系数旧值
    K00, ///< 湍动能旧值
    OMEGA00, ///< 比湍流耗散率旧值
    EPSILON00, ///< 湍流耗散率旧值
    V2BAR00, ///< 全湍流脉动动能旧值

    LAMBDA_CONVECTIVE, ///< 对流谱半径
    LAMBDA_VISCOUS, ///< 粘性谱半径
    LAMBDA_TUR_CONVECTIVE, ///< 湍流方程的对流谱半径
    LAMBDA_TUR_VISCOUS, ///< 湍流方程的粘性谱半径

    DELTA_T, ///< 时间步长

    PRESSURE_SWITCH, ///< 压力探测器
    
    PRECONDITION_PHI, ///< 预处理矩阵的phi

	LDOVERLR, ///< l_des / l_rans
	SHIELDINGFUNCTION, ///< DDES延迟函数
	MEANMUTURBULENT, ///< 时均湍流黏性系数场
	MEANMU, ///< 时均主流黏性系数场
	MEANPRESSURE, ///< 时均压力场

	GAMMA,  //间歇因子
	ReThet, //动量Reynolds数

	GAMMA0, //上一步间歇因子
	ReThet0, //上一步动量Reynolds数

	GAMMA00, //上上一步间歇因子
	ReThet00 //上上一步动量Reynolds数
};

/**
 * @brief 矢量场宏定义枚举
 * 
 */
enum Vector
{
    U = 0, ///< 均流速度

    RESIDUAL_MOMENTUM, ///< 动量残值
    
    GRADIENT_RHO, ///< 密度梯度
    GRADIENT_P, ///< 压强梯度
    GRADIENT_T, ///< 温度梯度

	GRADIENT_MASSFRACTION, ///< 组分梯度

    GRADIENT_NUT, ///< 湍流运动学涡粘性系数梯度
    GRADIENT_K, ///< 湍动能梯度
    GRADIENT_EPSILON, ///< 湍流耗散率梯度
    GRADIENT_OMEGA, ///< 比湍流耗散率梯度
    GRADIENT_V2BAR, ///< 全湍流脉动动能梯度

	GRADIENT_GAMMA, ///< 间歇因子梯度
	GRADIENT_ReThet, ///< 动量Reynolds数梯度
    
    U0, ///< 上一步速度
    
    U00, ///< 上上步速度

    meshVelocity, ///< 网格速度场

	MEANU ///< 时均速度场
};

/**
 * @brief 张量场宏定义枚举
 * 
 */
enum Tensor
{
    GRADIENT_U = 0 ///< 速度梯度
};

} // namespace FlowMacro

#endif
