﻿#include "sourceFlow/material/Materials.h"
#if defined(_EnableMultiSpecies_)
#include "feilian-specialmodule/multiSpecies/ChemkinFile.h"
#endif

namespace Material
{ 
namespace Flow
{
Materials::Materials(const DensityType &mType,
                     const ViscosityType &vType,
					 const CpType &cpType,
                     const std::string mName)
                     :
                     gt_(mType), vt_(vType), cpt_(cpType), name(mName),
                     refP_(101325.0), refT_(298.15), rho0_(1.225),
                     mu0_(1.716e-05), T0Sutherland_(273.15), T0Boussinesq_(288.15), S(110.56),
                     specificHeat_(1004.5), thermalConductivity_(0.0242), thermalExpansionCoeff_(0.0),
					 averagedMolarMass_(0.02897), R_(8.314462), g0(9.80665),
					 PrandtlLaminar(0.72), PrandtlTurbulent(0.9)
{
    lt_ = LambdaType::ltConstant;
}

#if defined(_EnableMultiSpecies_)
void Materials::SetMultiSpecies()
{
	MultiSpecies::ChemkinFile chemkinFile("mechanism.inp");
	
	const int speciesSize = chemkinFile.GetSpeciesSize();
	pureSpeciesDataList.resize(speciesSize);
	for (int k = 0; k < speciesSize; k++)
	{
		pureSpeciesDataList[k].speciesName = chemkinFile.GetSpeciesName(k);

		pureSpeciesDataList[k].molarMass = chemkinFile.GetMolarMass(k);
		pureSpeciesDataList[k].molarMassCGS = chemkinFile.GetMolarMass(k) * 1000.0;
		pureSpeciesDataList[k].gasConstant = chemkinFile.GetGasConstant(k);

		pureSpeciesDataList[k].nasaPolynomialHigh.resize(7);
		pureSpeciesDataList[k].nasaPolynomialLow.resize(7);
		pureSpeciesDataList[k].nasaPolynomialLow = chemkinFile.GetThermoDatLow(k);
		pureSpeciesDataList[k].nasaPolynomialHigh = chemkinFile.GetThermoDatHigh(k);

		pureSpeciesDataList[k].diameter = chemkinFile.GetDiameter(k);
		pureSpeciesDataList[k].wellDepth = chemkinFile.GetWellDepth(k);
		pureSpeciesDataList[k].polarizability = chemkinFile.GetPolarizability(k);
		pureSpeciesDataList[k].rotationalRelaxation = chemkinFile.GetRotationalRelaxation(k);
	}

	const int reactionSize = chemkinFile.GetReactionSize();
	reactionDataList.resize(reactionSize);
	for (int k = 0; k < reactionSize; k++)
	{
		reactionDataList[k].reverse = chemkinFile.GetReaction(k).reverse;
		reactionDataList[k].thirdBody = false;
		reactionDataList[k].pressureDepenent = false;

		reactionDataList[k].left = chemkinFile.GetReaction(k).left;
		reactionDataList[k].right = chemkinFile.GetReaction(k).right;
		reactionDataList[k].Order = chemkinFile.GetReaction(k).Order;

		reactionDataList[k].low = chemkinFile.GetReaction(k).low;
		reactionDataList[k].troe = chemkinFile.GetReaction(k).troe;

		Scalar sumStoichiometric = Scalar0;
		const int speciesNumb = reactionDataList[k].left.size();
		for (int i = 0; i < speciesNumb; i++)
		{
			sumStoichiometric = sumStoichiometric + reactionDataList[k].Order[i].second;
		}

		if (reactionDataList[k].low.size() > 0)
		{
			reactionDataList[k].pressureDepenent = true;

			reactionDataList[k].low[0] = chemkinFile.GetReaction(k).low[0] * pow(10.0, -6.0 * sumStoichiometric);
			reactionDataList[k].low[1] = chemkinFile.GetReaction(k).low[1];
			reactionDataList[k].low[2] = chemkinFile.GetReaction(k).low[2] * 4.184 / R_;
		}
		else
		{
			if (chemkinFile.GetReaction(k).thirdBody.size() > 0 && reactionDataList[k].low.size() == 0)
			{
				sumStoichiometric += 1;
				reactionDataList[k].thirdBody = true;
			}
		}

		const Scalar unitConver = pow(10.0, 6.0 - 6.0 * sumStoichiometric);

		reactionDataList[k].arrhenius.resize(3);
		reactionDataList[k].arrhenius[0] = chemkinFile.GetReaction(k).arrhenius[0] * unitConver;
		reactionDataList[k].arrhenius[1] = chemkinFile.GetReaction(k).arrhenius[1];
		reactionDataList[k].arrhenius[2] = chemkinFile.GetReaction(k).arrhenius[2] * 4.184 / R_;

		if (reactionDataList[k].thirdBody || reactionDataList[k].pressureDepenent)
		{
			reactionDataList[k].thirdBodyCoeff.resize(speciesSize, 1.0);
			int const speciesNumbTemp = chemkinFile.GetReaction(k).thirdBody.size();
			for (int kk = 0; kk < speciesNumbTemp; kk++)
			{
				const int indexTemp = chemkinFile.GetReaction(k).thirdBody[kk].first;
				reactionDataList[k].thirdBodyCoeff[indexTemp] = chemkinFile.GetReaction(k).thirdBody[kk].second;
			}
		}
	}
}
#endif

Scalar Materials::GetForwardRate(const Scalar &T, const int &index) const
{
	const Scalar &A = reactionDataList[index].arrhenius[0];
	const Scalar &beta = reactionDataList[index].arrhenius[1];
	const Scalar &Ea = reactionDataList[index].arrhenius[2];
	
	const Scalar kf = A * pow(T, beta) * exp(-Ea / T);

	if (reactionDataList[index].thirdBody)
	{
		Scalar totalConcentration = 0.0;
		for (int k = 0; k < pureSpeciesDataList.size(); k++)
		{
			totalConcentration = reactionDataList[index].thirdBodyCoeff[k];
		}
	}

	return kf;
}

Scalar Materials::GetForwardRateTB(const Scalar &T, const Scalar &rho, std::vector<Scalar> massFraction, const int &index) const
{
	const Scalar &A = reactionDataList[index].arrhenius[0];
	const Scalar &beta = reactionDataList[index].arrhenius[1];
	const Scalar &Ea = reactionDataList[index].arrhenius[2];

	const Scalar kf = A * pow(T, beta) * exp(-Ea / T);

	Scalar totalConcentration = 0.0;
	for (int k = 0; k < pureSpeciesDataList.size(); k++)
	{
		Scalar molarFraction = rho * massFraction[k] / pureSpeciesDataList[k].molarMass;
		totalConcentration += reactionDataList[index].thirdBodyCoeff[k] * molarFraction;
	}

	return kf * totalConcentration;
}

Scalar Materials::GetForwardRatePD(const Scalar &T, const Scalar &rho, std::vector<Scalar> massFraction, const int &index) const
{
	const Scalar &A = reactionDataList[index].arrhenius[0];
	const Scalar &beta = reactionDataList[index].arrhenius[1];
	const Scalar &Ea = reactionDataList[index].arrhenius[2];

	const Scalar kf = A * pow(T, beta) * exp(-Ea / T);

	Scalar totalConcentration = 0.0;
	for (int k = 0; k < pureSpeciesDataList.size(); k++)
	{
		Scalar molarFraction = rho * massFraction[k] / pureSpeciesDataList[k].molarMass;
		totalConcentration += reactionDataList[index].thirdBodyCoeff[k] * molarFraction;
	}

	const Scalar &ALow = reactionDataList[index].low[0];
	const Scalar &betaLow = reactionDataList[index].low[1];
	const Scalar &EaLow = reactionDataList[index].low[2];

	const Scalar kfLow = ALow * pow(T, betaLow) * exp(-EaLow / T);

	const std::vector<Scalar> &troe = reactionDataList[index].troe;
	const Scalar Fcent = (1.0 - troe[0]) * exp(-T / troe[1]) + troe[0] * exp(-T / troe[2]) + exp(-troe[3] / T);
	const Scalar cc = -0.4 - 0.67*log10(Fcent);
	const Scalar nn = 0.75 - 1.27*log10(Fcent);
	const Scalar Pr = kfLow / kf * (totalConcentration + SMALL);
	const Scalar log10Pr = log10(Pr);
	const Scalar Temp = (log10Pr + cc) / (nn - 0.14*(log10Pr + cc));
	const Scalar expon = 1.0 / (1.0 + Temp * Temp) * log10(Fcent);
	const Scalar fallOff = Fcent * pow(10.0, expon);

	return kf * Pr / (Pr + 1) * fallOff;
}

Scalar Materials::GetBackwardRate(const Scalar &T, const Scalar &kf, const int &index) const
{
	if (reactionDataList[index].reverse)
	{
		Scalar deltaH = Scalar0;
		Scalar deltaS = Scalar0;
		Scalar deltaCoeff = Scalar0;

		const int &speiciesRNumb = reactionDataList[index].left.size();
		for (int i = 0; i < speiciesRNumb; i++)
		{
			const int &speciesID = reactionDataList[index].left[i].first;
			const Scalar &coeff = reactionDataList[index].left[i].second;

			const Scalar &enthalpy = this->EnthalpyPureSpecies(T, speciesID);
			const Scalar &entropy = this->EntropyPureSpecies(T, speciesID);
			const Scalar &molarMass = pureSpeciesDataList[speciesID].molarMass;

			deltaH = deltaH - coeff * enthalpy * molarMass;
			deltaS = deltaS - coeff * entropy * molarMass;
			deltaCoeff = deltaCoeff - coeff;
		}

		const int &speiciesPNumb = reactionDataList[index].right.size();
		for (int i = 0; i < speiciesPNumb; i++)
		{
			const int &speciesID = reactionDataList[index].right[i].first;
			const Scalar &coeff = reactionDataList[index].right[i].second;

			const Scalar &enthalpy = this->EnthalpyPureSpecies(T, speciesID);
			const Scalar &entropy = this->EntropyPureSpecies(T, speciesID);
			const Scalar &molarMass = pureSpeciesDataList[speciesID].molarMass;

			deltaH = deltaH + coeff * enthalpy * molarMass;
			deltaS = deltaS + coeff * entropy * molarMass;
			deltaCoeff = deltaCoeff + coeff;
		}
		const Scalar ke = exp((deltaS - deltaH / T) / R_) * pow(101325.0 / R_ / T, deltaCoeff);
		return kf / ke;
	}
	else
	{
		return Scalar0;
	}
}

Scalar Materials::GetRateOfProgress(const Scalar &T, const Scalar &rho, const std::vector<Scalar> &massFraction, const int &index) const
{
	Scalar kf;
	if (reactionDataList[index].thirdBody)
	{
		kf = this->GetForwardRateTB(T, rho, massFraction, index);
	}
	else if (reactionDataList[index].pressureDepenent)
	{
		kf = this->GetForwardRatePD(T, rho, massFraction, index);
	}
	else
	{
		kf = this->GetForwardRate(T, index);
	}

	const Scalar kb = this->GetBackwardRate(T, kf, index);

	Scalar rateF = kf;
	const int &speiciesFNumb = reactionDataList[index].Order.size();
	for (int k = 0; k < speiciesFNumb; k++)
	{
		const int &speciesID = reactionDataList[index].Order[k].first;
		const Scalar &exponent = reactionDataList[index].Order[k].second;

		rateF = rateF * pow(rho * massFraction[speciesID] / pureSpeciesDataList[speciesID].molarMass, exponent);
	}

	Scalar rateB = kb;
	const int speiciesBNumb = reactionDataList[index].right.size();
	for (int k = 0; k < speiciesBNumb; k++)
	{
		const int &speciesID = reactionDataList[index].right[k].first;
		const Scalar &exponent = reactionDataList[index].right[k].second;

		rateB = rateB * pow(rho * massFraction[speciesID] / pureSpeciesDataList[speciesID].molarMass, exponent);
	}
	
	return (rateF - rateB);
}

Scalar Materials::GetProductionRate(const Scalar &T, const Scalar &rho, const std::vector<Scalar> &massFraction, std::vector<Scalar> rateOfProgressVector, const int &index) const
{
	Scalar productionRate= Scalar0;
	for (int i = 0; i < reactionDataList.size(); i++)
	{
		const int &speiciesFNumb = reactionDataList[i].left.size();
		for (int j = 0; j < speiciesFNumb; j++)
		{
			if (reactionDataList[i].left[j].first == index)
			{
				productionRate = productionRate - reactionDataList[i].left[j].second *rateOfProgressVector[i];
			}
		}

		const int &speiciesBNumb = reactionDataList[i].right.size();
		for (int j = 0; j < speiciesBNumb; j++)
		{
			if (reactionDataList[i].right[j].first == index)
			{
				productionRate = productionRate + reactionDataList[i].right[j].second *rateOfProgressVector[i];
			}
		}
	}
	return productionRate * pureSpeciesDataList[index].molarMass;
}

Scalar Materials::GetTemperature(const Scalar &p, const Scalar &rho, const std::vector<Scalar> &massFraction) const
{
	return p / (rho * R(massFraction));
}

Scalar Materials::GetDensity(const Scalar &p, const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	return p / (T * R(massFraction));
}

Scalar Materials::GetTemperature(const Scalar &h) const
{
	// 温度及压强随高度变化计算系数
	Scalar Hb = 0.0, Pb = 0.0, Tb = 0.0, Lb = 0.0;
	if      (h <= 11000) { Hb =  0000; Pb = 101325.0; Tb = 288.15; Lb = -0.0065; }
	else if (h <= 20000) { Hb = 11000; Pb = 22632.26; Tb = 216.65; Lb =  0.0000; }
	else if (h <= 32000) { Hb = 20000; Pb = 5474.980; Tb = 216.65; Lb =  0.0010; }
	else if (h <= 47000) { Hb = 32000; Pb = 868.0422; Tb = 228.65; Lb =  0.0028; }
	else if (h <= 51000) { Hb = 47000; Pb = 110.9106; Tb = 270.65; Lb =  0.0000; }
	else if (h <= 71000) { Hb = 51000; Pb = 66.94167; Tb = 270.65; Lb = -0.0028; }
	else if (h <= 84852) { Hb = 71000; Pb = 3.956649; Tb = 214.65; Lb = -0.0020; }
	else
	{
		FatalError("Materials::GetTemperature: Altitude isnot supported");
		return 288.15;
	}

	return Tb + Lb * (h - Hb);
}

Scalar Materials::GetPressure(const Scalar &h) const
{
	// 温度及压强随高度变化计算系数
	Scalar Hb, Pb, Tb, Lb;
	if      (h <= 11000) { Hb =  0000; Pb = 101325.0; Tb = 288.15; Lb = -0.0065; }
	else if (h <= 20000) { Hb = 11000; Pb = 22632.26; Tb = 216.65; Lb =  0.0000; }
	else if (h <= 32000) { Hb = 20000; Pb = 5474.980; Tb = 216.65; Lb =  0.0010; }
	else if (h <= 47000) { Hb = 32000; Pb = 868.0422; Tb = 228.65; Lb =  0.0028; }
	else if (h <= 51000) { Hb = 47000; Pb = 110.9106; Tb = 270.65; Lb =  0.0000; }
	else if (h <= 71000) { Hb = 51000; Pb = 66.94167; Tb = 270.65; Lb = -0.0028; }
	else if (h <= 84852) { Hb = 71000; Pb = 3.956649; Tb = 214.65; Lb = -0.0020; }
	else
	{
		FatalError("Materials::GetPressure: Altitude isnot supported");
		return 101325.0;
	}
	
	const Scalar coef = g0 * averagedMolarMass_ / R_;
	if (fabs(Lb) < SMALL)
	{
		return Pb * exp(-coef * (h - Hb) / Lb);
	}
	else
	{
		return Pb * pow(Tb / (Tb + Lb * (h - Hb)), coef / Lb);
	}
}

Scalar Materials::GetDensity(const Scalar &h) const
{
	return GetDensity(this->GetPressure(h), this->GetTemperature(h));
}

Scalar Materials::GetSoundSpeed(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	return sqrt(GAMMA(T, massFraction) * T * R(massFraction));
}

Scalar Materials::GetSoundSpeed(const Scalar &p, const Scalar &rho, const std::vector<Scalar> &massFraction) const
{
	return sqrt(GAMMA(p / (rho * R(massFraction)), massFraction) * p / rho);
}

Scalar Materials::R(const std::vector<Scalar> &massFraction) const
{
	if (massFraction.empty())
	{
		return R_ / averagedMolarMass_;
	}
	else
	{
		Scalar Average = Scalar0;
		for (int k = 0; k < massFraction.size(); k++)
		{
			Average = Average + massFraction[k] * pureSpeciesDataList[k].gasConstant;
		}
		return Average;
	}
}

Scalar Materials::GAMMA(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	return (CP(T, massFraction) / CV(T, massFraction));
}

Scalar Materials::CV(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	return (CP(T, massFraction) - R(massFraction));
}

Scalar Materials::CP(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	if (massFraction.empty())
	{
		if (cpt_ == CpType::cptConstant)
		{
			return specificHeat_;
		}
		else if (cpt_ == CpType::cptPiecewiseLinear)
		{
			Scalar phin = 0.0;
			Scalar phinp = 0.0;
			Scalar tn = 0.0;
			Scalar tnp = 0.0;

			for (auto iter = cptPiecewise_.begin(); iter != cptPiecewise_.end(); iter++)
			{
				if (T >= iter->first)
				{
					tn = iter->first;
					phin = iter->second;
				}
				else
				{
					tnp = iter->first;
					phinp = iter->second;

					break;
				}
			}
			return (phin + (phinp - phin) / (tnp - tn) * (T - tn));
		}
		else if (cpt_ == CpType::cptKineticTheory)
		{
			FatalError("Materials::CP: Kinetic theory is not supported by now");
			return 0.0;
		}
	}
	else
	{
		Scalar CpMix = Scalar0;
		for (int k = 0; k < massFraction.size(); k++)
		{
			CpMix = CpMix + this->CpPureSpecies(T, k) * massFraction[k];
		}
		return CpMix;
	}
}

Scalar Materials::AveragedMolarMass(const std::vector<Scalar> &massFraction) const
{
	if (massFraction.empty())
	{
		return averagedMolarMass_;
	}
	else
	{
		Scalar MolarMassMix = Scalar0;
		for (int k = 0; k < massFraction.size(); k++)
		{
			MolarMassMix = MolarMassMix + massFraction[k] / pureSpeciesDataList[k].molarMass;
		}
		return (1.0 / MolarMassMix);
	}
}

Scalar Materials::CpPureSpecies(const Scalar &T, const int &index) const
{
	const Scalar T2 = T * T;
	const Scalar T3 = T2 * T;
	const Scalar T4 = T3 * T;
	Scalar Cp;

	std::vector<Scalar> nasaCoeff;
	if (T < 1000.0)
	{
		nasaCoeff = pureSpeciesDataList[index].nasaPolynomialLow;
	}
	else
	{
		nasaCoeff = pureSpeciesDataList[index].nasaPolynomialHigh;
	}
	Cp = nasaCoeff[0] + nasaCoeff[1] * T + nasaCoeff[2] * T2 + nasaCoeff[3] * T3 + nasaCoeff[4] * T4;

	return (Cp * pureSpeciesDataList[index].gasConstant);
}

Scalar Materials::Entropy(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	Scalar Mix = Scalar0;
	for (int k = 0; k < massFraction.size(); k++)
	{
		Mix = Mix + this->EntropyPureSpecies(T, k) * massFraction[k];
	}
	return Mix;
}

Scalar Materials::EntropyPureSpecies(const Scalar &T, const int &index) const
{
	const Scalar T2 = T * T;
	const Scalar T3 = T2 * T;
	const Scalar T4 = T3 * T;
	const Scalar logT = log(T);
	Scalar Entropy;

	std::vector<Scalar> nasaCoeff;
	if (T < 1000.0)
	{
		nasaCoeff = pureSpeciesDataList[index].nasaPolynomialLow;
	}
	else
	{
		nasaCoeff = pureSpeciesDataList[index].nasaPolynomialHigh;
	}
	Entropy = nasaCoeff[0] * logT + nasaCoeff[1] * T + nasaCoeff[2] * T2 / 2.0+ nasaCoeff[3] * T3 /3.0 + nasaCoeff[4] * T4 /4.0 + nasaCoeff[6];

	return (Entropy * pureSpeciesDataList[index].gasConstant);
}

Scalar Materials::Enthalpy(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	if (massFraction.empty())
	{
		return CP(T) * T;
	}
	else
	{
		Scalar Mix = Scalar0;
		for (int k = 0; k < massFraction.size(); k++)
		{
			Mix = Mix + this->EnthalpyPureSpecies(T, k) * massFraction[k];
		}
		return Mix;
	}
}

Scalar Materials::EnthalpyPureSpecies(const Scalar &T, const int &index) const
{
	const Scalar T2 = T * T;
	const Scalar T3 = T2 * T;
	const Scalar T4 = T3 * T;
	const Scalar T5 = T4 * T;
	Scalar Enthalpy;

	std::vector<Scalar> nasaCoeff;
	if (T < 1000.0)
	{
		nasaCoeff = pureSpeciesDataList[index].nasaPolynomialLow;
	}
	else
	{
		nasaCoeff = pureSpeciesDataList[index].nasaPolynomialHigh;
	}
	Enthalpy = nasaCoeff[0] * T + nasaCoeff[1] * T2 / 2.0 + nasaCoeff[2] * T3 / 3.0 + nasaCoeff[3] * T4 / 4.0 + nasaCoeff[4] * T5 / 5.0 + nasaCoeff[5];

	return (Enthalpy * pureSpeciesDataList[index].gasConstant);
}

Scalar Materials::CalculateTotalEnergy(const Scalar &rhoTemp, const Scalar &pTemp, const Vector &UTemp, const std::vector<Scalar> &massFraction) const
{
	const Scalar TTemp = this->GetTemperature(pTemp, rhoTemp, massFraction);
	const Scalar HTemp = this->Enthalpy(TTemp, massFraction);
	return rhoTemp * HTemp - pTemp + 0.5 * rhoTemp * (UTemp & UTemp);
}

Scalar Materials::CalculatePressure(const Scalar &density, const Scalar &Temperature, const std::vector<Scalar> &massFraction) const
{
	return density * Temperature * this->R(massFraction);
}

#if defined(_EnableMultiSpecies_)
Scalar Materials::CalculateTemperature(const Scalar &energy, const Scalar &density, const Vector velocity, std::vector<Scalar> &massFraction, const Scalar &Tguess) const
{
	Scalar TTemp = Tguess;
	const int maxNewtonStep = 100;
	const Scalar criterion = 1.e-5;
	for (int i = 0; i < maxNewtonStep; i++)
	{
		Scalar enthalpy = this->Enthalpy(TTemp, massFraction);
		Scalar Cp = this->CP(TTemp, massFraction);
		Scalar RMix = this->R(massFraction);
		Scalar p = density * TTemp * RMix;

		Scalar equation = density * enthalpy - p + 0.5 * density * (velocity & velocity) - energy;
		Scalar diffEquation = density * (Cp - RMix);
		Scalar ratio = equation / diffEquation;

		if (abs(equation) < criterion || abs(ratio) < criterion) break;

		if (i == maxNewtonStep)
		{
			std::cout << "Warning: need more Newton Iterations" << std::endl;
			std::cout << TTemp << std::endl;
		}

		TTemp = TTemp - ratio;
	}
	return TTemp;
}
#endif

Scalar Materials::Mu(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	if (massFraction.empty())
	{
		if (vt_ == ViscosityType::SUTHERLAND)
		{
			const Scalar temp = T / T0Sutherland_;
			return mu0_ * sqrt(temp) * temp * (T0Sutherland_ + S) / (T + S);
		}
		else if (vt_ == ViscosityType::CONSTANT)
		{
			return mu0_;
		}
		else
		{
			FatalError("Materials::Mu: Sutherland is the Only Option for viscosity");
			return 0.0;
		}
	}
	else
	{
		Scalar MuMix1 = Scalar0;
		Scalar MuMix2 = Scalar0;

		const Scalar MolarMassMix = this->AveragedMolarMass(massFraction);

		for (int k = 0; k < massFraction.size(); k++)
		{
			const Scalar &muPureSpecies = this->MuPureSpecies(T, k);
			const Scalar &molarFraction = MolarMassMix / pureSpeciesDataList[k].molarMass * massFraction[k];
			MuMix1 = MuMix1 + molarFraction * muPureSpecies;
			MuMix2 = MuMix2 + molarFraction / muPureSpecies;
		}
		return 0.5 * (MuMix1 + 1.0 / MuMix2);
	}
}

Scalar Materials::MuPureSpecies(const Scalar &T, const int &index) const
{
	const Scalar &MolarMassCGS = pureSpeciesDataList[index].molarMassCGS;
	const Scalar &diameterSq = pureSpeciesDataList[index].diameter * pureSpeciesDataList[index].diameter;
	const Scalar &reducedT = T / pureSpeciesDataList[index].wellDepth;
	const Scalar &omega22 = 1.0413 * pow(reducedT, -0.1193) + pow(reducedT + 0.43628, -1.6041);

	return (2.67e-6 * sqrt(MolarMassCGS * T) / (diameterSq * omega22));
}

Scalar Materials::Kappa(const Scalar &T, const std::vector<Scalar> &massFraction) const
{
	Scalar KappaMix1 = Scalar0;
	Scalar KappaMix2 = Scalar0;

	const Scalar MolarMassMix = this->AveragedMolarMass(massFraction);

	for (int k = 0; k < massFraction.size(); k++)
	{
		const Scalar &muPureSpecies = this->KappaPureSpecies(T, k);
		const Scalar &molarFraction = MolarMassMix / pureSpeciesDataList[k].molarMass * massFraction[k];
		KappaMix1 = KappaMix1 + molarFraction * muPureSpecies;
		KappaMix2 = KappaMix2 + molarFraction / muPureSpecies;
	}
	return 0.5 * (KappaMix1 + 1.0 / KappaMix2);
}

Scalar Materials::KappaPureSpecies(const Scalar &T, const int &index) const
{
	// detailed
	const Scalar &MolarMassCGS = pureSpeciesDataList[index].molarMassCGS;
	const Scalar &diameterSq = pureSpeciesDataList[index].diameter * pureSpeciesDataList[index].diameter;
	const Scalar &reducedT = T / pureSpeciesDataList[index].wellDepth;
	const Scalar &omega22 = 1.0413 * pow(reducedT, -0.1193) + pow(reducedT + 0.43628, -1.6041);

	return (8.33e-2 * sqrt(T / MolarMassCGS) / (diameterSq * omega22));

	// simplified
	//const Scalar &Cp = this->CpPureSpecies(T, index);
	//const Scalar &muPureSpecies = this->MuPureSpecies(T, index);
	//const Scalar &MolarMass = pureSpeciesDataList[index].molarMass;

	//return (Cp + 1.25*R_ / MolarMass) * muPureSpecies;
}

Scalar Materials::Diffusion(const Scalar &T, const Scalar &p, const std::vector<Scalar> &massFraction, const int &index) const
{
	Scalar DiffTemp = Scalar0;
	const Scalar MolarMassMix = this->AveragedMolarMass(massFraction);
	for (int k = 0; k < massFraction.size(); k++)
	{
		if (k != index)
		{
			const Scalar &molarFraction = MolarMassMix / pureSpeciesDataList[k].molarMass * massFraction[k];
			const Scalar &diffBinary = this->DiffusionBinary(T, p, index, k);
			DiffTemp = DiffTemp + molarFraction / diffBinary;
		}
	}
	if (DiffTemp > 1.e-13)
	{
		return ((1 - massFraction[index]) / DiffTemp);
	}
	else
	{
		return Scalar0;
	}
}

Scalar Materials::DiffusionBinary(const Scalar &T, const Scalar &p, const int &indexJ, const int &indexK) const
{
	const Scalar &MolarMassCGSJ = pureSpeciesDataList[indexJ].molarMassCGS;
	const Scalar &MolarMassCGSK = pureSpeciesDataList[indexK].molarMassCGS;
	const Scalar &MolarMassCGSJK = MolarMassCGSJ * MolarMassCGSK / (MolarMassCGSJ + MolarMassCGSK);

	const Scalar &diameterJK = 0.5 * (pureSpeciesDataList[indexJ].diameter + pureSpeciesDataList[indexK].diameter);
	const Scalar &diameterJKSq = diameterJK * diameterJK;

	const Scalar &reducedT = T / sqrt(pureSpeciesDataList[indexJ].wellDepth * pureSpeciesDataList[indexK].wellDepth);
	const Scalar &omega11 = 1.0548 * pow(reducedT, -0.15504) + pow(reducedT + 0.55909, -2.1705);

	return (0.0188 * sqrt(T * T * T / MolarMassCGSJK) / (p * diameterJKSq * omega11));
}
}
}
