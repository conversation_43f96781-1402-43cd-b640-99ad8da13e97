﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Materials.h
//! <AUTHOR>
//! @brief 材料类
//! @date 2022-07-12
//
//------------------------------修改日志----------------------------------------
// 2022-07-12 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-05-29 杨思源
//    说明：建立。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_material_Materials_
#define _sourceFlow_material_Materials_

#include "basic/common/ConfigUtility.h"
#include "sourceFlow/configure/FlowConfigureMacro.h"

/**
 * @brief 材料命名空间
 * 
 */
namespace Material
{
/**
 * @brief 流体材料命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场的流动介质材料
 * 
 */

struct PureSpeciesStruct //储存单个组分信息
{
	std::string speciesName;
	Scalar molarMass;                       ///<  摩尔质量 kg/mol
	Scalar molarMassCGS;                       ///<  摩尔质量 g/mol
	Scalar gasConstant;
	std::vector<Scalar> nasaPolynomialLow;  ///<  T<1000K, NASA多项式系数用于计算Cp、熵、焓
	std::vector<Scalar> nasaPolynomialHigh; ///<  T>1000K, NASA多项式系数用于计算Cp、熵、焓
	Scalar wellDepth;
	Scalar diameter;
	Scalar polarizability;
	Scalar rotationalRelaxation;
};

struct ReactionStruct // 存储化学反应信息的结构体
{
	bool reverse; ///< 反应是否可逆（ture为可逆）
	bool thirdBody; ///< 反应是否可逆（ture为可逆）
	bool pressureDepenent; ///< 反应是否可逆（ture为可逆）

	std::vector<std::pair<int, Scalar>> left;  ///< 左端反应物序号(first)及系数(second)
	std::vector<std::pair<int, Scalar>> right; ///< 右端生成物序号及系数

	std::vector<std::pair<int, Scalar>> Order;  ///< 左端反应物序号(first)及系数(second)

	std::vector<Scalar> arrhenius;
	//std::vector<std::pair<int, Scalar>> thirdBodyCoeff;
	std::vector<Scalar> thirdBodyCoeff;
	std::vector<Scalar> low;
	std::vector<Scalar> troe;
};

class Materials
{
public:
    /**
     * @brief 构造函数，创建材料对象
     * 
     * @param[in] mType 密度类型
     * @param[in] vType 粘性系数类型
     * @param[in] mName 材料名称
     */
	Materials(const DensityType &mType, const ViscosityType &vType, const CpType &cpType = CpType::cptConstant, const std::string mName = "air");

#if defined(_EnableMultiSpecies_)
    /**
     * @brief 设置多组分属性
     * 
     */
    void SetMultiSpecies();
#endif
	
    /**
     * @brief 计算并返回气体常数
     * 
     * @return Scalar 
     */
    Scalar R(const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

    /**
     * @brief 计算并返回气体常数
     * 
     * @return Scalar 
     */
    Scalar R0() const { return R_; }

    /**
     * @brief 计算并返回比热容比
     * 
     * @param[in] T 温度
     * @return Scalar 
     */
	Scalar GAMMA(const Scalar &T = 273.15, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

    /**
     * @brief 计算并返回定体积比热容
     * 
     * @param[in] T 温度
     * @return Scalar 
     */
	Scalar CV(const Scalar &T = 273.15, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

    /**
     * @brief 计算并返回定压比热容
     * 
     * @param[in] T 温度
     * @return Scalar 
     */
	Scalar CP(const Scalar &T = 273.15, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

	Scalar AveragedMolarMass(const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;
	
	Scalar CpPureSpecies(const Scalar &T, const int &index) const;

	Scalar Entropy(const Scalar &T, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

	Scalar EntropyPureSpecies(const Scalar &T, const int &index) const;

	Scalar Enthalpy(const Scalar &T, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

	Scalar EnthalpyPureSpecies(const Scalar &T, const int &index) const;

    Scalar CalculateTotalEnergy(const Scalar &rhoTemp, const Scalar &pTemp, const Vector &UTemp, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;
    
    /**
     * @brief 根据密度和温度计算压强
     * 
     * @param[in] density 密度
     * @param[in] Temperature 温度
     * @param[in] massFraction 组分
     * @return Scalar 
     */
	Scalar CalculatePressure(const Scalar &density, const Scalar &Temperature, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

    Scalar CalculateTemperature(const Scalar &energy, const Scalar &density, const Vector velocity, std::vector<Scalar> &massFraction, const Scalar &Tguess) const;

    /**
     * @brief 计算层流粘性系数
     * 
     * @param[in] T 温度
     * @return Scalar 
     */
	Scalar Mu(const Scalar &T = 273.15, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

	Scalar MuPureSpecies(const Scalar &T, const int &index) const;

	Scalar Kappa(const Scalar &T, const std::vector<Scalar> &massFraction) const;

	Scalar KappaPureSpecies(const Scalar &T, const int &index) const;

	Scalar Diffusion(const Scalar &T, const Scalar &p, const std::vector<Scalar> &massFraction, const int &index) const;

	Scalar DiffusionBinary(const Scalar &T, const Scalar &p, const int &indexJ, const int &indexK) const;

    /**
     * @brief 根据压强和密度计算温度
     * 
     * @param[in] p 压强
     * @param[in] rho 密度
     * @return Scalar 
     */
    Scalar GetTemperature(const Scalar &p, const Scalar &rho, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

	/**
	* @brief 根据高度计算温度
	*
	* @param[in] h 高度
	* @return Scalar
	*/
	Scalar GetTemperature(const Scalar &h) const;

	/**
	* @brief 根据高度计算压强
	*
	* @param[in] h 高度
	* @return Scalar
	*/
	Scalar GetPressure(const Scalar &h) const;

	/**
	* @brief 根据高度计算密度
	*
	* @param[in] h 高度
	* @return Scalar
	*/
	Scalar GetDensity(const Scalar &h) const;

    /**
     * @brief 根据压强和温度计算密度
     * 
     * @param[in] p 压强
     * @param[in] T 温度
     * @return Scalar 
     */
	Scalar GetDensity(const Scalar &p = 101325.0, const Scalar &T = 273.15, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

    /**
     * @brief 根据温度计算音速
     * 
     * @param[in] T 温度
     * @return Scalar 
     */
    Scalar GetSoundSpeed(const Scalar &T, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

    /**
     * @brief 根据压强和密度计算音速
     * 
     * @param[in] p 压强
     * @param[in] rho 密度
     * @return Scalar 
     */
    Scalar GetSoundSpeed(const Scalar &p, const Scalar &rho, const std::vector<Scalar> &massFraction = std::vector<Scalar>()) const;

	const int GetSpeciesSize() const { return pureSpeciesDataList.size(); }

	const int GetReactionSize() const { return reactionDataList.size(); }

	Scalar GetForwardRate(const Scalar &T, const int &index) const;
	Scalar GetForwardRateTB(const Scalar &T, const Scalar &rho, const std::vector<Scalar> massFraction, const int &index) const;
	Scalar GetForwardRatePD(const Scalar &T, const Scalar &rho, const std::vector<Scalar> massFraction, const int &index) const;


	Scalar GetBackwardRate(const Scalar &T, const Scalar &kf, const int &index) const;

	Scalar GetRateOfProgress(const Scalar &T, const Scalar &rho, const std::vector<Scalar> &massFraction, const int &index) const;

	Scalar GetProductionRate(const Scalar &T, const Scalar &rho, const std::vector<Scalar> &massFraction, std::vector<Scalar> rateOfProgressVector, const int &index) const;

    const DensityType &GetDensityType() const { return gt_; }
    const ViscosityType &GetViscosityType() const { return vt_; } 
    const CpType &GetCpType() const { return cpt_; } 
    
	PureSpeciesStruct GetPureSpeciesData(const int &index) const { return pureSpeciesDataList[index]; }
	ReactionStruct GetReactionData(const int &index) const { return reactionDataList[index]; }

private:
    std::string name; ///< 材料名称
    ViscosityType vt_; ///< 粘性系数类型
    DensityType gt_; ///< 密度类型
    CpType cpt_; ///< 定压比热容类型
    LambdaType lt_; ///< 导热系数类型
    
    Scalar refP_; ///< 压强默认值
    Scalar refT_; ///< 温度默认值
    Scalar rho0_; ///< 密度默认值
    Scalar mu0_; ///< 粘性系数默认值
    Scalar T0Sutherland_; ///< Sutherland公式温度默认值
    Scalar S; ///< Sutherland公式系数
    Scalar T0Boussinesq_; ///< Boussinesq公式温度默认值
    Scalar specificHeat_; ///< 比热默认值
    Scalar thermalConductivity_; ///< 导热系数默认值
    Scalar thermalExpansionCoeff_; ///< 热膨胀系数默认值
    Scalar averagedMolarMass_; ///< 平均摩尔质量[kg / mol]
    Scalar R_; ///< 气体常数默认值
	Scalar g0; ///< 标准重力加速度
    
    std::map<Scalar, Scalar> cptPiecewise_; ///< 定压比热容分段线性变化系数

	std::vector<PureSpeciesStruct> pureSpeciesDataList;
	std::vector<ReactionStruct> reactionDataList;
    
	Scalar PrandtlLaminar; ///< 层流Ptrandtl数
	Scalar PrandtlTurbulent; ///< 湍流Ptrandtl数

};

} // namespace Flow
} // namespace Material
#endif
