﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ResidualProcess.h
//! <AUTHOR>
//! @brief 残值计算及监控.
//! @date 2022-06-30
//
//------------------------------修改日志----------------------------------------
// 2022-06-30 乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_flowSolver_ResidualProcess_
#define _sourceFlow_flowSolver_ResidualProcess_

#include "sourceFlow/package/FlowPackage.h"
#include "sourceFlow/configure/FlowConfigureMacro.h"

/**
 * @brief 残值计算及监控类
 * 
 */
class ResidualProcess
{
public:
    /**
     * @brief 构造函数，创建残值监控对象
     * 
     * @param[in] flowPackageVector_ 流场场包容器
     */
    ResidualProcess(std::vector<Package::FlowPackage *> flowPackageVector_);

    /**
    * @brief 初始化残值计算
    *
    * @param[in] level 网格层级
    */
    void Initialize(const int &level);

    /**
     * @brief 通过相对残差判断内迭代是否满足收敛条件
     * 
     * @param[in] criteria 收敛标准
     *
     * @return 1 收敛
     * @return 0 不收敛
     * @return -1 发散
     */
    int CheckConvergence(const Scalar &criteria);

    /**
     * @brief 读取残差文件并提取续算残差初始值
     * 
     * @param[out] startResidual 起始残差
     * @param[out] startResidualMax 最大残差
     */
    void GetRestartResiduals(std::vector<Scalar> &startResidual, std::vector<Scalar> &startResidualMax);

    /**
     * @brief 平均残值计算
     * 
     * @param[in] currentLevel 当前网格层级
     * @return std::vector<Scalar>& 
     */
    std::vector<Scalar> &CalculateMonitorResidual(const int &currentLevel);

    /**
     * @brief 最大残值计算
     * 
     * @param[in] currentLevel 当前网格层级
     * @return std::vector<Scalar>& 
     */
    std::vector<Scalar> &CalculateMaximumResidual(const int &currentLevel);

    /**
    * @brief 获取监控残差
    *
    * @return const std::vector<std::string>&
    */
    const std::vector<Scalar> &GetMonitorResidual()const{ return monitorResidual; }

    /**
    * @brief 获取最大残差
    *
    * @return const std::vector<std::string>&
    */
    const std::vector<Scalar> &GetMaximumResidual()const{ return maximumResidual; }

    /**
     * @brief 获取监控残差名称
     * 
     * @return const std::vector<std::string>& 
     */
    const std::vector<std::string> &GetMonitorResidualName() {return monitorResidualName;}

    /**
     * @brief 获取监控最大残差名称
     * 
     * @return const std::vector<std::string>& 
     */
    const std::vector<std::string> &GetMaximumResidualName() {return maximumResidualName;}
    
private:
    /// 流场包
    std::vector<Package::FlowPackage *> flowPackageVector;

    ElementField<Scalar> *residualMass; ///< 质量残值
    ElementField<Vector> *residualMomentum; ///< 动量残值
    ElementField<Scalar> *residualEnergy; ///< 能量残值
    std::vector<ElementField<Scalar> *> residualTurbulence; ///< 湍流量残值

    std::vector<std::string> totalResidualStrings; ///< 残值打印字符串
    std::vector<Scalar> monitorResidual; ///< 残值监控量容器
    std::vector<Scalar> monitorResidualReference; ///< 残值参考量容器
    std::vector<Scalar> maximumResidual; ///< 最大残值容器
    std::vector<Scalar> monitorResidual0; ///< 监控残值初始值
    std::vector<Scalar> maximumResidual0; ///< 最大残值初始值
    std::vector<std::string> monitorResidualName; ///< 监控残值名称
    std::vector<std::string> maximumResidualName; ///< 最大残值名称
    
    int nResidualTotal; ///< 可监测残差总数量
    int nMonitorResidual; ///< 监测残差数量
    int nMaximumResidual; ///< 监测最大残差数量
    
    bool residualsMaxPositon; ///< 是否输出最大残差位置
    bool residualsInLog10; ///< 是否采用log10形式输出残差
    bool normalizedResiduals; ///< 是否对残差进行归一化

    bool restartFlag; /// 续算标识，true为续算
    bool dimension3D; ///< 三维标识，true为三维
    int beginStep; ///< 起始步，续算为续算起始步，其余为0
    
    int nElementGlobal; ///< 全局单元数量

	bool unsteady; ///< 非定常标识
};

#endif