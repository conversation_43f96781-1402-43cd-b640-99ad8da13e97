﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowResultsProcess.h
//! <AUTHOR>
//! @brief 流场计算结果输出.
//! @date 2021-03-30
//
//------------------------------修改日志----------------------------------------
// 2021-03-30 乔龙
//     说明：添加注释，并对函数参数名称及参数顺序进行调整
//
// 2021-03-29 李艳亮、乔龙
//     说明：设计功能模块框架
//------------------------------------------------------------------------------

#ifndef _sourceFlow_flowSolver_ResultsProcess_
#define _sourceFlow_flowSolver_ResultsProcess_

#include "sourceFlow/resultsProcess/ForceProcess.h"
#include "sourceFlow/resultsProcess/ResidualProcess.h"

/**
 * @brief 流场计算结果监控类
 * 
 */
class FlowResultsProcess
{
public:
    /**
     * @brief 构造函数，创建流场结果处理对象
     * 
     * @param[in] localMesh_ 当地网格
     * @param[in] flowPackageVector_ 流场包容器
     */
    FlowResultsProcess(SubMesh *localMesh_, std::vector<Package::FlowPackage *> flowPackageVector_);

    /**
     * @brief 析构函数
     * 
     */
    ~FlowResultsProcess();

    /**
     * @brief 初始设置函数
     * 
     * @param[in] level 当前网格层级
     */
    void Initialize(const int &level);

    /**
    * @brief 计算残值监测值和检测力系数
    *
    */
    void CalculateMonitorValues();

    /**
    * @brief 非定常InnerLoop输出残值监测值和检测力系数
    *
    */
    void OutputMonitorValues(const int &innerStep);

    /**
    * @brief 非定常OuterLoop输出残值监测值和检测力系数
    *
    */
    void OutputMonitorValuesOutLoop();

    /**
     * @brief 非定常InnerLoop输出当前迭代步计算结果
     * 
     */
    void MonitorPerStep(const int &innerStep);

    /**
     * @brief 非定常OuterLoop输出当前迭代步计算的结果
     * 
     */
    void MonitorPerStepOutLoop();


    /**
     * @brief 保存中间物理场
     * 
     */
    void SaveIntervalResults();

    /**
    * @brief 保存最终物理场
    *
    */
    void SaveFinalResults();

    /**
    * @brief 合并物理场并输出
    *
    * @tparam Type 待合并物理场值的类型
    * @param phi 待合并物理场
    * @param stepString 当前步数
    */
    template<class Type>
    void MergeAndWriteField(ElementField<Type> *phi, const std::string &stepString);

	/**
	* @brief 合并运动后的点坐标并输出
	*/
	void MergeAndWriteNodes(const std::string &stepString);

    /**
     * @brief 通过相对残差判断内迭代是否满足收敛条件
     * 
     * @param[in] criteria 收敛标准
     *
     * @return 1 收敛
     * @return 0 不收敛
     * @return -1 发散
     */
    int CheckConvergence(const Scalar &criteria);

    /**
     * @brief 力系数输出
     * 
     */
    void OutputForceCoefficient();

    /**
    * @brief 获取当前步数
    *
    */
    const int &GetCurrentStep()const { return this->currentStep; }

    /**
    * @brief 更新监控时间
    *
    */
    void UpdateTime();

	/**
	* @brief 获取非定常续算内迭代步数
	*
	*/
	int GetInnerStep();

	/**
	* @brief 计算时均场
	*
	*/
	void CalculateMeanFields(int outerStep);

	/**
	* @brief 计算单个变量的时均场
	*
	*/
	template<class Type>
	void CalculateMeanValues(int outerStep, ElementField<Type> &value, ElementField<Type> &meanValue);

	/**
	* @brief 获取非定常续算时均起始步
	*
	*/
	int GetStartMeanStep();

	/**
	* @brief 获取非定常续算时间和时均起始步
	*
	*/
	void GetReStartValues();

	/**
	* @brief 获取非定常续算起始时间
	*
	*/
    Scalar GetStartTime()const { return this->startTime; }

private:
    /**
     * @brief 保存流场
     * 
     * @param[in] stepString 当前迭代步
     */
    void SaveFlowFields(const std::string stepString);

    /**
     * @brief 为输出全局物理场收集全局网格信息
     * 
     */
    void GatherGlobalInfo();
    
    /**
     * @brief 屏幕输出残值监测值和检测力系数
     * 
     */
	void OutputMonitorValuesScreen(const int &innerStep);
    
    /**
     * @brief 文件输出残值监测值和检测力系数
     * 
     */
	void OutputMonitorValuesFile(const int &innerStep);


private:
    std::vector<Package::FlowPackage *> flowPackageVector; ///< 流场包容器

    ResidualProcess residualProcess; ///< 残值监控对象
    ForceProcess forceProcess; ///< 力系数监控对象

    std::string resultName; ///< 结果名称
    std::string resultPath; ///< 结果路径
    std::fstream outFile; ///< 监测结果文件输出流对象
	std::fstream outForceFile; ///< 监测原始力数据结果文件输出流对象
    int currentStep; ///< 当前迭代步
    int currentLevel; ///< 当前网格层级
    int startStep; ///< 起始步
    int timeStep; ///< 当前物理时间步
	int meanStep;
	int startMeanStep;
    Scalar currentTime; ///< 当前物理时间
    bool unsteady; ///< 是否是非定常流动: ture为非定常，false为定常
    
    SystemTime CPUTimeTotal; ///< CPU时钟对象，用于计算求解所需耗时
    SystemTime CPUTimePerStep; ///< CPU时钟对象，用于计算求解所需耗时
    SystemTime CPUTimeOutLoop; ///< CPU时钟对象，用于计算外循环时间

    SubMesh *localMesh; ///< 当地网格

    int elementSize; ///< 全局网格单元数量
	int boundaryFaceSizeTotal; ///< 全局网格边界面数量
	std::vector<int> boundaryFaceSizeGlobal; ///< 全局网格边界面数量

    /// 当地网格单元编号与全局编号对应关系
    std::vector<std::vector<int>> elementIDLocalToGlobalTotal;

    /// 当地网格边界面编号与全局编号对应关系
    std::vector<std::vector<int>> boundaryFaceIDLocalToGlobalTotal;

    /// 续算标识
    bool restartFlag;

    /// 新监控文件标识
    bool newMonitorFileFlag;
	bool newUnsteadyMonitorFileFlag;

    std::vector<bool> monitorResidualFlag; ///< 残值监控标识
    std::vector<bool> maximumResidualFlag; ///< 最大残值监控标识
    std::vector<bool> monitorForceFlag; ///< 力系数监控标识

    ZoneManager *zoneManager; ///< 多域管理器

	Scalar startTime; ///< 非定常续算时间
	bool turbulentFlag; ///< 湍流标识

    bool dimension3D; ///< 三维标识

    bool dualTimeFlag; ///< 采用双时间计算标识
};

#endif
