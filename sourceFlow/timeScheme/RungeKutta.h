﻿////////////////////////////////////////////////////////////////////////////////////
////---------------------------------------ARI-CFD-----------------------------/////
////------------------------中国航空工业空气动力研究院-------------------------/////
///////////////////////////////////////////////////////////////////////////////////
//! @file      RungeKutta.h
//! @brief     CFD流场求解的龙格库塔时间推进类.
//! <AUTHOR>
//! @date      2021-03-30
//
//------------------------------修改日志----------------------------------------
//  2021-04-14 乔龙
//    说明：非定常实现。
//
//  2021-03-30 李艳亮、乔龙
//    说明：添加注释并进行规范化。
//------------------------------------------------------------------------------

# ifndef _sourceFlow_timeScheme_RungeKutta_
# define _sourceFlow_timeScheme_RungeKutta_

#include "sourceFlow/timeScheme/FlowTime.h"

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场Runge-Kutta时间推进类
 * 由流场时间基类派生
 * 
 */
class RungeKutta : public FlowTime
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    RungeKutta(Package::FlowPackage &flowPackage_);

    /**
     * @brief 析构函数
     * 
     */
    ~RungeKutta();
    
    /**
     * @brief 龙格库塔显式时间推进中内迭代求解一次
     * 
     * @param[in] recalculateResiduals 本层网格重新计算残值标识
     * @param[in] calculateForcingFunction 本层网格计算力源项标识
     */
    void Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction);

protected:
    /**
     * @brief 计算当前RK推进的系数（用于物理量更新）
     * 
     * @param[in] currentStage 当前Runge-Kutta步数
     * @param[in] elementID 单元编号
     */
    Scalar CalculateMarchCoefficient(const int &currentStage, const int &elementID);

    /**
     * @brief 流动变量更新
     * 
     * @param[in] currentStage 当前RK计算步数
     */
    void Update(const int &currentStage);

    /**
     * @brief 设置龙格库塔格式的stage系数
     * 
     */
    void SetAlpha();

    /**
     * @brief 设置CFL数
     * 
     */
    void SetCFL();

protected:
    std::vector<Scalar> alpha; ///< 龙格库塔格式的stage系数(多步RK和混合RK均需设置)
    Flux::ReconstructionOrder order; ///< 重构阶数
};

} // namespace Flow
} // namespace Time

#endif