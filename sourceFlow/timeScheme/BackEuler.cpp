﻿#include "sourceFlow/timeScheme/BackEuler.h"
#include "meshProcess/meshSorting/MeshSorting.h"
#include <limits>

namespace Time
{
namespace Flow
{
BackEuler::BackEuler(Package::FlowPackage &flowPackage)
	:
    FlowTime(flowPackage),
	flowConfigure(flowPackage.GetFlowConfigure()),
	jacobian(*flowPackage.GetImplicitSolver().jacobian),
	jacobianTur(*flowPackage.GetImplicitSolver().jacobianTur),
	systemNew(mesh, flowConfigure, mesh->GetMeshDimension()+2),
	systemNewTur(mesh, flowConfigure, flowPackage.GetTurbulentStatus().nVariable)
{
    numberStage = 1;
    hybridFlag = false;
    calculatedFlag = {true};

	dim3 = mesh->GetMeshDimension() == Mesh::MeshDim::md3D;
  	nVariable = dim3 ? 5 : 4;
	nTurbulence = flowPackage.GetTurbulentStatus().nVariable;

	resVector.resize(mesh->GetElementNumberAll() * nVariable);
	solVector.resize(mesh->GetElementNumberAll() * nVariable, 0.0); //首次参与迭代，需要置0
	jacobian.Initialize(mesh, nVariable);
	
	resVectorTur.resize(mesh->GetElementNumberAll() * nTurbulence);
	solVectorTur.resize(mesh->GetElementNumberAll() * nTurbulence, 0.0); //首次参与迭代，需要置0
	jacobianTur.Initialize(mesh, nTurbulence);
}

BackEuler::~BackEuler()
{
}

void BackEuler::Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction)
{
	// 保存旧值
    SaveOld();
	
    // 预处理计算
    if (precondition != nullptr) precondition->Calculate();

    // 计算CFL数
    CalculateCFL(CFLType);

    //计算谱半径
    fluxNS.CalculateSpectralRadius();

    // 计算当地时间步长
    CalculateDeltaT();

    // 初始化Jacobian和solVector
	this->InitializeSolver();
	
    // 计算本层网格流场残值
    this->CalculateFlowResidual(0, true, currentLevel > fineMeshLevel && calculateForcingFunction);

    // 方程求解
    this->Solve();
	
    // 实现流动变量的更新
    this->Update();

    // 边界条件更新
    UpdateBoundaryCondition();

    // 更新梯度和MuT
    UpdateGradientAndMuT();

    // 多重网格计算时，需采用最新的物理量计算本层网格残差，用于传递给粗网格
    if (numberLevel > 1 && currentLevel < numberLevel - 1 && recalculateResiduals)
    {
		// 仅计算残值，不需要计算Jacobian
		flowPackage.SetUpdateJacobian(false);

        // 计算残值
		CalculateFlowResidual(1, true, currentLevel > fineMeshLevel && calculateForcingFunction);

        // 边界残值更新（用于对偶网格）
        UpdateBoundaryResidual();
    }
}

void BackEuler::InitializeSolver()
{
	flowPackage.SetUpdateJacobian(true);
	jacobian.SetValZero();
	if (turbulenceNS != nullptr) jacobianTur.SetValZero();

	for (int i = 0; i < solVector.size(); ++i) solVector[i] = 0.0;
	// for (int i = 0; i < solVectorTur.size(); ++i) solVectorTur[i] = 0.0;
}

void BackEuler::Solve()
{	
	//建立矩阵求解系统	
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index0 = 0; index0 < elementNumber; ++index0)
    {
        const int &elementID = mesh->GetElementIDInDomain(index0);
		
        const Scalar deltaTInv = 1.0 / deltaT->GetValue(elementID);
		jacobian.AddVal2Diag(elementID, deltaTInv);
		jacobianTur.AddVal2Diag(elementID, deltaTInv);
		
		const int index = elementID * nVariable;
		resVector[index] = -residualMass->GetValue(elementID);
		resVector[index + 1] = -residualMomentum->GetValue(elementID).X();
		resVector[index + 2] = -residualMomentum->GetValue(elementID).Y();
		if (dim3) resVector[index + 3] = -residualMomentum->GetValue(elementID).Z();
		resVector[index + nVariable - 1] = -residualEnergy->GetValue(elementID);

		const int index_tur = elementID * nTurbulence;
		for (int iVar = 0; iVar < nTurbulence; iVar++)
			resVectorTur[index_tur + iVar] = -residualTurbulence[iVar]->GetValue(elementID);
	}

	//矩阵求解
	systemNew.Solve(jacobian, resVector, solVector);
	
	//矩阵求解
	systemNewTur.Solve(jacobianTur, resVectorTur, solVectorTur);
}

void BackEuler::Update()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index0 = 0; index0 < elementNumber; ++index0)
    {
        const int &elementID = mesh->GetElementIDInDomain(index0);
        
		const Vector &UTemp0 = U0->GetValue(elementID);
		const Scalar &pTemp0 = p0->GetValue(elementID);
		const Scalar rhoTemp0 = rho0->GetValue(elementID);
		const Vector rhoUTemp0 = rhoTemp0 * UTemp0;
		const Scalar rhoETemp0 = pTemp0 / flowPackage.GetMaterialNumber().gamma1 + 0.5 * rhoTemp0 * (UTemp0 & UTemp0);

		// 获取变化量
		const int index = elementID * nVariable;
		const Scalar deltaRho = solVector[index];
		const Vector deltaRhoU = Vector(solVector[index + 1], solVector[index + 2], dim3 ? solVector[index + 3] : Scalar0);
		const Scalar deltaRhoE = solVector[index + nVariable - 1];

		// 计算松弛系数
		const Scalar relaxRho = ComputeUnderRelaxationFactor(rhoTemp0, deltaRho);
		const Scalar relaxRhoE = ComputeUnderRelaxationFactor(rhoETemp0, deltaRhoE);
		Scalar relax = Min(relaxRho, relaxRhoE);
		if (relax < 1e-10) relax = 0.0;
		
		// 更新守恒量
		const Scalar rhoTemp = rhoTemp0 + relax * deltaRho;
		const Vector rhoUTemp = rhoUTemp0 + relax * deltaRhoU;
		const Scalar rhoETemp = rhoETemp0 + relax * deltaRhoE;

		// 将守恒变量转化为基本变量，并实现基本变量的体场更新
		const Scalar rhoTempInv = 1.0 / rhoTemp;
		const Vector UTemp = dim3 ? rhoTempInv * rhoUTemp : Vector(rhoTempInv * rhoUTemp.X(), rhoTempInv * rhoUTemp.Y(), Scalar0);
        const Scalar pTemp = (rhoETemp - 0.5 * rhoTemp * (UTemp & UTemp)) * gamma1;
    	rho->SetValue(elementID, rhoTemp);
    	U->SetValue(elementID, UTemp);
    	p->SetValue(elementID, pTemp);

		// 湍流量更新
		if (nTurbulence > 0)
		{
			const int index_tur = elementID * nTurbulence;
			Scalar relaxTur = 1.0;
			for (int m = 0; m < nTurbulence; ++m)
			{
				const Scalar &phi0 = flowPackage.GetField0().turbulence[m]->GetValue(elementID);
				relaxTur = Min(relaxTur, ComputeUnderRelaxationFactor(rhoTemp0 * phi0, solVectorTur[index_tur + m]));
			}
			if (relaxTur < 1e-10) relaxTur = 0.0;

			for (int m = 0; m < nTurbulence; ++m)
			{
				auto &phi = *flowPackage.GetField().turbulence[m];
				const Scalar &phi0 = flowPackage.GetField0().turbulence[m]->GetValue(elementID);
				phi.SetValue(elementID, Max((rhoTemp0 * phi0 + relaxTur * solVectorTur[index_tur + m]) / rhoTemp, SMALL));
			}
		}
	}

	flowPackage.UpdateExtrasField();

	// 限制更新量
	CheckAndLimit();
}

Scalar BackEuler::ComputeUnderRelaxationFactor(const Scalar &valueTemp, const Scalar &solTemp)
{	
	const Scalar delta = fabs(solTemp);
	return delta > 0.3 * valueTemp ? 0.3 * valueTemp / delta : 1.0;
}

}
}