﻿////////////////////////////////////////////////////////////////////////////////////
////---------------------------------------ARI-CFD-----------------------------////
////------------------------中国航空工业空气动力研究院-------------------------////
///////////////////////////////////////////////////////////////////////////////////
//! @file      LUSGS.h
//! @brief     时间求解的隐式方法：LUSGS类
//! <AUTHOR>
//! @date      2022-02-14
//
//------------------------------修改日志----------------------------------------
//
//  2022-02-14 李艳亮
//    说明：建立。
//------------------------------------------------------------------------------
# ifndef _sourceFlow_timeScheme_LUSGS_
# define _sourceFlow_timeScheme_LUSGS_

#include "sourceFlow/timeScheme/FlowTime.h"

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场LUSGS时间推进类
 * 由基础时间类进行派生
 * 
 */
class LUSGS : public FlowTime
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    LUSGS(Package::FlowPackage &flowPackage_);

    /**
    * @brief 析构函数
    */
    ~LUSGS();

    /**
     * @brief LUSGS求解内迭代求解一次
     * 
     * @param[in] recalculateResiduals 本层网格重新计算残值标识
     * @param[in] calculateForcingFunction 本层网格计算力源项标识
     */
    void Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction);

protected:
    /**
     * @brief LUSGS推进
     * 
     */
    void MarchLUSGS();    

    /**
     * @brief 流动变量更新
     * 
     */
    void Update(); 

    /**
    * @brief 网格重排
    *
    */
    void ReNumber();

    /**
    * @brief 计算对角阵的倒数
    *
    * @param[in] elementID 单元编号
    * @param[out] diagFlow 主流方程的对角阵倒数
    * @param[out] diagTur 湍流方程的对角阵倒数
    */
    void CalculateDiagInverse(const int &elementID, Scalar &diagFlow, std::vector<Scalar> &diagTur);

    /**
     * @brief 累加对流项差值（替代Jacobian矩阵系数）
     * 
     * @param[in] adjacentID 相邻单元编号
     * @param[in] faceID 面编号
     * @param[out] Dr    质量方程的对流项差值
     * @param[out] DrU   动量方程的对流项差值
     * @param[out] DrE   能量方程的对流项差值
     * @param[out] DrTur 湍流方程的对流项差值
     */
    void AddDeltaFlux(const int &adjacentID, const int &faceID,
        Scalar &Dr, Vector &DrU, Scalar &DrE, std::vector<Scalar> &DrTur);

    /**
    * @brief 累加rA项（替代Jacobian矩阵系数）
    *
    * @param[in] elementID 单元编号
    * @param[in] adjacentID 相邻单元编号
    * @param[in] faceID 面编号
    * @param[out] Dr    质量方程的对流项差值
    * @param[out] DrU   动量方程的对流项差值
    * @param[out] DrE   能量方程的对流项差值
    * @param[out] DrTur 湍流方程的对流项差值
    */
    void AddrAStar(const int &elementID, const int &adjacentID, const int &faceID, 
        Scalar &Dr, Vector &DrU, Scalar &DrE, std::vector<Scalar> &DrTur);

private:
    std::vector<std::vector<int>> OwnerIDCell; ///< 下三角阵的单元编号集合
    std::vector<std::vector<int>> NeighborIDCell; ///< 上三角阵的单元编号集合
    std::vector<std::vector<int>> OwnerIDFace; ///< 下三角阵的面编号集合
    std::vector<std::vector<int>> NeighborIDFace; ///< 上三角阵的面编号集合
    std::vector<int> elementIDSortMap; ///< 单元编号重排表
   
    ElementField<Scalar> deltaR; ///< 守恒量rho的差值场
    ElementField<Vector> deltaRU; ///< 守恒量rhoU的差值场
    ElementField<Scalar> deltaRE; ///< 守恒量rhoE的差值场
    std::vector<ElementField<Scalar>*> deltaRTur; ///< 湍流守恒量rhoX的差值场
   
    const Scalar &gamma1; ///< \gamma - 1.0  
    const Scalar &Cp; ///< 定压比热容  

    const Scalar omega; ///< 对流项谱半径的松弛系数（1~2）
    const Scalar coeff; ///< 系数 Max(4.0 / 3.0, gamma)

    std::vector<ElementField<Scalar>*> jacobianTurbulence; ///< 湍流的对角阵D中源项贡献部分
};

} // namespace Flow
} // namespace Time

# endif