﻿# ifndef _sourceFlow_timeScheme_BackEuler_
# define _sourceFlow_timeScheme_BackEuler_

#include "basic/CFD/linearSystemSolver/LinearSystemSolver.h"
#include "basic/common/Matrix.h"
#include "sourceFlow/timeScheme/FlowTime.h"
#include <iostream>
using namespace std;

/**
 * @brief 时间命名空间
 * 
 */
namespace Time
{
/**
 * @brief 流场时间命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 流场GMRES时间推进类
 * 由基础时间类进行派生
 * 
 */
class BackEuler : public FlowTime
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] flowPackage_ 流场数据包
     */
    BackEuler(Package::FlowPackage &flowPackage_);

	/**
	* @brief 析构函数
	*/
	~BackEuler();

    /**
     * @brief GMRES求解内迭代求解一次
     * 
     * @param[in] recalculateResiduals 本层网格重新计算残值标识
     * @param[in] calculateForcingFunction 本层网格计算力源项标识
     */
	void Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction);
	
protected:
    /**
     * @brief GMRES推进
     * 
     */
    void Solve();    
	
    /**
     * @brief 流动变量更新
     * 
     */
    void Update();  

	/**
	* @brief 初始化求解器
	*
	*/
    void InitializeSolver();

	/**
	* @brief 计算松弛系数
	*
	*/
	Scalar ComputeUnderRelaxationFactor(const Scalar &rhoTemp, const Scalar &rhoETemp);

private:
	const Configure::Flow::FlowConfigure &flowConfigure; ///< 流场参数
	
	int nVariable; ///< 主流方程数量
	int nTurbulence; ///< 湍流方程数量
	bool dim3; ///< 三维标识
    
	BlockSparseMatrix &jacobian; ///< 主流Jacobian矩阵
	std::vector<Scalar> resVector; ///< 主流残值矢量
	std::vector<Scalar> solVector; ///< 主流解矢量
	LinearSystemSolver systemNew; ///< 主流线性求解系统

	BlockSparseMatrix &jacobianTur; ///< 湍流Jacobian矩阵
	std::vector<Scalar> resVectorTur; ///< 湍流残值矢量
	std::vector<Scalar> solVectorTur; ///< 湍流解矢量
	LinearSystemSolver systemNewTur; ///< 湍流线性求解系统
};

} // namespace Flow
} // namespace Time

# endif