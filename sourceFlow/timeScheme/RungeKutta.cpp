﻿#include "sourceFlow/timeScheme/RungeKutta.h"

namespace Time
{
namespace Flow
{

RungeKutta::RungeKutta(Package::FlowPackage &flowPackage_)
	:
    FlowTime(flowPackage_)
{
    const auto &flowConfigure = flowPackage.GetFlowConfigure();
    numberStage = flowConfigure.GetTimeScheme().RungeKutta.stages;
    //if(unsteadyFlag && !dualTime) numberStage = 1;

	order = flowConfigure.GetFluxScheme(currentLevel).reconstructOrder;

    if (flowConfigure.GetTimeScheme().RungeKutta.coefficients.size() != numberStage) this->SetAlpha();
    else alpha = flowConfigure.GetTimeScheme().RungeKutta.coefficients;

    if (CFL < SMALL) SetCFL();

    if (flowConfigure.GetTimeScheme().RungeKutta.type == Time::RungeKuttaType::HYBRID)
    {
        hybridFlag = true;
        calculatedFlag.clear();
        calculatedFlag.resize(alpha.size(), false);
        calculatedFlag[0] = true;
    }
    else
    {
        hybridFlag = false;
        calculatedFlag.clear();
        calculatedFlag.resize(alpha.size(), true);
    }

	if (flowConfigure.GetTimeScheme().RungeKutta.type == Time::RungeKuttaType::HYBRID)
	{
		dissipativeResidualMass.Initialize();
		dissipativeResidualMomentum.Initialize();
		dissipativeResidualEnergy.Initialize();
        for (int k = 0; k < nTurbulence; k++)
            dissipativeResidualTurbulence[k]->Initialize();
	}
}

RungeKutta::~RungeKutta()
{
    for (int k = 0; k < dissipativeResidualTurbulence.size(); k++)
    {
        if (dissipativeResidualTurbulence[k] != nullptr)
        {
            delete dissipativeResidualTurbulence[k];
            dissipativeResidualTurbulence[k] = nullptr;
        }
    }
}

void RungeKutta::Iterate(const bool &recalculateResiduals, const bool &calculateForcingFunction)
{
    // 保存旧值
    SaveOld();
    
    // 预处理计算
    if (precondition != nullptr) precondition->Calculate();

	// 计算CFL数
	CalculateCFL(CFLType);

    //计算谱半径
    fluxNS.CalculateSpectralRadius();

	// 计算当地时间步长
	CalculateDeltaT();

    // 多步龙格库塔时间推进    
	for (int currentStage = 0; currentStage < numberStage; currentStage++)
	{
        // 计算本层网格流场残值
        this->CalculateFlowResidual(currentStage,
                                    calculatedFlag[currentStage],
                                    currentLevel > fineMeshLevel && calculateForcingFunction);

        // 残值光顺
        if (currentLevel == fineMeshLevel && currentStage == numberStage - 1) SmoothResiduals();

		// 实现流动变量的更新
		this->Update(currentStage);

        // 边界条件更新
        UpdateBoundaryCondition();

        // 更新梯度和湍流粘性系数
        if (currentStage == numberStage - 1) UpdateGradientAndMuT();
	}

    // 多重网格计算时，需采用最新的物理量计算本层网格残差，用于传递给粗网格
	if (numberLevel > 1 && currentLevel < numberLevel - 1 && recalculateResiduals)
    {
		// 计算残值
        CalculateFlowResidual(numberStage, true, currentLevel > fineMeshLevel);

	    // 边界残值更新（用于对偶网格）
	    UpdateBoundaryResidual();
    }
}

void RungeKutta::Update(const int &currentStage)
{
    std::vector<Scalar> massFractionTemp0;
    std::vector<Scalar> massFractionTemp;
    
#if defined(_EnableMultiSpecies_)
    massFractionTemp0.resize(speciesSize);
    massFractionTemp.resize(speciesSize);
#endif
    
    Scalar pTemp, TTemp;
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        
        // 计算推进系数, deltaT中未乘体积，这里不除体积
		const Scalar coefficient = CalculateMarchCoefficient(currentStage, elementID);
        
        // 获得单元旧值
        const Scalar &rhoTemp0 = rho0->GetValue(elementID);
        const Vector &UTemp0 = U0->GetValue(elementID);
        const Scalar &pTemp0 = p0->GetValue(elementID);
    
        // 时间步长
        const Scalar &deltaTTemp = deltaT->GetValue(elementID);

        // 更新密度和速度
        const Scalar &residualMassTemp = residualMass->GetValue(elementID);
        const Vector &residualMomentumTemp = residualMomentum->GetValue(elementID);
        const Scalar temp1 = Min(-residualMassTemp / (rhoTemp0 + SMALL), 0.0);
        const Scalar relaxRho = 1.0 / (1.0 - deltaTTemp * 1.1 * temp1);
        const Scalar rhoTemp = rhoTemp0 + coefficient * relaxRho * residualMassTemp;
        const Vector rhoUTemp = rhoTemp0 * UTemp0 + coefficient * residualMomentumTemp;
        rho->SetValue(elementID, rhoTemp);
        U->SetValue(elementID, rhoUTemp *(1.0 / rhoTemp));
    
        // 更新湍流量
        Scalar temp2 = INF;
		for (int k = 0; k < nTurbulence; k++)
        {
            const Scalar rhoPhi0 = rhoTemp0 * turbulence0[k]->GetValue(elementID);
            const Scalar &residualPhi = residualTurbulence[k]->GetValue(elementID);
            temp2 = Min(temp2, -residualPhi / Max(rhoPhi0, SMALL));
			temp2 = Min(temp2, jacobianTurbulence[k]->GetValue(elementID));
        }
        for (int k = 0; k < nTurbulence; k++)
        {
            const Scalar &residualPhi = residualTurbulence[k]->GetValue(elementID);
            const Scalar rhoPhi0 = rhoTemp0 * turbulence0[k]->GetValue(elementID);
            const Scalar rhoPhi = rhoPhi0 + coefficient * residualPhi / (1.0 - deltaTTemp * 1.1 * temp2);
            turbulence[k]->SetValue(elementID, Max(rhoPhi / rhoTemp, SMALL));
        }
        
#if defined(_EnableMultiSpecies_)
        // 更新组分
        for (int k = 0; k < speciesSize; k++)
        {
            massFractionTemp0[k] = massFraction0[k]->GetValue(elementID);
            const Scalar &residualPhi = residualMassFraction[k]->GetValue(elementID);
            const Scalar rhoPhi = rhoTemp0 * massFractionTemp0[k] + coefficient * relaxRho * residualPhi;
            massFractionTemp[k] = Max(rhoPhi / rhoTemp, SMALL);
            massFraction[k]->SetValue(elementID, massFractionTemp[k]);
        }
#endif

        // 计算rhoE
        const Scalar TTemp0 = material.GetTemperature(pTemp0, rhoTemp0, massFractionTemp0);
        const Scalar enthalpy0 = material.Enthalpy(TTemp0, massFractionTemp0);
        const Scalar rhoETemp0 = rhoTemp0 * enthalpy0 - pTemp0 + 0.5 * rhoTemp0 * (UTemp0 & UTemp0);
        const Scalar &residualEnergyTemp = residualEnergy->GetValue(elementID);
        const Scalar temp3 = Min(-residualEnergyTemp / (rhoETemp0 + SMALL), 0.0);
        const Scalar rhoETemp = rhoETemp0 + coefficient * residualEnergyTemp / (1.0 - deltaTTemp * 1.1 * temp3);
    
        // 更新压力和温度
#if defined(_EnableMultiSpecies_)
        if (flowPackage.GetFlowConfigure().GetMaterial().density != Material::Flow::DensityType::IDEAL_GAS)
        {
            TTemp = material.CalculateTemperature(rhoETemp, rhoTemp, rhoUTemp / rhoTemp, massFractionTemp, TTemp0);
            pTemp = material.CalculatePressure(rhoTemp, TTemp, massFractionTemp);
        }
        else
#endif
        {
            pTemp = (rhoETemp - 0.5 / rhoTemp * (rhoUTemp & rhoUTemp)) * gamma1;
            pTemp = Max(pTemp, SMALL);

            Scalar deltaP = -(pTemp0 - pTemp) / coefficient;
            const Scalar temp4 = Min(-deltaP / (p0->GetValue(elementID) + SMALL), 0.0);
            pTemp = pTemp0 + coefficient * deltaP / (1.0 - deltaTTemp * 1.1 * temp4);
        }

        p->SetValue(elementID, pTemp);
	}
    
    flowPackage.UpdateExtrasField();

    // 限制更新量
    CheckAndLimit();

	return;
}

Scalar RungeKutta::CalculateMarchCoefficient(const int &currentStage, const int &elementID)
{
	const Scalar coef = deltaT->GetValue(elementID) * alpha[currentStage];
	
	if (!dualTime) return -coef;
	
	if (unsteadyOrder == Time::UnsteadyOrder::FIRST)
	{
		FatalError("RungeKutta::CalculateMarchCoefficient: unsteady order is not supported!");
		return -coef;
	}
	else if (unsteadyOrder == Time::UnsteadyOrder::SECOND)
	{
		const Scalar &vol = mesh->GetElement(elementID).GetVolume();
		return -coef / (1.0 + 1.5 * coef * vol / physicalDeltaTime * beta);
	}
	else
	{
		FatalError("RungeKutta::CalculateMarchCoefficient: unsteady order is not supported!");
		return -coef;
	}

    return -coef;
}

void RungeKutta::SetAlpha()
{
    const auto &inviscidScheme = flowPackage.GetFlowConfigure().GetFluxScheme(currentLevel).inviscid;

	// order: 空间离散格式
	// numberStage: RK的层数
    alpha.resize(numberStage);

    if (inviscidScheme == Flux::Flow::Inviscid::CENTRAL)
    {
        if (numberStage == 1)
        {
            alpha[0] = 1.0000;
        }
        else if (numberStage == 2)
        {
            alpha[0] = 0.5000;
            alpha[1] = 1.0000;
        }
        else if (numberStage == 3)
	    {
            alpha[0] = 0.6666;
            alpha[1] = 0.6666;
	    	alpha[2] = 1.0000;
	    }
	    else if (numberStage == 4)
	    {
            alpha[0] = 0.2500;
            alpha[1] = 0.3333;
            alpha[2] = 0.5000;
	    	alpha[3] = 1.0000;
	    }
	    else if (numberStage == 5)
	    {
            alpha[0] = 0.2500;
            alpha[1] = 0.1666;
            alpha[2] = 0.3750;
            alpha[3] = 0.5000;
	    	alpha[4] = 1.0000;
	    }
    }
    else
    {
        if (numberStage == 1)
        {
            alpha[0] = 1.0000;
        }
        else if (numberStage == 2)
        {
            alpha[0] = 0.5000;
            alpha[1] = 1.0000;
        }
        else if (numberStage == 3)
        {
            alpha[0] = 0.1500;
            alpha[1] = 0.5000;
            alpha[2] = 1.0000;
        }
        else if (numberStage == 4)
        {
            alpha[0] = 0.1500;
            alpha[1] = 0.3275;
            alpha[2] = 0.5700;
            alpha[3] = 1.0000;
        }
        else if (numberStage == 5)
        {
            alpha[0] = 0.2742;
            alpha[1] = 0.2067;
            alpha[2] = 0.5020;
            alpha[3] = 0.5142;
            alpha[4] = 1.0000;
        }
    }

	return;
}

void RungeKutta::SetCFL()
{
	// order: 空间离散格式
	// numberStage: RK的层数
    if (order == Flux::ReconstructionOrder::FIRST)
    {
        if (numberStage == 3)
	    {
	    	CFL = 1.5;
	    }
	    else if (numberStage == 4)
	    {
	    	CFL = 2.0;
	    }
	    else if (numberStage == 5)
	    {
	    	CFL = 2.5;
	    }
    }
    else if (order == Flux::ReconstructionOrder::SECOND)
    {
        if (numberStage == 3)
	    {
	    	CFL = 0.69;
	    }
	    else if (numberStage == 4)
	    {
	    	CFL = 0.92;
	    }
	    else if (numberStage == 5)
	    {
	    	CFL = 1.15;
	    }
    }

	return;
}

}//namespace Flow
}//namespace Time
