﻿#include "sourceFlow/timeScheme/FlowTime.h"
#include "basic/field/FieldManipulation.h"

namespace Time
{
namespace Flow
{

FlowTime::FlowTime(Package::FlowPackage &flowPackage_)
	:
    flowPackage(flowPackage_),
	mesh(flowPackage_.GetMeshStruct().mesh),
    material(flowPackage_.GetMaterial()),
	boundaryConditionNS(flowPackage_),
	fluxNS(flowPackage_), 
	nTurbulence(flowPackage_.GetTurbulentStatus().nVariable),
	currentLevel(flowPackage_.GetMeshStruct().level),
	residualForceMass(flowPackage_.GetMeshStruct().mesh), 
	residualForceMomentum(flowPackage_.GetMeshStruct().mesh), 
	residualForceEnergy(flowPackage_.GetMeshStruct().mesh),
#if defined(_EnableMultiSpecies_)
    speciesSize(flowPackage_.GetMultiSpeciesStatus().speciesSize),
	massFraction(flowPackage.GetField().massFraction),
	massFraction0(flowPackage.GetField0().massFraction),
	residualMassFraction(flowPackage.GetResidualField().residualMassFraction),
#endif
	viscousFlag(flowPackage_.GetTurbulentStatus().viscousFlag),
	nodeCenter(flowPackage_.GetFlowConfigure().GetPreprocess().dualMeshFlag),
	turbulenceResidualMacro(flowPackage_.GetTurbulentStatus().residualMacro),
	gamma1(flowPackage_.GetMaterialNumber().gamma1),
    gamma1Inv(1.0/flowPackage_.GetMaterialNumber().gamma1),
    dissipativeResidualMass(mesh), dissipativeResidualMomentum(mesh), dissipativeResidualEnergy(mesh),
    CFL(flowPackage_.GetCFLNumber())
{
    nElementGlobal = mesh->GetElementNumberReal();
#if defined(_BaseParallelMPI_)
    SumAllProcessor(nElementGlobal, 0);
    MPIBroadcast(nElementGlobal, 0);
#endif

	if (nTurbulence > 0) turbulenceNS = new Turbulence::TurbulenceManager(flowPackage_);
	else                 turbulenceNS = nullptr;

    const auto &flowConfigure = flowPackage.GetFlowConfigure();
	numberLevel = flowConfigure.GetAcceleration().multigridSolver.level;

    unsteadyFlag = flowPackage.GetUnsteadyStatus().unsteadyFlag;
    dualTime = flowPackage.GetUnsteadyStatus().dualTime;
	physicalDeltaTime = flowConfigure.GetControl().outerLoop.timeStep;
    unsteadyOrder = flowConfigure.GetTimeScheme().unsteadyTimeOrder;
    
    precondition = fluxNS.GetPrecondition();
    boundaryConditionNS.SetBoundaryCondition(precondition);
    boundaryConditionNS.SetFluxScheme( fluxNS.GetInviscidFluxScheme(), fluxNS.GetViscousFluxScheme() );

    std::vector<int> symmetryPatchID;
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        if (flowConfigure.JudgeSymmetryLocal(currentLevel, patchID)) symmetryPatchID.push_back(patchID);

    // 确定梯度计算类型
    gradientScheme = flowConfigure.GetFluxScheme(currentLevel).gradient;
    gradient = new Gradient::Gradient(flowPackage.GetMeshStruct().mesh, gradientScheme, nodeCenter);

    const Smoother::Scheme &residualSmoothScheme = flowConfigure.GetAcceleration().residualSmooth;
    smoother = new Smoother::FieldSmoother(mesh, residualSmoothScheme, 1.3, nodeCenter, symmetryPatchID);

	timeScheme = flowConfigure.GetTimeScheme().innnerLoopType;

	// CFL数的设置
    coarseRatio = flowConfigure.GetTimeScheme().CFL.coarseMeshRatio;
    CFL = flowConfigure.GetTimeScheme().CFL.value * pow(coarseRatio, currentLevel);

    CFLType = flowConfigure.GetTimeScheme().CFL.variableFlag;
    CFLGrowthStep = 0;
    if (CFLType==1)
    {
        CFLGrowthStep = flowConfigure.GetTimeScheme().CFL.growthStep;
        CFLGrowthRatio = flowConfigure.GetTimeScheme().CFL.growthRatio;
        CFLMax = flowConfigure.GetTimeScheme().CFL.max;
        CFLMin = flowConfigure.GetTimeScheme().CFL.min;
    }    
    currrentCFLStep = 0;

    //自动CFL数初始值设为1
    monitorResidualCurrent=0;
    if (CFLType==2)
    {
        CFLGrowthStep = flowConfigure.GetTimeScheme().CFL.growthStep;
        CFL0=flowConfigure.GetTimeScheme().CFL.value;
        CFLGrowthRatio = flowConfigure.GetTimeScheme().CFL.growthRatio;
        AutoCFLMin=flowConfigure.GetTimeScheme().CFL.min;
        AutoCFLMax=flowConfigure.GetTimeScheme().CFL.max;       
    }

    startStep=0;
    epsilon=0.05;//定义阈值，该值越小收敛越严格
    delta1=5;//以残值判断CFL增大的阈值
    delta2=5;//以守恒量比值判断CFL增大的阈值
    CFLFactor=Max(CFLGrowthRatio - 1.0, 0.0);//初始增长系数

	beta = 2.0;
    
    rho = flowPackage.GetField().density;
    U = flowPackage.GetField().velocity;
    p = flowPackage.GetField().pressure;
	T = flowPackage.GetField().temperature;
	A = flowPackage.GetField().soundSpeed;
    H = flowPackage.GetField().enthalpy;
    turbulence = flowPackage.GetField().turbulence;

    residualMass = flowPackage.GetResidualField().residualMass;
    residualMomentum = flowPackage.GetResidualField().residualMomentum;
    residualEnergy = flowPackage.GetResidualField().residualEnergy;
    residualTurbulence = flowPackage.GetResidualField().residualTurbulence;
    
    rho0 = flowPackage.GetField0().density;
    U0 = flowPackage.GetField0().velocity;
    p0 = flowPackage.GetField0().pressure;
    turbulence0 = flowPackage.GetField0().turbulence;
    
    lambdaConvective = flowPackage.GetField().lambdaConvective;
    lambdaViscous = flowPackage.GetField().lambdaViscous;

    deltaT = flowPackage.GetField().deltaT;
    jacobianTurbulence = flowPackage.GetField().jacobianTurbulence;

	// 如果使用多重网格，则需要计算细网格对粗网格的限制项：
	// 流场残值限制项的初始化
	residualForceTurbulence.clear();
	if (currentLevel > 0)
	{
		residualForceMass.Initialize();
		residualForceMomentum.Initialize();
		residualForceEnergy.Initialize();
		for (int k = 0; k < nTurbulence; k++)
			residualForceTurbulence.push_back(new ElementField<Scalar>(mesh, Scalar0));
	}

    dissipativeResidualTurbulence.clear();
    for (int k = 0; k < nTurbulence; k++)
        dissipativeResidualTurbulence.push_back(new ElementField<Scalar>(mesh));
}

FlowTime::~FlowTime()
{
    if (turbulenceNS != nullptr) { delete turbulenceNS; turbulenceNS = nullptr; }

    if (currentLevel > 0)
    {
        for (int m = 0; m < nTurbulence; ++m)
        {
            if (residualForceTurbulence[m] != nullptr)
            {
                delete residualForceTurbulence[m];
                residualForceTurbulence[m] = nullptr;
            }
        }
    }

    if (smoother != nullptr) { delete smoother; smoother = nullptr; }
	if (gradient != nullptr) { delete gradient; gradient = nullptr; }
}

void FlowTime::Initialize(const Initialization::Type &initialType)
{
    boundaryConditionNS.Initialize();
    
    const auto gradients = flowPackage.GetGradientField();
    if (gradients.gradientRho != nullptr) gradient->Calculate(*rho, *gradients.gradientRho);
    if (gradients.gradientP != nullptr) gradient->Calculate(*p, *gradients.gradientP);
    if (gradients.gradientU != nullptr) gradient->Calculate(*U, *gradients.gradientU);
    if (gradients.gradientT != nullptr) gradient->Calculate(*T, *gradients.gradientT);
#if defined(_EnableMultiSpecies_)
    for (int m = 0; m < speciesSize; ++m)
    {
		if (gradients.gradientMassFraction[m] != nullptr)
            gradient->Calculate(*massFraction[m], *gradients.gradientMassFraction[m]);
    }
#endif

    if (turbulenceNS != nullptr)
    {
        turbulenceNS->Initialize(initialType);
        for (int k = 0; k < nTurbulence; k++)
        {
            if (gradients.gradientTurbulence[k] != nullptr)
                gradient->Calculate(*turbulence[k], *gradients.gradientTurbulence[k]);
        }
    }
    
    residualMass->Initialize();
    residualMomentum->Initialize();
    residualEnergy->Initialize();
    for (int k = 0; k < nTurbulence; k++) residualTurbulence[k]->Initialize();
#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < speciesSize; k++) residualMassFraction[k]->Initialize();
#endif

    deltaT->Initialize();
}

void FlowTime::SaveOld()
{
	*rho0 = *rho;
	*U0 = *U;
	*p0 = *p;
	for (int k = 0; k < nTurbulence; k++) *turbulence0[k] = *turbulence[k];
#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < speciesSize; k++) *massFraction0[k] = *massFraction[k];
#endif
}

Scalar FlowTime::CalculateMonitorMassResidual()
{
    // 监测残值置零
    monitorMassResidual = Scalar0;
    Scalar residualMassTmp;

    // 当地网格上所有残值求和
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        
        residualMassTmp = residualMass->GetValue(elementID);

        const Scalar volumeInverse = 1.0 / mesh->GetElement(elementID).GetVolume();
        Scalar localResidual = fabs(residualMassTmp) * volumeInverse;
        monitorMassResidual += localResidual * localResidual;
    }
    monitorMassResidual/= (Scalar)nElementGlobal;

#if defined(_BaseParallelMPI_)
    SumAllProcessor(monitorMassResidual, 0);
#endif

    // 0号进程监测残值计算
    if (GetMPIRank() == 0) monitorMassResidual=sqrt(monitorMassResidual);
    
    return monitorMassResidual;
}

Scalar FlowTime::CalculateDeltaQDivideQ()
{
    DeltaQ_Divide_Q = Scalar0;
    Scalar QTmp;

    // 当地网格上所有残值求和
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        // 计算ΔQ/Q
        QTmp = (rho->GetValue(elementID)-rho0->GetValue(elementID))/rho->GetValue(elementID);
        DeltaQ_Divide_Q += QTmp * QTmp;
    }
    DeltaQ_Divide_Q/= (Scalar)nElementGlobal;
    
#if defined(_BaseParallelMPI_)
    SumAllProcessor(DeltaQ_Divide_Q, 0);
    MPIBroadcast(DeltaQ_Divide_Q, 0);
#endif

    DeltaQ_Divide_Q=sqrt(DeltaQ_Divide_Q);

    return DeltaQ_Divide_Q;
}

void FlowTime::CalculateCFL(int CFLType)
{
    // 自动CFl数只在细网格层中调用
    if(currentLevel > 0) return;
    
    if (CFLType==1)
    {
        if(currrentCFLStep < CFLGrowthStep)
        {
            CFL = Min(Max(CFL*CFLGrowthRatio, CFLMin), CFLMax) * pow(coarseRatio, currentLevel);
            currrentCFLStep++;
        }
    }
    else if (CFLType==2)
    {
        currrentCFLStep++;
        
        // 储存上一步残值
	    monitorResidualOld = monitorResidualCurrent;
        if (monitorResidualOld == 0) monitorResidualOld=SMALL;
	    monitorResidualCurrent = this->CalculateMonitorMassResidual();

        // 保留第一步残值，用于计算R_0/R_k
        if(startStep==0)
        {
            initial_residual_0 = monitorResidualCurrent;
            CFL = CFL0;
            startStep++;
        }
        else
        {
            // 定义阈值大小，控制CFL数变化
            const Scalar m0 = log10(monitorResidualCurrent/monitorResidualOld);
            const Scalar m1 = log10(initial_residual_0/monitorResidualCurrent);
            const Scalar m2 = -log10(this->CalculateDeltaQDivideQ());

            // 计算CFL数，控制CFL上限
            CFL = Min(CFL * (1.0 + CFLFactor), AutoCFLMax);

	        // 若有发散趋势，CFL缩减为原来的0.8，并控制CFL数的下限
            if (m0 > epsilon)
            {
                if (CFL >= AutoCFLMin) CFL = Max(0.8 * CFL, AutoCFLMin);
                else                   CFL = Max(0.8 * CFL, CFL0);
            }

            // 若收敛趋于稳定，指数增大CFL
	        if (m1 > delta1 && m2 > delta2) CFLFactor = CFLFactor * CFLGrowthRatio;
        }
    }
    
	return;
}

void FlowTime::CalculateDeltaT()
{
	// 计算格式为： dt = CFL * V / (lambdaC + C * lambdaV)
    // 因在推进时还需除体积，这里不乘体积
	if (viscousFlag)
    {
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
		{
            const int &elementID = mesh->GetElementIDInDomain(index);
			deltaT->SetValue(elementID, CFL / ( 0.5 * lambdaConvective->GetValue(elementID)
                                              + 0.5 * lambdaViscous->GetValue(elementID) ));
		}
    }
    else
    {
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
		{
            const int &elementID = mesh->GetElementIDInDomain(index);
			deltaT->SetValue(elementID, CFL / (0.5 * lambdaConvective->GetValue(elementID)));
		}
    }
    
    if(unsteadyFlag && !dualTime)
    {
        Scalar deltaTMinLocal = INF;
        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
			const Scalar temp = deltaT->GetValue(elementID) * mesh->GetElement(elementID).GetVolume();
            if (temp < deltaTMinLocal) deltaTMinLocal = temp;
        }
        
        Scalar deltaTMinGlobal = deltaTMinLocal;
		MinAllProcessor(deltaTMinGlobal);
	    if( deltaTMinGlobal > physicalDeltaTime && physicalDeltaTime > 1.e-20 ) deltaTMinGlobal = physicalDeltaTime;
	    
        for (int index = 0; index < elementNumber; ++index)
		{
            const int &elementID = mesh->GetElementIDInDomain(index);
			deltaT->SetValue(elementID, deltaTMinGlobal / mesh->GetElement(elementID).GetVolume());
		}
	    
        if(unsteadyFlag && !dualTime) flowPackage.UpdateCurrentTime(deltaTMinGlobal);
    }
    
	// 非定常计算时修正当地时间步长
    //if (dualTime)
    //{
    //    // 此处deltaT相当于 deltaT / V
    //    const int elementNumber = mesh->GetElementNumberInDomain();
    //    for (int index = 0; index < elementNumber; ++index)
    //    {
    //        const int &elementID = mesh->GetElementIDInDomain(index);
    //        const Scalar &volume = mesh->GetElement(elementID).GetVolume();
    //        deltaT->SetValue(elementID, Min(2.0 / 3.0 * physicalDeltaTime / volume, deltaT->GetValue(elementID)));
    //    }
    //}

	return;
}

void FlowTime::CalculateResidual()
{
	fluxNS.CalculateSpectralRadius();
	this->CalculateFlowResidual(0, true, false);
}

void FlowTime::CalculateFlowResidual(const int &currentStage,
                                     const bool dissipativeResidualFlag,
                                     const bool calculateForcingFunction)
{
    // 预处理
    if (precondition != nullptr && currentStage > 0) precondition->Calculate();

    // 从细网格层中将残值赋给Force项
	if (currentStage == 0 && calculateForcingFunction) this->AcceptFlowForceFromFine();

    // 将每步RK时间推进步的NS方程残值置零
    fluxNS.SetResidualZero();
    if (turbulenceNS != nullptr) turbulenceNS->SetResidualZero();

    // 计算耗散残差
    if (dissipativeResidualFlag)
    {
        // 计算粘性项残值
		if (viscousFlag) fluxNS.AddDiffusiveResidual();
		if (turbulenceNS != nullptr) turbulenceNS->AddDiffusiveResidual();

        // 计算边界粘性残值
        if (viscousFlag) boundaryConditionNS.AddDiffusiveResidual();

        //计算源项
        fluxNS.AddSourceResidual();
        if (turbulenceNS != nullptr) turbulenceNS->AddSourceResidual();

        // 计算对流项耗散残值
        fluxNS.AddConvectiveDissipationResidual();
        if (turbulenceNS != nullptr) turbulenceNS->AddConvectiveDissipationResidual();
    }

    // 采用混合RK时，需保存stage=0耗散残差，RK循环内其它步可直接调用
    if (hybridFlag && currentStage < numberStage)
    {
        if (currentStage == 0) this->SaveFlowDissipativeResidual();
        else                   this->AddFlowDissipativeResidual();
    }

	// 计算对流项平均残值
	fluxNS.AddConvectiveAverageResidual();
	if (turbulenceNS != nullptr) turbulenceNS->AddConvectiveAverageResidual();

    // 计算边界对流残值
    boundaryConditionNS.AddConvectiveResidual();

	// 边界残值更新（用于对偶网格）
	this->UpdateBoundaryResidual();

    // 低速预处理后的残差计算
    if (precondition != nullptr)
    {
        this->CalculatePrecondition();
	    this->UpdateBoundaryResidual();
    }

    // 添加双时间步非定常残值源项
	if (dualTime) this->AddUnsteadyResidual();
    
    // 粗网格上stage=0时计算Force源项，由粗到细循环过程中，细网格上迭代时不计算Force源项
    // Force源项 = 上层细网格残值 - 本层网格残值
	if (currentStage == 0 && calculateForcingFunction) this->CalculateFlowForceFromFine();

    // 粗网格上计算时，将stage=0时计算Force源项加入到残值中
	if(currentLevel > fineMeshLevel) this->AddFlowForceFromFine();
}

void FlowTime::AcceptFlowForceFromFine()
{
    if (nodeCenter)
    {
        for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
        {
            const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
            if (typeString > Boundary::Type::WALL)
            {
	            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	            for (int index = 0; index < faceSize; ++index)
	            {
	            	// 得到面相关信息
	            	const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                    const Face &face = mesh->GetFace(faceID);
                    const int &ownerID = face.GetOwnerID();
                    const Vector &faceNorm = face.GetNormal();
                    const Vector &residualTemp = residualMomentum->GetValue(ownerID);
                    if (viscousFlag)
                    {
                        residualMomentum->AddValue(ownerID, Vector0);
                        for (int k = 0; k < nTurbulence; k++)
                            residualForceTurbulence[k]->SetValue(ownerID, Scalar0);
                    }
                    else
                    {
                        residualMomentum->AddValue(ownerID, -(residualTemp & faceNorm) * faceNorm);
                    }
                }
            }
            else if (typeString == Boundary::Type::SYMMETRY)
            {
	            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	            for (int index = 0; index < faceSize; ++index)
	            {
	            	// 得到面相关信息
	            	const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                    const Face &face = mesh->GetFace(faceID);
                    const int &ownerID = face.GetOwnerID();

                    const Vector &faceNorm = face.GetNormal();
                    const Vector &residualTemp = residualMomentum->GetValue(ownerID);
                    residualMomentum->AddValue(ownerID, -(residualTemp & faceNorm) * faceNorm);
                }
            }
        }
    }

	// UpdateBoundaryResidual();

    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
	{
        const int &elementID = mesh->GetElementIDInDomain(index);
		residualForceMass.SetValue(elementID, residualMass->GetValue(elementID));
		residualForceMomentum.SetValue(elementID, residualMomentum->GetValue(elementID));
		residualForceEnergy.SetValue(elementID, residualEnergy->GetValue(elementID));
		for (int k = 0; k < nTurbulence; k++)
			residualForceTurbulence[k]->SetValue(elementID, residualTurbulence[k]->GetValue(elementID));
	}

	return;
}

void FlowTime::CalculateFlowForceFromFine()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
	{
        const int &elementID = mesh->GetElementIDInDomain(index);
		residualForceMass.AddValue(elementID, -residualMass->GetValue(elementID));
		residualForceMomentum.AddValue(elementID, -residualMomentum->GetValue(elementID));
		residualForceEnergy.AddValue(elementID, -residualEnergy->GetValue(elementID));
		for (int k = 0; k < nTurbulence; k++)
			residualForceTurbulence[k]->AddValue(elementID, -residualTurbulence[k]->GetValue(elementID));
	}

	return;
}

void FlowTime::AddFlowForceFromFine()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
	{
        const int &elementID = mesh->GetElementIDInDomain(index);
		residualMass->AddValue(elementID, residualForceMass.GetValue(elementID));
		residualMomentum->AddValue(elementID, residualForceMomentum.GetValue(elementID));
		residualEnergy->AddValue(elementID, residualForceEnergy.GetValue(elementID));
		for (int k = 0; k < nTurbulence; k++)
			residualTurbulence[k]->AddValue(elementID, residualForceTurbulence[k]->GetValue(elementID));
	}
	
	return;
}

// 保存耗散残差
void FlowTime::SaveFlowDissipativeResidual()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
		dissipativeResidualMass.SetValue(elementID, residualMass->GetValue(elementID));
		dissipativeResidualMomentum.SetValue(elementID, residualMomentum->GetValue(elementID));
		dissipativeResidualEnergy.SetValue(elementID, residualEnergy->GetValue(elementID));
		for (int k = 0; k < nTurbulence; k++)
			dissipativeResidualTurbulence[k]->SetValue(elementID, residualTurbulence[k]->GetValue(elementID));
	}

    return;
}

// 添加耗散残差
void FlowTime::AddFlowDissipativeResidual()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
		residualMass->AddValue(elementID, dissipativeResidualMass.GetValue(elementID));
		residualMomentum->AddValue(elementID, dissipativeResidualMomentum.GetValue(elementID));
		residualEnergy->AddValue(elementID, dissipativeResidualEnergy.GetValue(elementID));
		for (int k = 0; k < nTurbulence; k++)
			residualTurbulence[k]->AddValue(elementID, dissipativeResidualTurbulence[k]->GetValue(elementID));
	}

    return;
}

void FlowTime::AddUnsteadyResidual()
{
	const Scalar beta31 = -3.0 * (beta - 1.0);
	const int unsteadyFieldSize = (int)flowPackage.GetFieldUnsteadyField().size();
    std::vector<Scalar> rhoTemp(unsteadyFieldSize), rhoETemp(unsteadyFieldSize);
    std::vector<Vector> rhoUTemp(unsteadyFieldSize);
    std::vector<std::vector<Scalar>> rhoPhiTemp(unsteadyFieldSize);

    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
	{
        const int &elementID = mesh->GetElementIDInDomain(index);

		// 计算当前物理时间、上一物理时间、上上一物理时间的流场守恒量
        for (int i = 0; i < unsteadyFieldSize; ++i)
        {
            rhoTemp[i] = flowPackage.GetFieldUnsteadyField()[i].density->GetValue(elementID);
            const Vector &UTemp = flowPackage.GetFieldUnsteadyField()[i].velocity->GetValue(elementID);
            const Scalar &pTemp = flowPackage.GetFieldUnsteadyField()[i].pressure->GetValue(elementID);
            flowPackage.CalcuateConservedValue(rhoTemp[i], UTemp, pTemp, rhoUTemp[i], rhoETemp[i]);

			rhoPhiTemp[i].resize(nTurbulence);
            for (int k = 0; k < nTurbulence; ++k)
            {
                const Scalar &phi = flowPackage.GetFieldUnsteadyField()[i].turbulence[k]->GetValue(elementID);
				rhoPhiTemp[i][k] = rhoTemp[i] * phi;
            }
        }
        
		// 基本物理场残值添加非定常修正项
        if(unsteadyOrder == Time::UnsteadyOrder::FIRST)
        {
		    const Scalar factor = -mesh->GetElement(elementID).GetVolume() / physicalDeltaTime;
		    residualMass->AddValue(elementID, factor * (-rhoTemp[0] + rhoTemp[1]));
		    residualMomentum->AddValue(elementID, factor * (-rhoUTemp[0] + rhoUTemp[1]));
		    residualEnergy->AddValue(elementID, factor * (-rhoETemp[0] + rhoETemp[1]));
            for (int k = 0; k < nTurbulence; ++k)
                residualTurbulence[k]->AddValue(elementID, factor * (-rhoPhiTemp[0][k] + rhoPhiTemp[1][k]));
        }
        else if(unsteadyOrder == Time::UnsteadyOrder::SECOND)
        {
		    const Scalar factor = -0.5 * mesh->GetElement(elementID).GetVolume() / physicalDeltaTime;
			residualMass->AddValue(elementID, factor * (beta31*rhoTemp[0] + 4.0*rhoTemp[1] - rhoTemp[2]));
			residualMomentum->AddValue(elementID, factor * (beta31*rhoUTemp[0] + 4.0*rhoUTemp[1] - rhoUTemp[2]));
			residualEnergy->AddValue(elementID, factor * (beta31*rhoETemp[0] + 4.0*rhoETemp[1] - rhoETemp[2]));
            for (int k = 0; k < nTurbulence; ++k)
				residualTurbulence[k]->AddValue(elementID, factor * (beta31*rhoPhiTemp[0][k] + 4.0*rhoPhiTemp[1][k] - rhoPhiTemp[2][k]));
        }
        else
        {
            FatalError("FlowTime::AddUnsteadyResidual: unsteady order isnot supported!");
			return;
        }
    }
}

void FlowTime::SmoothResiduals()
{   
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
	{
        const int &elementID = mesh->GetElementIDInDomain(index);

		const Scalar &dt = deltaT->GetValue(elementID);
		residualMass->MultiplyValue(elementID, dt);
		residualMomentum->MultiplyValue(elementID, dt);
		residualEnergy->MultiplyValue(elementID, dt);
    }

    ElementField<Scalar> *scalarFieldOld = &flowPackage.GetTempElementField("scalarFieldOld", Scalar0);
    ElementField<Scalar> *scalarFieldNew = &flowPackage.GetTempElementField("scalarFieldNew", Scalar0);
    ElementField<Vector> *vectorFieldOld = &flowPackage.GetTempElementField("vectorFieldOld", Vector0);
    ElementField<Vector> *vectorFieldNew = &flowPackage.GetTempElementField("vectorFieldNew", Vector0);
    smoother->Smooth(*residualMass, scalarFieldOld, scalarFieldNew, 2);
	smoother->Smooth(*residualMomentum, vectorFieldOld, vectorFieldNew, 2);
	smoother->Smooth(*residualEnergy, scalarFieldOld, scalarFieldNew, 2);

    for (int index = 0; index < elementNumber; ++index)
	{
        const int &elementID = mesh->GetElementIDInDomain(index);

		const Scalar temp = 1.0 / deltaT->GetValue(elementID);
		residualMass->MultiplyValue(elementID, temp);
		residualMomentum->MultiplyValue(elementID, temp);
		residualEnergy->MultiplyValue(elementID, temp);
    }

    boundaryConditionNS.UpdateBoundaryResidual();

	// 湍流量光顺
    for (int k = 0; k < nTurbulence; k++)
    {
		// omega不光顺
        if (turbulenceResidualMacro[k] == FlowMacro::Scalar::RESIDUAL_OMEGA) continue;

        if (turbulenceResidualMacro[k] == FlowMacro::Scalar::RESIDUAL_K)//湍动能残值特殊处理
        {
            const int elementNumber = mesh->GetElementNumberInDomain();
            for (int index = 0; index < elementNumber; ++index)
            {
                const int &elementID = mesh->GetElementIDInDomain(index);

                const Scalar &rhoTemp = rho->GetValue(elementID);
                const Scalar &kTemp = turbulence[k]->GetValue(elementID);
                const Scalar &kRes = residualTurbulence[k]->GetValue(elementID);
                const Scalar factor = 1.0 / (1.0 - Min(-kRes / (rhoTemp * kTemp + SMALL), 0.0));
                residualTurbulence[k]->MultiplyValue(elementID, factor);
            }
        }

        const int elementNumber = mesh->GetElementNumberInDomain();
        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            residualTurbulence[k]->MultiplyValue(elementID, deltaT->GetValue(elementID));
        }

        smoother->Smooth(*residualTurbulence[k], scalarFieldOld, scalarFieldNew, 2);

        for (int index = 0; index < elementNumber; ++index)
        {
            const int &elementID = mesh->GetElementIDInDomain(index);
            const Scalar temp = 1.0 / deltaT->GetValue(elementID);
            residualTurbulence[k]->MultiplyValue(elementID, temp);
        }

        turbulenceNS->UpdateBoundaryResidual();
    }

    flowPackage.FreeTempField(*scalarFieldOld);
    flowPackage.FreeTempField(*scalarFieldNew);
    flowPackage.FreeTempField(*vectorFieldOld);
    flowPackage.FreeTempField(*vectorFieldNew);
}

void FlowTime::UpdateBoundaryResidual()
{
	boundaryConditionNS.UpdateBoundaryResidual();
	if (turbulenceNS != nullptr) turbulenceNS->UpdateBoundaryResidual();
}

void FlowTime::InitializeBoundaryCondition()
{
	boundaryConditionNS.Initialize();
	if (turbulenceNS != nullptr) turbulenceNS->InitializeBoundary();
}

void FlowTime::UpdateBoundaryCondition()
{
	boundaryConditionNS.UpdateBoundaryCondition();
	if (turbulenceNS != nullptr) turbulenceNS->UpdateBoundaryCondition();
}

void FlowTime::UpdateGradientAndMuT()
{
    // 计算体心场梯度
    // fluxNS.CalculateGradient();
    // if (turbulenceNS != nullptr) turbulenceNS->CalculateGradient();
    const auto gradients = flowPackage.GetGradientField();
    if (gradients.gradientRho != nullptr) gradient->Calculate(*rho, *gradients.gradientRho);
    if (gradients.gradientP != nullptr) gradient->Calculate(*p, *gradients.gradientP);
    if (gradients.gradientU != nullptr) gradient->Calculate(*U, *gradients.gradientU);
    if (gradients.gradientT != nullptr) gradient->Calculate(*T, *gradients.gradientT);
    for (int k = 0; k < nTurbulence; k++)
    {
        if (gradients.gradientTurbulence[k] != nullptr)
            gradient->Calculate(*turbulence[k], *gradients.gradientTurbulence[k]);
    }
#if defined(_EnableMultiSpecies_)
    for (int k = 0; k < speciesSize; k++)
    {
        if (gradients.gradientMassFraction[k] != nullptr)
            gradient->Calculate(*massFraction[k], *gradients.gradientMassFraction[k]);
    }
#endif
    
    // 计算粘性系数
    if (turbulenceNS != nullptr) turbulenceNS->CalculateMuTurbulent();
}

void FlowTime::SetFineGridLevel(const int &fineMeshLevel_)
{
    this->fineMeshLevel = fineMeshLevel_;
}

void FlowTime::CalculatePrecondition()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);

        Scalar massTemp = residualMass->GetValue(elementID);
        Vector momentumTemp = residualMomentum->GetValue(elementID);
        Scalar enegryTemp = residualEnergy->GetValue(elementID);

		precondition->CalculateNewFlux(elementID, massTemp, momentumTemp, enegryTemp);

        residualMass->SetValue(elementID, massTemp);
        residualMomentum->SetValue(elementID, momentumTemp);
        residualEnergy->SetValue(elementID, enegryTemp);
    }
    
    UpdateBoundaryResidual();
}

void FlowTime::CheckAndLimit()
{
    //基本流场变量及湍流量限制
    int elementID = -1;
    elementID = Max(elementID, rho->CheckAndLimit(SMALL, 1000));
    elementID = Max(elementID, p->CheckAndLimit(SMALL, 101325 * 1000));
    elementID = Max(elementID, U->CheckAndLimit(-10000, 10000));
#if defined(_EnableMultiSpecies_)
    for (int k = 0; k < speciesSize; k++) elementID = Max(elementID, massFraction[k]->CheckAndLimit(0, 1));
#endif
	if (turbulenceNS != nullptr) elementID = Max(elementID, turbulenceNS->CheckAndLimit());
    
    if(elementID >= 0) this->CheckAndOutput(elementID);
}

void FlowTime::CheckAndOutput(const int &elementID)
{
	int processorID = flowPackage.GetMeshStruct().processorID;
    
	std::ostringstream s;
	s << " --------- Error: variable is error --------" << std::endl;
	s << "level is " << currentLevel << std::endl;
	s << "processorID = " << processorID << ", elementID = " << elementID << std::endl;
	s << "RHO0 = " << rho0->GetValue(elementID) << ", RHO = " << rho->GetValue(elementID) << std::endl;
	s << "U0 = " << U0->GetValue(elementID) << ", U = " << U->GetValue(elementID) << std::endl;
	s << "P0 = " << p0->GetValue(elementID) << ", P = " << p->GetValue(elementID) << std::endl;
	s << "RESIDUAL_MASS     = " << residualMass->GetValue(elementID) << std::endl;
	s << "RESIDUAL_MOMENTUM = " << residualMomentum->GetValue(elementID) << std::endl;
	s << "RESIDUAL_ENERGY   = " << residualEnergy->GetValue(elementID) << std::endl;

	if (turbulenceNS != nullptr)
	{
		s << std::endl;
		s << "Turbulence information: " << std::endl;
		for (int k = 0; k<nTurbulence; k++)
		{
			const auto &marco = flowPackage.GetTurbulentStatus().variableMacro[k];
			const auto &marco0 = flowPackage.GetTurbulentStatus().variableMacro0[k];
			const auto &variable = flowPackage.GetScalarElementField(marco);
			const auto &variable0 = flowPackage.GetScalarElementField(marco0);
			s << variable0.GetName() << " = " << variable0.GetValue(elementID) << ", "
				<< variable.GetName() << " = " << variable.GetValue(elementID) << std::endl;

			const auto &marcoResidual = flowPackage.GetTurbulentStatus().residualMacro[k];
			const auto &residual = flowPackage.GetScalarElementField(marcoResidual);
			s << residual.GetName() << " = " << residual.GetValue(elementID) << std::endl;
		}
	}
	Print(s.str());

	this->OutputCell(elementID);

    FatalError("");
    return;
}

void FlowTime::OutputCell(const int &elementID)
{
	std::ostringstream s;

    const auto &faceIDSize = mesh->GetElement(elementID).GetFaceSize();
	s << "Position of this element: (X,Y,Z)=  " << mesh->GetElement(elementID).GetCenter();
	s << "Face of this element: number =  " << faceIDSize;
	Print(s.str());

	for (int j = 0; j < faceIDSize; j++)
	{
		const int &faceID = mesh->GetElement(elementID).GetFaceID(j);
		std::string faceType = "inner";
        const int &neighborID = mesh->GetFace(faceID).GetNeighborID();
        if (!mesh->JudgeRealElement(neighborID)) 
		{
			bool flag = false;

            // 查找是否是并行面
            if(mesh->GetElement(neighborID).GetElemType() == Element::ElemType::ghostParallel)
            {
            }
            const auto &vv_ghostElement = mesh->GetGhostElementsParallel();
			for (int i = 0; i < vv_ghostElement.size(); i++)
			{
				for (int j = 0; j < vv_ghostElement[i].size(); j++)
				{
					if (vv_ghostElement[i][j].GetID() == faceID)
					{
						faceType = "parallel (processID = " + ToString( i)
							+ ", index = " + ToString( j) + ")";
						flag = true;
						break;
					}
				}
				if (flag) break;
			}

            // 查找是否是边界面
            if (!flag)
            {
                for (int patchID = 0; patchID < mesh->GetBoundarySize(); patchID++)
                {
	                const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
	                for (int index = 0; index < faceSize; ++index)
	                {
	                	// 得到面相关信息
	                	const int &faceID0 = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                        if (faceID0 == faceID)
                        {
                            const int globalPatchID = mesh->GetBoundaryIDGlobal(patchID);
                            faceType = "bound (globalPatchID = " + ToString( globalPatchID ) + ", localPatchID = " + ToString( patchID ) + ", indx = " + ToString( index) + ")";
                            flag = true;
                            break;
                        }
                    }
                    if (flag) break;
                }
            }
		}

		s.str("");
		s << "\t Face" << j << " : faceID = " << faceID << ", faceType = " << faceType;
		Print(s.str());
	}
}

void FlowTime::MonitorCellValue(const int &elementID)
{
    std::ofstream outFile1, outFile2;
	outFile1.open("resElement1.plt", std::ofstream::app);
    outFile1 << std::setiosflags(std::ios_base::scientific)
			 << std::setw(13) << rho->GetValue(elementID)
             << std::setw(13) << U->GetValue(elementID).X()
             << std::setw(13) << U->GetValue(elementID).Y()
             << std::setw(13) << U->GetValue(elementID).Z()
             << std::setw(13) << p->GetValue(elementID)
             << std::setw(13) << residualMass->GetValue(elementID)
             << std::setw(13) << residualMomentum->GetValue(elementID).X()
             << std::setw(13) << residualMomentum->GetValue(elementID).Y()
             << std::setw(13) << residualMomentum->GetValue(elementID).Z()
             << std::setw(13) << residualEnergy->GetValue(elementID)
             << std::endl;
    outFile1.close();

	outFile2.open("resElement2.plt", std::ofstream::app);
    for (int j=0; j< mesh->GetElement(elementID).GetFaceSize(); j++)
    {
        const int &faceID = mesh->GetElement(elementID).GetFaceID(j);
        Flux::Flow::NSFaceValue faceValue = fluxNS.GetFaceLeftRightValue(faceID);
        outFile2 << std::setiosflags(std::ios_base::scientific)
				 << std::setw(13) << faceValue.rhoLeft
                 << std::setw(13) << faceValue.rhoRight
                 << std::setw(13) << faceValue.ULeft.Mag()
                 << std::setw(13) << faceValue.URight.Mag()
                 << std::setw(13) << faceValue.pLeft
                 << std::setw(13) << faceValue.pRight
				 << ",  ";
    }
    outFile2 << std::endl;
    outFile2.close();
}

}//namespace Flow
}//namespace Time
