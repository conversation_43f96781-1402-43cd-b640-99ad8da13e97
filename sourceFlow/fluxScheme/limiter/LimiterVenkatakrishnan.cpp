﻿#include "sourceFlow/fluxScheme/limiter/LimiterVenkatakrishnan.h"

namespace Flux
{
namespace Flow
{
namespace Limiter
{

LimiterVenkatakrishnan::LimiterVenkatakrishnan(Package::FlowPackage &data)
    :
    Limiter(data)
{
}

Scalar LimiterVenkatakrishnan::CalculateFacePsi(const int &elementID, const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2)
{
    const Scalar r = CalculateR(deltaMax, deltaMin, delta2);
    const Scalar rSqr = r * r;
    return (rSqr + 2 * r) / (rSqr + r + 2);
}

} //namespace Limiter
} //namespace Flow
} //namespace Flux
