﻿#include "sourceFlow/fluxScheme/limiter/Limiter.h"

namespace Flux
{    
namespace Flow
{
namespace Limiter
{

Limiter::Limiter(Package::FlowPackage &data)
    :
    flowPackage(data),
    mesh(data.GetMeshStruct().mesh),
    rho(*data.GetField().density),
    U(*data.GetField().velocity),
    p(*data.GetField().pressure),
    rhoGradient(data.GetGradientField().gradientRho),
    UGradient(data.GetGradientField().gradientU),
    pGradient(data.GetGradientField().gradientP),
    rhoPsi(data.GetMeshStruct().mesh),
    UPsi(data.GetMeshStruct().mesh),
    pPsi(data.GetMeshStruct().mesh),
#if defined(_EnableMultiSpecies_)
	massFraction(data.GetField().massFraction),
	massFractionGradient(data.GetGradientField().gradientMassFraction),
	speciesSize(data.GetMultiSpeciesStatus().speciesSize),
#endif
    limitType(data.GetFlowConfigure().GetFluxScheme(data.GetMeshStruct().level).limiter),
    nodeCenter(data.GetFlowConfigure().GetPreprocess().dualMeshFlag),
	dim2(data.GetMeshStruct().dim2)
{
	rhoPsi.Initialize(1.0);
	UPsi.Initialize(Vector(1.0, 1.0, 1.0));
	pPsi.Initialize(1.0);
#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < speciesSize; k++)
	{
		const std::string &name = flowPackage.GetMultiSpeciesStatus().speciesList[k];
		massFractionPsi.push_back(new ElementField<Scalar>(mesh, 1.0, "YPsi_" + name));
	}
#endif
}

Limiter::~Limiter()
{
#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < speciesSize; k++)
	{
        if (massFractionPsi[k] != nullptr)
        {
		    delete massFractionPsi[k];
            massFractionPsi[k] = nullptr;
        }
	}
#endif
}

void Limiter::CalculatePsi()
{
    if(limitType == Flux::Flow::Limiter::Scheme::NONE_LIMITER) return;   

    // 计算密度限制器，初始值设置为2.0
    rhoPsi.Initialize(2.0);
    this->CalculatePsi(rho, *rhoGradient, rhoPsi);

    // 计算速度限制器，初始值设置为2.0
    UPsi.Initialize(Vector(2.0, 2.0, 2.0));
    this->CalculatePsi(U, *UGradient, UPsi);
    
    // 计算压力限制器，初始值设置为2.0
    pPsi.Initialize(2.0);
    this->CalculatePsi(p, *pGradient, pPsi);

#if defined(_EnableMultiSpecies_)
    // 计算组分限制器，初始值设置为2.0
    for (int k = 0; k < speciesSize; k++)
    {
        massFractionPsi[k]->Initialize(2.0);
        this->CalculatePsi(*massFraction[k], *massFractionGradient[k], *massFractionPsi[k]);
    }
#endif

    return;
}

const Scalar &Limiter::GetRhoPsi(const int &elementID)const
{
    return rhoPsi.GetValue(elementID);
}

const Vector &Limiter::GetUPsi(const int &elementID)const
{
    return UPsi.GetValue(elementID);
}

const Scalar &Limiter::GetPPsi(const int &elementID)const
{
    return pPsi.GetValue(elementID);
}

#if defined(_EnableMultiSpecies_)
const Scalar &Limiter::GetMassFractionPsi(const int &elementID, const int &index)const
{
    return massFractionPsi[index]->GetValue(elementID);
}
#endif

template void Limiter::CalculatePsi(const ElementField<Scalar> &field, const ElementField<Vector> &gradField, ElementField<Scalar> &psiField);
template void Limiter::CalculatePsi(const ElementField<Vector> &field, const ElementField<Tensor> &gradField, ElementField<Vector> &psiField);
template<class Type, class GradType>
void Limiter::CalculatePsi(const ElementField<Type> &field, const ElementField<GradType> &gradField, ElementField<Type> &psiField)
{
    //计算最大最小值
    ElementField<Type> *deltaMax = &flowPackage.GetTempElementField("deltaMax", field.GetZero());
    ElementField<Type> *deltaMin = &flowPackage.GetTempElementField("deltaMin", field.GetZero());
    this->CalculateDeltaMaxAndMin(field, *deltaMax, *deltaMin);
    
    //求单元值
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);

        //遍历所有的周围单元
        const Element &element = mesh->GetElement(elementID);
        for (int j = 0; j < element.GetFaceSize(); j++)
        {
            const int &faceID = element.GetFaceID(j);
            const Vector distance = mesh->GetFace(faceID).GetCenter() - element.GetCenter();
            Type delta2 = CalculateDelta2(distance, field.GetValue(elementID), gradField.GetValue(elementID));
            const Type psiTemp = CalculateFacePsi(elementID, deltaMax->GetValue(elementID), deltaMin->GetValue(elementID), delta2);
            psiField.SetValue(elementID, Min(psiField.GetValue(elementID), psiTemp));
        }
    }

    psiField.SetGhostlValueParallel();
    psiField.SetGhostlValueOverset();
    psiField.SetGhostlValueBoundary();

    flowPackage.FreeTempField(*deltaMax);
    flowPackage.FreeTempField(*deltaMin);

    return;
}

template<>
Scalar Limiter::CalculateDelta2(const Vector &distance, const Scalar &fieldValue, const Vector &gradValue)
{
    Scalar delta2 = Dot(distance, gradValue);
    if (fabs(delta2) / fieldValue < 1.0E-10) return Scalar0;
    return delta2;
}

template<>
Vector Limiter::CalculateDelta2(const Vector &distance, const Vector &fieldValue, const Tensor &gradValue)
{
    return Dot(distance, gradValue);
}

template<class Type>
void Limiter::CalculateDeltaMaxAndMin(const ElementField<Type> &field, ElementField<Type> &maxField, ElementField<Type> &minField)
{
    // 初始化
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        maxField.SetValue(elementID, field.GetValue(elementID));
        minField.SetValue(elementID, field.GetValue(elementID));
    }
    
    // 边界面循环
	const int &boundarySize = mesh->GetBoundarySize();
	for (int patchID = 0; patchID < boundarySize; ++patchID)
	{
		const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
		for (int index = 0; index < faceSize; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            const int &neighID = mesh->GetFace(faceID).GetOwnerID();

            // 计算最大最小差量
            const Type faceValue = 0.5 * (field.GetValue(ownerID) + field.GetValue(neighID));
            maxField.SetValue(ownerID, Max(maxField.GetValue(ownerID), faceValue));
            minField.SetValue(ownerID, Min(minField.GetValue(ownerID), faceValue));
        }
    }
    
	// 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
        const int &neighID = mesh->GetFace(faceID).GetNeighborID();

        // 计算最大最小差量
        maxField.SetValue(ownerID, Max(maxField.GetValue(ownerID), field.GetValue(neighID)));
        minField.SetValue(ownerID, Min(minField.GetValue(ownerID), field.GetValue(neighID)));

        maxField.SetValue(neighID, Max(maxField.GetValue(neighID), field.GetValue(ownerID)));
        minField.SetValue(neighID, Min(minField.GetValue(neighID), field.GetValue(ownerID)));
    }

    //计算UDeltaMax-Ui和UDeltaMin-Ui
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        maxField.AddValue(elementID, -field.GetValue(elementID));
        minField.AddValue(elementID, -field.GetValue(elementID));
    }
    
    return;
}

Scalar Limiter::CalculateR(const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2)
{
    if (fabs(delta2) < 100000 * SMALL)
    {
        return 2.0;
    }
    else if (delta2 > 0)
    {
        return deltaMax / delta2;
    }
    else
    {
        return deltaMin / delta2;
    }
}

Vector Limiter::CalculateFacePsi(const int &elementID, const Vector &deltaMax, const Vector &deltaMin, const Vector &delta2)
{
    Scalar psiX = CalculateFacePsi(elementID, deltaMax.X(), deltaMin.X(), delta2.X());
    Scalar psiY = CalculateFacePsi(elementID, deltaMax.Y(), deltaMin.Y(), delta2.Y());
    Scalar psiZ = Scalar0;
    if(!dim2) psiZ = CalculateFacePsi(elementID, deltaMax.Z(), deltaMin.Z(), delta2.Z());

    return Vector(psiX, psiY, psiZ);
}

} //namespace Limiter
} //namespace Flow
} //namespace Flux