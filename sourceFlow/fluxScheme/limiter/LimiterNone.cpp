﻿#include "sourceFlow/fluxScheme/limiter/LimiterNone.h"

namespace Flux
{
namespace Flow
{
namespace Limiter
{

LimiterNone::LimiterNone(Package::FlowPackage &data)
    :
    Limiter(data)
{    
}

Scalar LimiterNone::CalculateFacePsi(const int &elementID, const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2)
{
    return 1.0;
}

} //namespace Limiter
} //namespace Flow
} //namespace Flux
