﻿#include "sourceFlow/fluxScheme/limiter/LimiterVanLeer.h"

namespace Flux
{
namespace Flow
{
namespace Limiter
{

LimiterVanLeer::LimiterVanLeer(Package::FlowPackage &data)
    :
    Limiter(data)
{
}

Scalar LimiterVanLeer::CalculateFacePsi(const int &elementID, const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2)
{
    const Scalar r = CalculateR(deltaMax, deltaMin, delta2);
    return 2 * r / (r + 1);
}

} //namespace Limiter
} //namespace Flow
} //namespace Flux
