﻿#include "sourceFlow/fluxScheme/limiter/LimiterMinMod.h"

namespace Flux
{
namespace Flow
{
namespace Limiter
{

LimiterMinMod::LimiterMinMod(Package::FlowPackage &data)
    :
    Limiter(data)
{
}

Scalar LimiterMinMod::CalculateFacePsi(const int &elementID, const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2)
{
    return Min(CalculateR(deltaMax, deltaMin, delta2), 1.0);
}

} //namespace Limiter
} //namespace Flow
} //namespace Flux