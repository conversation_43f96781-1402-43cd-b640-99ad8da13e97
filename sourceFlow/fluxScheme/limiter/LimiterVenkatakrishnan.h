﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file LimiterVenkatakrishnan.h
//! <AUTHOR>
//! @brief NS方程通量限制器：Venkatakrishnan
//! @date 2021-07-13
//
//------------------------------修改日志----------------------------------------
// 2021-07-13 乔龙
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_limiter_LimiterVenkatakrishnan_
#define _sourceFlow_fluxScheme_limiter_LimiterVenkatakrishnan_

#include "sourceFlow/fluxScheme/limiter/Limiter.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 通量限制器命名空间
 * 
 */
namespace Limiter
{
/**
 * @brief NS方程通量限制器:Venkatakrishnan
 * 
 */
class  LimiterVenkatakrishnan : public Limiter
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     */
    LimiterVenkatakrishnan(Package::FlowPackage &data);

private:
    /**
     * @brief 计算面psi
     * 
     * @param[in] elementID 单元编号
     * @param[in] deltaMax 该单元物理场最大值与自身差值
     * @param[in] deltaMin 该单元物理场最小值与自身差值
     * @param[in] delta2 梯度与<体心到面心距离>的乘积
     * @return Scalar 
     */
    Scalar CalculateFacePsi(const int &elementID, const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2);
};

} // namespace Limiter
} // namespace Flow
} // namespace Flux

#endif 