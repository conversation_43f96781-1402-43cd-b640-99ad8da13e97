﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Limiter.h
//! <AUTHOR>
//! @brief NS方程通量限制器的基类，用于派生具体限制器类。
//! @date 2021-03-29
//
//------------------------------修改日志----------------------------------------
// 2021-04-29 李艳亮
//    说明：增加基本物理量和湍流量的梯度数据。
//
// 2021-03-29 李艳亮
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_limiter_Limiter_
#define _sourceFlow_fluxScheme_limiter_Limiter_

#include "sourceFlow/fluxScheme/FlowFluxScheme.h"

#include "basic/postTools/Tecplot.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 通量限制器命名空间
 * 
 */
namespace Limiter
{
/**
 * @brief NS方程通量限制器的基类
 * 用于派生具体限制器，不能直接实例化
 * 
 */
class Limiter
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     */
    Limiter(Package::FlowPackage &data);

    /**
     * @brief 析构函数
     * 
     */
    virtual ~Limiter();

    /**
     * @brief 计算psi场（需要显示调用）
     * 
     */
    void CalculatePsi();

    /**
     * @brief 获得单元密度限制器值
     * 
     * @param[in] elementID 单元编号
     * @return Scalar 
     */
    const Scalar &GetRhoPsi(const int &elementID)const;

    /**
     * @brief 获得单元速度限制器值
     * 
     * @param[in] elementID 单元编号
     * @return Vector 
     */
    const Vector &GetUPsi(const int &elementID)const;

    /**
     * @brief 获得单元压力限制器值
     * 
     * @param[in] elementID 单元编号
     * @return Scalar 
     */
    const Scalar &GetPPsi(const int &elementID)const;

#if defined(_EnableMultiSpecies_)
    /**
     * @brief 获得单元组分限制器值
     * 
     * @param[in] elementID 单元编号
     * @param[in] index 组分编号
     * @return Scalar 
     */
	const Scalar &GetMassFractionPsi(const int &elementID, const int &index)const;
#endif

protected:

    /**
     * @brief 计算场的限制器
     * 
     * @param[in] field 物理场
     * @param[in] gradField 梯度场
     * @param[out] psiField 限制器场
     */
    template<class Type, class GradType>
    void CalculatePsi(const ElementField<Type> &field, const ElementField<GradType> &gradField, ElementField<Type> &psiField);

    /**
     * @brief 计算物理场相对应的最大最小差量场
     * 单元的最大差量定义为单元及其周围单元值的最大值与单元值的差量
     * 单元的最小差量定义为单元及其周围单元值的最小值与单元值的差量
     * 
     * @param[in] field 物理场
     * @param[out] maxField 最大差量场
     * @param[out] minField 最小差量场
     */
    template<class Type>
    void CalculateDeltaMaxAndMin(const ElementField<Type> &field, ElementField<Type> &maxField, ElementField<Type> &minField);

    /**
     * @brief 计算物理场体心到面心的变化量
     * 
     * @param[in] distance 体心到面心的距离矢量
     * @param[out] fieldValue 体心物理场值
     * @param[out] gradValue 体心物理场梯度
     */
    template<class Type, class GradType>
    Type CalculateDelta2(const Vector &distance, const Type &fieldValue, const GradType &gradValue);

    /**
     * @brief 计算r值
     * 
     * @param[in] deltaMax 该单元物理场最大值与自身差值
     * @param[in] deltaMin 该单元物理场最小值与自身差值
     * @param[in] delta2 梯度与<体心到面心距离>的乘积
     * @return Scalar 
     */
    Scalar CalculateR(const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2);
    
    /**
     * @brief 计算面psi
     * 纯虚函数，在派生具体类中实现
     * 
     * @param[in] elementID 单元编号
     * @param[in] deltaMax 该单元物理场最大值与自身差值
     * @param[in] deltaMin 该单元物理场最小值与自身差值
     * @param[in] delta2 梯度与<体心到面心距离>的乘积
     * @return Scalar 
     */
    virtual Scalar CalculateFacePsi(const int &elementID, const Scalar &deltaMax, const Scalar &deltaMin, const Scalar &delta2) = 0;

    /**
     * @brief 计算面psi
     * 
     * @param[in] elementID 单元编号
     * @param[in] deltaMax 该单元物理场最大值与自身差值
     * @param[in] deltaMin 该单元物理场最小值与自身差值
     * @param[in] delta2 梯度与<体心到面心距离>的乘积
     * @return Vector 
     */
    Vector CalculateFacePsi(const int &elementID, const Vector &deltaMax, const Vector &deltaMin, const Vector &delta2);

protected:
    Package::FlowPackage &flowPackage; ///< 流场包
    Mesh *mesh; ///< 网格指针
    
    ElementField<Scalar> &rho; ///< 密度
    ElementField<Vector> &U; ///< 速度
    ElementField<Scalar> &p; ///< 压强

    ElementField<Vector> *rhoGradient; ///< 密度梯度
    ElementField<Tensor> *UGradient; ///< 速度梯度
    ElementField<Vector> *pGradient; ///< 压强梯度
    
#if defined(_EnableMultiSpecies_)
	int speciesSize; ///< 组分数量
	std::vector<ElementField<Scalar> *> massFraction; ///< 组分场
	const std::vector<ElementField<Vector> *> massFractionGradient; ///< 组分梯度
#endif

    const bool &dim2; ///< 是否是二维问题，true为二维
    
private:
    ElementField<Scalar> rhoPsi; ///< 密度限制器
    ElementField<Vector> UPsi; ///< 速度限制器
    ElementField<Scalar> pPsi; ///< 压强限制器
	std::vector<ElementField<Scalar> *> massFractionPsi; ///< 组分限制器

    const Flux::Flow::Limiter::Scheme limitType; ///< 限制器类型
    const bool nodeCenter; ///< 格点标识
};

} // namespace Limiter
} // namespace Flow
} // namespace Flux

#endif