﻿#include "sourceFlow/fluxScheme/sourceFluxScheme/SourceFluxScheme.h"

namespace Flux
{
namespace Flow
{
namespace Source
{

SourceFluxScheme::SourceFluxScheme(Package::FlowPackage &data)
    :
    FlowFluxScheme(data)
{
}

SourceFluxScheme::~SourceFluxScheme()
{
}

void SourceFluxScheme::AddResidual()
{
#if defined(_EnableMultiSpecies_)
	const Scalar reactionSize = flowPackage.GetMultiSpeciesStatus().reactionSize;
	std::vector<Scalar> rateOfProgress(reactionSize);

	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);
		const Scalar &volume = mesh->GetElement(elementID).GetVolume();

		const Scalar &rhoTemp = rho.GetValue(elementID);
		const Scalar &TTemp = T.GetValue(elementID);
		const Scalar &pTemp = p.GetValue(elementID);

		std::vector<Scalar> massFractionTemp(speciesSize);
		for (int k = 0; k < speciesSize; k++)
		{
			massFractionTemp[k] = massFraction[k]->GetValue(elementID);
		}

		for (int k = 0; k < reactionSize; k++)
		{
			rateOfProgress[k] = material.GetRateOfProgress(TTemp, rhoTemp, massFractionTemp, k);
		}

		for (int k = 0; k < speciesSize; k++)
		{
			const Scalar &productionRate = material.GetProductionRate(TTemp, rhoTemp, massFractionTemp, rateOfProgress, k);
			residualMassFraction[k]->AddValue(elementID, -productionRate * volume);
		}
	}
#endif
}

}//namespace Source
}//namespace Flow
}//namespace Flux
