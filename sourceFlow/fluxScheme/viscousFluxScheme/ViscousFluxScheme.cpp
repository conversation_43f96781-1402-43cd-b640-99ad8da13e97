﻿#include "sourceFlow/fluxScheme/viscousFluxScheme/ViscousFluxScheme.h"

namespace Flux
{
namespace Flow
{
namespace Viscous
{

ViscousFluxScheme::ViscousFluxScheme(Package::FlowPackage &data)
    :
    FlowFluxScheme(data),
    gradientU(data.GetGradientField().gradientU),
    gradientT(data.GetGradientField().gradientT),
#if defined(_EnableMultiSpecies_)
    gradientMassFraction(data.GetGradientField().gradientMassFraction),
#endif
    lambdaViscous(data.GetField().lambdaViscous),
    distance(data.GetExtraInfo().distance),
    distanceNormal(data.GetExtraInfo().distanceNormal)
{
    const int &level = data.GetMeshStruct().level;
    viscousScheme = data.GetFlowConfigure().GetFluxScheme(level).viscous;

    Jacobian_i_temp.Resize(5, 5);
    Jacobian_j_temp.Resize(5, 5);
    nVarSqr3D = 25;

    if (viscousScheme == Flux::Flow::Viscous::CENTRAL_DISTANCE)
        CalculateFaceFluxViscous = &ViscousFluxScheme::CalculateInnerFaceFluxDistance;
    else
        CalculateFaceFluxViscous = &ViscousFluxScheme::CalculateInnerFaceFluxFull;
}

void ViscousFluxScheme::AddResidual()
{
	(this->*CalculateFaceFluxViscous)();
    return;
}

void ViscousFluxScheme::CalculateInnerFaceFluxDistance()
{
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Vector &faceNormal = face.GetNormal();
        const Scalar metric = face.GetArea() / distance[faceID];

        // 计算面心值
        const Scalar muFace = 0.5 * (flowPackage.CalculateMu(ownerID) + flowPackage.CalculateMu(neighID));
        const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(ownerID) + flowPackage.CalculateKappa(neighID));
        const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));

        // 计算差量
        const Vector dU = U.GetValue(neighID) - U.GetValue(ownerID);
        const Scalar dT = T.GetValue(neighID) - T.GetValue(ownerID);
        const Scalar UNorm = (dU & faceNormal) / 3.0;

        // 计算面通量
        faceFlux.massFlux = 0.0;
        faceFlux.momentumFlux = muFace * (dU + UNorm * faceNormal) * metric;
        faceFlux.energyFlux = (faceFlux.momentumFlux & UFace) + kappaFace * dT * metric;

#if defined(_EnableMultiSpecies_)
		const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
		const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
		const Scalar TFace = 0.5 * (T.GetValue(ownerID) + T.GetValue(neighID));
		std::vector<Scalar> YFace(speciesSize);
		for (int k = 0; k < speciesSize; k++)
			YFace[k] = 0.5 * (massFraction[k]->GetValue(ownerID) + massFraction[k]->GetValue(neighID));

		for (int m = 0; m < speciesSize; ++m)
		{
			const Scalar diffCoeff = material.Diffusion(TFace, pFace, YFace, m);
			const Scalar deltaPhi = massFraction[m]->GetValue(neighID) - massFraction[m]->GetValue(ownerID);
			faceFlux.massFractionFlux[m] = rhoFace * diffCoeff * metric * deltaPhi;

			residualMassFraction[m]->AddValue(ownerID, -faceFlux.massFractionFlux[m]);
			residualMassFraction[m]->AddValue(neighID,  faceFlux.massFractionFlux[m]);

			const Scalar &enthalpySpecies = material.EnthalpyPureSpecies(TFace, m);
			faceFlux.energyFlux += enthalpySpecies * faceFlux.massFractionFlux[m];
		}
#endif

        if (updateJacobian) CalculateViscousJacobian(faceID, muFace, kappaFace, UFace, faceFlux);

        // 将面通量累加到残值中
        residualMass.AddValue(ownerID, -faceFlux.massFlux);
        residualMomentum.AddValue(ownerID, -faceFlux.momentumFlux);
        residualEnergy.AddValue(ownerID, -faceFlux.energyFlux);
        residualMass.AddValue(neighID, faceFlux.massFlux);
        residualMomentum.AddValue(neighID, faceFlux.momentumFlux);
        residualEnergy.AddValue(neighID, faceFlux.energyFlux);
    }
}

void ViscousFluxScheme::CalculateInnerFaceFluxFull()
{
    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();
        const Vector faceNormal = face.GetArea() * face.GetNormal();

        //计算粘性系数/热传导系数的面心值
        const Scalar muFace = 0.5 * (flowPackage.CalculateMu(ownerID) + flowPackage.CalculateMu(neighID));
        const Scalar kappaFace = 0.5 * (flowPackage.CalculateKappa(ownerID) + flowPackage.CalculateKappa(neighID));
        const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));

        //修正梯度所需的几何量
        const Scalar dMagInv = 1.0 / distance[faceID];
        const Vector &dNorm = distanceNormal[faceID];

        //计算速度、速度梯度、温度梯度的面心值
        Tensor gradUFace = 0.5 * (gradientU->GetValue(ownerID) + gradientU->GetValue(neighID));
        gradUFace += dNorm * ((U.GetValue(neighID) - U.GetValue(ownerID)) * dMagInv - dNorm * gradUFace);

        Vector gradTFace = 0.5 * (gradientT->GetValue(ownerID) + gradientT->GetValue(neighID));
        gradTFace += dNorm * ((T.GetValue(neighID) - T.GetValue(ownerID)) * dMagInv - (dNorm & gradTFace));

        const Tensor tauFace = flowPackage.CalculateTau(muFace, gradUFace);

        //计算面通量
        faceFlux.massFlux = 0.0;
        faceFlux.momentumFlux = faceNormal* tauFace;
        faceFlux.energyFlux = (faceFlux.momentumFlux & UFace) + kappaFace * (faceNormal & gradTFace);

        if (updateJacobian) CalculateViscousJacobian(faceID, muFace, kappaFace, UFace, faceFlux);

        // 将面通量累加到残值中
        residualMass.AddValue(ownerID, -faceFlux.massFlux);
        residualMomentum.AddValue(ownerID, -faceFlux.momentumFlux);
        residualEnergy.AddValue(ownerID, -faceFlux.energyFlux);
        residualMass.AddValue(neighID, faceFlux.massFlux);
        residualMomentum.AddValue(neighID, faceFlux.momentumFlux);
        residualEnergy.AddValue(neighID, faceFlux.energyFlux);
    }
}

void ViscousFluxScheme::CalculateViscousJacobian(const int &faceID, const Scalar &muFace, const Scalar &kappaFace,
                                                 const Vector &UFace, const NSFaceFlux &faceFlux)
{
    const Face &face = mesh->GetFace(faceID);
    const int &ownerID = face.GetOwnerID();
    const int &neighID = face.GetNeighborID();

    if (distance[faceID] == 0.0)
    {
        Jacobian_i_temp.SetZero();
        Jacobian_j_temp.SetZero();
    }
    else
    {
        const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
        const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));

        const Scalar oneThird = 1.0 / 3.0;
        const Scalar rhoInv = 1.0 / rhoFace;

        Jacobian_i_temp(0, 0) = Scalar0;
        Jacobian_i_temp(0, 1) = Scalar0;
        Jacobian_i_temp(0, 2) = Scalar0;
        Jacobian_i_temp(0, 3) = Scalar0;
        Jacobian_i_temp(0, 4) = Scalar0;

        const Scalar metric = face.GetArea() / distance[faceID];
        const Scalar coef = metric * muFace * rhoInv;
        const Tensor temp0 = -coef * (Tensor1 + ((oneThird * face.GetNormal()) * face.GetNormal()));
        const Vector temp1 = -(temp0 * UFace);
        Jacobian_i_temp(1, 0) = temp1.X();
        Jacobian_i_temp(1, 1) = temp0.XX();
        Jacobian_i_temp(1, 2) = temp0.XY();
        Jacobian_i_temp(1, 3) = temp0.XZ();
        Jacobian_i_temp(1, 4) = Scalar0;
        Jacobian_i_temp(2, 0) = temp1.Y();
        Jacobian_i_temp(2, 1) = temp0.YX();
        Jacobian_i_temp(2, 2) = temp0.YY();
        Jacobian_i_temp(2, 3) = temp0.YZ();
        Jacobian_i_temp(2, 4) = Scalar0;
        Jacobian_i_temp(3, 0) = temp1.Z();
        Jacobian_i_temp(3, 1) = temp0.ZX();
        Jacobian_i_temp(3, 2) = temp0.ZY();
        Jacobian_i_temp(3, 3) = temp0.ZZ();
        Jacobian_i_temp(3, 4) = Scalar0;

        const Scalar &R = flowPackage.GetMaterialNumber().R;
        const Scalar &gamma1 = flowPackage.GetMaterialNumber().gamma1;
        const Scalar phi = metric * kappaFace * gamma1 / (R * rhoFace);
        const Scalar temp2 = phi * (pFace / (rhoFace * gamma1) - 0.5 * (UFace & UFace)) + (temp1 & UFace);
        const Vector temp3 = phi * UFace - temp1;
        Jacobian_i_temp(4, 0) = temp2;
        Jacobian_i_temp(4, 1) = temp3.X();
        Jacobian_i_temp(4, 2) = temp3.Y();
        Jacobian_i_temp(4, 3) = temp3.Z();
        Jacobian_i_temp(4, 4) = -phi;

        for (unsigned short iVar = 0; iVar < nVarSqr3D; iVar++)
            Jacobian_j_temp(iVar) = -Jacobian_i_temp(iVar);

        const Scalar factor = 0.5 * rhoInv;
        const Scalar temp4 = factor * (faceFlux.momentumFlux & UFace);
        const Vector temp5 = factor * faceFlux.momentumFlux;
        Jacobian_i_temp(4, 0) -= temp4;
        Jacobian_j_temp(4, 0) -= temp4;
        Jacobian_i_temp(4, 1) += temp5.X();
        Jacobian_j_temp(4, 1) += temp5.X();
        Jacobian_i_temp(4, 2) += temp5.Y();
        Jacobian_j_temp(4, 2) += temp5.Y();
        Jacobian_i_temp(4, 3) += temp5.Z();
        Jacobian_j_temp(4, 3) += temp5.Z();
    }

    // 更新粘性通量Jacobian，粘性通量为负，Jacobian也为负
    if (nDim == 3)
    {
        // 三维Jacobian矩阵维度一致，直接取负号
        for (unsigned short iVar = 0; iVar < nVarSqr3D; iVar++)
        {
            Jacobian_i(iVar) = -Jacobian_i_temp(iVar);
            Jacobian_j(iVar) = -Jacobian_j_temp(iVar);
        }
    }
    else
    {
        // 三维Jacobian矩阵维度不一致，需跳过Z分量
        const int lastIndex = nDim + 1;
        for (unsigned short iVar = 0; iVar < lastIndex; iVar++)
        {
            for (unsigned short jVar = 0; jVar < lastIndex; jVar++)
            {
                Jacobian_i(iVar, jVar) = -Jacobian_i_temp(iVar, jVar);
                Jacobian_j(iVar, jVar) = -Jacobian_j_temp(iVar, jVar);
            }
            Jacobian_i(lastIndex, iVar) = -Jacobian_i_temp(lastIndex + 1, iVar);
            Jacobian_j(lastIndex, iVar) = -Jacobian_j_temp(lastIndex + 1, iVar);
            Jacobian_i(iVar, lastIndex) = -Jacobian_i_temp(iVar, lastIndex + 1);
            Jacobian_j(iVar, lastIndex) = -Jacobian_j_temp(iVar, lastIndex + 1);
        }
        Jacobian_i(lastIndex, lastIndex) = -Jacobian_i_temp(lastIndex + 1, lastIndex + 1);
        Jacobian_j(lastIndex, lastIndex) = -Jacobian_j_temp(lastIndex + 1, lastIndex + 1);
    }

	if(mesh->JudgeBoundaryFace(faceID))
	{
		jacobian->AddBlock2Diag(ownerID, Jacobian_i);
	}
	else
	{
		jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
	}
}

void ViscousFluxScheme::CalculateSpectralRadius()
{
    ElementField<Scalar> &SP = flowPackage.GetTempElementField("SP", SMALL);
    ElementField<Scalar> &distanceMin = flowPackage.GetTempElementField("distanceMin", INF);
    ElementField<Scalar> &muMax = flowPackage.GetTempElementField("muMax", Scalar0);
    ElementField<Scalar> &mu = flowPackage.GetTempElementField("mu", Scalar0);
    for (int elementID = 0; elementID < mesh->GetElementNumberAll(); elementID++)
        mu.SetValue(elementID, flowPackage.CalculateMu(elementID));

    // 边界面循环
    const int &boundarySize = mesh->GetBoundarySize();
    for (int patchID = 0; patchID < boundarySize; ++patchID)
    {
        const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
        for (int index = 0; index < faceSize; ++index)
        {
            // 得到面相关信息
            const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const Face &face = mesh->GetFace(faceID);
            const int &ownerID = face.GetOwnerID();
            SP.AddValue(ownerID, face.GetArea());
        }
    }

    // 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        // 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
        const Face &face = mesh->GetFace(faceID);
        const int &ownerID = face.GetOwnerID();
        const int &neighID = face.GetNeighborID();

        SP.AddValue(ownerID, face.GetArea());
        SP.AddValue(neighID, face.GetArea());

        const Scalar mu12 = Max(mu.GetValue(ownerID), mu.GetValue(neighID));
        muMax.SetValue(ownerID, Max(muMax.GetValue(ownerID), mu12));
        muMax.SetValue(neighID, Max(muMax.GetValue(neighID), mu12));

        distanceMin.SetValue(ownerID, Min(distanceMin.GetValue(ownerID), distance[faceID]));
        distanceMin.SetValue(neighID, Min(distanceMin.GetValue(neighID), distance[faceID]));
    }

    // 粘性谱半径场值的初始化
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        const Scalar &rhoTemp = rho.GetValue(elementID);
        const Scalar &volmeTemp = mesh->GetElement(elementID).GetVolume();
        const Scalar temp0 = Min(volmeTemp / SP.GetValue(elementID), 0.5 * distanceMin.GetValue(elementID));
        const Scalar temp = 4.0 * muMax.GetValue(elementID) * SP.GetValue(elementID) / (rhoTemp * temp0);
        lambdaViscous->SetValue(elementID, temp);
    }

    lambdaViscous->SetGhostlValueParallel();
    lambdaViscous->SetGhostlValueOverset();
    lambdaViscous->SetGhostlValueBoundary();

    flowPackage.FreeTempField(SP);
    flowPackage.FreeTempField(distanceMin);
    flowPackage.FreeTempField(muMax);
    flowPackage.FreeTempField(mu);

    return;
}

}//namespace Viscous
}//namespace Flow
}//namespace Flux