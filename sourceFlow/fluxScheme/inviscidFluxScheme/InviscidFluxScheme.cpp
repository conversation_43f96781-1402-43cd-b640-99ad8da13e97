﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/InviscidFluxScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

InviscidFluxScheme::InviscidFluxScheme(Package::FlowPackage &data,
                                       Limiter::Limiter *limiter_,
                                       Flux::Flow::Precondition::Precondition *precondition_)
	:
	FlowFluxScheme(data),
	limiter(limiter_), precondition(precondition_),
	lambdaConvective(*data.GetField().lambdaConvective),
	rhoGradient(data.GetGradientField().gradientRho),
	UGradient(data.GetGradientField().gradientU),
	pGradient(data.GetGradientField().gradientP),
#if defined(_EnableMultiSpecies_)
	massFractionGradient(data.GetGradientField().gradientMassFraction),
#endif
	gamma(data.GetMaterialNumber().gamma),
	gamma1(data.GetMaterialNumber().gamma1),
	gamma1Inv(1.0/data.GetMaterialNumber().gamma1),
	Cp(data.GetMaterialNumber().Cp),
	entropyFixFlag(data.GetFlowConfigure().GetAcceleration().entropyFixFlag)
{
	// 计算面谱半径的函数指针
	CalculateSpectralRadiusAverage = &InviscidFluxScheme::CalculateSpectralRadiusAverageNonPre;
	if (precondition != nullptr) CalculateSpectralRadiusAverage = &InviscidFluxScheme::CalculateSpectralRadiusAveragePre;

	GetFaceValue = &InviscidFluxScheme::GetFaceValueFirstOrder;
	if (limiter != nullptr) GetFaceValue = &InviscidFluxScheme::GetFaceValueSecondOrder;
}

InviscidFluxScheme::~InviscidFluxScheme()
{
}

void InviscidFluxScheme::CalculateSpectralRadius()
{
    // 当地时间步长体场值的初始化，所有体场值置零
	lambdaConvective.Initialize(Scalar0);

	// 内部面循环
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
	    const Face &face = mesh->GetFace(faceID);
	    const int &ownerID = face.GetOwnerID();
	    const int &neighID = face.GetNeighborID();
		const Scalar temp = (this->*CalculateSpectralRadiusAverage)(faceID);
		lambdaConvective.AddValue(ownerID, temp);
		lambdaConvective.AddValue(neighID, temp);
	}

    if (!nodeCenter)
    {
        // 边界面循环
        const int &boundarySize = mesh->GetBoundarySize();
        for (int patchID = 0; patchID < boundarySize; ++patchID)
        {
            const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
            for (int index = 0; index < faceSize; ++index)
            {
                const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
                const Face &face = mesh->GetFace(faceID);
                const int &ownerID = face.GetOwnerID();
                const Scalar temp = (this->*CalculateSpectralRadiusAverage)(faceID);
                lambdaConvective.AddValue(ownerID, temp);
            }
        }
    }

    lambdaConvective.SetGhostlValueParallel();
	lambdaConvective.SetGhostlValueOverset();
    lambdaConvective.SetGhostlValueBoundary();

	return;
}

NSFaceValue InviscidFluxScheme::GetFaceLeftRightValue(const int &faceID)
{
	(this->*GetFaceValue)(faceID, faceValue);
	return faceValue;
}

void InviscidFluxScheme::GetFaceValueFirstOrder(const int &faceID, NSFaceValue &faceValue)
{
    // 得到面相关信息
	const Face &face = mesh->GetFace(faceID);
	const int &ownerID = face.GetOwnerID();
	const int &neighID = face.GetNeighborID();

    //获取左右面心值
	faceValue.rhoLeft = rho.GetValue(ownerID);
	faceValue.ULeft = U.GetValue(ownerID);
	faceValue.pLeft = p.GetValue(ownerID);
	faceValue.rhoRight = rho.GetValue(neighID);
	faceValue.URight = U.GetValue(neighID);
	faceValue.pRight = p.GetValue(neighID);

    return;
}

void InviscidFluxScheme::GetFaceValueSecondOrder(const int &faceID, NSFaceValue &faceValue)
{
    // 得到面相关信息
	const Face &face = mesh->GetFace(faceID);
	const int &ownerID = face.GetOwnerID();
	const int &neighID = face.GetNeighborID();

    //获取左右面心值
	faceValue.rhoLeft = rho.GetValue(ownerID);
	faceValue.ULeft = U.GetValue(ownerID);
	faceValue.pLeft = p.GetValue(ownerID);
	faceValue.rhoRight = rho.GetValue(neighID);
	faceValue.URight = U.GetValue(neighID);
	faceValue.pRight = p.GetValue(neighID);

    //采用限制器重构左右面值
    // 得到面相关信息
    const Vector distanceLeft = mesh->GetFace(faceID).GetCenter() - mesh->GetElement(ownerID).GetCenter();
    const Vector distanceRight = mesh->GetFace(faceID).GetCenter() - mesh->GetElement(neighID).GetCenter();

    //密度,速度,压强的左右面心值
    const Scalar rhoLeftTemp = faceValue.rhoLeft + limiter->GetRhoPsi(ownerID) * (rhoGradient->GetValue(ownerID) & distanceLeft);
    const Scalar rhoRightTemp = faceValue.rhoRight + limiter->GetRhoPsi(neighID) * (rhoGradient->GetValue(neighID) & distanceRight);
    const Vector ULeftTemp = faceValue.ULeft + limiter->GetUPsi(ownerID).Multiply(distanceLeft * UGradient->GetValue(ownerID));
    const Vector URightTemp = faceValue.URight + limiter->GetUPsi(neighID).Multiply(distanceRight * UGradient->GetValue(neighID));
    const Scalar pLeftTemp = faceValue.pLeft + limiter->GetPPsi(ownerID) * (pGradient->GetValue(ownerID) & distanceLeft);
    const Scalar pRightTemp = faceValue.pRight + limiter->GetPPsi(neighID) * (pGradient->GetValue(neighID) & distanceRight);

#if defined(_EnableMultiSpecies_)
	for (int k = 0; k < speciesSize; k++)
	{
		faceValue.massFractionLeft[k] = massFraction[k]->GetValue(ownerID)  + limiter->GetMassFractionPsi(ownerID, k) * (massFractionGradient[k]->GetValue(ownerID) & distanceLeft);
		faceValue.massFractionRight[k] = massFraction[k]->GetValue(neighID) + limiter->GetMassFractionPsi(neighID, k) * (massFractionGradient[k]->GetValue(neighID) & distanceRight);
	}
#endif

    if (rhoLeftTemp > 0 && pLeftTemp > 0)
    {
        faceValue.rhoLeft = rhoLeftTemp;
        faceValue.ULeft = ULeftTemp;
        faceValue.pLeft = pLeftTemp;
    }

    if (rhoRightTemp > 0 && pRightTemp > 0)
    {
        faceValue.rhoRight = rhoRightTemp;
        faceValue.URight = URightTemp;
        faceValue.pRight = pRightTemp;
    }

    return;
}

const Scalar InviscidFluxScheme::CalculateSpectralRadiusAverageNonPre(const int &faceID)
{
	const Face &face = mesh->GetFace(faceID);
	const int &ownerID = face.GetOwnerID();
	const int &neighID = face.GetNeighborID();

	// 面单元的音速和速度矢量是通过变量的几何平均得到
    const Scalar aMeanFace = 0.5 * (A.GetValue(ownerID) + A.GetValue(neighID));
	const Vector UmeanFace = U.GetValue(ownerID) + U.GetValue(neighID);
    const Scalar UNormal = 0.5 * fabs(UmeanFace & face.GetNormal()); //0.5在这里乘
	
	// 主流对流谱半径计算  lambdaConvective = |U & S| + a * |S|
	return (UNormal + aMeanFace) * face.GetArea();
}

const Scalar InviscidFluxScheme::CalculateSpectralRadiusAveragePre(const int &faceID)
{
	const Face &face = mesh->GetFace(faceID);
	const int &ownerID = face.GetOwnerID();
	const int &neighID = face.GetNeighborID();

	// 面单元的音速和速度矢量是通过变量的几何平均得到
    Scalar aMeanFace = 0.5 * (A.GetValue(ownerID) + A.GetValue(neighID));
	const Vector UmeanFace = U.GetValue(ownerID) + U.GetValue(neighID);
    Scalar UNormal = 0.5 * fabs(UmeanFace & face.GetNormal()); //0.5在这里乘

	// 预处理修正
	const Scalar &betaOwner = precondition->GetBeta(ownerID);
	const Scalar &betaNeighbor = precondition->GetBeta(neighID);
	const Scalar betaFace = 0.5 * (betaOwner + betaNeighbor);
	const Scalar c2 = aMeanFace * aMeanFace;
	const Scalar theta = 1.0 / (betaFace * c2);
	const Scalar a4 = 1.0 / (theta * c2 - gamma1);
	const Scalar a5 = a4 * c2;
	const Scalar temp1 = UNormal * (a4 - 1.0);
	aMeanFace = 0.5 * sqrt(temp1 * temp1 + 4.0 * a5);
	UNormal *= (0.5 * (a4 + 1));
    
	// 主流对流谱半径计算  lambdaConvective = |U & S| + a * |S|
	return (UNormal + aMeanFace) * face.GetArea();
}

void InviscidFluxScheme::GetInviscidProjJac(const Vector &U, const Scalar &E, const Vector &normal, const Scalar &scale, Matrix &jacobian0)
{
    const int nDim = mesh->GetMeshDimension();
    
    const Scalar UNorm = U & normal;
    const Scalar phi = 0.5 * gamma1 * (U & U);
    const Scalar a1 = gamma * E - phi;
    const Scalar a2 = gamma - 1.0;
    const int lastIndex = nDim + 1;

    const Vector temp0 = scale * (normal * phi - U * UNorm);
    const Tensor temp1 = scale * (U * normal - a2 * normal * U);
    const Scalar temp2 = scale * UNorm;
    const Vector temp3 = scale * a2 * normal;
    const Vector temp4 = scale * (normal * a1 - a2 * U * UNorm);
    
    /*
     dF / dW = | dMass / dRho, dMass / dRhoU, dMass / drhoE |
               | dMomX / dRho, dMomX / dRhoU, dMomX / drhoE |
               | dMomY / dRho, dMomY / dRhoU, dMomY / drhoE |
               | dMomZ / dRho, dMomZ / dRhoU, dMomZ / drhoE |
               | dEneg / dRho, dEneg / dRhoU, dEneg / drhoE |
    */
   
    if (nDim == 3)
    {
        // 质量通量对守恒量的导数
        jacobian0(0, 0) = 0.0;
        jacobian0(0, 1) = scale * normal.X();
        jacobian0(0, 2) = scale * normal.Y();
        jacobian0(0, 3) = scale * normal.Z();
        jacobian0(0, lastIndex) = 0.0;

        // 动量方程对守恒量的导数
        jacobian0(1, 0) = temp0.X();
        jacobian0(2, 0) = temp0.Y();
        jacobian0(3, 0) = temp0.Z();

        jacobian0(1, 1) = temp1.XX(); jacobian0(1, 2) = temp1.XY();  jacobian0(1, 3) = temp1.XZ();
        jacobian0(2, 1) = temp1.YX(); jacobian0(2, 2) = temp1.YY();  jacobian0(2, 3) = temp1.YZ();
        jacobian0(3, 1) = temp1.ZX(); jacobian0(3, 2) = temp1.ZY();  jacobian0(3, 3) = temp1.ZZ();

        jacobian0(1, 1) += temp2;
        jacobian0(2, 2) += temp2;
        jacobian0(3, 3) += temp2;

        jacobian0(1, lastIndex) = temp3.X();
        jacobian0(2, lastIndex) = temp3.Y();
        jacobian0(3, lastIndex) = temp3.Z();

        // 能量通量对守恒量的导数
        jacobian0(lastIndex, 0) = scale * UNorm * (phi - a1);
        jacobian0(lastIndex, 1) = temp4.X();
        jacobian0(lastIndex, 2) = temp4.Y();
        jacobian0(lastIndex, 3) = temp4.Z();
        jacobian0(lastIndex, lastIndex) = scale * gamma * UNorm;
    }
    else
    {
        jacobian0(0, 0) = 0.0;
        jacobian0(0, 1) = scale * normal.X();
        jacobian0(0, 2) = scale * normal.Y();
        jacobian0(0, lastIndex) = 0.0;

        jacobian0(1, 0) = temp0.X();
        jacobian0(2, 0) = temp0.Y();
        
        jacobian0(1, 1) = temp1.XX(); jacobian0(1, 2) = temp1.XY();
        jacobian0(2, 1) = temp1.YX(); jacobian0(2, 2) = temp1.YY();

        jacobian0(1, 1) += temp2;
        jacobian0(2, 2) += temp2;

        jacobian0(1, lastIndex) = temp3.X();
        jacobian0(2, lastIndex) = temp3.Y();

        jacobian0(lastIndex, 0) = scale * UNorm * (phi - a1);
        jacobian0(lastIndex, 1) = temp4.X();
        jacobian0(lastIndex, 2) = temp4.Y();
        jacobian0(lastIndex, lastIndex) = scale * gamma * UNorm;
    }
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux