﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/VanLeerScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

VanLeerScheme::VanLeerScheme(Package::FlowPackage &data,
                             Limiter::Limiter *limiter,
                             Flux::Flow::Precondition::Precondition *precondition)
    :
    UpwindScheme(data, limiter, precondition)
{    
}

NSFaceFlux VanLeerScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    // 获取左右面值
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;
    
    // 得到面法向和面积大小
    const Face &face = mesh->GetFace(faceID);
    const Vector &faceNormal = face.GetNormal();
    const Scalar &faceArea = face.GetArea();    

    // 计算左右面的温度、焓和声速
    const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
    const Scalar soundLeft = material.GetSoundSpeed(TLeft);
    const Scalar enthalpyLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);

    const Scalar TRight = material.GetTemperature(pRight, rhoRight);
    const Scalar soundRight = material.GetSoundSpeed(TRight);
    const Scalar enthalpyRight = Cp * TRight + 0.5 * (URight & URight);

    // 计算左右面声速值
    const Scalar velocityNormLeft = ULeft & faceNormal;
    const Scalar velocityNormRight = URight & faceNormal;

    // 计算左右面马赫数
    const Scalar machLeft = velocityNormLeft / soundLeft;
    const Scalar machRight = velocityNormRight / soundRight;

    // 计算Ma+和p+
    Scalar machLeftPositive = 0.0;
    Scalar pLeftPositive = 0.0;
    if (machLeft >= 1)
    {
        machLeftPositive = machLeft;
        pLeftPositive = 1.0;
    }
    else if (machLeft > -1)
    {
        machLeftPositive = 0.25 * (machLeft + 1.0) * (machLeft + 1.0);
        pLeftPositive = machLeftPositive * (2.0 - machLeft);
    }

    // 计算Ma-和p-
    Scalar machRightNegative = 0.0;
    Scalar pRightNegative = 0.0;
    if (machRight <= -1)
    {
        machRightNegative = machRight;
        pRightNegative = 1.0;
    }
    else if (machRight < 1)
    {
        machRightNegative = -0.25 * (machRight - 1) * (machRight - 1);
        pRightNegative = -machRightNegative * (2.0 + machRight);
    }

    // 计算面心压强值
    const Scalar pShared   = pLeftPositive * pLeft + pRightNegative * pRight;

    // 计算面通量
    const Scalar left = rhoLeft * machLeftPositive * soundLeft * faceArea;
    const Scalar right = rhoRight * machRightNegative * soundRight * faceArea;
    faceFlux.massFlux = left + right;
    faceFlux.momentumFlux = left * ULeft + right * URight + pShared * faceNormal * faceArea;
    faceFlux.energyFlux = left * enthalpyLeft + right * enthalpyRight;

    return faceFlux;
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux