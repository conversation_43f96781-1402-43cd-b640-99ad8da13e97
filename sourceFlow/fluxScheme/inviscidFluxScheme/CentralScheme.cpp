﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/CentralScheme.h"

namespace Flux
{	
namespace Flow
{
namespace Inviscid
{

CentralScheme::CentralScheme(Package::FlowPackage &data,                             
                             Limiter::Limiter *limiter,
                             Flux::Flow::Precondition::Precondition *precondition)
	:
	InviscidFluxScheme(data, limiter, precondition),
    k0(0.1), k2(0.5), k4(0.02), maxK(0.5)
{
    pressureSensor = flowPackage.GetField().pressureSwitch;
	
    nDim = data.GetMeshStruct().mesh->GetMeshDimension();
    nVar = nDim + 2;

	<PERSON><PERSON>_<PERSON>.<PERSON>(nVar, nVar);
	<PERSON><PERSON>_<PERSON>.<PERSON>(nVar, nVar);
	
	// 单元内部面数量（含并行边界面）
	elementInnerFaceSize = &flowPackage.GetTempElementField("elementInnerFaceSize", Scalar0);
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();
		elementInnerFaceSize->AddValue(ownerID, 1.0);
		elementInnerFaceSize->AddValue(neighID, 1.0);
	}

	// 对称面需要作为内部面处理
    for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
    {
        const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
        if (typeString != Boundary::Type::SYMMETRY) continue;
		
        const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
        for (int index = 0; index < faceSize; ++index)
        {
            const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
            const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
            elementInnerFaceSize->AddValue(ownerID, 1.0);
        }
    }
    elementInnerFaceSize->SetGhostlValueParallel();

	// 面通量计算函数指针
	CalculateDissipationFlux = &CentralScheme::CalculateFaceFluxNonPrecondition;
	if (precondition != nullptr) CalculateDissipationFlux = &CentralScheme::CalculateFaceFluxPrecondition;

	// 不同层级网格面通量计算函数指针
	CalculateFaceFluxLevel = &CentralScheme::CalculateFaceFluxFine;
	if (flowPackage.GetMeshStruct().level != 0) CalculateFaceFluxLevel = &CentralScheme::CalculateFaceFluxCoarse;

	// 计算面谱半径的函数指针
	CalculateSpectralRadiusSide = &CentralScheme::CalculateSpectralRadiusSideNonPre;
	if (precondition != nullptr) CalculateSpectralRadiusSide = &CentralScheme::CalculateSpectralRadiusSidePre;
}

CentralScheme::~CentralScheme()
{
    flowPackage.FreeTempField(*elementInnerFaceSize);
}

void CentralScheme::AddAverageResidual()
{
	// 计算能量
    rhoE = &flowPackage.GetTempElementField("rhoE", Scalar0);
	for (int elementID = 0; elementID < mesh->GetElementNumberAll(); ++elementID)
	{
		const Scalar &rhoTemp = rho.GetValue(elementID);
		const Vector &UTemp = U.GetValue(elementID);
		const Scalar &pTemp = p.GetValue(elementID);
		const Scalar &HTemp = H.GetValue(elementID);
		const Scalar rhoETemp = rhoTemp * HTemp - pTemp + 0.5 * rhoTemp * (UTemp & UTemp);
		rhoE->SetValue(elementID, rhoETemp);
	}
    
    // 边界面循环，在边界条件中计算
	
	// 内部面循环
    const size_t innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (size_t index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		const Face &face = mesh->GetFace(faceID);
		const int &ownerID = face.GetOwnerID();
		const int &neighID = face.GetNeighborID();

		// 计算面平均值
		const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));
		const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
		const Scalar pFace = 0.5 * (p.GetValue(ownerID) + p.GetValue(neighID));
        const Scalar rhoEFace = 0.5 * (rhoE->GetValue(ownerID) + rhoE->GetValue(neighID));
        
		// 计算面通量
		const Vector faceArea = face.GetNormal() * face.GetArea();
		const Scalar UFlux = UFace & faceArea;
		faceFlux.massFlux = rhoFace * UFlux;
		faceFlux.momentumFlux = faceFlux.massFlux * UFace + pFace * faceArea;
		faceFlux.energyFlux = (rhoEFace + pFace) * UFlux;

    	if (updateJacobian)
    	{
			const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();
    		this->GetInviscidProjJac(UFace, rhoEFace / rhoFace, faceArea, 0.5, Jacobian_i);
    	    jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_i);
    	}

		//累加残值
		residualMass.AddValue(ownerID, faceFlux.massFlux);
		residualMomentum.AddValue(ownerID, faceFlux.momentumFlux);
		residualEnergy.AddValue(ownerID, faceFlux.energyFlux);
		residualMass.AddValue(neighID, -faceFlux.massFlux);
		residualMomentum.AddValue(neighID, -faceFlux.momentumFlux);
		residualEnergy.AddValue(neighID, -faceFlux.energyFlux);

#if defined(_EnableMultiSpecies_)
		for (int k = 0; k < speciesSize; k++)
		{
            const Scalar flux = faceFlux.massFlux > 0.0 ? massFraction[k]->GetValue(ownerID) : massFraction[k]->GetValue(neighID);
            faceFlux.massFractionFlux[k] = faceFlux.massFlux * flux;
			residualMassFraction[k]->AddValue(ownerID,  faceFlux.massFractionFlux[k]);
			residualMassFraction[k]->AddValue(neighID, -faceFlux.massFractionFlux[k]);
		}
#endif
	}

	flowPackage.FreeTempField(*rhoE);
	
	return;
}

void CentralScheme::AddDissipationResidual()
{
	if (precondition == nullptr)
	{
		rhoU = &flowPackage.GetTempElementField("rhoU", Vector0);
		rhoE = &flowPackage.GetTempElementField("rhoE", Scalar0);
		for (int elementID = 0; elementID < mesh->GetElementNumberAll(); ++elementID)
		{
			const Scalar &rhoTemp = rho.GetValue(elementID);
			const Vector &UTemp = U.GetValue(elementID);
			const Scalar &pTemp = p.GetValue(elementID);
			const Scalar &HTemp = H.GetValue(elementID);
			const Vector rhoUTemp = rhoTemp * UTemp;
			const Scalar rhoETemp = rhoTemp * HTemp - pTemp + 0.5 * rhoTemp * (UTemp & UTemp);
			rhoU->SetValue(elementID, rhoUTemp);
			rhoE->SetValue(elementID, rhoETemp);
		}
	}
    
	laplaceRho = &flowPackage.GetTempElementField("laplaceRho", Scalar0);
	laplaceRhoU = &flowPackage.GetTempElementField("laplaceRhoU", Vector0);
	laplaceRhoE = &flowPackage.GetTempElementField("laplaceRhoE", Scalar0);

	const bool level0Flag = (flowPackage.GetMeshStruct().level == 0);
	if (level0Flag)
	{
		this->CalculateLaplace();
		flowPackage.CalculatePressureSwitch();
	}
    
	// 内部面循环，计算面通量
    const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
    for (int index = 0; index < innerFaceNumber; ++index)
    {
		// 得到面相关信息
        const int &faceID = mesh->GetInnerFaceIDInDomain(index);
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		// 计算面通量
		(this->*CalculateDissipationFlux)(faceID, faceFlux);
		
		// 累加面通量
		residualMass.AddValue(ownerID, -faceFlux.massFlux);
		residualMomentum.AddValue(ownerID, -faceFlux.momentumFlux);
		residualEnergy.AddValue(ownerID, -faceFlux.energyFlux);
		residualMass.AddValue(neighID, faceFlux.massFlux);
		residualMomentum.AddValue(neighID, faceFlux.momentumFlux);
		residualEnergy.AddValue(neighID, faceFlux.energyFlux);
#if defined(_EnableMultiSpecies_)
		for (int k = 0; k < speciesSize; k++)
		{
            const Scalar flux = faceFlux.massFlux > 0.0 ? massFraction[k]->GetValue(ownerID) : massFraction[k]->GetValue(neighID);
            faceFlux.massFractionFlux[k] = faceFlux.massFlux * flux;
			residualMassFraction[k]->AddValue(ownerID, -faceFlux.massFractionFlux[k]);
			residualMassFraction[k]->AddValue(neighID,  faceFlux.massFractionFlux[k]);
		}
#endif
	}

	//释放辅助场
	flowPackage.FreeTempField(*laplaceRho);
	flowPackage.FreeTempField(*laplaceRhoU);
	flowPackage.FreeTempField(*laplaceRhoE);
	
	if (precondition == nullptr)
	{
		flowPackage.FreeTempField(*rhoU);
		flowPackage.FreeTempField(*rhoE);
	}
}

NSFaceFlux CentralScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
	const Face &face = mesh->GetFace(faceID);

	// 计算面平均值
	const Scalar rhoFace = 0.5 * (faceValue.rhoLeft + faceValue.rhoRight);
	const Vector UFace = 0.5 * (faceValue.ULeft + faceValue.URight);
	const Scalar pFace = 0.5 * (faceValue.pLeft + faceValue.pRight);
	const Scalar rhoELeft = faceValue.pLeft * gamma1Inv + 0.5 * faceValue.rhoLeft * (faceValue.ULeft & faceValue.ULeft);
	const Scalar rhoERight = faceValue.pRight * gamma1Inv + 0.5 * faceValue.rhoRight * (faceValue.URight & faceValue.URight);
    const Scalar rhoEFace = 0.5 * (rhoELeft + rhoERight);
    
	// 计算面通量
	const Vector faceArea = face.GetNormal() * face.GetArea();
	const Scalar UFlux = UFace & faceArea;
	faceFlux.massFlux = rhoFace * UFlux;
	faceFlux.momentumFlux = faceFlux.massFlux * UFace + pFace * faceArea;
	faceFlux.energyFlux = (rhoEFace + pFace) * UFlux;

    if (updateJacobian)
    {
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();
    	this->GetInviscidProjJac(UFace, rhoEFace / rhoFace, faceArea, 0.5, Jacobian_i);
	
        if (mesh->JudgeBoundaryFace(faceID))
	    {
        	jacobian->AddBlock2Diag(ownerID, Jacobian_i);
	    }
	    else
	    {
	    	jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_i);
	    }
    }

	return faceFlux;
}

void CentralScheme::CalculateFaceFluxAverage(const int &faceID, NSFaceFlux &faceFlux)
{
	this->GetFaceValueFirstOrder(faceID, faceValue);
	faceFlux = this->FaceFluxCalculate(faceID, faceValue);
}

void CentralScheme::CalculateFaceFluxNonPrecondition(const int &faceID, NSFaceFlux &faceFlux)
{
	// 得到面相关信息
	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	const int &neighID = mesh->GetFace(faceID).GetNeighborID();
	
	//计算二阶项的差分值
	const Scalar deltaRho = rho.GetValue(neighID) - rho.GetValue(ownerID);
	const Vector deltaRhoU = rhoU->GetValue(neighID) - rhoU->GetValue(ownerID);
	const Scalar deltaRhoE = rhoE->GetValue(neighID) - rhoE->GetValue(ownerID);

	// 计算面通量
	(this->*CalculateFaceFluxLevel)(faceID, deltaRho, deltaRhoU, deltaRhoE, faceFlux);
}

void CentralScheme::CalculateFaceFluxPrecondition(const int &faceID, NSFaceFlux &faceFlux)
{
	// 得到面相关信息
	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	const int &neighID = mesh->GetFace(faceID).GetNeighborID();
	
	// 计算二阶项的差分值, 预处理的求解变量为（p,u,v,w,T）
	const Scalar deltaRho = p.GetValue(neighID) - p.GetValue(ownerID);
	const Vector deltaRhoU = U.GetValue(neighID) - U.GetValue(ownerID);
	const Scalar deltaRhoE = T.GetValue(neighID) - T.GetValue(ownerID);

	// 计算面通量
	(this->*CalculateFaceFluxLevel)(faceID, deltaRho, deltaRhoU, deltaRhoE, faceFlux);
	
	// 低速预处理修改通量
	precondition->CalculatePrecondionDissipationResidual(faceID, faceFlux.massFlux, faceFlux.momentumFlux, faceFlux.energyFlux);
}

void CentralScheme::CalculateFaceFluxFine(const int &faceID, const Scalar &deltaRho, const Vector &deltaRhoU, const Scalar &deltaRhoE, NSFaceFlux &faceFlux)
{
	// 得到面相关信息
	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	const int &neighID = mesh->GetFace(faceID).GetNeighborID();
	
    //计算二阶项和四阶项的相关系数
	const Scalar lambdaLeft = (this->*CalculateSpectralRadiusSide)(faceID, ownerID);
	const Scalar lambdaRight = (this->*CalculateSpectralRadiusSide)(faceID, neighID);
	const Scalar lambdaFace = 2.0 * (lambdaLeft + lambdaRight);
	const Scalar lambdaFaceInv = 1.0 / (lambdaFace + SMALL);
	const Scalar phi0 = pow((lambdaConvective.GetValue(ownerID) * lambdaFaceInv), 0.3);
	const Scalar phi1 = pow((lambdaConvective.GetValue(neighID) * lambdaFaceInv), 0.3);
	const Scalar phi = 4.0 * phi0 * phi1 / (phi0 + phi1);

	const Scalar &nFaceOwner = elementInnerFaceSize->GetValue(ownerID);
	const Scalar &nFaceNeigh = elementInnerFaceSize->GetValue(neighID);
	const Scalar sc2 = 3.0 * (nFaceOwner + nFaceNeigh) / (nFaceOwner * nFaceNeigh);
    const Scalar sc4 = sc2 * sc2 * 0.25;
	
	const Scalar &ps0 = pressureSensor->GetValue(ownerID);
	const Scalar &ps1 = pressureSensor->GetValue(neighID);
    const Scalar epsilon20 = k2 * Max(ps0, ps1) * sc2;
    const Scalar epsilon40 = Max(0.0, k4 - epsilon20) * sc4;
    const Scalar epsilon2 = Min(epsilon20 * phi, maxK) * 0.25 * lambdaFace;
	const Scalar epsilon4 = epsilon40 * phi * 0.25 * lambdaFace;
	
	//计算四阶项的差分值
	const Scalar deltaRhoLaplace = laplaceRho->GetValue(neighID) - laplaceRho->GetValue(ownerID);
	const Vector deltaRhoULaplace = laplaceRhoU->GetValue(neighID) - laplaceRhoU->GetValue(ownerID);
	const Scalar deltaRhoELaplace = laplaceRhoE->GetValue(neighID) - laplaceRhoE->GetValue(ownerID);

	// 计算通量
	faceFlux.massFlux = epsilon2 * deltaRho - epsilon4 * deltaRhoLaplace;
	faceFlux.momentumFlux = epsilon2 * deltaRhoU - epsilon4 * deltaRhoULaplace;
	faceFlux.energyFlux = epsilon2 * deltaRhoE - epsilon4 * deltaRhoELaplace;
	
    if (updateJacobian)
    {
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		// 计算雅可比矩阵系数
		const Scalar cte_0 = epsilon20 + epsilon40 * Scalar((nFaceOwner + 1)) * phi * 0.25 * lambdaFace;
		const Scalar cte_1 = epsilon20 + epsilon40 * Scalar((nFaceNeigh + 1)) * phi * 0.25 * lambdaFace;
		const Scalar fix_factor = 4.0; // 为了使雅克比矩阵更加对角占优而采用的系数

		const Scalar sq_vel_own = 0.5 * gamma1 * (U.GetValue(ownerID) & U.GetValue(ownerID));
		const Scalar sq_vel_neigh = 0.5 * gamma1 * (U.GetValue(ownerID) & U.GetValue(ownerID));

		Jacobian_i.SetZero();
		Jacobian_j.SetZero();
		// n-1项加入对角值
		for (unsigned short iVar = 0; iVar < (nVar - 1); iVar++)
		{
			Jacobian_i(iVar, iVar) = fix_factor * cte_0;
			Jacobian_j(iVar, iVar) = -fix_factor * cte_1;
		}

		// 最后一行单独处理--左矩阵
		Jacobian_i(nVar - 1, 0) = fix_factor * cte_0 * sq_vel_own;
		Jacobian_i(nVar - 1, 1) = -fix_factor * cte_0 * U.GetValue(ownerID).X();
		Jacobian_i(nVar - 1, 2) = -fix_factor * cte_0 * U.GetValue(ownerID).Y();
		if (nDim == 3)
		{
			Jacobian_i(nVar - 1, 3) = -fix_factor * cte_0 * U.GetValue(ownerID).Z();
		}
		Jacobian_i(nVar - 1, nVar - 1) = fix_factor * cte_0 * gamma;

		// 最后一行单独处理--右矩阵
		Jacobian_j(nVar - 1, 0) = -fix_factor * cte_1 * sq_vel_neigh;
		Jacobian_j(nVar - 1, 1) = fix_factor * cte_1 * U.GetValue(neighID).X();
		Jacobian_j(nVar - 1, 2) = fix_factor * cte_1 * U.GetValue(neighID).Y();
		if (nDim == 3)
		{
			Jacobian_j(nVar - 1, 3) = fix_factor * cte_1 * U.GetValue(neighID).Z();
		}
		Jacobian_j(nVar - 1, nVar - 1) = -fix_factor * cte_1 * gamma;

		jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
    }
}

void CentralScheme::CalculateFaceFluxCoarse(const int &faceID, const Scalar &deltaRho, const Vector &deltaRhoU, const Scalar &deltaRhoE, NSFaceFlux &faceFlux)
{
	// 得到面相关信息
	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	const int &neighID = mesh->GetFace(faceID).GetNeighborID();

    //计算二阶项和四阶项的相关系数
	const Scalar lambdaLeft = (this->*CalculateSpectralRadiusSide)(faceID, ownerID);
	const Scalar lambdaRight = (this->*CalculateSpectralRadiusSide)(faceID, neighID);
	const Scalar lambdaFace = 2.0 * (lambdaLeft + lambdaRight);
	const Scalar lambdaFaceInv = 1.0 / (lambdaFace + SMALL);
	const Scalar phi0 = pow((lambdaConvective.GetValue(ownerID) * lambdaFaceInv), 0.3);
	const Scalar phi1 = pow((lambdaConvective.GetValue(neighID) * lambdaFaceInv), 0.3);
	const Scalar phi = 4.0 * phi0 * phi1 / (phi0 + phi1);

	const int dim = (int)mesh->GetMeshDimension();
	const Scalar &nFaceOwner = elementInnerFaceSize->GetValue(ownerID);
	const Scalar &nFaceNeigh = elementInnerFaceSize->GetValue(neighID);
    const Scalar sc0 = dim * Scalar(nFaceOwner + nFaceNeigh) / Scalar(nFaceOwner * nFaceNeigh);
    const Scalar epsilon2 = Min(k0 * sc0 * phi, maxK) * 0.25 * lambdaFace;
	
	// 计算通量
	faceFlux.massFlux = epsilon2 * deltaRho;
	faceFlux.momentumFlux = epsilon2 * deltaRhoU;
	faceFlux.energyFlux = epsilon2 * deltaRhoE;
	
    if (updateJacobian)
    {
		const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
		const int &neighID = mesh->GetFace(faceID).GetNeighborID();

		Jacobian_i.SetZero(); Jacobian_j.SetZero();
        for (unsigned short iVar = 0; iVar < nVar; iVar++)
		{
			Jacobian_i(iVar, iVar) = -epsilon2;
			Jacobian_j(iVar, iVar) =  epsilon2;
		}
	    jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
    }
}

void CentralScheme::CalculateLaplace()
{
	Scalar deltaRho, deltaRhoE;
	Vector deltaRhoU;

	// 对偶网格
	if (nodeCenter)
	{
		// 计算内部单元
		const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
		for (int index = 0; index < innerFaceNumber; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetInnerFaceIDInDomain(index);
			const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();

			// 边界边对应的面单独处理
			if (mesh->JudegBoundaryElemnt(ownerID) && mesh->JudegBoundaryElemnt(neighID)) continue;

			// 计算差量
			this->CalculateDeltaValue(ownerID, neighID, deltaRho, deltaRhoU, deltaRhoE);

			// 累计差量
			laplaceRho->AddValue(ownerID, deltaRho);
			laplaceRhoU->AddValue(ownerID, deltaRhoU);
			laplaceRhoE->AddValue(ownerID, deltaRhoE);
			laplaceRho->AddValue(neighID, -deltaRho);
			laplaceRhoU->AddValue(neighID, -deltaRhoU);
			laplaceRhoE->AddValue(neighID, -deltaRhoE);
		}

		// 对称面边界取双倍
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
			const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
			if (typeString != Boundary::Type::SYMMETRY) continue;

			const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int index = 0; index < faceSize; ++index)
			{
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				laplaceRho->MultiplyValue(ownerID, 2.0);
				laplaceRhoU->MultiplyValue(ownerID, 2.0);
				laplaceRhoE->MultiplyValue(ownerID, 2.0);
			}
		}

		// 算壁面边界置零
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
			const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
			if (typeString < Boundary::Type::WALL) continue;

			const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int index = 0; index < faceSize; ++index)
			{
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				laplaceRho->SetValue(ownerID, Scalar0);
				laplaceRhoU->SetValue(ownerID, Vector0);
				laplaceRhoE->SetValue(ownerID, Scalar0);
			}
		}

		// 计算边界边所对应的面
		for (int index = 0; index < innerFaceNumber; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetInnerFaceIDInDomain(index);
			const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();

			// 边界边对应的面单独处理
			if (!mesh->JudegBoundaryElemnt(ownerID) || !mesh->JudegBoundaryElemnt(neighID)) continue;

			// 计算差量
			this->CalculateDeltaValue(ownerID, neighID, deltaRho, deltaRhoU, deltaRhoE);

			// 累计差量
			laplaceRho->AddValue(ownerID, deltaRho);
			laplaceRhoU->AddValue(ownerID, deltaRhoU);
			laplaceRhoE->AddValue(ownerID, deltaRhoE);
			laplaceRho->AddValue(neighID, -deltaRho);
			laplaceRhoU->AddValue(neighID, -deltaRhoU);
			laplaceRhoE->AddValue(neighID, -deltaRhoE);
		}

		// 对称面边界修正
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
			const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
			if (typeString != Boundary::Type::SYMMETRY) continue;

			const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int index = 0; index < faceSize; ++index)
			{
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				const Vector &normal = mesh->GetFace(faceID).GetNormal();
				const Vector &temp = laplaceRhoU->GetValue(ownerID);
				laplaceRhoU->SetValue(ownerID, temp - (temp & normal) * normal);
			}
		}

		// 壁面边界修正
		for (int patchID = 0; patchID < mesh->GetBoundarySize(); ++patchID)
		{
			const Boundary::Type &typeString = flowPackage.GetLocalBoundaryType(patchID);
			if (typeString < Boundary::Type::WALL) continue;

			const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int index = 0; index < faceSize; ++index)
			{
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				if (!flowPackage.GetTurbulentStatus().viscousFlag || typeString == Boundary::Type::WALL_SLIPPING)
				{
					const Vector &normal = mesh->GetFace(faceID).GetNormal();
					const Vector &temp = laplaceRhoU->GetValue(ownerID);
					laplaceRhoU->SetValue(ownerID, temp - (temp & normal) * normal);
				}
				else
				{
					const int &innerID = mesh->GetInnerElementIDForBoundaryElement(patchID, index);
					laplaceRhoU->SetValue(ownerID, laplaceRhoU->GetValue(innerID));
				}
			}
		}
	}
	else
	{
		// 内部面循环
		const int innerFaceNumber = mesh->GetInnerFaceNumberInDomain();
		for (int index = 0; index < innerFaceNumber; ++index)
		{
			// 得到面相关信息
			const int &faceID = mesh->GetInnerFaceIDInDomain(index);
			const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
			const int &neighID = mesh->GetFace(faceID).GetNeighborID();

			// 计算差分值
			this->CalculateDeltaValue(ownerID, neighID, deltaRho, deltaRhoU, deltaRhoE);

			// 累计差分值
			laplaceRho->AddValue(ownerID, deltaRho);
			laplaceRhoU->AddValue(ownerID, deltaRhoU);
			laplaceRhoE->AddValue(ownerID, deltaRhoE);
			laplaceRho->AddValue(neighID, -deltaRho);
			laplaceRhoU->AddValue(neighID, -deltaRhoU);
			laplaceRhoE->AddValue(neighID, -deltaRhoE);
		}

		// 边界面循环
		const int &boundarySize = mesh->GetBoundarySize();
		for (int patchID = 0; patchID < boundarySize; ++patchID)
		{
			const int &faceSize = mesh->GetBoundaryFaceNumberInDomain(patchID);
			for (int index = 0; index < faceSize; ++index)
			{
				// 得到面相关信息
				const int &faceID = mesh->GetBoundaryFaceIDInDomain(patchID, index);
				const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
				const int &neighID = mesh->GetFace(faceID).GetNeighborID();

				// 计算差分值
				this->CalculateDeltaValue(ownerID, neighID, deltaRho, deltaRhoU, deltaRhoE);

				// 累计差分值
				laplaceRho->AddValue(ownerID, deltaRho);
				laplaceRhoU->AddValue(ownerID, deltaRhoU);
				laplaceRhoE->AddValue(ownerID, deltaRhoE);
			}
		}
	}

	//发送并接收其他进程的相关场
    laplaceRho->SetGhostlValueParallel();
    laplaceRhoU->SetGhostlValueParallel();
    laplaceRhoE->SetGhostlValueParallel();
	laplaceRho->SetGhostlValueOverset();
	laplaceRhoU->SetGhostlValueOverset();
	laplaceRhoE->SetGhostlValueOverset();
    
	return;
}

const Scalar CentralScheme::CalculateSpectralRadiusSideNonPre(const int &faceID, const int &elementID)
{
	// 获取面信息
	const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
	const Scalar &faceArea = mesh->GetFace(faceID).GetArea();
	
	// 面单元音速和速度矢量
    const Scalar &sound = A.GetValue(elementID);
    const Scalar UNormal = fabs(U.GetValue(elementID) & faceNormal);

	// 主流对流谱半径计算
	return (UNormal + sound) * faceArea;
}

const Scalar CentralScheme::CalculateSpectralRadiusSidePre(const int &faceID, const int &elementID)
{
	// 获取面信息
	const Vector &faceNormal = mesh->GetFace(faceID).GetNormal();
	const Scalar &faceArea = mesh->GetFace(faceID).GetArea();

	// 面单元音速和速度矢量
    Scalar sound = A.GetValue(elementID);
    Scalar UNormal = fabs(U.GetValue(elementID) & faceNormal);

	// 预处理修正
	const Scalar &beta = precondition->GetBeta(elementID);
	const Scalar c2 = sound * sound;
	const Scalar theta = 1.0 / (beta * c2);
	const Scalar a4 = 1.0 / (theta * c2 - gamma1);
	const Scalar a5 = a4 * c2;
	const Scalar temp1 = UNormal * (a4 - 1.0);
	sound = 0.5 * sqrt(temp1 * temp1 + 4.0 * a5);
	UNormal *= (0.5 * (a4 + 1));
    
	// 主流对流谱半径计算
	return (UNormal + sound) * faceArea;
}

void CentralScheme::CalculateDeltaValue(const int &ownerID, const int &neighID, Scalar &deltaRho, Vector &deltaRhoU, Scalar &deltaRhoE)
{
    if (precondition != nullptr) // 预处理的求解变量为（p,u,v,w,T）
    {
        deltaRho = p.GetValue(neighID) - p.GetValue(ownerID);
        deltaRhoU = U.GetValue(neighID) - U.GetValue(ownerID);
        deltaRhoE = T.GetValue(neighID) - T.GetValue(ownerID);
    }
    else
    {
        deltaRho = rho.GetValue(neighID) - rho.GetValue(ownerID);
        deltaRhoU = rhoU->GetValue(neighID) - rhoU->GetValue(ownerID);
        deltaRhoE = rhoE->GetValue(neighID) - rhoE->GetValue(ownerID);
    }
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux