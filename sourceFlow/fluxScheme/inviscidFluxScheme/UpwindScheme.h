﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file UpwindScheme.h
//! <AUTHOR>
//! @brief NS方程无粘项通量求解格式：迎风格式基类
//! @date 2022-03-02
//
//------------------------------修改日志----------------------------------------
// 2022-03-02 李艳亮、乔龙
//    说明：建立格式。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_UpwindScheme_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_UpwindScheme_

#include "sourceFlow/fluxScheme/inviscidFluxScheme/InviscidFluxScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief NS方程无粘项通量Roe格式求解类
 * 
 */
class  UpwindScheme : public InviscidFluxScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
    UpwindScheme(Package::FlowPackage &data,
                 Limiter::Limiter *limiter,
                 Flux::Flow::Precondition::Precondition *precondition);

    /**
     * @brief 对流项平均残差累加
     * 
     */
    void AddAverageResidual();

    /**
     * @brief 对流项耗散残差累加
     * 
     */
    void AddDissipationResidual() {}

protected:
    /**
     * @brief 内部面的通量计算
     * 纯虚函数，在具体格式中实现
     * 
     * @param[in] faceID 当前面编号
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
    virtual NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue) = 0;
    
    /**
     * @brief 采用一阶格式计算平均面通量
     * 
     * @param[in] faceID 当前面编号
     * @param[out] faceFlux 当前面的通量
     */
    void CalculateFaceFluxAverage(const int &faceID, NSFaceFlux &faceFlux);

    /**
     * @brief 计算激波开关
     * 
     */
    void CalculateShockSwitch();

    /**
     * @brief 修正特征值
     * 
     * @param[in] eigenvalue 特征值
     * @param[in] localSound 当地声速
     * @return Scalar 
     */
    Scalar ModifyEigenvalue(Scalar eigenvalue, const Scalar &localSound);

protected:
    bool shockSwitchFlag; ///< 激波开关标识，AUSMDVScheme为true，其他为false
    ElementField<Scalar> *shockSwitch; ///< 激波开关场
};
} // namespace Inviscid
} // namespace Flow
} // namespace Flux


#endif 