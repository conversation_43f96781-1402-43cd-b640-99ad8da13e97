﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/HLLCScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

HLLCScheme::HLLCScheme(Package::FlowPackage &data,
					   Limiter::Limiter *limiter,
					   Flux::Flow::Precondition::Precondition *precondition)
	:
	UpwindScheme(data, limiter, precondition)
{
	IntermediateState = new Scalar[nVar];
	dSm_dU            = new Scalar[nVar];
	dPI_dU            = new Scalar[nVar];
	drhoStar_dU       = new Scalar[nVar];
	dpStar_dU         = new Scalar[nVar];
	dEStar_dU         = new Scalar[nVar];

	Velocity_i        = new Scalar[nDim];
	Velocity_j        = new Scalar[nDim];

	UnitNormal = new Scalar[nDim];
}

HLLCScheme::~HLLCScheme()
{
	delete[] IntermediateState;
	delete[] dSm_dU;
	delete[] dPI_dU;
	delete[] drhoStar_dU;
	delete[] dpStar_dU;
	delete[] dEStar_dU;

	delete[] Velocity_i;
	delete[] Velocity_j;
	delete[] UnitNormal;
}

NSFaceFlux HLLCScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
	//得到面基本信息
	const Face &face = mesh->GetFace(faceID);
	const int &ownerID = face.GetOwnerID();
	const int &neighID = face.GetNeighborID();
	const Vector &faceNorm = face.GetNormal();
	const Scalar &faceArea = face.GetArea();

	//获取左面值
	const Scalar &rhoLeft = faceValue.rhoLeft;
	const Vector &ULeft = faceValue.ULeft;
	const Scalar &pLeft = faceValue.pLeft;

	const Scalar ULeft2 = ULeft & ULeft;
	const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
	const Scalar enthalpyLeft = Cp * TLeft + 0.5 * ULeft2;
	Scalar energyLeft = enthalpyLeft - pLeft / rhoLeft;
	Scalar soundLeft = sqrt((enthalpyLeft - 0.5 * ULeft2) * gamma1);
	const Scalar UNormLeft = ULeft & faceNorm;

	//获取右面值
	const Scalar &rhoRight = faceValue.rhoRight;
	const Vector &URight = faceValue.URight;
	const Scalar &pRight = faceValue.pRight;

	const Scalar URight2 = URight & URight;
	const Scalar TRight = material.GetTemperature(pRight, rhoRight);
	const Scalar enthalpyRight = Cp *  TRight + 0.5 * URight2;
	Scalar energyRight = enthalpyRight - pRight / rhoRight;
	Scalar soundRight = sqrt((enthalpyRight - 0.5 * URight2) * gamma1);
	const Scalar UNormRight = URight & faceNorm;

	UnitNormal[0] = faceNorm.X();
	UnitNormal[1] = faceNorm.Y();
	if (nDim == 3) UnitNormal[2] = faceNorm.Z();

	Velocity_i[0] = ULeft.X();
	Velocity_i[1] = ULeft.Y();
	Velocity_j[0] = URight.X();
	Velocity_j[1] = URight.Y();
	if (nDim == 3) { Velocity_i[2] = ULeft.Z(); Velocity_j[2] = URight.Z(); }

	/*--- Roe's averaging ---*/
	const Scalar rhoAvg = sqrt(rhoLeft) + sqrt(rhoRight);
	const Vector UAvg = (ULeft * sqrt(rhoLeft) + URight * sqrt(rhoRight)) / rhoAvg;
	const Scalar UNormAvg = UAvg & faceNorm;
	const Scalar enthalpyAvg = (sqrt(rhoRight) * enthalpyRight + sqrt(rhoLeft) * enthalpyLeft) / rhoAvg;
	const Scalar soundAvg = sqrt(gamma1 * (enthalpyAvg - 0.5 * (UAvg & UAvg)));

	/*--- Speed of sound at L and R ---*/
	Scalar sL = Min(UNormAvg - soundAvg, UNormLeft - soundLeft);
	Scalar sR = Max(UNormAvg + soundAvg, UNormRight + soundRight);

	/*--- speed of contact surface ---*/
	Scalar RHO = rhoRight * (sR - UNormRight) - rhoLeft * (sL - UNormLeft);
	Scalar sM = (pLeft - pRight - rhoLeft * UNormLeft * (sL - UNormLeft) + rhoRight * UNormRight * (sR - UNormRight)) / RHO;

	/*--- Pressure at right and left (Pressure_j=Pressure_i) side of contact surface ---*/
	Scalar pStar = rhoRight * (UNormRight - sR) * (UNormRight - sM) + pRight;
	
	// 计算flux通量
	if (sM > 0.0) 
	{
		if (sL > 0.0)//用左状态计算通量
		{ 
			faceFlux.massFlux = rhoLeft * UNormLeft;
			faceFlux.momentumFlux = faceFlux.massFlux * ULeft + pLeft * faceNorm;			
			faceFlux.energyFlux = faceFlux.massFlux * enthalpyLeft;
		}
		else //用左星状态计算通量
		{ 	
			Scalar rhoSL = (sL - UNormLeft) / (sL - sM);

			NSFaceFlux f;
			f.massFlux = rhoSL * rhoLeft;
			f.momentumFlux = rhoSL * (rhoLeft * ULeft + (pStar - pLeft) / (sL - UNormLeft) * faceNorm);
			f.energyFlux = rhoSL * (rhoLeft * energyLeft - (pLeft * UNormLeft - pStar * sM) / (sL - UNormLeft));			

			faceFlux.massFlux = sM * f.massFlux;
			faceFlux.momentumFlux = sM * f.momentumFlux + pStar * faceNorm;
			faceFlux.energyFlux = sM * (f.energyFlux + pStar);
		}
	}
	else
	{
		if (sR < 0.0) //用右状态计算通量
		{	
			faceFlux.massFlux = rhoRight * UNormRight;
			faceFlux.momentumFlux = faceFlux.massFlux * URight + pRight * faceNorm;
			faceFlux.energyFlux = faceFlux.massFlux * enthalpyRight;
		}
		else //用右星状态计算通量
		{	
			Scalar rhoSR = (sR - UNormRight) / (sR - sM);
			
			NSFaceFlux f;
			f.massFlux = rhoSR * rhoRight;
			f.momentumFlux = rhoSR * (rhoRight * URight + (pStar - pRight) / (sR - UNormRight) * faceNorm);
			f.energyFlux = rhoSR * (rhoRight * energyRight - (pRight * UNormRight - pStar * sM) / (sR - UNormRight));
			
			faceFlux.massFlux = sM * f.massFlux;
			faceFlux.momentumFlux = sM * f.momentumFlux + pStar * faceNorm;
			faceFlux.energyFlux = sM * (f.energyFlux + pStar);
		}
	}

	faceFlux.massFlux *= faceArea;
	faceFlux.momentumFlux *= faceArea;
	faceFlux.energyFlux *= faceArea;
	// 减去平均部分（已经在基类UpwindScheme中计算）
   /* NSFaceFlux faceFlux1;
    this->CalculateFaceFluxAverage(faceID, faceFlux1);
    faceFlux.massFlux -= faceFlux1.massFlux;
    faceFlux.momentumFlux -= faceFlux1.momentumFlux;
    faceFlux.energyFlux -= faceFlux1.energyFlux;*/
	if (!jacobian || !updateJacobian) return faceFlux;

	// 计算jacobian
	if (sM > 0.0)
	{
		if (sL > 0.0)//用左状态计算
		{			
			GetInviscidProjJac(ULeft, energyLeft, faceNorm, 1.0, Jacobian_i);
			Jacobian_j.SetZero();
		}
		else //用左星状态计算
		{
			Jacobian1(sM, UnitNormal, pStar, RHO, //通用部分
				sL, UNormLeft, rhoLeft, Velocity_i, pLeft, energyLeft, ULeft2, enthalpyLeft,//一面的值
				sR, UNormRight, //另一面的值
				true, //是否左边的标志
				Jacobian_i);
			
			Jacobian2(sM, UnitNormal, pStar, RHO, //通用部分
				sL, UNormLeft, rhoLeft, Velocity_i, pLeft, energyLeft,//一面的值	
				rhoRight, sR, UNormRight, URight2, Velocity_j,//另一面的值
				true, //是否左边的标志
				Jacobian_j); //返回值 
		}
	}
	else
	{
		if (sR < 0.0) //用右状态计算
		{
			Jacobian_i.SetZero();
			GetInviscidProjJac(URight, energyRight, faceNorm, 1.0, Jacobian_j);
		}
		else//用右星状态计算
		{			
			Jacobian2(sM, UnitNormal, pStar, RHO, //通用部分
				sR, UNormRight, rhoRight, Velocity_j, pRight, energyRight,//一面的值	
				rhoLeft, sL, UNormLeft, ULeft2, Velocity_i,//另一面的值
				false, //是否左边的标志
				Jacobian_i); //返回值	  

			Jacobian1(sM, UnitNormal, pStar, RHO, //通用部分
				sR, UNormRight, rhoRight, Velocity_j, pRight, energyRight, URight2, enthalpyRight,	//一面的值
				sL, UNormLeft, //另一面的值
				false, //是否左边的标志
				Jacobian_j);
		}
	}
	
	Scalar temp = faceArea * 0.5;
	for (int i = 0; i < nVar; i++)
		for (int j = 0; j < nVar; j++)
		{
			Jacobian_i(i, j) *= temp;
			Jacobian_j(i, j) *= temp;
		}

	if (mesh->JudgeBoundaryFace(faceID))
	{
    	jacobian->AddBlock2Diag(ownerID, Jacobian_i);
	}
	else
	{
		jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
	}

	return faceFlux;
}

void HLLCScheme::Jacobian1(const Scalar &sM, const Scalar *UnitNormal, const Scalar &pStar, const Scalar &RHO, //通用部分
	const Scalar &sX, const Scalar &ProjVelocity, //一面的值
	const Scalar &Density, const Scalar *Velocity, const Scalar &Pressure, const Scalar &Energy,//一面的值
	const Scalar &sq_vel, const Scalar &Enthalpy,	//一面的值
	const Scalar &s2, const Scalar &ProjVelocity_2, //另一面的值
	const bool &leftFlag, //是否左边的标志
	Matrix &jacobian_1) //返回值
{	
	Scalar rhoS = (sX - ProjVelocity) / (sX - sM);
	IntermediateState[0] = rhoS * Density;
	for (int iDim = 0; iDim < nDim; iDim++)
		IntermediateState[iDim + 1] = rhoS * (Density * Velocity[iDim] + (pStar - Pressure) / (sX - ProjVelocity) * UnitNormal[iDim]);
	IntermediateState[nVar - 1] = rhoS * (Density * Energy - (Pressure * ProjVelocity - pStar * sM) / (sX - ProjVelocity));

	const Scalar &EStar = IntermediateState[nVar - 1];
	const Scalar Omega = 1 / (sX - sM);
	const Scalar OmegaSM = Omega * sM;

	/*--- Computing pressure derivatives d/dU (PI) ---*/
	dPI_dU[0] = 0.5 * gamma1 * sq_vel;
	for (int iDim = 0; iDim < nDim; iDim++)
		dPI_dU[iDim + 1] = -gamma1 * Velocity[iDim];
	dPI_dU[nVar - 1] = gamma1;

	/*--- Computing d/dU (Sm) ---*/
	dSm_dU[0] = (-ProjVelocity * ProjVelocity + sM * sX + dPI_dU[0]) / RHO;
	for (int iDim = 0; iDim < nDim; iDim++)
		dSm_dU[iDim + 1] = (UnitNormal[iDim] * (2 * ProjVelocity - sX - sM) + dPI_dU[iDim + 1]) / RHO;
	dSm_dU[nVar - 1] = dPI_dU[nVar - 1] / RHO;
	if (!leftFlag) 
		for (int i = 0; i < nVar; i++) dSm_dU[i] *= -1.0;
	
	/*--- Computing d/dU (pStar) ---*/
	for (int i = 0; i < nVar; i++)
		dpStar_dU[i] = Density * (s2 - ProjVelocity_2) * dSm_dU[i];

	/*--- Computing d/dU (rhoStar) ---*/
	drhoStar_dU[0] = Omega * (sX + IntermediateState[0] * dSm_dU[0]);
	for (int iDim = 0; iDim < nDim; iDim++)
		drhoStar_dU[iDim + 1] = Omega * (-UnitNormal[iDim] + IntermediateState[0] * dSm_dU[iDim + 1]);
	drhoStar_dU[nVar - 1] = Omega * IntermediateState[0] * dSm_dU[nVar - 1];
	
	/*--- Computing d/dU (EStar) ---*/
	for (int i = 0; i < nVar; i++)
		dEStar_dU[i] = Omega * (sM * dpStar_dU[i] + (EStar + pStar) * dSm_dU[i]);

	dEStar_dU[0] += Omega * ProjVelocity * (Enthalpy - dPI_dU[0]);
	for (int iDim = 0; iDim < nDim; iDim++)
		dEStar_dU[iDim + 1] += Omega * (-UnitNormal[iDim] * Enthalpy - ProjVelocity * dPI_dU[iDim + 1]);
	dEStar_dU[nVar - 1] += Omega * (sX - ProjVelocity - ProjVelocity * dPI_dU[nVar - 1]);
	
	/*--- Jacobian First Row ---*/
	for (int i = 0; i < nVar; i++)
		jacobian_1(0, i) = sM * drhoStar_dU[i] + IntermediateState[0] * dSm_dU[i];

	/*--- Jacobian Middle Rows ---*/
	for (int jDim = 0; jDim < nDim; jDim++)
	{
		for (int i = 0; i < nVar; i++)
			jacobian_1(jDim + 1, i) = (OmegaSM + 1) * (UnitNormal[jDim] * dpStar_dU[i] + IntermediateState[jDim + 1] * dSm_dU[i]);

		jacobian_1(jDim + 1, 0) += OmegaSM * Velocity[jDim] * ProjVelocity;

		jacobian_1(jDim + 1, jDim + 1) += OmegaSM * (sX - ProjVelocity);

		for (int iDim = 0; iDim < nDim; iDim++)
			jacobian_1(jDim + 1, iDim + 1) -= OmegaSM * Velocity[jDim] * UnitNormal[iDim];

		for (int i = 0; i < nVar; i++)
			jacobian_1(jDim + 1, i) -= OmegaSM * dPI_dU[i] * UnitNormal[jDim];
	}

	/*--- Jacobian Last Row ---*/
	for (int i = 0; i < nVar; i++)
		jacobian_1(nVar - 1, i) = sM * (dEStar_dU[i] + dpStar_dU[i]) + (EStar + pStar) * dSm_dU[i];
}

void HLLCScheme::Jacobian2(const Scalar &sM, const Scalar *UnitNormal, const Scalar &pStar, const Scalar &RHO, //通用部分
	const Scalar &sX, const Scalar &ProjVelocity, //一面的值
	const Scalar &Density, const Scalar *Velocity, const Scalar &Pressure, const Scalar &Energy,//一面的值	
	const Scalar &Density_2, const Scalar &s2, const Scalar &ProjVelocity_2, const Scalar &sq_vel_2, const Scalar *Velocity_2,//另一面的值
	const bool &leftFlag, //是否左边的标志
	Matrix &jacobian_2) //返回值
{
	Scalar rhoS = (sX - ProjVelocity) / (sX - sM);
	IntermediateState[0] = rhoS * Density;
	for (int iDim = 0; iDim < nDim; iDim++)
		IntermediateState[iDim + 1] = rhoS * (Density * Velocity[iDim] + (pStar - Pressure) / (sX - ProjVelocity) * UnitNormal[iDim]);
	IntermediateState[nVar - 1] = rhoS * (Density * Energy - (Pressure * ProjVelocity - pStar * sM) / (sX - ProjVelocity));
	
	const Scalar &EStar = IntermediateState[nVar - 1];
	const Scalar Omega = 1 / (sX - sM);
	const Scalar OmegaSM = Omega * sM;

	/*--- Computing d/dU (Sm) ---*/
	dSm_dU[0] = (ProjVelocity_2 * ProjVelocity_2 - sM * s2 - 0.5 * gamma1 *sq_vel_2) / RHO;
	for (int iDim = 0; iDim < nDim; iDim++)
		dSm_dU[iDim + 1] = -(UnitNormal[iDim] * (2 * ProjVelocity_2 - s2 - sM) - gamma1*Velocity_2[iDim]) / RHO;
	dSm_dU[nVar - 1] = - gamma1 / RHO;
	if (!leftFlag)
		for (int i = 0; i < nVar; i++) dSm_dU[i] *= -1.0;
	
	/*--- Computing d/dU (pStar) ---*/
	for (int i = 0; i < nVar; i++)
		dpStar_dU[i] = Density_2 * (sX - ProjVelocity) * dSm_dU[i];	
	
	/*--- Computing d/dU (EStar) ---*/
	for (int i = 0; i < nVar; i++)
		dEStar_dU[i] = Omega * (sM * dpStar_dU[i] + (EStar + pStar) * dSm_dU[i]);
	
	/*--- Jacobian First Row ---*/
	for (int i = 0; i < nVar; i++)
		jacobian_2(0, i) = IntermediateState[0] * (OmegaSM + 1) * dSm_dU[i];

	/*--- Jacobian Middle Rows ---*/
	for (int jDim = 0; jDim < nDim; jDim++)	
		for (int i = 0; i < nVar; i++)
			jacobian_2(jDim + 1, i) = (OmegaSM + 1) * (IntermediateState[jDim + 1] * dSm_dU[i] + UnitNormal[jDim] * dpStar_dU[i]);
	
	/*--- Jacobian Last Row ---*/
	for (int i = 0; i < nVar; i++)
		jacobian_2(nVar - 1, i) = sM * (dEStar_dU[i] + dpStar_dU[i]) + (EStar + pStar) * dSm_dU[i];
}

}
}
}