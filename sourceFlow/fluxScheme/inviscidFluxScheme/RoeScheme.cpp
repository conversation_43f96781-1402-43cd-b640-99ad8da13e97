﻿#include "sourceFlow/fluxScheme/inviscidFluxScheme/RoeScheme.h"

namespace Flux
{
namespace Flow
{
namespace Inviscid
{

RoeScheme::RoeScheme(Package::FlowPackage &data,
                     Limiter::Limiter *limiter,
                     Flux::Flow::Precondition::Precondition *precondition)
    :
    UpwindScheme(data, limiter, precondition)
{
	if (precondition != nullptr)
	{
		FatalError("RoeScheme::RoeScheme: precondition is not supported...");
		return;
	}

    kappa = 0.5;

    Flux.resize(nVar);
    Diff_U.resize(nVar);
    Lambda.resize(nVar);

    P_Tensor.Resize(nVar, nVar);
    invP_Tensor.Resize(nVar, nVar);
}

NSFaceFlux RoeScheme::FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue)
{
    if (jacobian) return this->FaceFluxCalculate_Implicit(faceID, faceValue);
    else          return this->FaceFluxCalculate_Explicit(faceID, faceValue);
}

NSFaceFlux RoeScheme::FaceFluxCalculate_Explicit(const int &faceID, const NSFaceValue &faceValue)
{
    //得到面单位法矢
    const Face &face = mesh->GetFace(faceID);
    const Vector &faceNormal = face.GetNormal();    
    
    //获取左右面值
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;
    
    //计算左右面的温度、焓和法向速度
    const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
    const Scalar enthalpyLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);
    const Scalar TRight = material.GetTemperature(pRight, rhoRight);
    const Scalar enthalpyRight = Cp * TRight + 0.5 * (URight & URight);
    const Scalar UNormLeft = ULeft & faceNormal;
    const Scalar UNormRight = URight & faceNormal;

    //面心处平均量计算
    const Scalar ratio = sqrt(rhoRight / rhoLeft);
    const Scalar weightLeft = 1.0 / (1.0 + ratio);
    const Scalar weightRight = 1.0 - weightLeft;
    const Scalar rhoMean = sqrt(rhoLeft * rhoRight);
    const Scalar enthalpyhMean = enthalpyLeft  * weightLeft + enthalpyRight * weightRight;
    const Vector UMean = ULeft * weightLeft + URight * weightRight;
    const Scalar UMean2Half = 0.5 * (UMean & UMean);
    const Scalar UNormMean = UNormLeft * weightLeft + UNormRight * weightRight;
    const Scalar soundMean2 = gamma1 * (enthalpyhMean - UMean2Half);
    const Scalar soundMean = sqrt(soundMean2);
    const Scalar soundMean2Reciprocal = 1.0 / soundMean2;

    //计算相关差量值
    Scalar deltaRho = rhoRight - rhoLeft;
    Vector deltaU = URight - ULeft;
    Scalar deltaP = pRight - pLeft;
    Scalar deltaUNorm = UNormRight - UNormLeft;

    //系数0.5和面积统一在最后乘

    // 1. 基本部分
    // 平均项计算
    Vector rhoULeft = rhoLeft * ULeft;
    Vector rhoURight = rhoRight * URight;
    Scalar rhoHLeft = pLeft * gamma1Inv + 0.5 * (rhoULeft & ULeft) + pLeft;
    Scalar rhoHRight = pRight * gamma1Inv + 0.5 * (rhoURight & URight) + pRight;
    faceFlux.massFlux = faceNormal & (rhoULeft + rhoURight);
    faceFlux.momentumFlux = faceNormal * (rhoULeft * ULeft + rhoURight * URight) + faceNormal * (pLeft + pRight);
    faceFlux.energyFlux = faceNormal & (rhoHLeft * ULeft + rhoHRight * URight);
    
    // 为预处理预留
	const Scalar &Umodified = UNormMean;
	const Scalar &cModified = soundMean;

    // 计算特征值
    Scalar eigenvalue1 = fabs(Umodified - cModified);
    Scalar eigenvalue2 = fabs(UNormMean);
    Scalar eigenvalue3 = fabs(Umodified + cModified);
    
    // 熵修正
    if (entropyFixFlag)
    {
        eigenvalue1 = ModifyEigenvalue(eigenvalue1, cModified);
        eigenvalue3 = ModifyEigenvalue(eigenvalue3, cModified);
    }

    // 部分<1>通量计算
    const Scalar rhoCdV = rhoMean * soundMean * deltaUNorm;
    const Scalar coefficient1 = 0.5 * eigenvalue1 * (deltaP - rhoCdV) * soundMean2Reciprocal;
    faceFlux.massFlux -= coefficient1;
    faceFlux.momentumFlux -= coefficient1 * (UMean - soundMean * faceNormal);
    faceFlux.energyFlux -= coefficient1 * (enthalpyhMean - soundMean * UNormMean);

    // 部分<2,3,4>通量计算
    const Scalar coefficient2 = eigenvalue2 * (deltaRho - deltaP * soundMean2Reciprocal);
    const Scalar coefficientTemp = eigenvalue2 * rhoMean;
    faceFlux.massFlux -= coefficient2;
    faceFlux.momentumFlux -= (coefficient2 * UMean + coefficientTemp * (deltaU - deltaUNorm * faceNormal));
    faceFlux.energyFlux -= (coefficient2 *  UMean2Half + coefficientTemp * ((UMean & deltaU) - UNormMean * deltaUNorm));

    // 部分<5>通量计算
    const Scalar coefficient3 = 0.5 * eigenvalue3 * (deltaP + rhoCdV) * soundMean2Reciprocal;
    faceFlux.massFlux -= coefficient3;
    faceFlux.momentumFlux -= coefficient3 * (UMean + soundMean * faceNormal);
    faceFlux.energyFlux -= coefficient3 * (enthalpyhMean + soundMean * UNormMean);

    // 系数0.5
    const Scalar halfArea = 0.5 * face.GetArea();
    faceFlux.massFlux *= halfArea;
    faceFlux.momentumFlux *= halfArea;
    faceFlux.energyFlux *= halfArea;

    return faceFlux;
}

NSFaceFlux RoeScheme::FaceFluxCalculate_Implicit(const int &faceID, const NSFaceValue &faceValue)
{
    // 获取左右面值
    const Scalar &rhoLeft = faceValue.rhoLeft;
    const Vector &ULeft = faceValue.ULeft;
    const Scalar &pLeft = faceValue.pLeft;
    const Scalar &rhoRight = faceValue.rhoRight;
    const Vector &URight = faceValue.URight;
    const Scalar &pRight = faceValue.pRight;

    const Face &face = mesh->GetFace(faceID);
    const int &ownerID = face.GetOwnerID();
    const int &neighID = face.GetNeighborID();

    // 得到面法向和面积大小
    const Vector &faceNorm = face.GetNormal();
    const Scalar &faceArea = face.GetArea();

    // 计算左右面的温度、焓和声速
    const Scalar TLeft = material.GetTemperature(pLeft, rhoLeft);
    const Scalar enthalpyLeft = Cp * TLeft + 0.5 * (ULeft & ULeft);
    const Scalar soundLeft = material.GetSoundSpeed(TLeft);
    const Scalar TRight = material.GetTemperature(pRight, rhoRight);
    const Scalar enthalpyRight = Cp * TRight + 0.5 * (URight & URight);
    const Scalar soundRight = material.GetSoundSpeed(TRight);

    // 计算面心值
    const Scalar VelocityNormLeft = ULeft & faceNorm;
    const Scalar VelocityNormRight = URight & faceNorm;
    const Scalar energyLeft = enthalpyLeft - pLeft / rhoLeft;
    const Scalar energyRight = enthalpyRight - pRight / rhoRight;

    const Vector Normal = faceNorm * faceArea;
    
    // 变量Roe平均计算
    const Scalar ratio = sqrt(fabs(rhoRight / rhoLeft));
    const Scalar rhoMean = ratio * rhoLeft;
    const Vector UMean = (ratio * URight + ULeft) / (ratio + 1);
    const Scalar enthalpyhMean = (ratio * enthalpyRight + enthalpyLeft) / (ratio + 1);
    const Scalar soundMean2 = gamma1 * (enthalpyhMean - 0.5 * (UMean & UMean));

    // soundMean2负值，跳过通量计算
    if (soundMean2 <= 0.0)
    {
        for (int iVar = 0; iVar < nVar; iVar++)
        {
            Flux[iVar] = 0.0;
            if (updateJacobian)
            {
                for (int jVar = 0; jVar < nVar; jVar++)
                {
                    Jacobian_i(iVar, jVar) = 0.0;
                    Jacobian_j(iVar, jVar) = 0.0;
                }
            }
        }

        return NSFaceFlux();
    }

    const Scalar soundMean = sqrt(soundMean2);
    const Scalar UNormMean = UMean & faceNorm;
    
    // 特征值计算
    for (int iDim = 0; iDim < nDim; iDim++) Lambda[iDim] = UNormMean;
    Lambda[nVar - 2] = UNormMean + soundMean;
    Lambda[nVar - 1] = UNormMean - soundMean;

    // Mavriplis熵修正
    const Scalar MaxLambda = fabs(UNormMean) + soundMean;
    const Scalar EntropyFix_Coeff = 0.001;
    for (int iVar = 0; iVar < nVar; iVar++)
        Lambda[iVar] = Max(fabs(Lambda[iVar]), EntropyFix_Coeff * MaxLambda);
    
    // 平均残值计算
    const Scalar massFluxLeft = rhoLeft * (ULeft & Normal);
    const Vector momentFluxLeft = massFluxLeft * ULeft + pLeft * Normal;
    const Scalar energyFluxLeft = massFluxLeft * enthalpyLeft;
    const Scalar massFluxRight = rhoRight * (URight & Normal);
    const Vector momentFluxRight = massFluxRight * URight + pRight * Normal;
    const Scalar energyFluxRight = massFluxRight * enthalpyRight;
    Flux[0] = kappa * (massFluxLeft + massFluxRight);
    Flux[1] = kappa * (momentFluxLeft.X() + momentFluxRight.X());
    Flux[2] = kappa * (momentFluxLeft.Y() + momentFluxRight.Y());
    if (nDim == 3) Flux[3] = kappa * (momentFluxLeft.Z() + momentFluxRight.Z());
    Flux[nDim + 1] = kappa * (energyFluxLeft + energyFluxRight);

    // 平均残值Jacobian计算
    if (updateJacobian)
    {
        GetInviscidProjJac(ULeft, energyLeft, Normal, kappa, Jacobian_i);
        GetInviscidProjJac(URight, energyRight, Normal, kappa, Jacobian_j);
    }

    // 计算P矩阵和P^-1矩阵
    GetPMatrix(rhoMean, UMean, soundMean, faceNorm, P_Tensor);
    GetPMatrix_inv(rhoMean, UMean, soundMean, faceNorm, invP_Tensor);

    // 守恒变量差量计算
    Diff_U[0] = rhoRight - rhoLeft;
    Diff_U[1] = rhoRight * URight.X() - rhoLeft * ULeft.X();
    Diff_U[2] = rhoRight * URight.Y() - rhoLeft * ULeft.Y();
    if (nDim == 3) Diff_U[3] = rhoRight * URight.Z() - rhoLeft * ULeft.Z();
    Diff_U[nDim + 1] = rhoRight * energyRight - rhoLeft * energyLeft;
    
    // ROE耗散计算
    const Scalar temp0 = (1.0 - kappa) * faceArea;
    for (int iVar = 0; iVar < nVar; iVar++)
    {
        for (int jVar = 0; jVar < nVar; jVar++)
        {
            // 计算 A_Roe = P x |Lambda| x (P^-1)
            Scalar roeMatrixIJ = 0.0;
            for (int kVar = 0; kVar < nVar; kVar++)
                roeMatrixIJ += P_Tensor(iVar, kVar) * Lambda[kVar] * invP_Tensor(kVar, jVar);

            // 耗散残值及Jacobian计算
            const Scalar temp1 = roeMatrixIJ * temp0;
            Flux[iVar] -= temp1 * Diff_U[jVar];
            if (updateJacobian)
            {
                Jacobian_i(iVar, jVar) += temp1;
                Jacobian_j(iVar, jVar) -= temp1;
            }
        }
    }

    if (updateJacobian)
    {
        if (mesh->JudgeBoundaryFace(faceID))
	    {
        	jacobian->AddBlock2Diag(ownerID, Jacobian_i);
	    }
	    else
	    {
	    	jacobian->UpdateBlocks(faceID, ownerID, neighID, Jacobian_i, Jacobian_j);
	    }
    }

    faceFlux.massFlux = Flux[0];
    faceFlux.momentumFlux = Vector(Flux[1], Flux[2], nDim == 3 ? Flux[3] : 0);
    faceFlux.energyFlux = Flux[nVar - 1];
    
    return faceFlux;
}

void RoeScheme::GetPMatrix(const Scalar &rhoFace, const Vector &UFace,
                           const Scalar &soundFace, const Vector &normal, Matrix &PMatrix)
{
    const Scalar U2Half = 0.5 * (UFace & UFace);
    const Scalar gamma1Inv = 1.0 / gamma1;
    const Scalar rhooc = rhoFace / soundFace;
    const Scalar rhoxc = rhoFace * soundFace;
    const Scalar UNormal = UFace & normal;
    const Scalar rhoUNormal = rhoFace * UNormal;
    const Scalar rhoCGamma1Inv = rhoxc * gamma1Inv;
    const Scalar rhoOCU2Half = rhooc * U2Half;
    const Vector rhoOCUFace = rhooc * UFace;
    
    const Vector rhoNormal = rhoFace * normal;

    if (nDim == 2)
    {
        PMatrix(0, 0) = 1.0;
        PMatrix(0, 1) = 0.0;
        PMatrix(0, 2) = 0.5 * rhooc;
        PMatrix(0, 3) = 0.5 * rhooc;

        PMatrix(1, 0) = UFace.X();
        PMatrix(1, 1) = rhoNormal.Y();
        PMatrix(1, 2) = 0.5 * (rhoOCUFace.X() + rhoNormal.X());
        PMatrix(1, 3) = 0.5 * (rhoOCUFace.X() - rhoNormal.X());

        PMatrix(2, 0) = UFace.Y();
        PMatrix(2, 1) = -rhoNormal.X();
        PMatrix(2, 2) = 0.5 * (rhoOCUFace.Y() + rhoNormal.Y());
        PMatrix(2, 3) = 0.5 * (rhoOCUFace.Y() - rhoNormal.Y());

        PMatrix(3, 0) = U2Half;
        PMatrix(3, 1) = UFace.X() * rhoNormal.Y() - UFace.Y() * rhoNormal.X();
        PMatrix(3, 2) = 0.5 * (rhoOCU2Half + rhoUNormal + rhoCGamma1Inv);
        PMatrix(3, 3) = 0.5 * (rhoOCU2Half - rhoUNormal + rhoCGamma1Inv);
    }
    else
    {
        PMatrix(0, 0) = normal.X();
        PMatrix(0, 1) = normal.Y();
        PMatrix(0, 2) = normal.Z();
        PMatrix(0, 3) = 0.5 * rhooc;
        PMatrix(0, 4) = 0.5 * rhooc;

        const Tensor temp1 = UFace * normal;
        PMatrix(1, 0) = temp1.XX();
        PMatrix(1, 1) = temp1.XY() - rhoNormal.Z();
        PMatrix(1, 2) = temp1.XZ() + rhoNormal.Y();
        PMatrix(1, 3) = 0.5 * (rhoOCUFace.X() + rhoNormal.X());
        PMatrix(1, 4) = 0.5 * (rhoOCUFace.X() - rhoNormal.X());

        PMatrix(2, 0) = temp1.YX() + rhoNormal.Z();
        PMatrix(2, 1) = temp1.YY();
        PMatrix(2, 2) = temp1.YZ() - rhoNormal.X();
        PMatrix(2, 3) = 0.5 * (rhoOCUFace.Y() + rhoNormal.Y());
        PMatrix(2, 4) = 0.5 * (rhoOCUFace.Y() - rhoNormal.Y());

        PMatrix(3, 0) = temp1.ZX() - rhoNormal.Y();
        PMatrix(3, 1) = temp1.ZY() + rhoNormal.X();
        PMatrix(3, 2) = temp1.ZZ();
        PMatrix(3, 3) = 0.5 * (rhoOCUFace.Z() + rhoNormal.Z());
        PMatrix(3, 4) = 0.5 * (rhoOCUFace.Z() - rhoNormal.Z());

        PMatrix(4, 0) = U2Half * normal.X() + (UFace.Y() * rhoNormal.Z() - UFace.Z() * rhoNormal.Y());
        PMatrix(4, 1) = U2Half * normal.Y() + (UFace.Z() * rhoNormal.X() - UFace.X() * rhoNormal.Z());
        PMatrix(4, 2) = U2Half * normal.Z() + (UFace.X() * rhoNormal.Y() - UFace.Y() * rhoNormal.X());
        PMatrix(4, 3) = 0.5 * (rhoOCU2Half + rhoUNormal + rhoCGamma1Inv);
        PMatrix(4, 4) = 0.5 * (rhoOCU2Half - rhoUNormal + rhoCGamma1Inv);
    }
}

void RoeScheme::GetPMatrix_inv(const Scalar &rhoFace, const Vector &UFace, const Scalar &soundFace, const Vector &normal, Matrix &PMatrixInv)
{
    const Scalar gamma1OC2 = gamma1 / (soundFace * soundFace);
    const Scalar gamma1ORhoC = gamma1 / (rhoFace * soundFace);
    const Vector gamma1ORhoCU = gamma1ORhoC * UFace;
    const Vector normalORho = normal / rhoFace;
    const Scalar U2Half = 0.5 * (UFace & UFace);
    const Scalar UNormalORho = normalORho & UFace;

    if (nDim == 3)
    {
        const Vector UNormalXRho = normalORho ^ UFace;
        const Vector gamma1OC2Normal = gamma1OC2 * normal;

        PMatrixInv(0, 0) = - gamma1OC2Normal.X() * U2Half + UNormalXRho.X() + normal.X();
        PMatrixInv(0, 1) =   gamma1OC2Normal.X() * UFace.X();
        PMatrixInv(0, 2) =   gamma1OC2Normal.X() * UFace.Y() + normalORho.Z();
        PMatrixInv(0, 3) =   gamma1OC2Normal.X() * UFace.Z() - normalORho.Y();
        PMatrixInv(0, 4) = - gamma1OC2Normal.X();

        PMatrixInv(1, 0) = - gamma1OC2Normal.Y() * U2Half + UNormalXRho.Y() + normal.Y();
        PMatrixInv(1, 1) =   gamma1OC2Normal.Y() * UFace.X() - normalORho.Z();
        PMatrixInv(1, 2) =   gamma1OC2Normal.Y() * UFace.Y();
        PMatrixInv(1, 3) =   gamma1OC2Normal.Y() * UFace.Z() + normalORho.X();
        PMatrixInv(1, 4) = - gamma1OC2Normal.Y();

        PMatrixInv(2, 0) = - gamma1OC2Normal.Z() * U2Half + UNormalXRho.Z() + normal.Z();
        PMatrixInv(2, 1) =   gamma1OC2Normal.Z() * UFace.X() + normalORho.Y();
        PMatrixInv(2, 2) =   gamma1OC2Normal.Z() * UFace.Y() - normalORho.X();
        PMatrixInv(2, 3) =   gamma1OC2Normal.Z() * UFace.Z();
        PMatrixInv(2, 4) = - gamma1OC2Normal.Z();

        PMatrixInv(3, 0) = - UNormalORho + gamma1ORhoC * U2Half;
        PMatrixInv(3, 1) =   normalORho.X() - gamma1ORhoCU.X();
        PMatrixInv(3, 2) =   normalORho.Y() - gamma1ORhoCU.Y();
        PMatrixInv(3, 3) =   normalORho.Z() - gamma1ORhoCU.Z();
        PMatrixInv(3, 4) =   gamma1ORhoC;

        PMatrixInv(4, 0) =   UNormalORho + gamma1ORhoC * U2Half;
        PMatrixInv(4, 1) = - normalORho.X() - gamma1ORhoCU.X();
        PMatrixInv(4, 2) = - normalORho.Y() - gamma1ORhoCU.Y();
        PMatrixInv(4, 3) = - normalORho.Z() - gamma1ORhoCU.Z();
        PMatrixInv(4, 4) =   gamma1ORhoC;
    }
    else
    {
        PMatrixInv(0, 0) = - gamma1OC2 * U2Half + 1.0;
        PMatrixInv(0, 1) =   gamma1OC2 * UFace.X();
        PMatrixInv(0, 2) =   gamma1OC2 * UFace.Y();
        PMatrixInv(0, 3) = - gamma1OC2;

        PMatrixInv(1, 0) = -normalORho.Y() * UFace.X() + normalORho.X() * UFace.Y();
        PMatrixInv(1, 1) =  normalORho.Y();
        PMatrixInv(1, 2) = -normalORho.X();
        PMatrixInv(1, 3) = 0.0;

        PMatrixInv(2, 0) = - UNormalORho + gamma1ORhoC * U2Half;
        PMatrixInv(2, 1) =   normalORho.X() - gamma1ORhoCU.X();
        PMatrixInv(2, 2) =   normalORho.Y() - gamma1ORhoCU.Y();
        PMatrixInv(2, 3) =   gamma1ORhoC;

        PMatrixInv(3, 0) =   UNormalORho + gamma1ORhoC * U2Half;
        PMatrixInv(3, 1) = - normalORho.X() - gamma1ORhoCU.X();
        PMatrixInv(3, 2) = - normalORho.Y() - gamma1ORhoCU.Y();
        PMatrixInv(3, 3) =   gamma1ORhoC;
    }
}

}//namespace Inviscid
}//namespace Flow
}//namespace Flux