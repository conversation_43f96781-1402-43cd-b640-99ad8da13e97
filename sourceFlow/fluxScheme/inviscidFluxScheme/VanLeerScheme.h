﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file VanLeerScheme.h
//! <AUTHOR>
//! @brief NS方程无粘项通量求解格式：VanLeer格式
//! @date 2021-04-08
//
//------------------------------修改日志----------------------------------------
// 2021-04-08 乔龙
//    说明：建立格式。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_VanLeerScheme_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_VanLeerScheme_

#include "sourceFlow/fluxScheme/inviscidFluxScheme/UpwindScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief NS方程无粘项通量VanLeer格式计算类
 * 
 */
class  VanLeerScheme : public UpwindScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
    VanLeerScheme(Package::FlowPackage &data,                  
                  Limiter::Limiter *limiter,
                  Flux::Flow::Precondition::Precondition *precondition);

    /**
     * @brief 内部面的通量计算
     * 
     * @param[in] face 当前面
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
    NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue);
};
} // namespace Inviscid
} // namespace Flow
} // namespace Flux


#endif 