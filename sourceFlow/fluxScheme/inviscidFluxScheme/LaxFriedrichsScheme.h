﻿
#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_LaxFriedrichsScheme_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_LaxFriedrichsScheme_

#include "sourceFlow/fluxScheme/inviscidFluxScheme/UpwindScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief NS方程无粘项通量LaxFriedrichs格式计算类
 * 
 */
class  LaxFriedrichsScheme : public UpwindScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
    LaxFriedrichsScheme(Package::FlowPackage &data,
                        Limiter::Limiter *limiter,
                        Flux::Flow::Precondition::Precondition *precondition);
    
    /**
     * @brief 内部面的通量计算
     * 
     * @param[in] face 当前面
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
    NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue);

};

} // namespace Inviscid
} // namespace Flow
} // namespace Flux


#endif 