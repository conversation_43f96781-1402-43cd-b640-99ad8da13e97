﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file HLLCScheme.h
//! <AUTHOR>
//! @brief NS方程无粘项通量求解格式：HLLC格式
//! @date 2024-4-08
//------------------------------------------------------------------------------


#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_HLLCScheme_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_HLLCScheme_

#include "sourceFlow/fluxScheme/inviscidFluxScheme/UpwindScheme.h"


/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{

class HLLCScheme : public UpwindScheme
{
public:
	/**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
	HLLCScheme(Package::FlowPackage &data,
		       Limiter::Limiter *limiter,
		       Flux::Flow::Precondition::Precondition *precondition);
     
	/**
     * @brief 析构函数
     * 
     */
	~HLLCScheme();

	/**
     * @brief 内部面的通量计算
     * 
     * @param[in] face 当前面
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
	NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue);

protected:
	void Jacobian1(const Scalar &sM, const Scalar *UnitNormal, const Scalar &pStar, const Scalar &RHO, //通用部分
		const Scalar &sX, const Scalar &ProjVelocity, //一面的值
		const Scalar &Density, const Scalar *Velocity, const Scalar &Pressure, const Scalar &Energy,//一面的值
		const Scalar &sq_vel, const Scalar &Enthalpy,	//一面的值
		const Scalar &s2, const Scalar &ProjVelocity_2, //另一面的值
		const bool &leftFlag, //是否左边的标志
		Matrix &jacobian_1); //返回值

	void Jacobian2(const Scalar &sM, const Scalar *UnitNormal, const Scalar &pStar, const Scalar &RHO, //通用部分
		const Scalar &sX, const Scalar &ProjVelocity, //一面的值
		const Scalar &Density, const Scalar *Velocity, const Scalar &Pressure, const Scalar &Energy,//一面的值	
		const Scalar &Density_2, const Scalar &s2, const Scalar &ProjVelocity_2, const Scalar &sq_vel_2, const Scalar *Velocity_2,//另一面的值
		const bool &leftFlag, //是否左边的标志
		Matrix &jacobian_2); //返回值


private:
     Scalar *IntermediateState;
     Scalar *Velocity_i, *Velocity_j;
     Scalar *dSm_dU, *dPI_dU, *drhoStar_dU, *dpStar_dU, *dEStar_dU;
     Scalar *UnitNormal;
};
} // namespace Inviscid
} // namespace Flow
} // namespace Flux

#endif
