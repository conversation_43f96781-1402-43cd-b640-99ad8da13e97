﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file RoeSchemeMod.h
//! <AUTHOR>
//! @brief NS方程无粘项通量求解格式：RoeMod格式
//! @date 2022-02-23
//
//------------------------------修改日志----------------------------------------
// 2022-02-23 李艳亮
//    说明：建立格式。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_fluxScheme_inviscidFluxScheme_RoeSchemeMod_
#define _sourceFlow_fluxScheme_inviscidFluxScheme_RoeSchemeMod_

#include "sourceFlow/fluxScheme/inviscidFluxScheme/UpwindScheme.h"

/**
 * @brief 通量命名空间
 * 
 */
namespace Flux
{
/**
 * @brief 流场通量命名空间
 * 
 */
namespace Flow
{
/**
 * @brief 无粘项通量命名空间
 * 
 */
namespace Inviscid
{
/**
 * @brief NS方程无粘项通量Roe格式计算类
 * 
 */
class  RoeSchemeMod : public UpwindScheme
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in, out] data 流场包
     * @param[in] limiter_ 通量限制器指针
     * @param[in] precondition_ 低速预处理指针
     */
    RoeSchemeMod(Package::FlowPackage &data,
              Limiter::Limiter *limiter,
              Flux::Flow::Precondition::Precondition *precondition);

    /**
     * @brief 内部面的通量计算
     * 
     * @param[in] face 当前面
     * @param[in] faceValue 当前面的左右面心值
     * @return NSFaceFlux 
     */
    NSFaceFlux FaceFluxCalculate(const int &faceID, const NSFaceValue &faceValue);
   
protected: 
    /// 计算特征矢量
    void CalculateCharacteristics(Scalar &cRho, Vector &cU, Scalar &cP,
        const Scalar &density, const Scalar &sound, const Vector &normal,
        const Scalar &deltaRho, const Vector &deltaU, const Scalar &deltaEnthalpy);

    /**
     * @brief 利用左值、右值和均值计算差量
     * 
     * @param[in] left 左值
     * @param[in] right 右值
     * @param[in] mean 均值
     * @return Scalar 
     */
    Scalar CalculateDelta(const Scalar &left, const Scalar &right, const Scalar &mean);

    /**
     * @brief 计算限制器
     * 
     * @param[in] delta 差量
     * @param[in] mean 均值
     * @return Scalar 
     */
    Scalar MinMod(const Scalar &delta, const Scalar &mean);

private:
    bool secondOrderFlag; ///< 二阶标识，true为二阶
    bool dim3; ///< 三维标识，true为三维

};
} // namespace Inviscid
} // namespace Flow
} // namespace Flux


#endif 