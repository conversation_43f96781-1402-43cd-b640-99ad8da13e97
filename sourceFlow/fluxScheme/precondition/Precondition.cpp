﻿#include "sourceFlow/fluxScheme/precondition/Precondition.h"

namespace Flux
{
namespace Flow
{
namespace Precondition
{

Precondition::Precondition(Package::FlowPackage &data)
	:
	flowPackage(data),
	mesh(data.GetMeshStruct().mesh),
    material(data.GetMaterial()),
	rho(*data.GetField().density),
	U(*data.GetField().velocity),
	p(*data.GetField().pressure),
	T(*data.GetField().temperature),
	A(*data.GetField().soundSpeed),
	beta(data.GetMeshStruct().mesh, 0.0, "PRECONDITION_BETA"),
	gamma1(data.GetMaterialNumber().gamma1),
	gamma1Inv(1.0/data.GetMaterialNumber().gamma1)
{
	const Scalar K = 4.0;
	const Scalar &MaInf = data.GetFlowConfigure().GetFlowReference().mach;
	MaMin2 = K * MaInf * MaInf;
}

void Precondition::Calculate()
{
	// 计算矩阵系数
	const int elementNumber = mesh->GetElementNumberInDomain();
	for (int index = 0; index < elementNumber; ++index)
	{
		const int &elementID = mesh->GetElementIDInDomain(index);

		const Vector &UCell = U.GetValue(elementID);
		const Scalar &sound = A.GetValue(elementID);

		const Scalar q2 = UCell & UCell;		
		const Scalar c2 = sound * sound;
		const Scalar Mr2 = Min(Max(q2 / c2, MaMin2), 1.0);

		beta.SetValue(elementID, Mr2 / (1.0 + gamma1 * Mr2));
	}

	beta.SetGhostlValueParallel();
	beta.SetGhostlValueOverset();
	beta.SetGhostlValueBoundary();

	return;
}

void Precondition::CalculateNewFlux(const int &elementID, Scalar &massFlux, Vector &momentumFlux, Scalar &energyFlux)
{
	const Scalar &rhoElem = rho.GetValue(elementID);
	const Vector &UElem = U.GetValue(elementID);
	const Scalar &pElem = p.GetValue(elementID);
	const Scalar &TElem = T.GetValue(elementID);
	const Scalar &soundElem = A.GetValue(elementID);
	
	const Scalar c2 = soundElem * soundElem;
	const Scalar thetaElem = 1.0 / (beta.GetValue(elementID) * c2);
	const Scalar q2 = UElem & UElem;
	const Scalar rhoInverse = 1.0 / rhoElem;
	const Scalar Eelem = pElem / (gamma1 * rhoElem) + 0.5 * q2;

	const Scalar phi = 1.0 / (thetaElem * c2 - gamma1);
	const Scalar a2 = gamma1 * phi;
	const Scalar a3 = gamma1 * phi * TElem / rhoElem;
	const Scalar thetaA3 = thetaElem * a3;

	const Scalar rhoTInverse = -1.0 * rhoElem / TElem;
	const Scalar rhoPInverse = rhoElem / pElem;

	// 矩阵P
	const Scalar &P00 = rhoPInverse;
	const Scalar &P04 = rhoTInverse;

	const Scalar  P10 = UElem.X() * rhoPInverse;
	const Scalar &P11 = rhoElem;
	const Scalar  P14 = UElem.X() * rhoTInverse;

	const Scalar  P20 = UElem.Y() * rhoPInverse;
	const Scalar &P22 = rhoElem;
	const Scalar  P24 = UElem.Y() * rhoTInverse;

	const Scalar  P30 = UElem.Z() * rhoPInverse;
	const Scalar &P33 = rhoElem;
	const Scalar  P34 = UElem.Z() * rhoTInverse;

	const Scalar  P40 = Eelem * rhoPInverse;
	const Scalar  P41 = UElem.X() * rhoElem;
	const Scalar  P42 = UElem.Y() * rhoElem;
	const Scalar  P43 = UElem.Z() * rhoElem;
	const Scalar  P44 = 0.5 * q2 * rhoTInverse;

	// 矩阵GammaInverse
	const Scalar  GammaInv00 = 0.5 * q2 * a2;
	const Scalar  GammaInv01 = -a2 * UElem.X();
	const Scalar  GammaInv02 = -a2 * UElem.Y();
	const Scalar  GammaInv03 = -a2 * UElem.Z();
	const Scalar &GammaInv04 = a2;
	
	const Scalar  GammaInv10 = -UElem.X() * rhoInverse;
	const Scalar &GammaInv11 = rhoInverse;

	const Scalar  GammaInv20 = -UElem.Y() * rhoInverse;
	const Scalar &GammaInv22 = rhoInverse;

	const Scalar  GammaInv30 = -UElem.Z() * rhoInverse;
	const Scalar &GammaInv33 = rhoInverse;

	const Scalar  GammaInv40 = a3 * (0.5 * q2 * thetaElem - 1.0 / a2);
	const Scalar  GammaInv41 = -1.0 * UElem.X() * thetaA3;
	const Scalar  GammaInv42 = -1.0 * UElem.Y() * thetaA3;
	const Scalar  GammaInv43 = -1.0 * UElem.Z() * thetaA3;
	const Scalar &GammaInv44 = thetaA3;

	//预处理阵的计算
	Precon[0][0] = P00 * GammaInv00 + P04 * GammaInv40;
	Precon[0][1] = P00 * GammaInv01 + P04 * GammaInv41;
	Precon[0][2] = P00 * GammaInv02 + P04 * GammaInv42;
	Precon[0][3] = P00 * GammaInv03 + P04 * GammaInv43;
	Precon[0][4] = P00 * GammaInv04 + P04 * GammaInv44;

	Precon[1][0] = P10 * GammaInv00 + P11 * GammaInv10 + P14 * GammaInv40;
	Precon[1][1] = P10 * GammaInv01 + P11 * GammaInv11 + P14 * GammaInv41;
	Precon[1][2] = P10 * GammaInv02 + P14 * GammaInv42;
	Precon[1][3] = P10 * GammaInv03 + P14 * GammaInv43;
	Precon[1][4] = P10 * GammaInv04 + P14 * GammaInv44;

	Precon[2][0] = P20 * GammaInv00 + P22 * GammaInv20 + P24 * GammaInv40;
	Precon[2][1] = P20 * GammaInv01 + P24 * GammaInv41;
	Precon[2][2] = P20 * GammaInv02 + P22 * GammaInv22 + P24 * GammaInv42;
	Precon[2][3] = P20 * GammaInv03 + P24 * GammaInv43;
	Precon[2][4] = P20 * GammaInv04 + P24 * GammaInv44;

	Precon[3][0] = P30 * GammaInv00 + P33 * GammaInv30 + P34 * GammaInv40;
	Precon[3][1] = P30 * GammaInv01 + P34 * GammaInv41;
	Precon[3][2] = P30 * GammaInv02 + P34 * GammaInv42;
	Precon[3][3] = P30 * GammaInv03 + P33 * GammaInv33 + P34 * GammaInv43;
	Precon[3][4] = P30 * GammaInv04 + P34 * GammaInv44;

	Precon[4][0] = P40 * GammaInv00 + P41 * GammaInv10 + P42 * GammaInv20 + P43 * GammaInv30 + P44 * GammaInv40;
	Precon[4][1] = P40 * GammaInv01 + P41 * GammaInv11 + P44 * GammaInv41;
	Precon[4][2] = P40 * GammaInv02 + P42 * GammaInv22 + P44 * GammaInv42;
	Precon[4][3] = P40 * GammaInv03 + P43 * GammaInv33 + P44 * GammaInv43;
	Precon[4][4] = P40 * GammaInv04 + P44 * GammaInv44;

	// 残值的预处理
	Scalar temp[5] = { 0.0, 0.0, 0.0, 0.0, 0.0 };
	Scalar tmpFlux[5] = { massFlux, momentumFlux.X(), momentumFlux.Y(), momentumFlux.Z(), energyFlux };
	for (int i = 0; i < 5; ++i)
		for (int j = 0; j < 5; ++j)
			temp[i] += Precon[i][j] * tmpFlux[j];

	massFlux = temp[0];
	momentumFlux = Vector(temp[1], temp[2], temp[3]);
	energyFlux = temp[4];
}

void Precondition::CalculatePrecondionDissipationResidual(const int &faceID, Scalar &massFlux, Vector &momentumFlux, Scalar &energyFlux)
{
	const int &ownerID = mesh->GetFace(faceID).GetOwnerID();
	const int &neighID = mesh->GetFace(faceID).GetNeighborID();	

	const Scalar TFace = 0.5 * (T.GetValue(ownerID) + T.GetValue(neighID));
	const Vector UFace = 0.5 * (U.GetValue(ownerID) + U.GetValue(neighID));
	const Scalar rhoFace = 0.5 * (rho.GetValue(ownerID) + rho.GetValue(neighID));

	const Scalar q2 = UFace & UFace;
	const Scalar sound = material.GetSoundSpeed(TFace);
	const Scalar c2 = sound * sound;
	const Scalar Mr2 = Min(Max(q2 / c2, MaMin2), 1.0);
	const Scalar betaFace = Mr2 / (1.0 + gamma1 * Mr2);
	const Scalar thetaFace = 1.0 / (betaFace * c2);

	const Scalar H = c2 * gamma1Inv + 0.5 * q2;
	const Scalar rhoT = -1.0 * rhoFace / TFace;
	const Vector rhoU = rhoFace * UFace;
	const Vector thetaU = thetaFace * UFace;
	const Vector rhoUT = rhoT * UFace;

	// 预处理阵Gamma阵的计算
	const Scalar &Gamma00 = thetaFace;
	const Scalar &Gamma04 = rhoT;

	const Scalar &Gamma10 = thetaU.X();
	const Scalar &Gamma11 = rhoFace;
	const Scalar &Gamma14 = rhoUT.X();
	
	const Scalar &Gamma20 = thetaU.Y();
	const Scalar &Gamma22 = rhoFace;
	const Scalar &Gamma24 = rhoUT.Y();;
	
	const Scalar &Gamma30 = thetaU.Z();
	const Scalar &Gamma33 = rhoFace;
	const Scalar &Gamma34 = rhoUT.Z();
	
	const Scalar  Gamma40 = thetaFace * H - 1.0;
	const Scalar &Gamma41 = rhoU.X();
	const Scalar &Gamma42 = rhoU.Y();
	const Scalar &Gamma43 = rhoU.Z();
	const Scalar  Gamma44 = rhoT * q2 / 2.0;

	// 残值的预处理
	Scalar temp0 = Gamma00 * massFlux + Gamma04 * energyFlux;
	Scalar temp1 = Gamma10 * massFlux + Gamma11 * momentumFlux.X() + Gamma14 * energyFlux;
	Scalar temp2 = Gamma20 * massFlux + Gamma22 * momentumFlux.Y() + Gamma24 * energyFlux;
	Scalar temp3 = Gamma30 * massFlux + Gamma33 * momentumFlux.Z() + Gamma34 * energyFlux;
	Scalar temp4 = Gamma40 * massFlux + Gamma41 * momentumFlux.X() + Gamma42 * momentumFlux.Y()
		         + Gamma43 * momentumFlux.Z() + Gamma44 * energyFlux;

	massFlux = temp0;
	momentumFlux = Vector(temp1, temp2, temp3);
	energyFlux = temp4;
}

} //namespace Precondition
} //namespace Flow
} //namespace Flux


