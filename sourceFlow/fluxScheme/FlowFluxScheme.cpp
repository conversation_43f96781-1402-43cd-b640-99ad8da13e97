﻿#include "sourceFlow/fluxScheme/FlowFluxScheme.h"

namespace Flux
{
namespace Flow
{
FlowFluxScheme::FlowFluxScheme(Package::FlowPackage &data)
    :
    flowPackage(data),
    mesh(data.GetMeshStruct().mesh),
    material(data.GetMaterial()),
    rho(*data.GetField().density),
    U(*data.GetField().velocity),
    p(*data.GetField().pressure),
    T(*data.GetField().temperature),
    H(*data.GetField().enthalpy),
	A(*data.GetField().soundSpeed),
    residualMass(*data.GetResidualField().residualMass),
    residualMomentum(*data.GetResidualField().residualMomentum),
    residualEnergy(*data.GetResidualField().residualEnergy),
#if defined(_EnableMultiSpecies_)
	massFraction(data.GetField().massFraction),
	residualMassFraction(data.GetResidualField().residualMassFraction),
    speciesSize(data.GetMultiSpeciesStatus().speciesSize),
#endif
    nodeCenter(data.GetFlowConfigure().GetPreprocess().dualMeshFlag),
	jacobian(data.GetImplicitSolver().jacobian),
    updateJacobian(data.GetImplicitSolver().updateJacobian)
{
    nDim = data.GetMeshStruct().mesh->GetMeshDimension();
    nVar = nDim + 2;

    Jacobian_i.Resize(nVar, nVar);
    Jacobian_j.Resize(nVar, nVar);
    Jacobian_i.SetZero();
    Jacobian_j.SetZero();
    
#if defined(_EnableMultiSpecies_)
    faceFlux.massFractionFlux.resize(speciesSize);
    faceValue.massFractionLeft.resize(speciesSize);
    faceValue.massFractionRight.resize(speciesSize);
#endif
}

void FlowFluxScheme::SetResidualZero()
{
    const int elementNumber = mesh->GetElementNumberInDomain();
    for (int index = 0; index < elementNumber; ++index)
    {
        const int &elementID = mesh->GetElementIDInDomain(index);
        residualMass.SetValue(elementID, Scalar0);
        residualMomentum.SetValue(elementID, Vector0);
        residualEnergy.SetValue(elementID, Scalar0);
#if defined(_EnableMultiSpecies_)
	    for (int m = 0; m < speciesSize; ++m) residualMassFraction[m]->SetValue(elementID, Scalar0);
#endif
    }
    
    return;
}

}//namespace Flow
}//namespace Flux

