﻿//! @file 
//! <AUTHOR>
//! @brief
//! @date 

#ifndef _specialModule_staticAeroelastic_method_AeroStaticMethod_
#define _specialModule_staticAeroelastic_method_AeroStaticMethod_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <array>
#include <algorithm>
#include <Eigen/Core>
#include <Eigen/LU>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <ctime>
#include <Eigen/Dense>  // 引入Eigen库

using Eigen::VectorXd;
using Eigen::MatrixXd;

#if defined(_EnableMKL_)
#include "mkl.h"
#include "mkl_lapacke.h"
#include "mkl_cblas.h"
#endif

#include "meshProcess/meshConverter/CgnsMesh.h"
#include "meshProcess/meshConverter/MeshConvertManager.h"
#include "feilian-specialmodule/staticAeroelastic/meshDeform/A_MeshDeform.h"
#include "feilian-specialmodule/staticAeroelastic/meshData/MeshData.h"
#include "sourceFlow/package/FlowPackage.h"
#include "feilian-specialmodule/staticAeroelastic/meshDeform/A_RBFGreedyAlg.h"

#include "basic/postTools/Tecplot.h"
#include "basic/configure/Configure.h"
#include "basic/postTools/BasePost.h"



#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif


class AeroStaticMethod
{

    public:
 

    AeroStaticMethod(std::vector<struct Solver::AeroMeshData> aeroMeshData_, std::vector<struct Solver::SolidMeshData> solidMeshData_, SubMesh *subMesh_ ,const Package::FlowPackage *data_,SubMesh *globalMesh_);
    ~AeroStaticMethod();


    //**计算各部件转换矩阵
    void calculateCouplmat();

    std::vector<std::vector<double>> calculateHm(std::vector<double> xso,std::vector<double> yso,std::vector<double> zso,std::vector<double> xao,std::vector<double> yao,std::vector<double> zao,int nanodes,int nsnodes);

    std::vector<std::vector<double>> calculateHm_adv(std::vector<double> xso,std::vector<double> yso,std::vector<double> zso,std::vector<double> xao,std::vector<double> yao,std::vector<double> zao,int nanodes,int nsnodes);

    Scalar calculatecss( std::string bfun, double dis, double sra);
    
    //**计算耦合界面气动力
    void Calcaeroforce();

    std::vector<Vector> CalculateNodeArea(std::vector<Node> faceNodeList, const Node &faceCenter);
    
    //**由转换矩阵hm及气动力计算结构载荷
    void CalcuateForceSolid();
    
    //**将分部件结构载荷写入到Force.dat文件中
    void WriteForceSolid();
    
    //**调用Nastran分析，并解析f06文件  
    void UseNastAndReadF06();
    
    //**由结构变形计算气动变形，并对各部件交线处作平均
    void CalcuateAeroDeform();

    void AverageIntersectPartDxyz();

    void OutSurfFile();
    
    //网格变形
    void ProcessMeshDeform();

    void DeleteBdfFile();

    std::vector<Vector> RebuildNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode,std::vector<Vector> v_Node);
    void RebuildOriginalGlobalNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode);

    void UpdateSubMeshAndParallelBoundary();

    void SetV_wallboundaryID(std::vector<int> v_wallboundaryID_,std::multimap<int, std::vector<int>> map_data_,int ncsdt_);

    void BuildMapRelation();

    void WriteLog(const std::string& message);

    int CopyFile();

    void gettime();

    void cgns2cgns( std::vector<Node> v_globalNode);

    std::vector<Node> FindAndGatherGlobalNode();

    void CoordTransfCSDToCFD();

    void RBFSInSolid();

    Eigen::Vector3d local_to_global(Vector &local_displacement_,Vector &A_, Vector &B_ ,Vector &C_);

    Vector rotate_and_translate_fem_coordinates(Eigen::Vector3d &global_displacement,Eigen::Vector3d &translation_vector, std::string &RwingDir, std::string &FuseDir,int &index);

    std::vector<std::vector<double>> Lapack_degv(std::vector<std::vector<double>> css,int n);

    void OutMaxDefom();

    void OutFinalCgns();

    Node RebuildCSDNode(std::vector<Vector> v_weight,std::vector<Vector> v_wallNode, Node &node, double &R_deform);

    std::vector<Vector> ReadPreviousDeformationFromWallDat(int id_aero);

    bool FileExists(const std::string& wtfilename);

    private:	
      	
    SubMesh *subMesh;

    SubMesh *globalMesh;  

    std::vector< struct Solver::AeroMeshData> v_aeroMeshData; //气动数据结构体数组
    std::vector<struct Solver::SolidMeshData> v_solidMeshData; //结构数据结构体数组
    const Package::FlowPackage *data;

    //bool flag;   

    int ncsdt;

    std::vector<int> v_wallboundaryID; 
    std::multimap<int, std::vector<int>> map_data; //当前分区下，所有部件点(globalID)所属流场数据包的位置

    std::vector<std::vector<std::vector<double>>> hm_all; //存储全部部件的转换矩阵

#ifdef _Supports_CXX11_
    std::unordered_map<int, std::map<int, int>> map_part;//部件的相交点所属流场数据包的位置（用于对各部件交线处的变形量作平均 ）
    std::unordered_map<int, std::map<int, int>> map_allWallNode; //所有部件点所属流场数据包的位置（一对一或一对多）
#else
    std::map<int, std::map<int, int>> map_part;//部件的相交点所属流场数据包的位置（用于对各部件交线处的变形量作平均 ）
    std::map<int, std::map<int, int>> map_allWallNode; //所有部件点所属流场数据包的位置（一对一或一对多）
#endif

    int npart;
    int processorID;
    double pressure_ref;
    std::vector<Scalar> v_maxDeform;

    std::vector<Node> v_subNode;


    //int LoopStep;

#if defined(_BaseParallelMPI_)
    mpi::communicator mpi_world;
#endif 

};
#endif


