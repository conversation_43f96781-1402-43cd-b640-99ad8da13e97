﻿#include "feilian-specialmodule/staticAeroelastic/meshDeform/A_RBFGreedyAlg.h"

RBFGreedyAlg::RBFGreedyAlg(std::vector<Node> &v_wallNode_,std::vector<Node> &v_wallNode_deform_,Scalar &R_)
					:v_wallNode(v_wallNode_), v_wallNode_deform(v_wallNode_deform_),R(R_)
{
	  limx = 1.0e-4;
	  n_wallNode =  v_wallNode.size();
	  cycle = 0;  

}

RBFGreedyAlg::~RBFGreedyAlg()
{
}

void RBFGreedyAlg::rbfGreedy()
{
   	AllocateDim(Matrix_all, n_wallNode, n_wallNode);
	  AllocateDim(v_wallDelta_all, n_wallNode);

	  for (int i = 0; i < n_wallNode; i++)
	  {
		  for (int j = 0; j < n_wallNode; j++)
		  {
			 Scalar yita = sqrt(pow((v_wallNode[j].X() - v_wallNode[i].X()), 2)
			  	+ pow((v_wallNode[j].Y() - v_wallNode[i].Y()), 2)
			  	+ pow((v_wallNode[j].Z() - v_wallNode[i].Z()), 2)) / R;

			  if (yita > 1.0)
			  {
			  	Matrix_all[i][j] = 0.0;
			  }
			  else
			  {
			  	Matrix_all[i][j] = pow((1 - yita), 4)*(4 * yita + 1.0);
			  }

	  	}
	  }
	  
	  for (int i = 0; i < v_wallNode.size(); i++)
	   {
 
		   v_wallDelta_all[i].SetX( v_wallNode_deform[i].X() - v_wallNode[i].X());
		   v_wallDelta_all[i].SetY( v_wallNode_deform[i].Y() - v_wallNode[i].Y());
		   v_wallDelta_all[i].SetZ( v_wallNode_deform[i].Z() - v_wallNode[i].Z());

	  }
}

 std::vector<Vector> RBFGreedyAlg:: DirectGreedyAlg()
{
	
	//Print("\t物面点数大于10000，使用贪心算法求RBF权重系数..\n");

	std::vector<Node> v_node1;
	std::vector<Node> v_node2;
	std::vector<Vector> v_wallDelta1;
	std::vector<Vector> v_wallDelta2;
	std::vector<std::vector<Scalar>> Matrix;
	std::vector<Vector> v_weight;
	std::vector<Vector> v_weightall;

	std::vector<int> nlim;
	std::vector<int> n_weightnum;
	std::vector<Scalar> lim;
	std::vector<Scalar> fai;

	Scalar yita;

	Node Delta;
	Node xyz;

	int n = v_wallNode.size();
	int n_node = 100;
	int n0 = 5;

	AllocateDim(v_weightall, n);

	AllocateDim(nlim, n_node);
	AllocateDim(lim, n_node);

	lim[0] = R;
	int n1 = n / n0;

	AllocateDim(v_node1, n1);
	AllocateDim(v_wallDelta1, n1);
	AllocateDim(v_wallDelta2, n1);

	for (int i = 0; i < n1; i++)            
	{
		v_node1[i] = v_wallNode[i*n0];
		v_wallDelta2[i] = v_wallDelta_all[i*n0];
		v_wallDelta1[i] = v_wallDelta2[i];
		n_weightnum.push_back(i*n0);
	}

	while (lim[0] > limx)  
	{
		AllocateDim(Matrix, n1, n1);

			for (int i = 0; i < n1; i++)
			{
				for (int j = i; j < n1; j++)
				{
					yita = sqrt(pow((v_node1[j].X() - v_node1[i].X()), 2)
						+ pow((v_node1[j].Y() - v_node1[i].Y()), 2)
						+ pow((v_node1[j].Z() - v_node1[i].Z()), 2)) / R;

					if (yita > 1.0)
					{
						Matrix[i][j] = 0.0;
						Matrix[j][i] = 0.0;
					}
					else
					{
						Matrix[i][j] = pow((1 - yita), 4)*(4 * yita + 1.0);
						Matrix[j][i] = Matrix[i][j];
					}
				}
			}

			AllocateDim(v_weight, n1);
			v_weight = Gaussin_L(Matrix, v_wallDelta1);

			Matrix.clear();

			AllocateDim(fai, n1);

			for (int i = 0; i < n_node; i++)
			{
				lim[i] = 0.0;
			}

			for (int i = 0; i < n; i++)
			{
				Delta = Vector0;
				yita = 0.0;
				Node &nodeTemp1 = v_wallNode[i];

				for (int j = 0; j < n1; j++)
				{
					Node nodeTemp2 = v_node1[j];   //物面坐标点 
					yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2)
						+ pow((nodeTemp2.Y() - nodeTemp1.Y()), 2)
						+ pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R;

					if (yita > 1.0)
					{
						fai[j] = 0.0;
					}
					else
					{
						fai[j] = pow((1 - yita), 4)*(4 * yita + 1);
					}

				}
				for (int j = 0; j < n1; j++)
				{
					Delta.SetX( Delta.X() + v_weight[j].X() * fai[j] );
					Delta.SetY( Delta.Y() + v_weight[j].Y() * fai[j] );
					Delta.SetZ( Delta.Z() + v_weight[j].Z() * fai[j] );
				}

				xyz.SetX( fabs(v_wallDelta_all[i].X() - Delta.X()) );
				xyz.SetY( fabs(v_wallDelta_all[i].Y() - Delta.Y()) );
				xyz.SetZ( fabs(v_wallDelta_all[i].Z() - Delta.Z()) );

				//求x、y、z中最大值

				if (xyz.X() < xyz.Y())
				{
					xyz.SetX( xyz.Y() );
				}

				if (xyz.X() < xyz.Z())
				{
					xyz.SetX( xyz.Z() );
				}

				for (int j = 0; j<n_node; j++)
				{

					if (xyz.X() > lim[j])
					{
						for (int k = n_node - 1; k>j; k--)  //!!
						{
							lim[k] = lim[k - 1];
							nlim[k] = nlim[k - 1];
						}
						lim[j] = xyz.X();
						nlim[j] = i;
						break;
					}
				}
			}

			Print("\t循环.." + std::to_string((long double)lim[0]) + "......" + std::to_string((long long int)n1));

			if (lim[0]>limx)
			{
				AllocateDim(v_node2, n1 + n_node);

				for (int i = 0; i<n1; i++)
				{
					v_node2[i] = v_node1[i];
				}

				v_node1.clear();
				v_wallDelta1.clear();
				AllocateDim(v_wallDelta1, n1 + n_node);

				for (int i = 0; i<n1; i++)
				{
					v_wallDelta1[i] = v_wallDelta2[i];
				}

				v_wallDelta2.clear();

				for (int i = 0; i<n_node; i++)
				{
					if (lim[i] > 1.0e-7)
					{
						v_node2[n1 + i] = v_wallNode[nlim[i]];
						v_wallDelta1[n1 + i] = v_wallDelta_all[nlim[i]];
						n_weightnum.push_back(nlim[i]);
					}
					else
					{
						n_node = i + 1;
						break;
					}
				}

				n1 += n_node;

				AllocateDim(v_node1, n1);
				AllocateDim(v_wallDelta2, n1);

				for (int i = 0; i<n1; i++)
				{
					v_node1[i] = v_node2[i];
					v_wallDelta2[i] = v_wallDelta1[i];
				}

				v_wallDelta1.clear();
				AllocateDim(v_wallDelta1, n1);

				for (int i = 0; i<n1; i++)
				{
					v_wallDelta1[i] = v_wallDelta2[i];
				}

				v_node2.clear();
				fai.clear();
				v_weight.clear();

			}
		
       }

	   Print("\tv_weight求解结束.." );

	   v_wallDelta2.clear();

	   for (int i = 0; i <n_wallNode; i++)
	   {
		   v_weightall[i] = Vector0;
	   }

	   for (int i = 0; i < n_weightnum.size(); i++)
	   {
		   v_weightall[n_weightnum[i]].SetX( v_weight[i].X());
		   v_weightall[n_weightnum[i]].SetY( v_weight[i].Y());
		   v_weightall[n_weightnum[i]].SetZ( v_weight[i].Z());
	   }

	   v_weight.clear();

	   return v_weightall;

}

std::vector<Vector> RBFGreedyAlg::GreedyAlgImp()
{
		std::vector<Vector> v_weight;
		std::vector<int> nlim;
		std::vector<Scalar> lim;
		std::vector<Scalar> fai;

		Scalar yita;
		Node Delta;
		Node xyz;
		Scalar max;

		int n = v_wallNode.size();

		std::vector<Vector> v_Delta;
		std::vector<Vector> v_delta;	

		AllocateDim(lim, n);
		AllocateDim(nlim, n);

		AllocateDim(v_weight, n);	
		AllocateDim(v_wallDelta, n);
		
		for (int i = 0; i < n; i++)
		 {
			v_weight[i] = Vector0;
			v_wallDelta[i] = v_wallDelta_all[i];
		 }
		lim[0] = R;

		Print("\t使用基于贪心法的空间子集逼近求RBF权重系数....\n");

		//根据误差循环求解
		while (lim[0] > limx)
		 {
			cycle++;

			AllocateDim(v_delta, n);

			for (int i = 0; i < n; i++)
			{
				lim[i] = 0.0;
				v_delta[i] = Vector0;
			}

			AllocateDim(v_weightM, n);
			GreedyAlgorithm();
	
			for (int i = 0; i < n; i++)
			{
			   for (int j = 0; j < n; j++)
				{
					v_delta[i].SetX(  v_delta[i].X() + Matrix_all[i][j] * v_weightM[j].X() );
					v_delta[i].SetY(  v_delta[i].Y() + Matrix_all[i][j] * v_weightM[j].Y() );
					v_delta[i].SetZ(  v_delta[i].Z() + Matrix_all[i][j] * v_weightM[j].Z() );
				}
			}
				
			AllocateDim(v_Delta, n);

			for (int i = 0; i < n; i++)
			{
				v_Delta[i].SetX( v_wallDelta[i].X() - v_delta[i].X() );
				v_Delta[i].SetY( v_wallDelta[i].Y() - v_delta[i].Y() );
				v_Delta[i].SetZ( v_wallDelta[i].Z() - v_delta[i].Z() );

				if (fabs(v_Delta[i].X()) > fabs(v_Delta[i].Y()))
				{
					max = fabs(v_Delta[i].X());
				}
				else
				{
					max = fabs(v_Delta[i].Y());
				}

				if (max < fabs(v_Delta[i].Z()))
				{
					max = fabs(v_Delta[i].Z());
				}

				//判断物面节点最大误差值，并排序
				for (int j = 0; j<n; j++)
				{
					if (max > lim[j])
					{
						for (int k = n - 1; k>j; k--)
						{
							lim[k] = lim[k - 1];
							nlim[k] = nlim[k - 1];
						}
						lim[j] = max;
						nlim[j] = i;

						break;
					}
				}


			}

			v_wallDelta.clear();
			AllocateDim(v_wallDelta, n);

			for (int i = 0; i < n; i++)
			{
				v_wallDelta[i].SetX( v_Delta[i].X() );
				v_wallDelta[i].SetY( v_Delta[i].Y() );
				v_wallDelta[i].SetZ( v_Delta[i].Z() );
			}

			for (int i = 0; i < n; i++)
			{
				v_weight[i].SetX(  v_weight[i].X() + v_weightM[i].X() );
				v_weight[i].SetY(  v_weight[i].Y() + v_weightM[i].Y() );
				v_weight[i].SetZ(  v_weight[i].Z() + v_weightM[i].Z() );
			}

			v_Delta.clear();
			v_delta.clear();
			v_weightM.clear();

			Print("\t循环.." + std::to_string((long long int)cycle) + "......" + std::to_string((long double)lim[0]));

	}
			
			Print("\t算法结束..............\n");

			lim.clear();
			nlim.clear();

			return v_weight;
}

void RBFGreedyAlg::GreedyAlgorithm()
{

	std::vector<Node> v_node1;
	std::vector<Node> v_node2;
	std::vector<Vector> v_wallDelta1;
	std::vector<Vector> v_wallDelta2;
	std::vector<Vector> v_weightG;
	std::vector<std::vector<Scalar>> Matrix;

	std::vector<int> nlim;
	std::vector<int> n_weightID;
	std::vector<Scalar> lim;
	std::vector<Scalar> fai;

	std::vector<int> num_rand;

	Scalar yita;
	int a;
	Node Delta;
	Node xyz;

	int n = v_wallNode.size();
	int n_node = 300;
	int n1 = 500;

	AllocateDim(nlim, n_node);
	AllocateDim(lim, n_node);

	lim[0] = 1.0;

	AllocateDim(v_node1, n1);
	AllocateDim(v_wallDelta1, n1);
	AllocateDim(v_wallDelta2, n1);


	for (int i = 0; i < v_wallNode.size(); i++)
	{
		num_rand.push_back(i);
	}
	std::random_shuffle(num_rand.begin(), num_rand.end());

	for (int i = 0; i < n1; i++)
	{
		a = num_rand[i];
		v_node1[i] = v_wallNode[a];
		v_wallDelta2[i] = v_wallDelta[a];
		v_wallDelta1[i] = v_wallDelta2[i];
		n_weightID.push_back(a);

	}

	while (lim[0]>limx)
	{

		AllocateDim(Matrix, n1, n1);

		for (int i = 0; i < n1; i++)
		{
			for (int j = i; j < n1; j++)
			{

				yita = sqrt(pow((v_node1[j].X() - v_node1[i].X()), 2)
					+ pow((v_node1[j].Y() - v_node1[i].Y()), 2)
					+ pow((v_node1[j].Z() - v_node1[i].Z()), 2)) / R;

				if (yita > 1.0)
				{
					Matrix[i][j] = 0.0;
					Matrix[j][i] = 0.0;
				}
				else
				{
					Matrix[i][j] = pow((1 - yita), 4)*(4 * yita + 1.0);
					Matrix[j][i] = Matrix[i][j];
				}
			}
		}
		//矩阵求解
		AllocateDim(v_weightG, n1);
		v_weightG = Gaussin_L(Matrix, v_wallDelta1);

		//误差求解
		Matrix.clear();
		AllocateDim(fai, n1);

		for (int i = 0; i < n_node; i++)
		{
			lim[i] = 0.0;
		}

		for (int i = 0; i < n; i++)
		{
			Delta = Vector0;
			yita = 0.0;
			Node nodeTemp1 = v_wallNode[i];

			for (int j = 0; j < n1; j++)
			{
				Node nodeTemp2 = v_node1[j];   //物面坐标点 
				yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2)
					+ pow((nodeTemp2.Y() - nodeTemp1.Y()), 2)
					+ pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R;

				if (yita > 1.0)
				{
					fai[j] = 0.0;
				}
				else
				{
					fai[j] = pow((1 - yita), 4)*(4 * yita + 1);
				}

			}

			for (int j = 0; j < n1; j++)
			{
				Delta.SetX( Delta.X() + v_weightG[j].X() * fai[j] );
				Delta.SetY( Delta.Y() + v_weightG[j].Y() * fai[j] );
				Delta.SetZ( Delta.Z() + v_weightG[j].Z() * fai[j] );
			}

			xyz.SetX( fabs(Delta.X() - v_wallDelta[i].X()) );
			xyz.SetY( fabs(Delta.Y() - v_wallDelta[i].Y()) );
			xyz.SetZ( fabs(Delta.Z() - v_wallDelta[i].Z()) );

			//求x、y、z中最大值

			if (xyz.X() < xyz.Y())
			{
				xyz.SetX( xyz.Y() );
			}

			if (xyz.X() < xyz.Z())
			{
				xyz.SetX( xyz.Z() );
			}

			//判断物面节点最大误差值，并排序
			for (int j = 0; j<n_node; j++)
			{
				if (xyz.X()>lim[j])
				{
					for (int k = n_node - 1; k>j; k--)
					{
						lim[k] = lim[k - 1];
						nlim[k] = nlim[k - 1];
					}
					lim[j] = xyz.X();
					nlim[j] = i;

					break;
				}
			}


		}

		Print("\t使用贪心算法求RBF权重系数...............");
		Print("\t循环.." + std::to_string((long double)lim[0]) + "......" + std::to_string((long long int)n1));

		//根据误差值及其他条件在集合中加入物面节点
		//不满足误差线及集合点数小于1000
		if (lim[0] > limx && n1 + n_node < 1001)
		{

			AllocateDim(v_node2, n1 + n_node);

			for (int i = 0; i<n1; i++)
			{
				v_node2[i] = v_node1[i];
			}

			v_node1.clear();
			v_wallDelta1.clear();
			AllocateDim(v_wallDelta1, n1 + n_node);

			for (int i = 0; i<n1; i++)
			{
				v_wallDelta1[i] = v_wallDelta2[i];
			}

			v_wallDelta2.clear();

			for (int i = 0; i<n_node; i++)

			{
				if (lim[i] > limx)
				{
					v_node2[n1 + i] = v_wallNode[nlim[i]];
					v_wallDelta1[n1 + i] = v_wallDelta[nlim[i]];
					n_weightID.push_back(nlim[i]);
				}
				else
				{
					n_node = i;
					break;
				}

			}

			n1 += n_node;

			AllocateDim(v_node1, n1);
			AllocateDim(v_wallDelta2, n1);

			for (int i = 0; i<n1; i++)
			{
				v_node1[i] = v_node2[i];
			}

			for (int i = 0; i<n1; i++)
			{
				v_wallDelta2[i] = v_wallDelta1[i];
			}

			v_wallDelta1.clear();
			AllocateDim(v_wallDelta1, n1);

			for (int i = 0; i<n1; i++)
			{
				v_wallDelta1[i] = v_wallDelta2[i];
			}

			v_node2.clear();
			fai.clear();
			v_weightG.clear();

		}
		//满足误差线及集合点数小于1000
		else if (lim[0] < limx && n1 + n_node < 1001)
		{
			break;
		}
		//不满足误差线及集合点数大于1000
		else if (lim[0] > limx && n1 + n_node >1001)
		{
			break;
		}
		//满足误差线及集合点数大于1000
		else if (lim[0] < limx && n1 + n_node >1001)
		{
			break;
		}

	}

	Print("\tv_weight求解结束..");

	for (int i = 0; i < n; i++)
	{
		v_weightM[i] = Vector0;
	}

	for (int i = 0; i < n_weightID.size(); i++)
	{
		v_weightM[n_weightID[i]] = v_weightG[i];
	}

	v_weightG.clear();
	n_weightID.clear();

}

std::vector<Vector> RBFGreedyAlg::DirectRBFAlg()
{
	std::vector<Vector> v_weight;
	
	v_weight = Gaussin_L(Matrix_all, v_wallDelta_all);
	
	return v_weight;
}