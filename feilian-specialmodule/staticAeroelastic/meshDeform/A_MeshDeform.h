﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------�й����չ�ҵ���������о�Ժ------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshDeform.h
//! <AUTHOR>
//! @brief ��������
//! @date 2022-02-14
//
//------------------------------�޸���־----------------------------------------
//------------------------------------------------------------------------------
#ifndef _specialModule_staticAeroelastic_meshDeform_MeshDeform_
#define _specialModule_staticAeroelastic_meshDeform_MeshDeform_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <array>


#include "feilian-specialmodule/staticAeroelastic/meshDeform/A_RBFGreedyAlg.h"
#include "basic/common/AllocateArray.h"
#include "basic/mesh/MeshSupport.h" 
#include "basic/common/Vector.h"  
#include "basic/postTools/Tecplot.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

/*extern "C" {
	     int numroc_(int*, int*, int*, int*, int*);
       void descinit_(int*, int*, int*, int*, int*, int*, int*, int*, int*, int*);	
}*/

class MeshDeform
{
    public:
    
     MeshDeform(std::vector<Node> &v_wallNode_,std::vector<Node> &v_wallNode_deform_, Scalar &R_ );

		~MeshDeform();
	        
		void Process();
			
		void ScalapackDeform();
    
    inline const std::vector<Vector> GetV_weight()const  { return this->v_weight; } 
    	 
    private:	

		std::vector<Vector> v_weight;		  	      
		  
		std::vector<Node> &v_wallNode;
		
		std::vector<Node> &v_wallNode_deform;
			
		Scalar &R ;	
			
		
#if defined(_BaseParallelMPI_)
    mpi::communicator mpi_world;
#endif 
			
	      
};

#endif 