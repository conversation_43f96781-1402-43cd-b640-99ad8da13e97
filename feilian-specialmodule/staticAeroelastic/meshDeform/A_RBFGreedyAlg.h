﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------�й����չ�ҵ���������о�Ժ------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file MeshDeform.h
//! <AUTHOR>
//! @brief ����̰���㷨��RBFs��
//! @date 2022-02-14
//
//------------------------------�޸���־----------------------------------------
//------------------------------------------------------------------------------
#ifndef _specialModule_staticAeroelastic_meshDeform_RBFGreedyAlg_
#define _specialModule_staticAeroelastic_meshDeform_RBFGreedyAlg_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>

#include "basic/mesh/MeshSupport.h"
#include "basic/common/Vector.h"
#include "basic/common/AllocateArray.h"
#include "basic/common/InterpolationTools.h"


class RBFGreedyAlg
{
public:
	RBFGreedyAlg(std::vector<Node> &v_wallNode_,std::vector<Node> &v_wallNode_deform_,Scalar &R_);

	// ��������
	~RBFGreedyAlg();

	//ֱ��ʹ��̰���㷨���Ȩ��ϵ��
	std::vector<Vector> DirectGreedyAlg();

	//����̰�ķ��Ŀռ��Ӽ��ƽ��㷨���Ȩ��ϵ��
	std::vector<Vector> GreedyAlgImp();

	//̰�ķ�
	void GreedyAlgorithm();
	
	//ֱ�����Ȩ��ϵ��
	std::vector<Vector> DirectRBFAlg();
		
	void rbfGreedy();

private:

	// �������������
	std::vector<Vector> v_wallDelta_all;

	// �������������
	std::vector<Vector> &v_wallNode;
		
	// ���κ���������������
	std::vector<Vector> &v_wallNode_deform;

	//�������
	std::vector<std::vector<Scalar>> Matrix_all;

	//ȫ������ϵ��
	std::vector<Vector> v_weightM;

	std::vector<Vector> v_wallDelta;

	//�����������	
	int n_wallNode;

	//�����
	Scalar limx;

	Scalar &R;

	int cycle ;


};

#endif 