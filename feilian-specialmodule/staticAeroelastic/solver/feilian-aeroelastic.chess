#!/bin/bash
#BSUB -J test
#BSUB -R 'span[ptile=76]'
#BSUB -n 8
#BSUB -W 72:00
#BSUB -q batch
#BSUB -e %J.err
#BSUB -o %J.log
##BSUB -x

# 并行资源调用参数
WORK_DIR=`pwd`
BASE_FILE=$(echo $WORK_DIR|awk -F'/' '{print $NF}')
echo ${LSB_MCPU_HOSTS} | sed 's/ /\n/g' |grep node > node.${LSB_JOBID}
NN=`cat node.${LSB_JOBID}|wc -l`
NP=${LSB_DJOB_NUMPROC}
nodelist=node.${LSB_JOBID}
# 打印信息文件
export OUT_FILE=screen_${LSB_JOBID}.out

# 待修改参数
export XML_FILE=default.xml

# 求解器路径
export FEILIAN_HOME=/home/<USER>/guochengpeng/2025-1-26/feilian
export PATH=/home/<USER>/MSC/MSC_Nastran/20121/bin:$PATH
export PATH=$FEILIAN_HOME/bin:$PATH

/usr/bin/python3 $FEILIAN_HOME/ARI_FeiLian/FeiLian_Aeroelastic.py \
    --xmlfile $XML_FILE \
    --machinefile $nodelist \
    --np $NP > $OUT_FILE
