﻿#include "feilian-specialmodule/staticAeroelastic/solver/AeroStaticSolver.h"


AeroStaticSolver::AeroStaticSolver( SubMesh *subMesh_, Configure::Flow::FlowConfigure &flowConfigure_)
    : subMesh(subMesh_), flowConfigure(flowConfigure_)
{

}

AeroStaticSolver::~AeroStaticSolver()
{   
}

void AeroStaticSolver::Initialize()
{
    MPIBarrier();

    if (GetMPIRank() == 0) Print("\nInitialize flow fields ...");

    // 读全局网格
    SubMesh *globalMesh = new SubMesh();
    
    if (GetMPIRank() == 0)
    {
        std::string temp;
        
        const auto &meshDim = flowConfigure.GetMeshParameters().dimension;
        const auto &meshPath = flowConfigure.GetStaticAero().CFDParameter.meshPath[0];
        const auto &fileName = flowConfigure.GetStaticAero().CFDParameter.fileName[0];
        const auto &meshType = flowConfigure.GetStaticAero().CFDParameter.meshType[0];
        const auto &meshTransform = flowConfigure.GetStaticAero().CFDParameter.meshTransform;

        MeshConvertManager meshConvertManager(meshPath+fileName, meshType, meshDim, globalMesh,  meshTransform);
        meshConvertManager.ReadMesh(true);
        meshConvertManager.BuildTopology();
    }

    //**创建流场解算器对象及初始化
    outerLoop = new OuterLoop(subMesh, flowConfigure);
    outerLoop->Initialize();
    const Package::FlowPackage *data = outerLoop->GetFlowPackage();

    //**创建AeroStatic_Data对象，对气动数据和结构数据结构体赋值
    //**subMesh：存储分区后的网格相关信息；SolidMeshName:结构文件名称,例如xyzfp_id.plt
    Vector scale = flowConfigure.GetStaticAero().CSDParameter.scale;
    aeroStatic_Data = new AeroStaticData(subMesh , flowConfigure,scale);  
    aeroStatic_Data->FindAeroMeshData();
    aeroStatic_Data->ReadSolidMeshData();
    aeroStatic_Data->checkdat();
    
    //**创建方法类，并计算各部件转换矩阵hm
    std::vector<struct Solver::AeroMeshData> aeroMeshData = aeroStatic_Data->GetAeroMeshData();
    std::vector<struct Solver::SolidMeshData> solidMeshData = aeroStatic_Data->GetSolidMeshData();
    aeroStatic_Method = new AeroStaticMethod(aeroMeshData, solidMeshData, subMesh, data,globalMesh);
    std::vector<int> v_wallboundaryID = aeroStatic_Data->GetV_wallboundaryID();
    std::multimap<int, std::vector<int>> map_data  = aeroStatic_Data->Getmapdata();
    int ncsdt = aeroStatic_Data->GetNcsdt();
    aeroStatic_Method->SetV_wallboundaryID(v_wallboundaryID,map_data,ncsdt);  
    aeroStatic_Method->calculateCouplmat();
    // 下面这个打印语句在windows下很重要，如果使用python驱动气弹计算，没有这个打印语句会突然跳出程序，原因不明
	if (GetMPIRank() == 0) std::cout << "Initialize flow fields finished!" << std::endl;

}

void AeroStaticSolver::Process()
{
    if (GetMPIRank() == 0) Print("\nBegin to solve ...");
    SystemTime time, timeTotal;
    timeTotal.UpdateTime();

    // Step1:流场求解
    time.UpdateTime();
    outerLoop->Solve();
    if (GetMPIRank() == 0) Print("流场求解耗时：" + ToString(time.GetElapsedTime()));

    // Step2:计算耦合界面气动力
    time.UpdateTime();
    aeroStatic_Method->Calcaeroforce();

    // Step3:由转换矩阵hm及气动力计算结构载荷
    aeroStatic_Method->CalcuateForceSolid();

    // Step4:将分部件结构载荷写入到Force.dat文件中
    aeroStatic_Method->WriteForceSolid();

    // Step5:调用Nastran分析，并解析f06文件
    aeroStatic_Method->UseNastAndReadF06();

    // Step6:由结构变形计算气动变形，并对各部件交线处作平均
    aeroStatic_Method->CalcuateAeroDeform();
    if (GetMPIRank() == 0) Print("流固耦合数据交换耗时：" + ToString(time.GetElapsedTime()));

    // Step7:输出最大变形量
    aeroStatic_Method->OutMaxDefom();

    // Step8:ReMesh_Flag = false，调用网格变形模块并输出变形后的cgns网格
    //****** ReMesh_Flag = true, 直接输出surf,外部调用aflr3软件生成网格
    if (flowConfigure.GetStaticAero().CFDParameter.ReMesh_Flag)
    {
        aeroStatic_Method->OutSurfFile(); 
    }
    else{
        time.UpdateTime();
        aeroStatic_Method->ProcessMeshDeform();
        if (GetMPIRank() == 0) Print("气动体网格变形耗时：" + ToString(time.GetElapsedTime()));       
    }

    if (GetMPIRank() == 0) Print("静气动弹性计算总耗时：" + ToString(timeTotal.GetElapsedTime()));
}

