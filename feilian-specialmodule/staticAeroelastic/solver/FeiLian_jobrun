#!/bin/bash 
########################################################
#
#           FILE: Fei<PERSON><PERSON>_jobruan
#          USAGE: Fei<PERSON>ian_jobruan case.xml nodefile NP
#
#    DESCRIPTION:     
#        OPTIONS: -----
#   REQUIREMENTS: -----
#           BUGS: -----
#          NOTES: -----
#         AUTHOR: <PERSON>, <PERSON>
#   ORGANIZATION: AVIC
#        CREATED: 2025.1.15
#  LAST MODIFIED: 2025.2.17
#       REVISION: v1.1
#
########################################################


#判断输入参数
if [ $# -eq 3 ];then
	XML_FILE=$1
	####hostfile文件需要在当前目录
	HOST_FILE=$2
	NP_CORE=$3
else
	echo "Usage: Fei<PERSON><PERSON>_jobruan case.xml nodefile  20"
 	echo "                         |        |       |"
 	echo "                         |        |    并行核数"
 	echo "                         |        |"
 	echo "                         |     节点信息"
 	echo "                         参数文件"
 	exit	
fi


##定义软件路径
export FEILIAN_HOME=/gpfs/software/FeiLian_1.1.7.18

export PATH=$FEILIAN_HOME/bin:$PATH
export LD_LIBRARY_PATH=$FEILIAN_HOME/lib:$LD_LIBRARY_PATH

export PATH=$FEILIAN_HOME/mpich/bin:$PATH
export LD_LIBRARY_PATH=$FEILIAN_HOME/mpich/lib:$LD_LIBRARY_PATH

NASTRAN_HOME=/gpfs/software/nastran/nastran2014
export PATH=$NASTRAN_HOME/bin:$PATH

export AUTHINFO=27500@mgt01
# ulimit -m 899617304
# ulimit -s 8192
# ulimit -c 0

/usr/bin/python3 $FEILIAN_HOME/FeiLian_Aeroelastic.py \
    --xmlfile $XML_FILE \
    --machinefile $HOST_FILE \
    --np $NP_CORE > Screen.out

