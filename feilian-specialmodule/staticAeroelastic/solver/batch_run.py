﻿import os
import platform
import sys
import shutil
import glob
import FeiLian_Aeroelastic

# 默认环境变量，可以在外部设置一些环境变量
feilian_path = r'D:\Apps\HiFNLSAE_1.1.8.6'
python_path = r'D:\Apps\HiFNLSAE_1.1.8.6\python'
nastran_path = r'C:\MSC.Software\MSC_Nastran\20170\bin'
# 更新PATH环境变量
os.environ['PATH'] = f'{feilian_path};{python_path};{nastran_path};{os.environ.get("PATH", "")}'
os.putenv('PATH', os.environ['PATH'])
####################################################################################


# 查找必要的可执行程序是否都存在
exe = ['mpiexec', 'mainAeroStatic', 'flowForceNondimensionalize', 'aflr3', 'aflr3_to_cgns', 'flowPreprocessor']
tmp = None
for i in range(6):
    tmp = shutil.which(exe[i])
    if tmp is None:
        print(f"错误：{exe[i]}不存在，请检查可执行程序路径！")
        sys.exit()

# 对于Windows系统可以查找nastran，但Linux系统查找的不固定，可能是nast20141
nastexe = ['nastran', 'nast20121', 'nast20141', 'nast20141', 'nast20171', 'nast20191']
tmp = None
for i in range(6):
    tmp = shutil.which(nastexe[i])
    if tmp is not None:
        final = tmp
        break
if tmp is None:
    print(f"错误：{nastexe} 没有找到！")
    sys.exit()


def extract_partition_number(file_path):
    """
    从指定的XML文件中提取<partitionNumber>和</partitionNumber>之间的整型数字。
    
    Args:
        file_path (str): XML文件路径。
    
    Returns:
        int: 提取到的partitionNumber值。如果文件未找到、标签不存在或格式错误，则返回None。
    """
    try:
        # 打开并读取XML文件
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # 定位到<partitionNumber>的起始位置
        start_tag = "<partitionNumber>"
        end_tag = "</partitionNumber>"

        start_index = content.find(start_tag)
        if start_index == -1:
            print("未找到<partitionNumber>标签!")
            return None

        start_index += len(start_tag)  # 移动到标签结束后的位置

        end_index = content.find(end_tag, start_index)
        if end_index == -1:
            print("未找到</partitionNumber>标签!")
            return None

        # 提取数字字符串
        number_str = content[start_index:end_index].strip()

        # 尝试将其转换为整数
        try:
            number = int(number_str)
            return number
        except ValueError:
            print("提取的内容不是有效的整数!")
            return None

    except FileNotFoundError:
        print(f"文件 {file_path} 未找到!")
        return None
    except Exception as e:
        print(f"发生了错误：{str(e)}")
        return None


# 示例使用：
# number = extract_partition_number('default.xml')
# if number is not None:
#     print(f"提取到的partitionNumber值为：{number}")


def copy_files(src, dst):
    # 遍历源目录中的所有文件和子目录
    for root, dirs, files in os.walk(src):
        # 计算相对于 src 的路径，用于构建目标路径
        rel_path = os.path.relpath(root, src)

        # 构建目标路径
        dst_path = os.path.join(dst, rel_path)

        # 创建目标目录（如果不存在）
        if not os.path.exists(dst_path):
            os.makedirs(dst_path, exist_ok=True)

        # 复制文件
        for file in files:
            src_file = os.path.join(root, file)
            dst_file = os.path.join(dst_path, file)

            # 检查文件后缀
            if file.endswith('.cgns') or file.endswith('.bMesh'):
                try:
                    try:
                        os.remove(dst_file)
                        print(f"文件{dst_file}已经存在，先删除")
                        os.link(src_file, dst_file)
                        print(f"创建硬链接：{src_file} -> {dst_file}")
                    except FileNotFoundError:
                        # 使用硬链接替代复制
                        os.link(src_file, dst_file)
                        print(f"创建硬链接：{src_file} -> {dst_file}")
                    except PermissionError:
                        print(f"无权限删除{dst_file}文件，请手动解决")
                        sys.exit()
                    except Exception as e:
                        print(f"删除{dst_file}过程中，发生意外，请手动解决")
                        sys.exit()

                except OSError as e:
                    print(f"无法创建硬链接 '{dst_file}'：{e}")
                    sys.exit()
            else:
                # 对于其他类型的文件，使用复制
                try:
                    shutil.copy2(src_file, dst_file)
                    print(f"复制文件：{src_file} -> {dst_file}")
                except Exception as e:
                    print(f"复制文件 '{file}' 失败：{e}")
                    sys.exit()


# 先将common中的案例进行前处理
os.chdir('common')
# 获取参数文件xml文件名
xml_files = glob.glob("*.xml")
xml_files = [f for f in xml_files if f != "boundary.xml"]
if len(xml_files) == 1:
    XML_FILE = xml_files[0]
else:
    print(f"错误：有多个参数文件{xml_files}, 请删除common文件夹中多余的xml参数文件，注意不要删除boundary.xml")
    sys.exit()
# 执行前处理
command = ' '.join(["flowPreprocessor", XML_FILE])
try:
    os.system(command)
except:
    print("错误：执行错误")
    sys.exit()
os.chdir('..')

# 先将所有的工况文件夹都准备好所有的文件
src_dir = 'common'
for dir_name in [d for d in os.listdir('.') if d.startswith('Ma') and os.path.isdir(d)]:
    print(f'=== 处理文件夹：{dir_name} ===')
    copy_files(src_dir, dir_name)
print("所有文件和子目录已成功复制到 Ma1 文件夹中。")


# 遍历以"Ma"开头的目录
for dir_name in [d for d in os.listdir('.') if d.startswith('Ma') and os.path.isdir(d)]:
    print(f'=== 进入文件夹：{dir_name} ===')

    # 进入目标目录
    current_dir = os.getcwd()
    target_dir = os.path.join(current_dir, dir_name)
    os.chdir(target_dir)

    # 获取并行核数
    np = extract_partition_number(f'{dir_name}.xml')
    if np is None:
        print(f"错误：并行核数partitionNumber设置错误, 退出!")
        sys.exit()

    # 开始计算
    try:
        FeiLian_Aeroelastic.run(f'{dir_name}.xml', 'nodefile', str(np))
    except:
        print(f'运行脚本时发生错误')
    finally:
        # 返回上一级目录
        os.chdir(current_dir)
