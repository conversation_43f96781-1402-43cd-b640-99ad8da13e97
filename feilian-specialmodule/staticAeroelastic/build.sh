#!/bin/bash
#

module load gcc/gcc-4.8.5
export PATH=/home/<USER>/guochengpeng/gcc-rpm/cmake-3.16.6-Linux-x86_64/bin:$PATH
export LD_LIBRARY_PATH=/home/<USER>/gcc-4.8.5/lib:/home/<USER>/gcc-4.8.5/lib64:$LD_LIBRARY_PATH


# cd ../../feilian-external/
# 
# ln -s linux-gcc4.4.7 linux-gcc4.8.5
# cd packages
# tinyxml2_path=$(pwd)/../tinyxml2
# tar -zxvf tinyxml2-10.0.0.tar.gz
# cd tinyxml2-10.0.0
# sed -i "s#/usr/local#$tinyxml2_path#" Makefile
# sed -i "s/AR = ar/AR = \/usr\/bin\/ar/g" Makefile
# sed -i "s/RANLIB = ranlib/RANLIB = \/usr\/bin\/ranlib/g" Makefile
# make &&  make install
# cd .. && rm -rf tinyxml2-10.0.0
# cd ../linux-gcc4.8.5
# ln -s ../tinyxml2 .
# ln -s ../linux-intel2021/mkl . 


cd ../../
sed -i "/option(ARI_ENABLE_AEROSTATIC/s/OFF/ON/g" CMakeLists.txt
sed -i "/option(ARI_ENABLE_MESHDEFORM/s/OFF/ON/g" CMakeLists.txt
sed -i "/option(ARI_ENABLE_MKL/s/OFF/ON/g" CMakeLists.txt

rm -rf build
mkdir build
cd build
cmake ..

for name in `find .|xargs grep -ri "gcc-4.8.5" -l`;do
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/ar/\/usr\/bin\/ar/g" $name
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/gcc-ar/\/usr\/bin\/gcc-ar/g" $name
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/ranlib/\/usr\/bin\/ranlib/g" $name
        sed -i "s/\/home\/<USER>\/gcc-4.8.5\/bin\/gcc-ranlib/\/usr\/bin\/gcc-ranlib/g" $name
done
make -j40

