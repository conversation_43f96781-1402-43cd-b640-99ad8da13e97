﻿#include "feilian-specialmodule/staticAeroelastic/meshData/AeroStaticData.h"
#include <iostream>  // 用于错误输出

AeroStaticData::AeroStaticData( SubMesh *subMesh_ ,Configure::Flow::FlowConfigure &flowConfig_,Vector &scale_)
:subMesh(subMesh_),flowConfig(flowConfig_),scale(scale_)
{
    try {
        npart = GetMPISize();
        processorID = GetMPIRank();
    } catch (const std::exception& e) {
        std::cerr << "MPI初始化错误: " << e.what() << std::endl;
        throw;  // 重新抛出异常，让上层调用者处理
    }
}

AeroStaticData::~AeroStaticData()
{}

void AeroStaticData::FindAeroMeshData()
{
    //**查找subMesh中的物面（部件）数目及名称
    int n = subMesh->GetBoundarySize(); //当前分区下包含的边界数目
    std::vector<std::string> v_wallboundaryName; 
    for (int patchID = 0; patchID < n; patchID++)
    {
        int globalPatchID = subMesh->GetBoundaryIDGlobal(patchID);     
        if (flowConfig.JudgeWallGlobal(globalPatchID))//**判断该边界是否为物面（部件）
        {
            v_wallboundaryID.push_back(patchID);  //v_wallboundary：当前分区下包含的部件编号
            v_wallboundaryName.push_back(subMesh->GetBoundaryName(patchID));//**部件名称
        }
    }

    //**查找各部件物面点坐标    
    std::vector<std::vector<int>> vv_wallNodeGlobalID;
    std::vector<std::vector<Node>> vv_wallNode;

    vv_wallNodeGlobalID.resize(v_wallboundaryID.size());
    vv_wallNode.resize(v_wallboundaryID.size());

    for (int i = 0; i < v_wallboundaryID.size(); i++)
    {
        int n1 = v_wallboundaryID[i];
        if (flowConfig.GetPreprocess().dualMeshFlag)
        {
            const int faceSize = subMesh->GetBoundaryFaceSize(n1);
            for (int index = 0; index < faceSize; index++)
            {
                const int faceIDLocal = subMesh->GetBoundaryFaceID(n1, index);
                const int faceIDGlobal = subMesh->GetFaceGlobalID(faceIDLocal);  //将当前分区的点ID转换为全局点ID
                vv_wallNodeGlobalID[i].push_back(faceIDGlobal);    
                Node node = subMesh->GetFace(faceIDLocal).GetCenter();
                vv_wallNode[i].push_back(node); 
            }
        }
        else
        {
            std::vector<int> v_wallNodeID;
            subMesh->PopulateBoundaryNodeID(n1, v_wallNodeID);  //**函数PopulateBoundaryNodeID():指定边界ID,给出该边界下的所有点的ID
            for (int j = 0; j < v_wallNodeID.size(); j++)
            {
                int globalID = subMesh->GetNodeGlobalID(v_wallNodeID[j]);  //将当前分区的点ID转换为全局点ID
                vv_wallNodeGlobalID[i].push_back(globalID);    
                Node node = subMesh->GetNode(v_wallNodeID[j]);
                vv_wallNode[i].push_back(node); 
            }
        }
    }

    std::vector<std::string> Name_all; 
    
    //** 收集全部部件名称  
    if(processorID == 0)
    {
        std::vector<std::string> NameTemp;
#ifdef _Supports_CXX11_
        std::unordered_map<std::string, int> temp;
#else
        std::map<std::string, int> temp;
#endif

        for(int i = 0; i < v_wallboundaryName.size(); i++){

            Name_all.push_back(v_wallboundaryName[i]);
            temp.insert(std::make_pair(v_wallboundaryName[i], -1));     //0进程部件名未添加到temp中
        }
            

        for(int i = 1; i < npart; i++) 
        {
            try {
                mpi_world.recv(i, 50, NameTemp);
            } catch (const std::exception& e) {
                std::cerr << "MPI接收错误 (进程 " << i << "): " << e.what() << std::endl;
                continue;  // 跳过当前进程，继续处理下一个进程
            }
            for(int j = 0; j < NameTemp.size(); j++) 
            {
               std::string name = NameTemp[j];
               if(temp.find(name) == temp.end()){
                  temp.insert(std::make_pair(name, -1));
                  Name_all.push_back(name);
               }
            }
        }

    }
    else
    {
        try {
            mpi_world.send(0, 50, v_wallboundaryName); 
        } catch (const std::exception& e) {
            std::cerr << "MPI发送错误: " << e.what() << std::endl;
            return;  // 出错则直接返回
        }
    }

    //**填充气动数据包v_aeroMeshData
    if(processorID == 0)
    {
        //**根据部件数目为气动数据包开辟大小，并赋值部件名称PartName
        int n_part = Name_all.size(); //部件的数目
        v_aeroMeshData.resize(n_part);
        for(int i = 0; i < n_part; i++) 
        {         
            v_aeroMeshData[i].PartName =  Name_all[i];
            v_aeroMeshData[i].v_id.clear();
            v_aeroMeshData[i].v_xyz_o.clear();
        } 


        //**向气动数据包填充0进程的数据
        std::vector<int> location;
        location.resize(2);
        for(int i = 0; i < v_wallboundaryName.size(); i++) 
        {
            for(int j = 0; j < n_part; j++) 
            {
               if( v_aeroMeshData[j].PartName == v_wallboundaryName[i]) 
                {
                    for(int k = 0; k < vv_wallNodeGlobalID[i].size(); k++) 
                    {
                        v_aeroMeshData[j].v_id.push_back(vv_wallNodeGlobalID[i][k]);
                        location[0] = j;
                        location[1] = v_aeroMeshData[j].v_id.size()-1;
                        map_data.insert(std::make_pair(vv_wallNodeGlobalID[i][k], location)); //map_data：存储v_wallboundary中所有点在v_aeroMeshData中位置，包含重复点（每个分区包含一个映射数组map_data）
                        v_aeroMeshData[j].v_xyz_o.push_back(vv_wallNode[i][k]);  //填充v_aeroMeshData中的初始的网格点坐标v_xyz_o
                    }
                }
            }
        }


        //**向气动数据包填充其他进程的数据
        
        std::vector<std::string> v_wallboundaryName_recv; 
        std::vector<std::vector<int>> vv_wallNodeGlobalID_recv;
        std::vector<std::vector<Node>> vv_wallNode_recv;
        std::vector<int>::iterator it;
        for(int i1 = 1; i1 < npart; i1++) 
        {
            std::multimap<int, std::vector<int>> map_data_temp;
            try {
                mpi_world.recv(i1, 60, v_wallboundaryName_recv);
                mpi_world.recv(i1, 70, vv_wallNodeGlobalID_recv); 
                mpi_world.recv(i1, 80, vv_wallNode_recv);
            } catch (const std::exception& e) {
                std::cerr << "MPI接收错误 (进程 " << i1 << "): " << e.what() << std::endl;
                continue;  // 跳过当前进程，继续处理下一个进程
            }
            
            for(int i = 0; i < v_wallboundaryName_recv.size(); i++) 
            {
               for(int j = 0; j < n_part; j++) 
               {
                  if( v_aeroMeshData[j].PartName == v_wallboundaryName_recv[i])
                  {
                    for(int k = 0; k < vv_wallNodeGlobalID_recv[i].size(); k++) 
                    {
 
                        int idd = vv_wallNodeGlobalID_recv[i][k];
                        it = std::find(v_aeroMeshData[j].v_id.begin(),v_aeroMeshData[j].v_id.end(),idd);  //向v_aeroMeshData填充其他进程的数据时，需要进行去重操作
                        if(it == v_aeroMeshData[j].v_id.end())
                        {

                            v_aeroMeshData[j].v_id.push_back(idd);
                            location[0] = j;
                            location[1] = v_aeroMeshData[j].v_id.size()-1;
                            map_data_temp.insert(std::make_pair(idd, location));
                            v_aeroMeshData[j].v_xyz_o.push_back(vv_wallNode_recv[i][k]);
                            
                        }
                        else
                        {                         
                            for(int kk = 0; kk < v_aeroMeshData[j].v_id.size(); kk++){
                                if(idd == v_aeroMeshData[j].v_id[kk])  location[1] = kk;    //v_aeroMeshData下标问题
                            }
                            location[0] = j;                           
                            map_data_temp.insert(std::make_pair(idd, location));
                        }
                        
                    }
                  }
                }
            }
            try {
                mpi_world.send(i1, 90, map_data_temp);
            } catch (const std::exception& e) {
                std::cerr << "MPI发送错误 (进程 " << i1 << "): " << e.what() << std::endl;
                continue;  // 跳过当前进程，继续处理下一个进程
            }
            map_data_temp.clear();
        }

        for(int i = 0; i < n_part; i++)        
            v_aeroMeshData[i].ncfd = v_aeroMeshData[i].v_id.size();

        bool flag =true;
        if(flag) this->HandleAeroMeshData();
        
    }
    else
    {
        try {
            mpi_world.send(0, 60, v_wallboundaryName);
            mpi_world.send(0, 70, vv_wallNodeGlobalID); 
            mpi_world.send(0, 80, vv_wallNode);
            mpi_world.recv(0, 90, map_data); 
        } catch (const std::exception& e) {
            std::cerr << "MPI通信错误: " << e.what() << std::endl;
            return;  // 出错则直接返回
        }
    }

#ifdef _Supports_CXX11_
    std::unordered_map<int, int> v_SymNodeID = this->GetSymData();
#else
    std::map<int, int> v_SymNodeID = this->GetSymData();
#endif
    if (processorID == 0)
    {

        for (int i = 0; i < v_aeroMeshData.size(); i++)
        {
            v_aeroMeshData[i].v_IfSymFlag.resize(v_aeroMeshData[i].ncfd);
            for (int j = 0; j < v_aeroMeshData[i].ncfd; j++)
            {
                int id = v_aeroMeshData[i].v_id[j];
                auto it = v_SymNodeID.find(id);
                if (it != v_SymNodeID.end())
                    v_aeroMeshData[i].v_IfSymFlag[j] = 1;
            }
        }

        for (int i = 1; i < npart; i++)
        {
            try {
                mpi_world.send(i, 100, v_aeroMeshData);
            } catch (const std::exception& e) {
                std::cerr << "MPI发送错误 (进程 " << i << "): " << e.what() << std::endl;
                continue;  // 跳过当前进程，继续处理下一个进程
            }
        }
    }
    else
    {
        try {
            mpi_world.recv(0, 100, v_aeroMeshData);
        } catch (const std::exception& e) {
            std::cerr << "MPI接收错误: " << e.what() << std::endl;
            return;  // 出错则直接返回
        }
    }

}


#ifdef _Supports_CXX11_
std::unordered_map<int,int> AeroStaticData::GetSymData()
#else
std::map<int, int> AeroStaticData::GetSymData()
#endif
{
    std::vector<int> v_SymBoundryGlobalID;
    int n_allBoundry = flowConfig.GetGlobalBoundarySize();
    for(int i = 0 ; i < n_allBoundry; i++)
    {
      Configure::BoundaryStruct  Boundary = flowConfig.GetGlobalBoundary(i);
      if(Boundary.type == Boundary::Type::SYMMETRY)
      {
        v_SymBoundryGlobalID.push_back(i);
      }
    }
    
    int n_sym = v_SymBoundryGlobalID.size();
    std::vector<std::vector<int>> vv_SymNodeID;
    vv_SymNodeID.resize(n_sym);
    
    for (int patchID = 0; patchID < subMesh->GetBoundarySize(); patchID++)
    {

       int globalId = subMesh->GetBoundaryIDGlobal(patchID);  
       for(int j = 0; j < n_sym; j++)
       {
            if(v_SymBoundryGlobalID[j] == globalId)            // 修正此处的比较运算符错误
            {   
                subMesh->PopulateBoundaryNodeID(patchID, vv_SymNodeID[j]); 
                for(int k = 0; k < vv_SymNodeID[j].size(); k++)
                vv_SymNodeID[j][k] = subMesh->GetNodeGlobalID(vv_SymNodeID[j][k]);  
            }     
       }

    }

#ifdef _Supports_CXX11_
    std::unordered_map<int,int> v_SymNodeID;
#else
    std::map<int, int> v_SymNodeID;
#endif
   //收集其他进程对称面点，并去重
   if(processorID == 0)
    {

        for(int i = 0; i < n_sym; i++)
        {
            for(int k = 0; k < vv_SymNodeID[i].size(); k++) 
            {
                int n_temp = vv_SymNodeID[i][k];
                v_SymNodeID.insert(std::make_pair(n_temp,-1));
            }
        }

       std::vector<std::vector<int>> vv_temp;
       for(int i = 1; i < npart; i++) 
       {
            try {
                mpi_world.recv(i, 1000,  vv_temp);
            } catch (const std::exception& e) {
                std::cerr << "MPI接收错误 (进程 " << i << "): " << e.what() << std::endl;
                continue;  // 跳过当前进程，继续处理下一个进程
            }
            for(int j = 0; j < n_sym; j++) 
            {
               for(int k = 0; k < vv_temp[j].size(); k++) 
               {
                    int n_temp = vv_temp[j][k];
                    v_SymNodeID.insert(std::make_pair(n_temp,-1));
               }
            }
       }

    }
    else{

        try {
            mpi_world.send(0, 1000, vv_SymNodeID);
        } catch (const std::exception& e) {
            std::cerr << "MPI发送错误: " << e.what() << std::endl;
            return v_SymNodeID;  // 出错则返回当前结果
        }

    }

    return v_SymNodeID;

}


void AeroStaticData::HandleAeroMeshData()
{
    //判断物面点是否输出
    std::vector<std::map<int,Node>> v_IdMapNode;
    v_IdMapNode.resize(v_aeroMeshData.size());


    std::string name = "wall.dat";
    std::fstream file1;
    file1.open(name,std::fstream::in);
    if(file1.is_open()){
        std::cout<<"wall.dat is already exist, this file is original wall mesh data"<<std::endl;
        std::string line;
       
        while (getline(file1, line)){                    
            std::string s0 =  getSubstringAfterChar(line,'=');
            int id;
            for (int i = 0; i <  v_aeroMeshData.size(); i++){
                if(s0 == v_aeroMeshData[i].PartName) {
                    id = i;
                    std::cout<<"id = "<<id <<", ncfd = "<<v_aeroMeshData[id].ncfd<<", partname = "<<v_aeroMeshData[id].PartName<<std::endl;

                    for (int j = 0; j <  v_aeroMeshData[id].ncfd; j++)
                    {
                        double temp[4];
                        Node t_node;
                        file1>>temp[0]>>temp[1]>>temp[2]>>temp[3];
                        t_node.SetX(temp[0]);
                        t_node.SetY(temp[1]);
                        t_node.SetZ(temp[2]);
                        v_IdMapNode[id].insert(std::make_pair(temp[3], t_node));
                    }
                }
            }  
        //   file1.ignore();           
        }
        file1.close();
        std::cout<<"wall.dat file is closed "<<std::endl;
        

        for (int i = 0; i <  v_aeroMeshData.size(); i++)
        {
           int n = v_aeroMeshData[i].ncfd;
           for (int j = 0; j < n; j++)
           {
               int id = v_aeroMeshData[i].v_id[j];
               auto it = v_IdMapNode[i].find(id);
               if(it != v_IdMapNode[i].end()){
                    Node temp = it->second;
                    v_aeroMeshData[i].v_xyz_o[j] = temp;
               }
           }
        }

    }else{
        std::cout<<"wall.dat is not exist, maybe it is first time to run this program"<<std::endl;
        std::fstream file;
        file.open(name,std::fstream::out);
        for (int i = 0; i <  v_aeroMeshData.size(); i++)
        {
            std::cout<<"PartID = "<< i <<", PartName = "<<v_aeroMeshData[i].PartName<<std::endl;
            file<<"zone T = "<<v_aeroMeshData[i].PartName<<std::endl;
            for(int j = 0; j < v_aeroMeshData[i].ncfd; j++ )
            file<<v_aeroMeshData[i].v_xyz_o[j].X()<<"  "<<v_aeroMeshData[i].v_xyz_o[j].Y()<<"  "<< v_aeroMeshData[i].v_xyz_o[j].Z()<< " "<< v_aeroMeshData[i].v_id[j] <<std::endl;
        }
        file.close();
    }

}


void AeroStaticData::ReadSolidMeshData()
{
    if(processorID == 0){
        struct Solver::SolidMeshData Datatemp;
        Scalar temp[12] = {0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0};
        std::fstream file, file1;
        file.open("bdf/xyzfp_id.plt",std::fstream::in);
          
        
        if (file)
        {
            std::string line;
            getline(file,line);
            ncsdt = ReadNumFromLine(line);  //ncsdt未发送其他进程问题

            while(getline(file,line))
            {
                std::string s0 = line.substr(0,4);
                Datatemp.v_id.clear();
                Datatemp.v_xyz.clear();
                Datatemp.v_Index.clear();
                if(s0 == "zone")
                {
                    
                    Datatemp.PartName = getSubstringAfterChar(line,'=');
                    getline(file, line);
                    Datatemp.ncsd = ReadNumFromLine(line);

                    Datatemp.v_id.resize(Datatemp.ncsd);
                    Datatemp.v_xyz.resize(Datatemp.ncsd);
                    Datatemp.v_Index.resize(Datatemp.ncsd);
                    Datatemp.local_A.resize(Datatemp.ncsd);
                    Datatemp.local_B.resize(Datatemp.ncsd);
                    Datatemp.local_C.resize(Datatemp.ncsd);
                    Datatemp.local_CD.resize(Datatemp.ncsd);
                    Datatemp.v_RbfID.resize(Datatemp.ncsd);
                    
                    for(int i = 0; i < Datatemp.ncsd; i++)
                    {
                        file>>temp[0]>>temp[1]>>temp[2]>>Datatemp.v_Index[i]>>Datatemp.v_id[i] >>Datatemp.local_CD[i] >>temp[3]>>temp[4]>>temp[5]>>temp[6]>>temp[7]>>temp[8]>>temp[9]>>temp[10]>>temp[11]>>Datatemp.v_RbfID[i];
                      
                        Datatemp.v_xyz[i].SetX(temp[0]*scale.X());
                        Datatemp.v_xyz[i].SetY(temp[1]*scale.Y());
                        Datatemp.v_xyz[i].SetZ(temp[2]*scale.Z());

                        Datatemp.local_A[i].SetX(temp[3]);
                        Datatemp.local_A[i].SetY(temp[4]);
                        Datatemp.local_A[i].SetZ(temp[5]);

                        Datatemp.local_B[i].SetX(temp[6]);
                        Datatemp.local_B[i].SetY(temp[7]);
                        Datatemp.local_B[i].SetZ(temp[8]);

                        Datatemp.local_C[i].SetX(temp[9]);
                        Datatemp.local_C[i].SetY(temp[10]);
                        Datatemp.local_C[i].SetZ(temp[11]);

                    }
                    //file.ignore(); //忽略掉file读取数据后残留在输入流中的换行符
                    v_solidMeshData.push_back(Datatemp);     //&问题
                }
            }
        }
         
        for(int i = 0; i < v_solidMeshData.size(); i++)        
        {
            std::string name  = v_solidMeshData[i].PartName; 
               for(int j = 0; j < name.size(); j++) {
                  if(isalnum(name[j])){
                    int n = name.size()-j;
                    name = name.substr(j, n);
                    break;
                  }
               }

            for(int j = name.size()-1; j >0; j--) {
                  if(isalnum(name[j])){
                    int n = j+1;
                    name = name.substr(0, n);
                    break;
                  }
            }
            v_solidMeshData[i].PartName = name;
            for(int j = 0; j < v_aeroMeshData.size(); j++){
                
                std::string namea = v_aeroMeshData[j].PartName;             
                if(name == namea)
                { 
                    v_solidMeshData[i].id_aero = j;
                }                  
            }
        } 

        file.close();

        for(int i = 1; i < npart; i++) {
            mpi_world.send(i, 120,  v_solidMeshData); 
            mpi_world.send(i, 130,  ncsdt); 
        }
    }
    else{
        mpi_world.recv(0, 120, v_solidMeshData);
        mpi_world.recv(0, 130,  ncsdt); 
    }
}


int AeroStaticData::ReadNumFromLine(std::string line)
{
    int num;
    std::string s0;
    for(int i = 0; i< line.size(); i++)
    {
        if(line[i] == ':')
        {
            if(line[i+1] == ' ')
            {
                int n = line.size()-i-2;
                s0 = line.substr(i+2, n);
                break;
            }
            else
            {
                int n = line.size()-i-1;
                s0 = line.substr(i+1,n);
                break;
            }
        }
    }

    std::stringstream s1(s0);
    s1>>num;
    return num;
}

std::string AeroStaticData::trim(std::string str){
    size_t first = str.find_first_not_of(' ');
    size_t last = str.find_last_not_of(' ');
    if (first == std::string::npos || last == std::string::npos) {
        return "";
    }
    return str.substr(first,(last - first +1));
}

std::string AeroStaticData::getSubstringAfterChar(std::string str, char ch) {
    size_t pos = str.find(ch);
    if (pos== std::string::npos){
        return "";
    }
    pos += 1;
    return trim(str.substr(pos));
}

void AeroStaticData::checkdat(){


 /* if(processorID == 0){
     std::fstream file;
     file.open("ncsd.dat",std::fstream::out);

      for(int i = 0; i <v_solidMeshData.size(); i++)    {
          file<< "zone T =  "<<v_solidMeshData[i].PartName<<std::endl;
           for(int j = 0; j <v_solidMeshData[i].ncsd; j++){
               Node node = v_solidMeshData[i].v_xyz[j];
               file<< node.X()  <<" " <<node.Y() << "  " <<" " <<node.Z() << "  "<< v_solidMeshData[i].v_Index[j] << "  "<<  v_solidMeshData[i].v_id[j]<<std::endl;
           }
      }
      file.close();

        file.open("ncfd.dat",std::fstream::out);

        for(int i = 0; i <v_aeroMeshData.size(); i++)    {
          file<< "zone T =  "<<v_aeroMeshData[i].PartName<<std::endl;
           for(int j = 0; j <v_aeroMeshData[i].ncfd; j++){
               Node node = v_aeroMeshData[i].v_xyz_o[j];
               file<< node.X()  <<" " <<node.Y() << "  " <<" " <<node.Z() << "  "<<  v_aeroMeshData[i].v_id[j]<<std::endl;
           }
        }
      file.close();

    }*/

}


