﻿//! @file 
//! <AUTHOR>
//! @brief
//! @date 

#ifndef _specialModule_staticAeroelastic_meshData_MeshData_
#define _specialModule_staticAeroelastic_meshData_MeshData_


#include <iostream>
#include <string>
#include <fstream>
#include <vector>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <array>
#include <algorithm>
#include <ctime>

#include "meshProcess/meshConverter/CgnsMesh.h"

namespace Solver
{
   
struct AeroMeshData
{
    std::string PartName;//**名称;
    int ncfd;//**网格点数;
    int id_aero; //该部件对应的结构部件位置
    std::vector<int> v_id;//**网格点全局ID;    
    std::vector<Node> v_xyz_o;//**初始的网格点坐标;

    std::vector<Node> v_xyz;//**更新的网格点坐标;
    std::vector<Node> v_force;//**网格点气动力;
    std::vector<Node> v_dxyz;//**网格点变形量;
    std::vector<Node> v_Axis;//**轴坐标;

    std::vector<bool> v_IfSymFlag;//**网格点全局ID;    

    AeroMeshData()
    {
        PartName = "  ";
        ncfd = 0;
        id_aero = -1;
        v_id.clear();
        v_xyz_o.clear();
        v_xyz.clear();
        v_dxyz.clear();
        v_force.clear();
        v_Axis.clear();
        v_IfSymFlag.clear();
    }

    #if defined(_BaseParallelMPI_)
    /// 并行数据发送使用
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & PartName;
        ar & ncfd;
        ar & id_aero;
        ar & v_id;
        ar & v_xyz_o;
        ar & v_xyz;
        ar & v_force;
        ar & v_dxyz;
        ar & v_Axis;
        ar & v_IfSymFlag;
    }
#endif
};



struct SolidMeshData
{
    std::string PartName;//**名称，PartName;
    int ncsd;//**加载点数目，ncsd;
    int id_aero; //该部件对应的气动部件位置
    std::vector<int> v_id;//**加载点真实ID，v_id(bdf);    
    std::vector<Node> v_xyz;//**加载点坐标，v_xyz; 

    std::vector<int> v_Index;//**自然编号，v_Index = v_id - 1;
    std::vector<Node> v_dxyz;//**变形量，v_dxyz;
    std::vector<Node> v_force;//**载荷，v_force;

    std::vector<int> v_RbfID;
    
    std::vector<int> local_CD;
    std::vector<Node> local_A;
    std::vector<Node> local_B;
    std::vector<Node> local_C;


    SolidMeshData()
    {
        PartName = "  ";
        ncsd = 0;
        id_aero = -1;
        v_id.clear();
        v_xyz.clear();
        v_Index.clear();
        v_dxyz.clear();
        v_force.clear();
        local_A.clear();
        local_C.clear();
        local_B.clear();
        local_CD.clear();
        v_RbfID.clear();
    }

#if defined(_BaseParallelMPI_)
    /// 并行数据发送使用
    template<class Archive>
    void serialize(Archive & ar, const unsigned int version)
    {
        ar & PartName;
        ar & ncsd;
        ar & id_aero;
        ar & v_id;
        ar & v_xyz;
        ar & v_Index;
        ar & v_dxyz;
        ar & v_force;
        ar & local_A;
        ar & local_B;
        ar & local_C;
        ar & local_CD;
        ar & v_RbfID;
    }
#endif

};


};
#endif