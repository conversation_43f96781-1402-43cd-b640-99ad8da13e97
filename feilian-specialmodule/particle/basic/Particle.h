﻿#ifndef _particle_basic_Particle_
#define _particle_basic_Particle_

#include "basic/common/ConfigUtility.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

class Particle
{
public:
    enum ParticleFlag
    {
        IN_DOMAIN = 0,
        DELETED = -2,
        NO_INSERTED = -1
    };
    
public:
    int ID;                         ///< 颗粒编号
    int type;                       ///< 颗粒属性编号
    ParticleFlag flag;              ///< 颗粒标识
    Vector position;                ///< 颗粒位置
    Scalar diameter;                ///< 颗粒直径
    Vector linearVelocity;          ///< 颗粒线速度
    Vector angularPosition;         ///< 颗粒角位置
    Vector angularVelocity;         ///< 颗粒角速度
    Vector force;                   ///< 颗粒所受合力
    Vector torque;                  ///< 颗粒所受力矩
    Vector linearAcceleration;      ///< 颗粒线加速度
    Vector angularAcceleration;     ///< 颗粒角加速度
	int contactPWIndex;             ///< 颗粒壁面接触编号

	Vector position0;                        ///< 上一步位置
	std::vector<Vector> linearVelocity0;	 ///< 前几步线速度
	std::vector<Vector> linearAcceleration0; ///< 前几步线加速度

	Vector angularPosition0;				 ///< 上一步角位置
	std::vector<Vector> angularVelocity0;	 ///< 前几步角加速度
	std::vector<Vector> angularAcceleration0;///< 前几步角加速度

public:
    /**
     * @brief 颗粒构造函数
     * 
     */
    Particle();

    /**
     * @brief 颗粒是否在计算域判断函数
     * 
     * @return true 在计算
     * @return false 不在计算域
     */
    bool IsInDomain()const { return this->flag >= ParticleFlag::IN_DOMAIN; }

	/**
	* @brief 更新与壁面接触状态
	*
    * @param[in] flag_ 接触状态
	*/
	void SetContactWallIndex(const int &flag_) { this->contactPWIndex = flag_; }

	/**
	* @brief 获取与壁面接触状态
	*
    * @return bool 与壁面接触状态
	*/
	int GetContactWallIndex()const { return this->contactPWIndex; }

#if defined(_BaseParallelMPI_)
public:
	template<class Archive>
	void serialize(Archive& ar, const unsigned int version)
	{
		//当前类成员序列化
		ar& ID;
		ar& type;
		ar& flag;
		ar& position;
		ar& diameter;
		ar& linearVelocity;
		ar& angularPosition;
		ar& angularVelocity;
		ar& force;
		ar& torque;
		ar& linearAcceleration;
		ar& angularAcceleration;
		ar& contactPWIndex;

		ar& position0;
		ar& linearVelocity0;
		ar& linearAcceleration0;
		ar& angularPosition0;
		ar& angularVelocity0;
		ar& angularAcceleration0;
	}
#endif

};

/**
 * @brief 两个颗粒重叠量计算函数
 * 
 * @param[in] particle1 颗粒1
 * @param[in] particle2 颗粒2
 * @return Scalar 重叠量
 */
Scalar Overlap(const Particle &particle1, const Particle &particle2);

} // namespace Particle

#endif