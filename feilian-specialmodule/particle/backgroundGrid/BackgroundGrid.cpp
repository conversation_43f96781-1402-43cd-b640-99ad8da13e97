﻿#include "feilian-specialmodule/particle/backgroundGrid/BackgroundGrid.h"
#include <unordered_set>

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif

BackgroundGrid::BackgroundGrid(SubMesh *localMesh_) : localMesh(localMesh_)
{
    // backGrid = new CartesianGrid(localMesh);
    // backGrid->CreateMapForMesh();

	dim2 = localMesh_->GetMeshDimension() == Mesh::MeshDim::md2D;
	this->CreateElementKdtTree();

	this->CreatAdjInfo();
}

BackgroundGrid::~BackgroundGrid()
{
}

int BackgroundGrid::GetAdjElementID(const Vector &pos, const bool &firstFind, const int &lastID, const Vector &vel)
{
	if (pos.X() < spaceMin[0] || pos.Y() < spaceMin[1]) return -1;
	if (pos.X() > spaceMax[0] || pos.Y() > spaceMax[1]) return -1;
	if (!dim2 && (pos.Z() < spaceMin[2] || pos.Z() > spaceMax[2])) return -1;
	
	if (!firstFind && lastID > 0)
	{
		return GetAdjElementID_Nearest(pos, lastID, vel);
	}
	else
	{
		return GetAdjElementID_KDT(pos);
	}
}

int BackgroundGrid::GetAdjElementID_KDT(const Vector &pos)
{
	std::vector<Scalar> rangeMin = { -INF, -INF, -INF, pos.X(), pos.Y(), pos.Z() };
	std::vector<Scalar> rangeMax = std::vector<Scalar>{pos.X(), pos.Y(), pos.Z(), INF, INF, INF};

	std::vector<KdtNode *> nodeList;
	kdtree->FindNodesInRegion(rangeMin, rangeMax, nodeList);

	for (int i = 0; i < nodeList.size(); i++)
	{
		const int elemID = nodeList[i]->data;
		const Element &element = this->localMesh->GetElement(elemID);
		if (this->NodeInElem(pos, element)) return elemID;
	}

	return -1;
}

int BackgroundGrid::GetAdjElementID_Cartesian(const Vector &pos)
{
	std::vector<int> adjanctElementID = backGrid->GetAdjanctElementID(pos);

	const int adjanctNum = adjanctElementID.size();
	for (int i = 0; i < adjanctNum; i++)
	{
		const int &elemID = adjanctElementID[i];
		const Element &elem = this->localMesh->GetElement(elemID);
		if (this->NodeInElem(pos, elem)) return elemID;
	}

	int nearestID;
	Scalar distance = INF;
	for (int i = 0; i < adjanctNum; i++)
	{
		const int &elemID = adjanctElementID[i];
		const Vector &center = this->localMesh->GetElement(elemID).GetCenter();
		Scalar distance1 = (center - pos).Mag();
		if (distance1 < distance)
		{
			distance = distance1;
			nearestID = elemID;
		}
	}
	const Element &elem = this->localMesh->GetElement(nearestID);
	for (int i = 0; i < elem.GetFaceSize(); i++)
	{
		const int &faceID = elem.GetFaceID(i);
		const int &ownerID = this->localMesh->GetFace(faceID).GetOwnerID();
		const int &neighID = this->localMesh->GetFace(faceID).GetNeighborID();
		const int adjElementID = (ownerID == nearestID) ? neighID : ownerID;
		const Element &adjElement = this->localMesh->GetElement(adjElementID);
		if (this->NodeInElem(pos, adjElement)) return adjElementID;
	}

	return -1;
}

int BackgroundGrid::GetAdjElementID_Nearest(const Vector &pos, const int &lastID, const Vector &vel)
{
	const Element &elem = this->localMesh->GetElement(lastID);
	if (this->NodeInElem(pos, elem)) return lastID;

	const Vector &center0 = elem.GetCenter();
	const int adjElementSize = this->adjElemIDList[lastID].size();
	for (int i = 0; i < adjElementSize; i++)
	{
		const int adjElementID = this->adjElemIDList[lastID][i];
		const Element &adjElement = this->localMesh->GetElement(adjElementID);
		const Vector &distance = adjElement.GetCenter() - center0;

		if ((vel.GetNormal() & distance.GetNormal()) < 0.7)
		{
			if (this->NodeInElem(pos, adjElement)) return adjElementID;
		}
	}

	for (int i = 0; i < adjElementSize; i++)
	{
		const int adjElementID = this->adjElemIDList[lastID][i];
		const Element &adjElement = this->localMesh->GetElement(adjElementID);
		if (this->NodeInElem(pos, adjElement)) return adjElementID;
	}

	return this->GetAdjElementID_KDT(pos);
}

void BackgroundGrid::CreateElementKdtTree()
{
    const int elementSize = localMesh->GetElementNumberReal();
	std::vector<std::vector<Scalar>> elemNodeCloud(elementSize);
	std::vector<int> elemIDCloud(elementSize);
	int nodeCloudIndex = -1;
	for (int elementID = 0; elementID < elementSize; elementID++)
	{
		const Element &elem = localMesh->GetElement(elementID);
		Vector min = localMesh->GetNode(elem.GetNodeID(0));
		Vector max = localMesh->GetNode(elem.GetNodeID(0));
		for (int i = 1; i < elem.GetNodeSize(); i++)
		{
			const Vector &node = localMesh->GetNode(elem.GetNodeID(i));
			min = Min(min, node);
			max = Max(max, node);
		}
		nodeCloudIndex++;
		elemNodeCloud[nodeCloudIndex] = { min.X(), min.Y(), min.Z(), max.X(), max.Y(), max.Z() };
		elemIDCloud[nodeCloudIndex] = elementID;
	}

	kdtree = new KdtTree(dim2 ? 2 : 3, elemNodeCloud, elemIDCloud);

    spaceMin = kdtree->GetMin();
    spaceMax = kdtree->GetMax();
}

bool BackgroundGrid::NodeInElem(const Vector &pos, const Element &elem)
{
	for (int faceI = 0; faceI < elem.GetFaceSize(); faceI++)
	{
		const int &faceID = elem.GetFaceID(faceI);
		const Face &face = localMesh->GetFace(faceID);
		const Vector elemCenterToFace = face.GetCenter() - elem.GetCenter();
		const Vector nodeToFace = face.GetCenter() - pos;
		const Scalar d1 = face.GetNormal() & elemCenterToFace;
		const Scalar d2 = face.GetNormal() & nodeToFace;
		if (d1 * d2 < -SMALL) return false;
	}

	return true;
}

int BackgroundGrid::GetAdjProcess( const Vector &pos )
{
	return this->GetAdjElementID_KDT(pos) > -1 ? GetMPIRank() : -1;
}

Scalar BackgroundGrid::GetMinTime(const Vector &pos, const Vector &vel, const int &adjElemID)
{
	Scalar deltaT = INF, velNormMax = -INF;
	bool found = false;
	const int adjFaceIDSize = this->adjFaceIDList[adjElemID].size();
	for (int i = 0; i < adjFaceIDSize; i++)
	{
		const int &adjFaceID = this->adjFaceIDList[adjElemID][i];
		const Face &adjFace = this->localMesh->GetFace(adjFaceID);
	
		const Vector distance = adjFace.GetCenter() - pos;
		const Vector disNormal = distance.GetNormal();
	
		const Scalar velNorm = vel & disNormal;
		if (velNorm > velNormMax && velNorm > 0)
		{
			velNormMax = velNorm;
			const Vector &faceNormal = adjFace.GetNormal();
			deltaT = 0.3 * fabs(distance & faceNormal) / (fabs(vel & faceNormal) + SMALL);
			found = true;
		}
	}
	
	if (!found)
	{
		for (int i = 0; i < adjFaceIDSize; i++)
		{
			const int &adjFaceID = this->adjFaceIDList[adjElemID][i];
			const Face &adjFace = this->localMesh->GetFace(adjFaceID);

			const int nodeSize = adjFace.GetNodeSize();
			for (int j = 0; j < nodeSize; ++j)
			{
				const int &nodeID = adjFace.GetNodeID(j);
				const Vector distance = this->localMesh->GetNode(nodeID) - pos;
				const Vector disNormal = distance.GetNormal();

				const Scalar velNorm = vel & disNormal;
				if (velNorm > velNormMax && velNorm > 0)
				{
					velNormMax = velNorm;
					const Vector &faceNormal = adjFace.GetNormal();
					deltaT = 0.3 * fabs(distance & faceNormal) / (fabs(vel & faceNormal) + SMALL);
				}
			}
		}
	}

	return Max(deltaT, 1.0E-6);
}

void BackgroundGrid::CreatAdjInfo()
{
	const int elementSize = localMesh->GetElementNumberReal();
	const int nodeSize = localMesh->GetNodeNumber();
	
	// 统计点相邻的单元数量
	std::vector<int> count(nodeSize, 0);
	for (auto elemID = 0; elemID < elementSize; ++elemID)
	{
		const Element &elem = this->localMesh->GetElement(elemID);
		for (int index = 0; index < elem.GetNodeSize(); ++index)
			count[elem.GetNodeID(index)]++;
	}

	// 生成点相邻单元编号列表
	std::vector<std::vector<int>> nodeElemID(nodeSize);
	for (auto i = 0; i < nodeSize; ++i) nodeElemID[i].reserve(count[i]);
	for (auto elemID = 0; elemID < elementSize; ++elemID)
	{
		const Element &elem = this->localMesh->GetElement(elemID);
		for (int index = 0; index < elem.GetNodeSize(); ++index)
			nodeElemID[elem.GetNodeID(index)].push_back(elemID);
	}

	adjElemIDList.resize(elementSize);
	adjElemDistantList.resize(elementSize);
	adjFaceIDList.resize(elementSize);
	for (int elemID0 = 0; elemID0 < elementSize; elemID0++)
	{
		const Element &elem0 = this->localMesh->GetElement(elemID0);
		const Vector &center0 = elem0.GetCenter();

		// 形成目标单元的点相邻单元列表
		std::unordered_set<int> adjElemIDSet;
		const int nodeSize = elem0.GetNodeSize();
		for (int i = 0; i < nodeSize; i++)
		{
			const int &nodeID = elem0.GetNodeID(i);
			const std::vector<int> list = nodeElemID[nodeID];
			for (int j = 0; j < list.size(); j++) adjElemIDSet.insert(list[j]);
		}

		// 去除目标单元本身，形成单元的点相邻单元列表及单元体心距离列表
		const int adjElemSize = adjElemIDSet.size();
		adjElemIDList[elemID0].reserve(adjElemSize);
		adjElemDistantList[elemID0].reserve(adjElemSize);
		for (auto it = adjElemIDSet.begin(); it != adjElemIDSet.end(); ++it)
		{
			const int &adjElemID = *it;
			if (adjElemID != elemID0)
			{
				adjElemIDList[elemID0].push_back(adjElemID);
				const Element &adjElem = this->localMesh->GetElement(adjElemID);
				Vector norm = (adjElem.GetCenter() - center0).GetNormal();
				adjElemDistantList[elemID0].push_back(norm);
			}
		}

		// 遍历点相邻单元所有面，形成面列表
		std::unordered_set<int> adjFaceIDSet;
		for (auto it = adjElemIDSet.begin(); it != adjElemIDSet.end(); ++it)
		{
			const Element &adjElem = this->localMesh->GetElement(*it);
			const int faceSize = adjElem.GetFaceSize();
			for (int i = 0; i < faceSize; i++) adjFaceIDSet.insert(adjElem.GetFaceID(i));
		}

		// 去除目标单元的面
		const int faceSize = elem0.GetFaceSize();
		for (int i = 0; i < faceSize; i++) adjFaceIDSet.erase(elem0.GetFaceID(i));

		// 去除点相邻的面
		const int adjFaceSize = adjFaceIDSet.size();
		adjFaceIDList[elemID0].reserve(adjFaceSize);
		for (auto it = adjFaceIDSet.begin(); it != adjFaceIDSet.end(); ++it)
		{
			const int &adjFaceID = *it;
			const Face &adjFace = this->localMesh->GetFace(adjFaceID);

			bool found = false;
			const int nodeSize1 = adjFace.GetNodeSize();
			for (int j = 0; j < nodeSize1; j++)
			{
				const int &nodeID1 = adjFace.GetNodeID(j);
				for (int k = 0; k < nodeSize; k++)
				{
					if (nodeID1 == elem0.GetNodeID(k))
					{
						found = true;
						break;
					}
				}
				if (found) break;
			}

			// 去掉与单元点相邻的面
			if (!found)
			{
				adjFaceIDList[elemID0].push_back(adjFaceID);
			}
		}
	}
}
