﻿#ifndef _basic_mesh_CartesianGrid_
#define _basic_mesh_CartesianGrid_

#include "basic/mesh/Mesh.h"

/**
 * @brief 基于Mesh构造笛卡尔背景网格
 * 
 */
class CartesianGrid
{

/**
 * @brief 笛卡尔网格节点
 * 
 */
struct OctElement
{
    int level; ///< 当前层级
    Vector center; ///< 体心
    OctElement *parent; ///< 父节点指针
    std::vector<OctElement *> children; ///< 子节点指针列表
    std::vector<int> elementIDList; ///< 所对应mesh的单元编号

    /**
     * @brief 默认构造函数
     * 
     */
    OctElement()
    {
        level = 0;
        center = Vector0;
        parent = nullptr;
    }

    /**
     * @brief 根据父节点创建子节点
     * 
     * @param parent_ 父节点
     */
    OctElement(OctElement *parent_)
    {
        level = parent_->level + 1;
        parent = parent_;
    }
};

public:

    /**
     * @brief 构造背景网格信息
     * 
     * @param mesh_ 输入网格
     */
    CartesianGrid(Mesh *mesh_);

    /**
     * @brief 场景Mesh单元与直角网格的映射关系
     * 
     */
    void CreateMapForMesh();

    /**
     * @brief 节点分裂
     * 
     * @param parent 父节点
     */
    void Refine(OctElement *parent);

    /**
     * @brief 获取当前节点内所有Mesh网格单元数量
     * 
     * @param parent 当前节点
     * @param size 单元数量
     */
    void GetAllElementIDSize(OctElement *parent, int &size);

    /**
     * @brief 获取当前节点内所有Mesh网格单元编号
     * 
     * @param parent 当前节点
     * @param elemIDList 单元编号容器
     */
    void GetAllElementID(OctElement *parent, std::vector<int> &elemIDList);

    /**
     * @brief 根据给定坐标获得临近单元编号
     * 
     * @param pos 坐标点
     * @return std::vector<int> 临近单元编号列表
     */
    std::vector<int> GetAdjanctElementID(const Vector &pos);

    /**
     * @brief 根据给定坐标获得临近单元编号
     * 
     * @param parent 当前节点
     * @param pos 坐标点
     * @return std::vector<int> 临近单元编号列表
     */
    std::vector<int> GetAdjanctElementID(OctElement *parent, const Vector &pos);

private:
    bool dim2; ///< 二维标识
    Mesh *mesh; ///< 输入网格
    Vector domMin, domMax; ///< 范围
    Scalar dX, dY, dZ; ///< 0层三个方向网格间距
    int nGridsX, nGridsY, nGridsZ; ///< 0层三个方向网格数量
    int refSize; ///< 节点内部所包含Mesh单元编号数量的上限
    int refineSize; ///< 节点分裂数量，二维为4，三维为8
    int maxLevel; ///< 分裂总次数
    std::vector<std::vector<std::vector<OctElement>>> OctGrid; ///< 所有根节点容器
};

#endif