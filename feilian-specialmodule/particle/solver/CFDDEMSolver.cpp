﻿#include "feilian-specialmodule/particle/solver/CFDDEMSolver.h"

CFDDEMSolver::CFDDEMSolver(SubMesh *subMesh_,
                           Configure::Flow::FlowConfigure &flowConfigure_,
						   Configure::Particle::ParticleConfigure &DEMConfigure_)
	: subMesh(subMesh_), flowConfigure(flowConfigure_), DEMConfigure(DEMConfigure_)
{
	// 创建物理场包容器
	for (int level = 0; level < flowConfigure.GetAcceleration().multigridSolver.level; level++)
	{
		// 依据网格层级编号level，获取相应层级网格，并以此网格定义物理场包，同时将物理场包置于物理场包容器中
		flowPackageVector.push_back(new Package::FlowPackage(level, subMesh, flowConfigure));
	}

	// 获取非定常标识
	unsteady = flowPackageVector[0]->GetUnsteadyStatus().unsteadyFlag;
	dualTime = flowPackageVector[0]->GetUnsteadyStatus().dualTime;

	fullMultigirdFlag = true;
	if (unsteady
		|| flowConfigure.GetControl().initialization.type == Initialization::Type::RESTART
		|| flowConfigure.GetControl().initialization.fullMultigridSteps <= 0)
	{
		fullMultigirdFlag = false;
	}

	// 获取初始化类型
	initialType = flowConfigure.GetControl().initialization.type;

	// 根据输入参数，确定多重网格总层数
	multigridLevel = flowConfigure.GetAcceleration().multigridSolver.level;

	// 依据输入参数中的时间格式，创建当前网格层级上物理场包更新的时间推进对象，并将其置于时间推进对象容器中        
	for (int level = 0; level < multigridLevel; level++)
		timeSchemeVector.push_back(new Time::Flow::FlowTimeManager(*flowPackageVector[level]));

	// 创建残值容器
	resultProcess = new FlowResultsProcess(subMesh, flowPackageVector);

	// 创建全多重求解器
	fullMultigird = nullptr;
	if (fullMultigirdFlag) fullMultigird = new FullMultigird(subMesh, flowPackageVector, timeSchemeVector, resultProcess);

	// 创建内循环求解器
	innerLoop = new InnerLoop(subMesh, flowPackageVector, timeSchemeVector, resultProcess);

	// 创建DEM计算所需几何信息
	this->CreateGeometry();

	// 创建DEM求解器
	DEMSolver = new Particle::DEMSolver( DEMConfigure, geometryGlobal, boundaryTypeList, subMesh, flowPackageVector[0] );

}

CFDDEMSolver::~CFDDEMSolver()
{
	if (resultProcess != nullptr) { delete resultProcess; resultProcess = nullptr; }

	for (int i = 0; i < timeSchemeVector.size(); i++)
	{
		if (timeSchemeVector[i] != nullptr)
		{
			delete timeSchemeVector[i];
			timeSchemeVector[i] = nullptr;
		}
	}

	if (fullMultigird != nullptr) { delete fullMultigird; fullMultigird = nullptr; }
	if (innerLoop != nullptr) { delete innerLoop; innerLoop = nullptr; }

	for (int i = 0; i < flowPackageVector.size(); i++)
	{
		if (flowPackageVector[i] != nullptr)
		{
			delete flowPackageVector[i];
			flowPackageVector[i] = nullptr;
		}
	}
}

void CFDDEMSolver::Initialize()
{
	MPIBarrier();

	if (GetMPIRank() == 0) Print("\n流场初始化...");

	// 物理场初始化
	flowPackageVector[0]->InitializeField(initialType);

	// 时间格式初始化
	timeSchemeVector[0]->Initialize(initialType);

	// 残差监控初始化
	resultProcess->Initialize(0);

	// 保存初始物理场
	const int innerInterval = flowConfigure.GetControl().innerLoop.interval;
	if (innerInterval > 0) resultProcess->SaveIntervalResults();

	// DEM 求解器初始化
    DEMSolver->Initialize();
}

void CFDDEMSolver::Solve()
{
	MPIBarrier();

	// 获取外循环流场输出间隔及总迭代步数
	const int &outerInterval = flowConfigure.GetControl().outerLoop.interval;
	const int &outerLoopSteps = flowConfigure.GetControl().outerLoop.steps;
	const int &averagedStep = flowConfigure.GetControl().outerLoop.averagedStep;
	int computeAveragedStep = 0;

	// 非定常计算总物理时间及当前物理时间
	const Scalar &totalTime = flowPackageVector[0]->GetUnsteadyStatus().totalTime;
	const Scalar &currentTime = flowPackageVector[0]->GetUnsteadyStatus().currentTime;

	if (GetMPIRank() == 0) Print("\n开始计算...");

	// 非定常计算,流场迭代求解外循环（物理时间迭代）
	for (int outerStep = 0; outerStep < outerLoopSteps; outerStep++)
	{
		resultProcess->Initialize(0);

		// 非定常计算时保存上一时间步物理场
		if(dualTime)
		{
			this->SetTimeStep();
			this->UpdateOldField(outerStep);
		}

		if (GetMPIRank() == 0)
		{
			std::cout << "\nCFD Solve ...";
			Info::infoFile << "\nCFD Solve ...";
		}

		// 内迭代求解
		innerLoop->Solve();
		const int &innerLoopSteps = flowConfigure.GetControl().innerLoop.steps;
		if (innerLoopSteps == 0 && GetMPIRank() == 0) Print("");

		// 外循环物理场输出
		if (unsteady)
		{
			resultProcess->MonitorPerStepOutLoop();
			CheckStatus(2401);

			if (currentTime >= totalTime || outerStep + 1 > outerLoopSteps || (outerInterval > 0 && (outerStep + 1) % outerInterval == 0))
			{
				resultProcess->SaveIntervalResults();
				CheckStatus(2402);
			}
		}

		// DEM求解
		if (GetMPIRank() == 0) Print("\nDEM Solve ...\n" + std::string(99, '='));
		DEMSolver->Solve();
		if (GetMPIRank() == 0) Print(std::string(99, '='));
	}
}

void CFDDEMSolver::CreateGeometry()
{
    // 计算域最大最小点
    Vector minDomain = subMesh->GetNode(0);
    Vector maxDomain = subMesh->GetNode(0);

	// //if (i == 1050)
	// {
	// 	int a = 0;
	// 	while (a ==0)
	// 	{
	// 		sleep(1);
	// 	}
	// }

    // 根据流场计算网格创建颗粒计算域几何信息
    if (GetMPIRank() == 0) Print("创建颗粒计算域几何信息... ", 1);
    geometry = new Geometry::Geometry();
    geometry->SetDimension((int)subMesh->GetMeshDimension());
    for (int patchID = 0; patchID < subMesh->GetBoundarySize(); patchID++)
    {
        boundaryTypeList.push_back(flowConfigure.GetLocalBoundary(0, patchID).type);
        const int faceSize = subMesh->GetBoundaryFaceSize(patchID);
        for (int index = 0; index < faceSize; index++)
        {
            const int &faceID = subMesh->GetBoundaryFaceID(patchID, index);
            const Face &face = subMesh->GetFace(faceID);
            const int &nodeSize = face.GetNodeSize();
            std::vector<Vector> nodeList(nodeSize);
            for (int i = 0; i < nodeSize; i++)
            {
				nodeList[i] = subMesh->GetNode(face.GetNodeID(i));
                minDomain = Min(minDomain, nodeList[i]);
                maxDomain = Max(maxDomain, nodeList[i]);
            }

            // 非壁面边界跳过
            if (!flowConfigure.JudgeWallLocal(0, patchID)) continue;

            geometry->AddPlaneWall(nodeList, patchID);
            const int wallID = geometry->GetPlaneWallNumber() - 1;
            const auto &wallNormal = geometry->GetWall(wallID).GetNormal();
            if ((wallNormal & face.GetNormal()) > 0) geometry->ReverseWall(wallID);
        }
    }
    if (GetMPIRank() == 0) Print( "几何边界包括碎面数量为" + ToString( geometry->GetPlaneWallNumber() ), 2);

	Scalar minDomainX = minDomain.X(), minDomainY = minDomain.Y(), minDomainZ = minDomain.Z();
	Scalar maxDomainX = maxDomain.X(), maxDomainY = maxDomain.Y(), maxDomainZ = maxDomain.Z();
	
	MinAllProcessor(minDomainX, 0);
	MinAllProcessor(minDomainY, 0);
	MinAllProcessor(minDomainZ, 0);
	MaxAllProcessor(maxDomainX, 0);
	MaxAllProcessor(maxDomainY, 0);
	MaxAllProcessor(maxDomainZ, 0);

	MPIBroadcast(minDomainX, 0);
	MPIBroadcast(minDomainY, 0);
	MPIBroadcast(minDomainZ, 0);
	MPIBroadcast(maxDomainX, 0);
	MPIBroadcast(maxDomainY, 0);
	MPIBroadcast(maxDomainZ, 0);

	minDomain = Vector(minDomainX, minDomainY, minDomainZ);
	maxDomain = Vector(maxDomainX, maxDomainY, maxDomainZ);

	// 更新计算域最大最小范围
    DEMConfigure.SetDomainMinMax(minDomain, maxDomain); 

	// 创建全局几何
	CreateGlobalGeometry();
}

void CFDDEMSolver::CreateGlobalGeometry()
{
	const int mpiRank = GetMPIRank();
	const int mpiSize = GetMPISize();

	if (mpiSize == 1 || subMesh == nullptr)
	{
		geometryGlobal = this->geometry;
		return;
	}

	std::vector<mpi::request> sendRequests;
	std::vector<mpi::request> recvRequests;

	std::vector<std::vector<Geometry::PlaneWall>> recvPlaneWallList;
	if (mpiRank > 0)
	{
		const int count = this->geometry->GetPlaneWallNumber();
		std::vector<Geometry::PlaneWall> sendPlaneWallList(count);
		for (int j = 0; j < count; j++)
		{
			sendPlaneWallList[j] = this->geometry->GetWall(j);
		}
		sendRequests.push_back(MPI::mpiWorld.isend(0, 0, sendPlaneWallList));
	}
	else
	{
		recvPlaneWallList.resize(mpiSize);
		for (int procID = 1; procID < mpiSize; procID++)
		{
			recvRequests.push_back(MPI::mpiWorld.irecv(procID, 0, recvPlaneWallList[procID]));
		}
	}
	MPIWaitAll(recvRequests);
	MPIWaitAll(sendRequests);

	geometryGlobal = new Geometry::Geometry();
	geometryGlobal->SetDimension((int)subMesh->GetMeshDimension());
	if (mpiRank == 0)
	{
		const int count = this->geometry->GetPlaneWallNumber();
		for (int j = 0; j < count; j++)
		{
			geometryGlobal->AddPlaneWall(this->geometry->GetWall(j));
		}

		for (int procID = 1; procID < mpiSize; procID++)
		{
			const auto &planeWallList = recvPlaneWallList[procID];
			const int listSize = planeWallList.size();
			for (int i = 0; i < listSize; i++)
			{
				geometryGlobal->AddPlaneWall(planeWallList[i]);
			}
		}
	}

	std::vector<Geometry::PlaneWall> planeWallList;
	if (mpiRank == 0)
	{
		const int count = this->geometryGlobal->GetPlaneWallNumber();
		planeWallList.resize(count);
		for (int j = 0; j < count; j++) planeWallList[j] = this->geometryGlobal->GetWall(j);
	}
	boost::mpi::broadcast(MPI::mpiWorld, planeWallList, 0);

	if (mpiRank != 0)
	{
		const int count = planeWallList.size();
		for (int j = 0; j < count; j++)
		{
			geometryGlobal->AddPlaneWall(planeWallList[j]);
		}
	}
}

void CFDDEMSolver::SetTimeStep()
{
	const Scalar &timeStep = flowPackageVector[0]->GetUnsteadyStatus().timeStep;
	const Scalar &currentTime = flowPackageVector[0]->GetUnsteadyStatus().currentTime;

	for (int level = 0; level < flowPackageVector.size(); ++level)
		flowPackageVector[level]->UpdateCurrentTime(timeStep);
	
	DEMSolver->SetTimeStep(timeStep, currentTime - timeStep);
}

void CFDDEMSolver::UpdateOldField(const int &outerStep)
{
	for (int level = 0; level < flowConfigure.GetAcceleration().multigridSolver.level; ++level)
	{
		const int unsteadyFieldSize = (int)flowPackageVector[0]->GetFieldUnsteadyField().size();
		for (int i = unsteadyFieldSize - 1; i > 0; --i)
		{
			const int j = i - 1;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].density = *flowPackageVector[level]->GetFieldUnsteadyField()[j].density;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].velocity = *flowPackageVector[level]->GetFieldUnsteadyField()[j].velocity;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].pressure = *flowPackageVector[level]->GetFieldUnsteadyField()[j].pressure;
			for (int k = 0; k < flowPackageVector[level]->GetTurbulentStatus().nVariable; ++k)
				*flowPackageVector[level]->GetFieldUnsteadyField()[i].turbulence[k] = *flowPackageVector[level]->GetFieldUnsteadyField()[j].turbulence[k];
		}
	}
}
