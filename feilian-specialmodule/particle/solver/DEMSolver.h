﻿#ifndef _particle_solver_DEMSolver_
#define _particle_solver_DEMSolver_

#include "basic/postTools/Tecplot.h"
#include "sourceFlow/package/FlowPackage.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/contact/contactForce/LinearForce.h"
#include "feilian-specialmodule/particle/contact/contactForce/NonlinearForce.h"
#include "feilian-specialmodule/particle/contact/contactSearch/SearchParticleParticle.h"
#include "feilian-specialmodule/particle/contact/contactSearch/SearchParticleWall.h"
#include "feilian-specialmodule/particle/force/ExternalForce.h"
#include "feilian-specialmodule/particle/integration/TranslationIntegration.h"
#include "feilian-specialmodule/particle/integration/RotationIntegration.h"
#include "feilian-specialmodule/particle/solver/Insertion.h"
#include "feilian-specialmodule/particle/backgroundGrid/BackgroundGrid.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 颗粒求解器类
 * 
 */
class DEMSolver
{
public:
    /**
     * @brief 颗粒求解器构造函数
     * 
     * @param[in] configure_ 控制参数
     * @param[in] geometry_ 几何
     * @param[in] boundaryTypeList_ 边界属性列表
     * @param[in] localMesh_ 流场网格
     * @param[in] flowPackage_  流场物理场
     * 
     */
    DEMSolver(const Configure::Particle::ParticleConfigure &configure_,
              Geometry::Geometry *geometry_,
              const std::vector<Boundary::Type> &boundaryTypeList_,
              SubMesh *localMesh_ = nullptr,
              const Package::FlowPackage *flowPackage_ = nullptr);
    
    /**
     * @brief 析构函数
     * 
     */
    ~DEMSolver();

    /// 迭代求解
    void Solve();
    
    /// 初始化
    void Initialize();

    // 设置计算时间步长
	void SetTimeStep(const Scalar &timeStep_, const Scalar &currentTime_);
    
private:
	/// 创建全局几何
	void CreateGlobalGeometry();

    /// 创建物理属性对象
    void CreateProperty();
    
    /// 创建颗粒注入对象
    void CreateInsertion();

    /// 创建接触力对象
    void CreateContactForce();
   
    /// 创建接触搜索对象
    void CreateContactSearch();
    
    /// 创建积分对象
    void CreateIntegration();
    
	/// 创建背景网格信息
	void CreateBackGroundInfo();

    /// 创建外部力计算对象
    void CreateExternalForce();

	/// 计算前准备工作(颗粒注入，接触信息、接触力置零)
	void PreCalculate();

	/// 检查注入的颗粒
	void CheckInsertedParticles();

	/// 更新颗粒标识
	void UpdateParticleFlag();

	/// 计算颗粒加速度
	void CalculateAcceleration(const int &numIter);

    /// 统计计算域内的颗粒数量
    void CountParticlesInDomain();

	/// 更新空间颗粒体积分数
	void UpdateVolumeFraction();

	/// 计算时间步长
	void CalculateDeltaT();

	/// 输出解
	void OutputSolution();

	/// 收集颗粒
	void GatherParticles(std::vector<int> &recvParticleIDList);

	/// 按照Tecplot格式输出颗粒和几何
	void WriteGeometryAndParticlesTecplot();

	/// 输出颗粒信息
	void WriteParticleFields();

	/// 检测颗粒信息
	void MonitorParticle();

	/// 打印信息
	void PrintInfomation();

private:
    /// 控制参数
    const Configure::Particle::ParticleConfigure &configure;
    
    /// 颗粒注入对象
    Insertion *insertion;
    
    /// 计算域几何属性
    Geometry::Geometry *geometry;

    /// 计算域几何属性
    SubMesh *localMesh;
    
    /// 边界属性列表
    const std::vector<Boundary::Type> &boundaryTypeList;

    /// 流场物理场
    const Package::FlowPackage *flowPackage;

    /// 颗粒分布属性（含颗粒材料属性、位置、速度、质量等）
    DistributionProperty *distributionProperty;
    
    /// 颗粒物理属性对象（含颗粒属性、壁面属性、颗粒颗粒接触、颗粒壁面接触）
    PhysicalProperty *physicalProperty;
    
	/// 颗粒碰撞计算标识
	bool contactFlagPP;

    /// 接触力计算对象
    Contact::BaseForce *contactforce;
    
    /// 颗粒颗粒接触信息
    Contact::ContactList *contactListPP;

    /// 颗粒壁面接触信息
    Contact::ContactList *contactListPW;
    
    /// 颗粒颗粒接触搜索对象
    Contact::SearchParticleParticle *contactSearchPP;

    /// 颗粒壁面接触搜索对象
    Contact::SearchParticleWall *contactSearchPW;
    
    /// 平移运动积分
    TranslationIntegration *integrationTranslation;

    /// 旋转运动积分
    RotationIntegration *integrationRotation;
    
	/// 背景网格对象
	BackgroundGrid *backgroundGrid;

	/// 外部力对象
    ExternalForce *externalForce;

    /// 当前步数
    int currentStep;
    
    /// 当前非定常计算步数
    int currentStepOutloop;
    
    /// 颗粒运动积分时间步长
    Scalar deltaTFromFlow;

	/// 当前计算物理时间
	Scalar currentTime;

	/// 计算起始物理时间
	Scalar startTime;

	/// 计算结束物理时间
	Scalar stopTime;

	/// 颗粒运动积分时间步长
	Scalar deltaT;

    /// 最大颗粒数量
    int numParticleMaximum;

    /// 当前进程的所有颗粒数量
    int numParticleCurrent;

	/// 当前进程包含的颗粒数量
	int numParticlesInDomain;

	/// 当前计算域包含的颗粒数量
	int numParticlesInDomainTotal;

	/// 出现在虚单元中的颗粒数量
	int numParticlesInGhost;

	/// 全局出现在虚单元中的颗粒数量
	int numParticlesInGhostTotal;

    /// 每次注入的颗粒数量                           
    int numParticleInserted;
    
	/// 全局颗粒搜索次数
	int numContactSearchPPTotal;

	/// 全局颗粒与颗粒碰撞次数
	int numContactPPTotal;

	/// 全局颗粒与壁面碰撞次数
	int numContactPWTotal;

    /// 颗粒属性                      
    std::vector<Particle*> particles;

    /// 背景网格信息
    std::vector<int> backgroundID;
    
	/// CPU时钟对象，用于计算求解所需耗时
	SystemTime time;

	/// 二维计算标识
	bool dim2;

	/// 新注入颗粒标识
	std::vector<bool> newInserted;

	/// 颗粒体积分数场
	ElementField<Scalar> *volumeFraction;

	/// 接触力矩计算标识
	bool contactTorqueFlag;
};

} // namespace Particle

#endif