﻿#ifndef _particle_solver_CFDDEMSolver_
#define _particle_solver_CFDDEMSolver_

#include "sourceFlow/flowSolver/FullMultigird.h"
#include "sourceFlow/flowSolver/InnerLoop.h"
#include "feilian-specialmodule/particle/solver/DEMSolver.h"

/**
 * @brief 流场计算外循环类
 * 
 */
class CFDDEMSolver
{
public:
    /**
     * @brief流场解算器构造函数
     * 根据设置参数文件，建立基于当地网格的特定流体流动解算器
     * 
     * @param[in] subMesh_ 当地网格，含细网格和所有粗网格
     * @param[in] flowConfig_ 流场相关设置参数
     * @param[in] DEMConfigure_ 颗粒相关设置参数
     */
    CFDDEMSolver(SubMesh *subMesh_,
                 Configure::Flow::FlowConfigure &flowConfig_,
                 Configure::Particle::ParticleConfigure &DEMConfigure_);

    /**
     * @brief 析构函数
     * 释放流场解算器构造函数中通过new建立的数据
     * 
     */
    ~CFDDEMSolver();

    /**
     * @brief 流场解算器求解函数
     * 
     */
    void Solve();

    /**
     * @brief 流场初始化
     * 
     */
    void Initialize();

private:

    /**
     * @brief 创建DEM计算所需几何域
     * 
     */
    void CreateGeometry();

    /**
     * @brief 创建DEM计算所需几何域
     * 
     */
    void CreateGlobalGeometry();

    /**
     * @brief 设置当前计算物理时间步长
     * 
     */
    void SetTimeStep();
    
    /**
     * @brief 保存上一物理时间步流场解变量
     * 
     * @param[in] outerStep 外循环迭代步数
     */
    void UpdateOldField(const int &outerStep);

private:
    /// 当地网格，含细网格和所有粗网格
    SubMesh *subMesh;

    /// 流场相关设置参数，含输入输出控制、离散格式、求解策略、边界参数等
    Configure::Flow::FlowConfigure &flowConfigure;

    /// 颗粒计算相关参数
    Configure::Particle::ParticleConfigure &DEMConfigure;

    /// 由细网格和所有粗网格上的物理场包所构成的容器, 容器大小为总网格层数
    std::vector<Package::FlowPackage *> flowPackageVector;

    /// 对于不同层级的网格，流场求解时采用的具体时间推进对象所构成的容器, 容器大小为总网格层数
    std::vector<Time::Flow::FlowTimeManager *> timeSchemeVector;

    /// 多重网格总层数
    int multigridLevel;

    /// 多重网格求解器对象
    FullMultigird *fullMultigird;

    /// 内循环求解器
    InnerLoop *innerLoop;

    /// 流场计算结果输出对象
    FlowResultsProcess *resultProcess;

    /// DEM求解器
    Particle::DEMSolver *DEMSolver;

    /// DEM求解所需几何域
    Geometry::Geometry *geometry;

    /// DEM求解所需几何域
    Geometry::Geometry *geometryGlobal;

    /// 边界类型列表
    std::vector<Boundary::Type> boundaryTypeList;

    /// 初始化类型
    Initialization::Type initialType;

    /// 非定常标识,true为非定常，false为定常
    bool unsteady;

    /// 双时间步标识,true为双时间步，false为定常
    bool dualTime;

    /// 全多重标识
    bool fullMultigirdFlag;
};
#endif
