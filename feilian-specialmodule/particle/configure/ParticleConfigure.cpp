﻿#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"

#if defined(_BasePlatformWinddows_)
#include <direct.h>
#else
#include <unistd.h>
#endif

namespace Configure
{

namespace Particle
{

ParticleConfigure::ParticleConfigure()
{
    workPath = getcwd(nullptr, 0);
    workPath = workPath + "/";
#if defined(_BasePlatformWinddows_)
    while (workPath.rfind("\\") != workPath.npos) workPath.replace(workPath.rfind("\\"), 1, "/");
#endif

	caseName = "default";
}

void ParticleConfigure::ReadParameter(const std::string &fileName)
{
    // 读取基本控制参数
    ReadBasicCaseXml(fileName);

    //创建基本参数文件PropertyTree
	PropertyTree ptree(fileName, &mapPosValue);
    
    // 读取算例名称
	this->ReadCaseName(ptree);
    
    // 读取颗粒计算节点
    PropertyTree paticleNode(ptree, "particle");

    // 读取参考量
	this->ReadReferenceParameter(paticleNode);
    
    // 读取颗粒材料参数
	this->ReadMaterialParameter(paticleNode);
    
    // 读取颗粒碰撞物性参数
	this->ReadBinaryParameters(paticleNode);
    
    // 读取颗粒注入参数
	this->ReadInsertionParameter(paticleNode);
    
    // 读取颗粒分布参数
	this->ReadDistributionParameter(paticleNode);
    
    // 读取颗粒接触计算参数
	this->ReadContactCalculationParameter(paticleNode);

	// 读取颗粒外部力计算参数
	this->ReadExternalForceParameter(paticleNode);

    // 读取计算控制参数
	this->ReadControlParameter(paticleNode);

	// 读取监控控制参数
	this->ReadMonitorParameter(paticleNode);

	// 打印基本参数
    this->PrintBasicInformation();
}

void ParticleConfigure::SetDomainMinMax(const Vector &minDomain, const Vector &maxDomain)
{
    reference.minDomain = Max(minDomain, reference.minDomain);
    reference.maxDomain = Min(maxDomain, reference.maxDomain);
}

void ParticleConfigure::ReadCaseName(PropertyTree &ptree)
{
    ReadNodeValue(ptree, std::string("caseName"), caseName, false);
    if(caseName.rfind("/") != caseName.npos || caseName.rfind("\\") != caseName.npos)
        FatalError("ParticleConfigure::ReadCaseName: case name is wrong!");
}

void ParticleConfigure::ReadReferenceParameter(PropertyTree &ptree)
{
	PropertyTree referenceNode;
	if (!ptree.GetChildTree("reference", referenceNode)) return;

    ReadNodeValue(referenceNode, std::string("gravity"), reference.gravity);
    ReadNodeValue(referenceNode, std::string("minDomain"), reference.minDomain);
    ReadNodeValue(referenceNode, std::string("maxDomain"), reference.maxDomain);
}

void ParticleConfigure::ReadMaterialParameter(PropertyTree &ptree)
{
	PropertyTree materialNode;
	if (!ptree.GetChildTree("material", materialNode)) return;

    ReadNodeValue(materialNode, std::string("density"), material.density);
    ReadNodeValue(materialNode, std::string("YoungModulus"), material.YoungModulus);
    ReadNodeValue(materialNode, std::string("PoissonRatio"), material.PoissonRatio);
    ReadNodeValue(materialNode, std::string("yeildStress"), material.yeildStress);
}

void ParticleConfigure::ReadBinaryParameters(PropertyTree &ptree)
{
    PropertyTree binaryPPNode(ptree, "binaryParticleParticle");
    ReadNodeValue(binaryPPNode, std::string("mu"), binaryParasPP.mu);
    ReadNodeValue(binaryPPNode, std::string("mur"), binaryParasPP.mur);
    ReadNodeValue(binaryPPNode, std::string("en"), binaryParasPP.en);
    ReadNodeValue(binaryPPNode, std::string("et"), binaryParasPP.et);
    ReadNodeValue(binaryPPNode, std::string("relativeVelocity"), binaryParasPP.relativeVelocity);
    
    PropertyTree binaryPWNode(ptree, "binaryParticleWall");
    ReadNodeValue(binaryPWNode, std::string("mu"), binaryParasPW.mu);
    ReadNodeValue(binaryPWNode, std::string("mur"), binaryParasPW.mur);
    ReadNodeValue(binaryPWNode, std::string("en"), binaryParasPW.en);
    ReadNodeValue(binaryPWNode, std::string("et"), binaryParasPW.et);
    ReadNodeValue(binaryPWNode, std::string("relativeVelocity"), binaryParasPW.relativeVelocity);
}

void ParticleConfigure::ReadInsertionParameter(PropertyTree &ptree)
{
	PropertyTree insertionNode;
	if (!ptree.GetChildTree("insertion", insertionNode)) return;

    std::string stringTemp1 = insertionNode.ReadString("method", true, insertionMethodReverseMap.find(insertion.method)->second);
    if(insertionMethodMap.find(stringTemp1) != insertionMethodMap.end()) insertion.method = insertionMethodMap.find(stringTemp1)->second;

	if (insertion.insertionBox != nullptr) { delete insertion.insertionBox; insertion.insertionBox = nullptr; }
	if (insertion.insertionFile != nullptr) { delete insertion.insertionFile; insertion.insertionFile = nullptr; }

    if(insertion.method == InsertionMethod::BOX)
    {
        PropertyTree insertionBoxNode(insertionNode, "insertionBox");
		insertion.insertionBox = new InsertionBox;

		auto &insertionBox = insertion.insertionBox;
        ReadNodeValue(insertionBoxNode, std::string("steps"), insertionBox->steps);
		ReadNodeValue(insertionBoxNode, std::string("velocity"), insertionBox->velocity);

		std::string stringTemp2 = insertionBoxNode.ReadString("portShape", true, insertionShapeReverseMap.find(insertionBox->portShape)->second);
		if (insertionShapeMap.find(stringTemp2) != insertionShapeMap.end()) insertionBox->portShape = insertionShapeMap.find(stringTemp2)->second;

		if (insertionBox->portShape == InsertionShape::PLANE)
        {
            PropertyTree insertPlaneNode(insertionBoxNode, "insertionPlaneNodes");
            int nodeSize = 0;
            ReadNodeValue(insertPlaneNode, std::string("nodeSize"), nodeSize);
            if (nodeSize < 2 || nodeSize > 4) FatalError("Configure::ReadInsertionParameter: plane node size is wrong.");
			insertionBox->insertionShapeNodes.resize(nodeSize);

            for (int i = 0; i < nodeSize; i ++)
            {
				ReadNodeValue(insertPlaneNode, std::string("p" + ToString(i)), insertionBox->insertionShapeNodes[i]);
            }
        }
		else if (insertionBox->portShape == InsertionShape::ZONE)
		{
            PropertyTree insertZoneNode(insertionBoxNode, "insertionZoneNodes");
            
    		Vector minDomain = this->reference.minDomain;
    		Vector maxDomain = this->reference.maxDomain;
    		ReadNodeValue(insertZoneNode, std::string("minDomain"), minDomain);
    		ReadNodeValue(insertZoneNode, std::string("maxDomain"), maxDomain);
			minDomain = Max(this->reference.minDomain, minDomain);
			maxDomain = Min(this->reference.maxDomain, maxDomain);
			
			const bool dim2 = (maxDomain.Z() - minDomain.Z()) > SMALL;
			auto &nodes = insertionBox->insertionShapeNodes;
			nodes.resize(dim2 ? 4 : 8);
			nodes[0] = Vector(minDomain.X(), minDomain.Y(), minDomain.Z());
			nodes[1] = Vector(minDomain.X(), maxDomain.Y(), minDomain.Z());
			nodes[2] = Vector(maxDomain.X(), maxDomain.Y(), minDomain.Z());
			nodes[3] = Vector(maxDomain.X(), minDomain.Y(), minDomain.Z());
			if (!dim2)
			{
				nodes[4] = Vector(minDomain.X(), minDomain.Y(), maxDomain.Z());
				nodes[5] = Vector(minDomain.X(), maxDomain.Y(), maxDomain.Z());
				nodes[6] = Vector(maxDomain.X(), maxDomain.Y(), maxDomain.Z());
				nodes[7] = Vector(maxDomain.X(), minDomain.Y(), maxDomain.Z());
			}
		}
	}
    else if(insertion.method == InsertionMethod::FILE)
    {
        PropertyTree insertionFileNode(insertionNode, "insertionFile");
		insertion.insertionFile = new InsertionFile;

		ReadNodeValue(insertionFileNode, std::string("binary"), insertion.insertionFile->binary);
		ReadNodeValue(insertionFileNode, std::string("fileName"), insertion.insertionFile->fileName);
    }
}

void ParticleConfigure::ReadDistributionParameter(PropertyTree &ptree)
{
	PropertyTree distributionNode;
	if (!ptree.GetChildTree("distribution", distributionNode)) return;

    ReadNodeValue(distributionNode, std::string("numParticles"), distribution.numParticles);
    ReadNodeValue(distributionNode, std::string("numBins"), distribution.numBins);
    
    std::string stringTemp = distributionNode.ReadString("distributionType", true, distributionTypeReverseMap.find(distribution.distributionType)->second);
    if(distributionTypeMap.find(stringTemp) != distributionTypeMap.end()) distribution.distributionType = distributionTypeMap.find(stringTemp)->second;
    
    ReadNodeValue(distributionNode, std::string("minDiameter"), distribution.minDiameter);
    ReadNodeValue(distributionNode, std::string("maxDiameter"), distribution.maxDiameter);
}

void ParticleConfigure::ReadContactCalculationParameter(PropertyTree &ptree)
{
	PropertyTree contactCalculationNode;
	if (!ptree.GetChildTree("contactCalculation", contactCalculationNode)) return;

	ReadNodeValue(contactCalculationNode, std::string("particlesContactFlag"), contact.particlesContactFlag);

	std::string stringTemp0 = contactCalculationNode.ReadString("wallContactType", true, wallContactTypeReverseMap.find(contact.wallContactType)->second);
	if (wallContactTypeMap.find(stringTemp0) != wallContactTypeMap.end()) contact.wallContactType = wallContactTypeMap.find(stringTemp0)->second;

    std::string stringTemp1 = contactCalculationNode.ReadString("contactForceType", true, contactForceTypeReverseMap.find(contact.contactForceType)->second);
    if(contactForceTypeMap.find(stringTemp1) != contactForceTypeMap.end()) contact.contactForceType = contactForceTypeMap.find(stringTemp1)->second;
    
    std::string stringTemp2 = contactCalculationNode.ReadString("contactTorqueType", true, contactTorqueTypeReverseMap.find(contact.contactTorqueType)->second);
    if(contactTorqueTypeMap.find(stringTemp2) != contactTorqueTypeMap.end()) contact.contactTorqueType = contactTorqueTypeMap.find(stringTemp2)->second;
    
    std::string stringTemp3 = contactCalculationNode.ReadString("contactSearchMethod", true, contactSearchMethodReverseMap.find(contact.contactSearchMethod)->second);
    if(contactSearchMethodMap.find(stringTemp3) != contactSearchMethodMap.end()) contact.contactSearchMethod = contactSearchMethodMap.find(stringTemp3)->second;
    
    ReadNodeValue(contactCalculationNode, std::string("numLevel"), contact.numLevel);
    ReadNodeValue(contactCalculationNode, std::string("sizeRatio"), contact.sizeRatio);
}

void ParticleConfigure::ReadExternalForceParameter(PropertyTree &ptree)
{
	PropertyTree externalForceNode;
	if (!ptree.GetChildTree("externalForce", externalForceNode)) return;

	ReadNodeValue(externalForceNode, std::string("dragForce"), externalForce.dragForce);
	ReadNodeValue(externalForceNode, std::string("liftForce"), externalForce.liftForce);
	ReadNodeValue(externalForceNode, std::string("pressureGradientForce"), externalForce.pressureGradientForce);
	ReadNodeValue(externalForceNode, std::string("addedMassForce"), externalForce.addedMassForce);
	ReadNodeValue(externalForceNode, std::string("rotationlDrag"), externalForce.rotationlDrag);

	PropertyTree interpolationNode;
	if (!externalForceNode.GetChildTree("interpolation", interpolationNode)) return;

	std::string stringTemp1 = interpolationNode.ReadString("method", true, interpolationMethodReverseMap.find(externalForce.interpolation.method)->second);
	if (interpolationMethodMap.find(stringTemp1) != interpolationMethodMap.end()) externalForce.interpolation.method = interpolationMethodMap.find(stringTemp1)->second;

	ReadNodeValue(interpolationNode, std::string("conservationFlag"), externalForce.interpolation.conservationFlag);
}

void ParticleConfigure::ReadControlParameter(PropertyTree &ptree)
{
	PropertyTree controlNode;
	if (!ptree.GetChildTree("control", controlNode)) return;

    std::string stringTemp1 = controlNode.ReadString("translationIntegrationScheme", true, motionIntegrationSchemeReverseMap.find(control.translationIntegrationScheme)->second);
    if(motionIntegrationSchemeMap.find(stringTemp1) != motionIntegrationSchemeMap.end()) control.translationIntegrationScheme = motionIntegrationSchemeMap.find(stringTemp1)->second;
    
    std::string stringTemp2 = controlNode.ReadString("rotationIntegrationScheme", true, motionIntegrationSchemeReverseMap.find(control.rotationIntegrationScheme)->second);
    if(motionIntegrationSchemeMap.find(stringTemp2) != motionIntegrationSchemeMap.end()) control.rotationIntegrationScheme = motionIntegrationSchemeMap.find(stringTemp2)->second;
    
    ReadNodeValue(controlNode, std::string("timeStep"), control.timeStep);
	ReadNodeValue(controlNode, std::string("totalSteps"), control.totalSteps);
	ReadNodeValue(controlNode, std::string("variableTimeStep"), control.variableTimeStep);
    ReadNodeValue(controlNode, std::string("monitorInterval"), control.monitorInterval);
    ReadNodeValue(controlNode, std::string("saveInterval"), control.saveInterval);

    ReadNodeValue(controlNode, std::string("resultsPath"), control.resultsPath, false);
    control.resultsPath = ObtainAbsolutePath(control.resultsPath);

	std::string stringTemp3 = controlNode.ReadString("outputFileType", true, fileTypeReverseMap.find(control.outputFileType)->second);
	if (fileTypeMap.find(stringTemp3) != fileTypeMap.end()) control.outputFileType = fileTypeMap.find(stringTemp3)->second;

	ReadNodeValue(controlNode, std::string("outputFileBinary"), control.outputFileBinary);

    ReadNodeValue(controlNode, std::string("outputLevelFile"), control.outputLevelFile);
    ReadNodeValue(controlNode, std::string("outputLevelScreen"), control.outputLevelScreen);
}

void ParticleConfigure::ReadMonitorParameter(PropertyTree &ptree)
{
	PropertyTree monitorNode;
	if (!ptree.GetChildTree("monitor", monitorNode)) return;

	ReadNodeValue(monitorNode, std::string("particleID"), monitor.particleID);
	ReadNodeValue(monitorNode, std::string("positionFlag"), monitor.positionFlag);
	ReadNodeValue(monitorNode, std::string("velocityFlag"), monitor.velocityFlag);
	ReadNodeValue(monitorNode, std::string("forceFlag"), monitor.forceFlag);
}

void ParticleConfigure::PrintBasicInformation()
{
    std::ostringstream outString;

	outString.str("");
	outString << " Basic parameters: " << std::endl
        << "\t" << "caseName: " << caseName << std::endl
        << "\t" << "workPath: " << workPath << std::endl;
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "reference parameters: " << std::endl
		<< "\t\t" << "gravity: " << reference.gravity << std::endl
		<< "\t\t" << "minDomain: " << reference.minDomain << std::endl
		<< "\t\t" << "maxDomain: " << reference.maxDomain << std::endl;
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "material parameters: " << std::endl
		<< "\t\t" << "density: " << material.density << std::endl
		<< "\t\t" << "YoungModulus: " << material.YoungModulus << std::endl
		<< "\t\t" << "PoissonRatio: " << material.PoissonRatio << std::endl
		<< "\t\t" << "yeildStress: " << material.yeildStress << std::endl;
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "particle-particle binary parameters: " << std::endl
		<< "\t\t" << "mu: " << binaryParasPP.mu << std::endl
		<< "\t\t" << "mur: " << binaryParasPP.mur << std::endl
		<< "\t\t" << "en: " << binaryParasPP.en << std::endl
		<< "\t\t" << "et: " << binaryParasPP.et << std::endl
		<< "\t\t" << "relativeVelocity: " << binaryParasPP.relativeVelocity << std::endl;
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "particle-wall binary parameters: " << std::endl
		<< "\t\t" << "mu: " << binaryParasPW.mu << std::endl
		<< "\t\t" << "mur: " << binaryParasPW.mur << std::endl
		<< "\t\t" << "en: " << binaryParasPW.en << std::endl
		<< "\t\t" << "et: " << binaryParasPW.et << std::endl
		<< "\t\t" << "relativeVelocity: " << binaryParasPW.relativeVelocity << std::endl;
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "insertion parameters: " << std::endl
		<< "\t\t" << "insertion method: " << insertionMethodReverseMap.find(insertion.method)->second << std::endl;
    if(insertion.method == InsertionMethod::BOX)
    {
		outString << "\t\t" << "insertion steps: " << insertion.insertionBox->steps << std::endl
			<< "\t\t" << "insertion velocity: " << insertion.insertionBox->velocity << std::endl
			<< "\t\t" << "insertion port shape: " << insertionShapeReverseMap.find(insertion.insertionBox->portShape)->second << std::endl;
		if (insertion.insertionBox->portShape == InsertionShape::PLANE)
        {
	    	outString << "\t\t" << "insertion plane: " << std::endl;
			for (int i = 0; i < insertion.insertionBox->insertionShapeNodes.size(); i++)
            {
				outString << "\t\t\tp" << ToString(i) << ": " << insertion.insertionBox->insertionShapeNodes[i] << std::endl;
            }
        }
		else if (insertion.insertionBox->portShape == InsertionShape::ZONE)
		{
	    	outString << "\t\t" << "insertion ZONE: " << std::endl;
			const int nNodes = insertion.insertionBox->insertionShapeNodes.size();
			outString << "\t\t\tminDomain: " << insertion.insertionBox->insertionShapeNodes[0] << std::endl;
			outString << "\t\t\tmaxDomain: " << insertion.insertionBox->insertionShapeNodes[nNodes - 2] << std::endl;
		}
	}
    else if(insertion.method == InsertionMethod::FILE)
	{
		outString << "\t\t" << "binary: " << insertion.insertionFile->binary << std::endl;
		outString << "\t\t" << "file name: " << insertion.insertionFile->fileName << std::endl;
    }
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "distribution parameters: " << std::endl
		<< "\t\t" << "particle number: " << distribution.numParticles << std::endl
		<< "\t\t" << "bin number: " << distribution.numBins << std::endl
		<< "\t\t" << "distribution type: " << distributionTypeReverseMap.find(distribution.distributionType)->second << std::endl
		<< "\t\t" << "min diameter: " << distribution.minDiameter << std::endl
		<< "\t\t" << "max diameter: " << distribution.maxDiameter << std::endl;
	PrintFile(outString.str());
    
	outString.str("");
	outString << "\t" << "contact parameters: " << std::endl
		<< "\t\t" << "contact force type: " << contactForceTypeReverseMap.find(contact.contactForceType)->second << std::endl
		<< "\t\t" << "contact torque type: " << contactTorqueTypeReverseMap.find(contact.contactTorqueType)->second << std::endl
		<< "\t\t" << "contact search method: " << contactSearchMethodReverseMap.find(contact.contactSearchMethod)->second << std::endl
		<< "\t\t" << "numLevel: " << contact.numLevel << std::endl
		<< "\t\t" << "sizeRatio: " << contact.sizeRatio << std::endl;
	PrintFile(outString.str());

	outString.str("");
	outString << "\t" << "control parameters: " << std::endl
		<< "\t\t" << "translation integration scheme: " << motionIntegrationSchemeReverseMap.find(control.translationIntegrationScheme)->second << std::endl
		<< "\t\t" << "rotation integration scheme: " << motionIntegrationSchemeReverseMap.find(control.rotationIntegrationScheme)->second << std::endl
		<< "\t\t" << "time step: " << control.timeStep << std::endl
		<< "\t\t" << "total steps: " << control.totalSteps << std::endl
		<< "\t\t" << "variable time step: " << control.variableTimeStep << std::endl
		<< "\t\t" << "monitor interval: " << control.monitorInterval << std::endl
		<< "\t\t" << "save interval: " << control.saveInterval << std::endl
		<< "\t\t" << "output file type: " << fileTypeReverseMap.find(control.outputFileType)->second << std::endl
		<< "\t\t" << "output file binary: " << control.outputFileBinary << std::endl
		<< "\t\t" << "results path: " << control.resultsPath << std::endl
		<< "\t\t" << "output level for file: " << control.outputLevelFile << std::endl
		<< "\t\t" << "output level for screen: " << control.outputLevelScreen << std::endl;
	PrintFile(outString.str());
}

std::string ParticleConfigure::ObtainAbsolutePath(std::string stringTemp)
{
    // 判断是否为相对路径
    bool relativePathFlag = true;
#if defined(_BasePlatformWinddows_)
    if (stringTemp.find(":") != stringTemp.npos) relativePathFlag = false;
#else
    if (stringTemp.find("/") == 0) relativePathFlag = false;
#endif

    // 提取路径，并将相对路径转为绝对路径
    std::string absolutePath = stringTemp;
    if (relativePathFlag)
    {
        if (stringTemp.find("./") == 0) absolutePath = absolutePath.substr(2, -1);
        absolutePath = workPath + absolutePath;
    }

    while (absolutePath.find("../") != absolutePath.npos)
    {
        std::string::size_type pos = absolutePath.find("../");
        std::string tempPath = absolutePath.substr(0, pos - 1);

        std::string::size_type pos1 = tempPath.rfind("/");
        if (pos1 != tempPath.npos) tempPath = absolutePath.substr(0, pos1);
        else                       FatalError("Configure::ObtainAbsolutePath: path is wrong!");

        absolutePath = tempPath + absolutePath.substr(pos + 2, -1);
    }

    if (absolutePath.rfind("/") != absolutePath.length() - 1) absolutePath += "/";

    return absolutePath;
}

} // namespace Particle

} // namespace Configure
