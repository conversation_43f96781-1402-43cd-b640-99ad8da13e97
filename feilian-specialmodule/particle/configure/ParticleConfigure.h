﻿#ifndef _particle_configure_ParticleConfigure_
#define _particle_configure_ParticleConfigure_

#include "basic/configure/Configure.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigureMacro.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigureMacro.hxx"

/**
 * @brief 参数命名空间
 * 
 */
namespace Configure
{

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 参考量
 * 
 */
struct ReferenceParameter
{
    Vector gravity; ///< 重力加速度
    Vector minDomain; ///< 计算域最小坐标
    Vector maxDomain; ///< 计算域最大坐标
	
	ReferenceParameter()
	{
    	gravity = Vector(0.0, -9.8, 0.0);
    	minDomain = Vector(-INF, -INF,  -INF);
    	maxDomain = Vector( INF , INF ,  INF);
	}
};

struct MaterialParameter
{
	Scalar density; ///< 密度
    Scalar YoungModulus; ///< 杨氏模量
    Scalar PoissonRatio; ///< 泊松比
	Scalar yeildStress; ///< 屈服应力

	MaterialParameter()
	{
		density = 2500.0;
    	YoungModulus = 1000000.0;
    	PoissonRatio = 0.23;
		yeildStress = 0.0;
	}
};

struct BinaryParameters
{
	Scalar mu;
	Scalar mur;
	Scalar en;
	Scalar et;
    Scalar relativeVelocity; ///< 计算弹性硬度中的相对速度relative particle velocity for calculating spring stiffness
	BinaryParameters()
	{
		mu = 0.2;
		mur = 0.1;
		en = 0.8;
		et = 0.8;
		relativeVelocity = 1.0;
	}
};

struct InsertionBox
{
	int steps; ///< 注入步数
	Vector velocity; ///< 注入颗粒速度大小
	InsertionShape portShape; ///< 注入口形状
	std::vector<Vector> insertionShapeNodes; ///< 注入形状端点

	InsertionBox()
	{
		portShape = InsertionShape::PLANE;
		insertionShapeNodes.clear();
		steps = 0;
		velocity = Vector0;
	}
};

struct InsertionFile
{
	bool binary; ///< 二进制文件标识
	std::string fileName; ///< 文件名称

	InsertionFile()
	{
		binary = true;
		fileName = "";
	}
};

/**
 * @brief 颗粒注入参数
 * 
 */
struct InsertionParameter
{
	InsertionMethod method; ///< 注入方法
	InsertionBox *insertionBox;
	InsertionFile *insertionFile;

	InsertionParameter()
	{
		method = InsertionMethod::BOX;
		insertionBox = new InsertionBox();
		insertionFile = nullptr;
	}
};

/// 颗粒分布参数
struct DistributionParameter
{
    int numParticles; ///< 颗粒数量
    int numBins; ///< 颗粒分组数量
    DistributionType distributionType; ///< 颗粒大小分布类型
    Scalar minDiameter; ///< 颗粒最小尺寸
    Scalar maxDiameter; ///< 颗粒最大尺寸

	DistributionParameter()
	{
    	numParticles = 10;
    	numBins = 1; 
    	distributionType = DistributionType::UNIFORM;
    	minDiameter = 0.04;
    	maxDiameter = 0.04;
	}
};

struct ContactCalculationParameter
{
	bool particlesContactFlag; ///< 颗粒碰撞计算标识
	WallContactType wallContactType; ///< 壁面接触类型
    ContactForceType contactForceType; ///< 接触力计算方法
    ContactTorqueType contactTorqueType; ///< 接触力矩计算方法
    ContactSearchMethod contactSearchMethod; ///< 接触搜索方法
    int numLevel; ///< 多层级接触搜索中的层级数量
    Scalar sizeRatio; ///< 单元搜索方法中单元和颗粒尺寸比

	ContactCalculationParameter()
	{
		particlesContactFlag = true;
		wallContactType = WCT_GRANULAR;
    	contactForceType = LINEAR_LIMITED;
    	contactTorqueType = CTT_NONE;
    	contactSearchMethod = NBS_Munjiza;
    	numLevel = 0;
    	sizeRatio = 1.0; 
	}
};

struct InterpolationParameter
{
	InterpolationMethod method;
	bool conservationFlag;
	InterpolationParameter()
	{
		method = InterpolationMethod::INTERPOLATION_CONSTANT;
		conservationFlag = false;
	}
};

struct ExternalForceParameter
{
	bool dragForce;
	bool liftForce;
	bool pressureGradientForce;
	bool addedMassForce;
	bool rotationlDrag;
	InterpolationParameter interpolation;
	ExternalForceParameter()
	{
		dragForce = true;
		liftForce = true;
		pressureGradientForce = true;
		addedMassForce = false;
		rotationlDrag = false;
	}
};

/**
 * @brief 计算控制参数
 * 
 */
struct ControlParameter
{
    MotionIntegrationScheme translationIntegrationScheme; ///< 平移运动积分格式
    MotionIntegrationScheme rotationIntegrationScheme; ///< 旋转运动积分格式
    Scalar timeStep; ///< 物理时间步长
	int totalSteps; ///< 计算步数
	bool variableTimeStep; ///< 变时间步长标识
    int monitorInterval; ///< 屏幕打印间隔
    int saveInterval; ///< 保存间隔
    FileType outputFileType; ///< 保存文件类型
	bool outputFileBinary; ///< 采用二进制输出
    std::string resultsPath; ///< 计算结果保存路径
    int outputLevelFile; ///< 文件输出信息级别
    int outputLevelScreen; ///< 屏幕打印信息级别
	
	ControlParameter()
	{
    	translationIntegrationScheme = PIM_AB3AM4;
    	rotationIntegrationScheme = PIM_AB3AM4;
    	timeStep = 0.02;
		totalSteps = 25;
		variableTimeStep = true;
    	monitorInterval = 1;
		saveInterval = 1000;
		resultsPath = "./results/";
    	outputFileType = FileType::TECPLOT;
		outputFileBinary = true;
    	outputLevelFile = 5;
    	outputLevelScreen = 3;
	}
};

struct MonitorParameter
{
	int particleID;
	bool positionFlag;
	bool velocityFlag;
	bool forceFlag;
	MonitorParameter()
	{
		particleID = -1;
		positionFlag = false;
		velocityFlag = false;
		forceFlag = false;
	}
};

class ParticleConfigure : public Configure
{
public:
    /**
     * @brief 构造函数
     * 
     */
    ParticleConfigure();

    /**
     * @brief 读取颗粒控制参数
     * 
     * @param[in] fileName case文件名
     * 
     */
    void ReadParameter(const std::string &fileName);

	/**
	 * @brief 设置计算域最大最小位置
	 * 
	 * @param minDomain 
	 * @param maxDomain 
	 */
	void SetDomainMinMax(const Vector &minDomain, const Vector &maxDomain);

private:
	void ReadCaseName(PropertyTree &ptree);
	void ReadReferenceParameter(PropertyTree &ptree);
	void ReadMaterialParameter(PropertyTree &ptree);
	void ReadBinaryParameters(PropertyTree &ptree);
	void ReadInsertionParameter(PropertyTree &ptree);
	void ReadDistributionParameter(PropertyTree &ptree);
	void ReadContactCalculationParameter(PropertyTree &ptree);
	void ReadExternalForceParameter(PropertyTree &ptree);
	void ReadControlParameter(PropertyTree &ptree);
	void ReadMonitorParameter(PropertyTree &ptree);
	void PrintBasicInformation();
	std::string ObtainAbsolutePath(std::string stringTemp);

public:
    std::string caseName; ///< 工程名称
    std::string workPath; ///< 工作路径
	ReferenceParameter reference; ///< 参考量
	MaterialParameter material; ///< 颗粒材料属性
	BinaryParameters binaryParasPP; 	///< 颗粒颗粒碰撞属性参数
	BinaryParameters binaryParasPW; 	///< 颗粒壁面碰撞属性参数
	InsertionParameter insertion; ///< 颗粒注入参数
	DistributionParameter distribution; ///< 颗粒分布参数
	ContactCalculationParameter contact; ///< 颗粒接触计算参数
	ExternalForceParameter externalForce; ///< 颗粒外部力计算参数
	ControlParameter control; ///< 计算控制参数
	MonitorParameter monitor; ///< 监控参数
};

} // namespace Particle

} // namespace Configure

#endif