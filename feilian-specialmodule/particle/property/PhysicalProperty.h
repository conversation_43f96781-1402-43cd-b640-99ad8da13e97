﻿#ifndef _particle_property_
#define _particle_property_

#include "feilian-specialmodule/particle/property/PureProperty.h"
#include "feilian-specialmodule/particle/distribution/DistributionProperty.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

struct BinaryProperty
{
    Scalar Yeff;   ///< 有效杨氏模量
    Scalar Geff;   ///< 有效剪切模量
    Scalar Reff;   ///< 有效半径
    Scalar meff;   ///< 有效质量
    Scalar kn;
    Scalar kt;
    Scalar en;
    Scalar et;
    Scalar fric;
    Scalar roll_fric;
    Scalar dmp_n;
    Scalar dmp_t;
    BinaryProperty()
    {
        en   = 0.75;
        et   = 1.0;
        fric = 0.3;
        roll_fric = 0.1;
        dmp_n = 0.0;
        dmp_t = 0.0;
    }
};

class PhysicalProperty
{
private:
    int particleTypeNumber;
    int wallTypeNumber;
    
    bool particlePurePropertyFlag;
	bool binaryPropertyPPFlag;
	bool binaryPropertyPWFlag;
    
    Scalar relativeVelocity; ///< 计算kn参数

    std::vector<PureProperty> particlePureProperty;
    std::vector<PureProperty> wallPureProperty;
    std::vector< std::vector<BinaryProperty>> binaryPropertyPP;
    std::vector< std::vector<BinaryProperty>> binaryPropertyPW;
    
public:
    PhysicalProperty();
    const int &GetParticleTypeNumber()const { return this->particleTypeNumber; }
    const int &GetWallTypeNumber()const { return this->wallTypeNumber; }
    const Scalar &GetMass(const int &pt)const { return this->particlePureProperty[pt].GetMass(); }
    const Scalar &GetInertia(const int &pt)const { return this->particlePureProperty[pt].GetInertia(); }
    const Scalar &GetRadius(const int &pt)const {return this->particlePureProperty[pt].GetRad();}
    const BinaryProperty &GetBinaryPropertyPP(const int &pt_i, const int &pt_j)const {return this->binaryPropertyPP[pt_i][pt_j];}
    const BinaryProperty &GetBinaryPropertyPW(const int &pt_i, const int &w_prop)const {return this->binaryPropertyPW[pt_i][w_prop];}
    
    void SetParticleProperty(DistributionProperty *PSDP);
    void SetWallTypeNumber(const int &wallTypeNumber_);
    void SetWallProperty( const int &wallTypeID, const PureProperty &pureProperty );
    void SetNonWallProperty( const int &wallTypeID, const PureProperty &pureProperty);
    void SetBinaryPropertyPP(const Configure::Particle::ParticleConfigure &DEM_opt);
    void SetBinaryPropertyPW(const Configure::Particle::ParticleConfigure &DEM_opt);
    
    BinaryProperty CalculateBinaryProperty( const PureProperty &pari, const PureProperty &parj,
                                            const Scalar &mu, const Scalar &mur, const Scalar &en, const Scalar &et,
                                            const Configure::Particle::ContactForceType &contactForceType );

    Scalar CalculateDampingNormal( const BinaryProperty &Bnry, const Configure::Particle::ContactForceType &contactForceType );
    Scalar CalculateDampingTangential( const BinaryProperty &Bnry, const Configure::Particle::ContactForceType &contactForceType );
};

} // namespace Particle

#endif