﻿#include "feilian-specialmodule/particle/distribution/DistributionProperty.h"

#include <random>

namespace Particle
{

DistributionProperty::DistributionProperty(const Distribution &sizeDistribution , const PureProperty &pureProperty)
: Distribution(sizeDistribution)
{
    // locals
    this->num_props = this->numBins;
    this->pureProperty.resize(this->num_props);
    
    for(int i = 0; i < this->num_props; ++i)
    {
        this->pureProperty[i] = pureProperty;
	    this->pureProperty[i].SetRad(0.5 * this->meanSizeInBin[i]);
        this->pureProperty[i].Calculate();
    }
    
    this->propertyID.resize(this->numParticles);
    this->mass.resize(this->numParticles);
    this->inertia.resize(this->numParticles);
    for(int i = 0; i < this->numParticles; ++i)
    {
        this->propertyID[i] = this->particleBinIndex[i];
        this->mass[i]   = this->pureProperty[this->particleBinIndex[i]].GetMass();
        this->inertia[i]= this->pureProperty[this->particleBinIndex[i]].GetInertia();
    }
}

DistributionProperty DistributionProperty::operator+(const DistributionProperty &property )
{
    DistributionProperty res;
    (Distribution)res = (Distribution)(*this) + (Distribution)(property);
    
    res.num_props = this->num_props + property.num_props;
    
    res.pureProperty.resize(res.num_props);
    for(int i = 0; i < this->num_props; ++i) res.pureProperty[i] = this->pureProperty[i];
    for(int i = 0; i < property.num_props; ++i) res.pureProperty[this->num_props+i] = property.pureProperty[i];
    
    for(int i = 0; i < this->numParticles; ++i)
    {
        res.propertyID[i]   = this->propertyID[i];
        res.mass[i]     = this->mass[i];
        res.inertia[i]  = this->inertia[i];
    }
    
    for(int i = 0; i < property.numParticles; ++i)
    {
        res.propertyID[this->numParticles+i] = property.propertyID[i]+this->num_props;
        res.mass[this->numParticles+i]   = property.mass[i];
        res.inertia[this->numParticles+i]= property.inertia[i];
    }

    return res;
}

} // namespace Particle