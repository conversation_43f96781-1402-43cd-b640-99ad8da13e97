﻿#include "feilian-specialmodule/particle/distribution/Distribution.h"

#include <random>

namespace Particle
{

Distribution::Distribution(const Distribution& sizeDistribution)
{
    this->numParticles = sizeDistribution.numParticles;
    this->numBins = sizeDistribution.numBins;
    this->PSD = sizeDistribution.PSD;
    this->minDiameter = sizeDistribution.minDiameter;
    this->maxDiameter = sizeDistribution.maxDiameter;
    this->ids = sizeDistribution.ids;
    this->particleDiameter = sizeDistribution.particleDiameter;
    this->particlePosition = sizeDistribution.particlePosition;
    this->particleBinIndex = sizeDistribution.particleBinIndex;
    this->meanSizeInBin = sizeDistribution.meanSizeInBin;
}

Distribution::Distribution(const Configure::Particle::ParticleConfigure &DEM_opt_)
{
    this->numParticles = DEM_opt_.distribution.numParticles;
    this->numBins  = DEM_opt_.distribution.numBins;
    this->PSD      = DEM_opt_.distribution.distributionType;
    this->maxDiameter     = DEM_opt_.distribution.maxDiameter;
    this->minDiameter     = DEM_opt_.distribution.minDiameter;
    
    Scalar mu = this->minDiameter;
    Scalar std= this->maxDiameter;
    
    this->ids.resize(this->numParticles);
    this->particleDiameter.resize(this->numParticles);
    this->particlePosition.resize(this->numParticles);
    this->particleBinIndex.resize(this->numParticles);
    this->meanSizeInBin.resize(this->numBins);

    for(int i = 0; i < this->numParticles; ++i) this->ids[i] = i;
    
    switch(this->PSD)
    {
    case Configure::Particle::DistributionType::UNIFORM:
        this->make_uniform();
        break;

    case Configure::Particle::DistributionType::NORMAL:
        this->minDiameter = mu-2*std;
        this->maxDiameter = mu+2*std;
        this->make_normal(mu, std);
        break;
        
    case Configure::Particle::DistributionType::LOG_NORMAL:
        this->minDiameter = Max(0.0, mu-2*std);
        this->maxDiameter = mu+4*std;
        this->make_lognormal(mu,std);
        break;
        
    default:
        FatalError("PS_MakeDistribution: Wrong input distribution type, set to uniform with one bin");
    }
}

Distribution Distribution::operator+(const Distribution &op2)
{
    Distribution res;

    res.numParticles = this->numParticles + op2.numParticles;
    res.numBins  = this->numBins  + op2.numBins;
    res.PSD      = (Configure::Particle::DistributionType) Max((int)this->PSD , (int)op2.PSD);
    res.minDiameter     = Min( this->minDiameter, Min(this->maxDiameter, Min( op2.minDiameter , op2.maxDiameter) ) );
    res.maxDiameter     = Max( this->minDiameter, Max(this->maxDiameter, Max( op2.minDiameter , op2.maxDiameter) ) );
    
    res.ids.resize(res.numParticles);
    res.particleDiameter.resize(res.numParticles);
    res.particlePosition.resize(res.numParticles);
    res.particleBinIndex.resize(res.numParticles);
    res.meanSizeInBin.resize(res.numBins);

    for(int i = 0; i < this->numParticles; ++i)
    {
        res.ids[i] = this->ids[i];
        res.particleDiameter[i] = this->particleDiameter[i]; 
        res.particlePosition[i] = this->particlePosition[i]; 
        res.particleBinIndex[i] = this->particleBinIndex[i];
    }

    for(int i = 0; i < op2.numParticles; ++i)
    {
        res.ids[this->numParticles+i]= op2.ids[i] + this->numParticles;
        res.particleDiameter[this->numParticles+i]= op2.particleDiameter[i];
        res.particlePosition[this->numParticles+i]= op2.particlePosition[i];
        res.particleBinIndex[this->numParticles+i]= op2.particleBinIndex[i] + this->numBins;
    }

    for(int i = 0; i < this->numBins; ++i)
        res.meanSizeInBin[i] = this->meanSizeInBin[i];
    
    for(int i = 0; i < op2.numBins; ++i)
        res.meanSizeInBin[this->numBins+i]= op2.meanSizeInBin[i];
    
    return res;
}

Distribution Distribution::operator*(int &num)
{
    Distribution res;
    res.numParticles = this->numParticles * num;
    res.numBins  = this->numBins;
    res.PSD      = this->PSD;
    res.minDiameter     = this->minDiameter;
    res.maxDiameter     = this->minDiameter;
    
    res.ids.resize(res.numParticles);
    res.particleDiameter.resize(res.numParticles);
    res.particlePosition.resize(res.numParticles);
    res.particleBinIndex.resize(res.numParticles);
    res.meanSizeInBin.resize(res.numBins);

    for(int n = 0; n < num; ++n)
    {
        for(int i = 0; i < this->numParticles; ++i)
        {
            res.ids    [i + n * this->numParticles] = i + n * this->numParticles;
            res.particleDiameter [i + n * this->numParticles] = this->particleDiameter[i];
            res.particlePosition [i + n * this->numParticles] = this->particlePosition[i];
            res.particleBinIndex[i + n * this->numParticles] = this->particleBinIndex[i];
        }
    }
    
    for(int i = 0; i < this->numBins; ++i)
        res.meanSizeInBin[i] = this->meanSizeInBin[i];
    
    return res;
}

void Distribution::make_uniform()
{
    Scalar size_dx = Scalar0;
    this->AssignBins(size_dx);
    
    std::random_device dev;
    std::mt19937 gen(dev());
    std::uniform_real_distribution<> dis(this->minDiameter, this->maxDiameter);

    for(int i = 0; i < this->numParticles; ++i)
    {
        Scalar r = dis(gen);
        int Bin_num = int( (r - this->minDiameter) / size_dx ) + 1;
        Bin_num = Min( this->numBins , Max(1, Bin_num) );
        this->particleDiameter[i] = (this->meanSizeInBin[Bin_num-1]);
        this->particleBinIndex[i] = Bin_num - 1;
    }
}

void Distribution::make_normal(Scalar &mu, Scalar &std)
{
    Scalar size_dx = Scalar0;
    this->AssignBins(size_dx);

    std::random_device dev;
    std::mt19937 gen(dev());
    std::normal_distribution<> dis(mu, std);
    
    for(int i = 0; i < this->numParticles; ++i)
    {
        Scalar r = dis(gen);
        int Bin_num = int( (r-this->minDiameter)/size_dx ) + 1;
        Bin_num = Min( this->numBins , Max(1, Bin_num) );
        this->particleDiameter[i] = this->meanSizeInBin[Bin_num];
        this->particleBinIndex[i] = Bin_num - 1;
    }
}
    
void Distribution::make_lognormal(Scalar &mu, Scalar &std)
{
    Scalar size_dx = Scalar0;
    this->AssignBins(size_dx);
    
    std::random_device dev;
    std::mt19937 gen(dev());
    std::lognormal_distribution<> dis(mu, std);

    for(int i = 0; i < this->numParticles; ++i)
    {
        Scalar r = dis(gen);
        int Bin_num = int( (r-this->minDiameter)/size_dx ) + 1;
        Bin_num = Min( this->numBins , Max(1, Bin_num) );
        this->particleDiameter[i] = this->meanSizeInBin[Bin_num];
        this->particleBinIndex[i] = Bin_num - 1;
    }
}

void Distribution::AssignBins(Scalar sizeGap)
{
    int nBins = this->numBins;
    
    sizeGap = (this->maxDiameter - this->minDiameter) / nBins;
    if( nBins == 1 && sizeGap <  (0.001 * this->maxDiameter) ) sizeGap = 10 * this->maxDiameter;
    
    if(nBins == 1 )
    {
        this->meanSizeInBin[0] = 0.5 * (this->maxDiameter+this->minDiameter);
    }
    else
    {
        for(int i = 0; i < nBins; ++i)
            this->meanSizeInBin[i] = this->minDiameter + i * sizeGap + 0.5 * sizeGap;
    }
}

} // namespace Particle