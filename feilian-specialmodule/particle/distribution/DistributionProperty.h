﻿#ifndef _particle_DistributionProperty_
#define _particle_DistributionProperty_

#include "feilian-specialmodule/particle/distribution/Distribution.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

class DistributionProperty : public Distribution
{
public:
    int num_props;                          ///< 颗粒分布属性数量
    std::vector<PureProperty> pureProperty; ///< 颗粒基本属性
    std::vector<int> propertyID;            ///< 颗粒属性编号
    std::vector<Scalar> mass;               ///< 颗粒质量
    std::vector<Scalar> inertia;            ///< 颗粒转动惯量

public:
    /**
     * @brief 默认构造函数
     * 
     */
    DistributionProperty(){}

    /**
     * @brief 根据颗粒尺寸分布及基本属性构造 
     * 
     * @param[in] sizeDistribution 颗粒尺寸分布
     * @param[in] pureProperty 颗粒基本属性
     */
    DistributionProperty(const Distribution &sizeDistribution , const PureProperty &pureProperty);

    /**
     * @brief 颗粒分布属性加法
     * 
     * @param[in] property 颗粒分布属性
     * @return DistributionProperty 颗粒分布属性
     */
    DistributionProperty operator+(const DistributionProperty &property );
    
    /**
     * @brief 获得指定编号基本属性
     * 
     * @param[in] propertyID 属性编号
     * @return const PureProperty& 基本属性
     */
    inline const PureProperty &GetPureProperty(int &propertyID)const {return this->pureProperty[propertyID];}

    /**
     * @brief 获得属性类型数量
     * 
     * @return const int& 数量
     */
    inline const int &GetPropertyTypeNumber()const {return this->num_props;}

    /**
     * @brief 获得指定颗粒质量
     * 
     * @param[in] particleID 颗粒编号
     * @return const Scalar& 颗粒质量
     */
    inline const Scalar &GetParticleMass(int &particleID)const {return this->mass[particleID];}

    /**
     * @brief 获得指定颗粒转动惯量
     * 
     * @param[in] particleID 颗粒编号
     * @return const Scalar& 颗粒转动惯量
     */
    inline const Scalar &GetParticleInertia(int &particleID)const {return this->inertia[particleID];}

    /**
     * @brief 获得颗粒属性编号
     * 
     * @param[in] particleID 颗粒编号
     * @return const int& 颗粒属性编号
     */
    inline const int &GetParticlePropertyID(const int &particleID)const {return this->propertyID[particleID];}
};

} // namespace Particle

#endif