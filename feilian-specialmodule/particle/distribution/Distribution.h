﻿#ifndef _particle_Distribution_
#define _particle_Distribution_

#include "feilian-specialmodule/particle/property/PureProperty.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{
/**
 * @brief 颗粒大小分布
 * 
 */
class Distribution
{
public:
    int numParticles;                           ///< 颗粒数量 
    int numBins;                                ///< 颗粒分组数量
    Configure::Particle::DistributionType PSD;  ///< 颗粒分布类型
    Scalar minDiameter;                         ///< 最小颗粒直径
    Scalar maxDiameter;                         ///< 最大颗粒直径
    std::vector<int> ids;                       ///< 颗粒编号
    std::vector<Scalar> particleDiameter;       ///< 颗粒直径分布
    std::vector<Vector> particlePosition;       ///< 颗粒位置分布
    std::vector<int> particleBinIndex;          ///< 颗粒分组编号
    std::vector<Scalar> meanSizeInBin;          ///< 每组颗粒平均尺寸

public:
    /**
     * @brief 默认构造函数
     * 
     */
    Distribution(): minDiameter(0.001), maxDiameter(0.001) {}

    /**
     * @brief 根据颗粒大小分布
     * 
     * @param 拷贝构造函数 
     */
    Distribution(const Distribution& sizeDistribution);
    
    /**
     * @brief 根据输入参数构造分布
     * 
     * @param[in] DEM_opt_ 控制参数
     */
    Distribution(const Configure::Particle::ParticleConfigure &DEM_opt_);

    /**
     * @brief 颗粒分布加法
     * 
     * @param[in] distribdution 颗粒分布
     * @return Distribution 颗粒分布
     */
    Distribution operator+(const Distribution &distribdution);

    /**
     * @brief 颗粒分布乘法
     * 
     * @param[in] number 整数
     * @return Distribution 颗粒分布
     */
    Distribution operator*(int &number);

    /**
     * @brief 获得颗粒数量
     * 
     * @return[in] const int& 颗粒数量
     */
    inline const int &GetParticleNumber()const {return this->numParticles;}

    /**
     * @brief 获得颗粒编号
     * 
     * @param[in] index 颗粒索引号
     * @return const int& 颗粒编号
     */
    inline const int &GetParticleID(const int &index)const {return this->ids[index];}

    /**
     * @brief 获得颗粒直径
     * 
     * @param[in] index 颗粒索引号
     * @return const Scalar& 颗粒直径
     */
	inline const Scalar &GetParticleDiameter(const int &index)const { return this->particleDiameter[index]; }

    /**
     * @brief 获得颗粒位置
     * 
     * @param[in] index 颗粒索引号
     * @return const Scalar& 颗粒位置
     */
	inline const Vector &GetParticlePosition(const int &index)const { return this->particlePosition[index]; }

private:
    /**
     * @brief 均匀分布函数
     * 
     */
    void make_uniform();
    
    /**
     * @brief 正态分布函数
     * 
     * @param[in] mu 期望值
     * @param[in] std 方差
     */
    void make_normal(Scalar &mu, Scalar &std);
    
    /**
     * @brief 对数正态分布函数
     * 
     * @param[in] mu 期望值
     * @param[in] std 方差
     */
    void make_lognormal(Scalar &mu, Scalar &std);
    
    /**
     * @brief 根据分组数进行颗粒分组，计算颗粒分组尺寸间隔及平均尺寸
     * 
     * @param[out] sizeGap 分组颗粒尺寸间隔
     */
    void AssignBins(Scalar sizeGap);
};

} // namespace Particle

#endif