﻿#ifndef _particle_ContactInfo_
#define _particle_ContactInfo_

#include "basic/common/ConfigUtility.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{
    
/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class ContactInfo
{
public:
    int pari; ///< 接触对象i的属性类型
    int parj; ///< 接触对象j的属性类型
    int id_i; ///< 接触对象i的编号
    int id_j; ///< 接触对象j的编号
    
    Vector tang_del; ///< 切向重叠量

public:
    /**
     * @brief 接触信息构造函数
     * 
     * @param mem_i_ 接触对象i的属性类型
     * @param mem_j_ 接触对象j的属性类型
     * @param id_i_ 接触对象i的编号
     * @param id_j_ 接触对象j的编号
     */
    ContactInfo(const int &mem_i_ = -1, const int &mem_j_ = -1, const int &id_i_ = -1, const int &id_j_ = -1)
    : pari(mem_i_), parj(mem_j_), id_i(id_i_), id_j(id_j_), tang_del(Vector0)
    {}
};

} // namespace Contact

} // namespace Particle

#endif