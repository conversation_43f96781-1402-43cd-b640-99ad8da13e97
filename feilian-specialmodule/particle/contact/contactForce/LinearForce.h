﻿#ifndef _particle_contact_contactForce_LSD_ContactForce_
#define _particle_contact_contactForce_LSD_ContactForce_

#include "feilian-specialmodule/particle/contact/contactForce/BaseForce.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class LinearForce : public BaseForce
{
public:
    PhysicalProperty *Ph_Props;
    ContactList *PP_CntctList;
    ContactList *PW_CntctList;

public:

    /**
     * @brief 线性接触力构造函数
     * 
     * @param geom 几何
     * @param CF_Type_ 接触力计算方法
     * @param CT_Model_ 接触力矩计算方法
     * @param wallContactType_ 壁面接触模型
     * @param max_nPrtcl_ 最大颗粒数量
     * @param numParticles_ 当前颗粒数量
     * @param particles_ 颗粒对象容器
     * @param dt_ 时间步长
     * @param PP_Cont_List_ 颗粒颗粒碰撞信息列表
     * @param PW_Cont_List_ 颗粒壁面碰撞信息列表
     * @param m_PhysProp_ 物理属性
     */
	LinearForce(Geometry::Geometry *geom,
                const Configure::Particle::ContactForceType &CF_Type_,
				const Configure::Particle::ContactTorqueType &CT_Model_,
				const Configure::Particle::WallContactType &wallContactType_,
                const int & max_nPrtcl_, const int &numParticles_,
                std::vector<Particle *> &particles_, const Scalar &dt_,
                ContactList *PP_Cont_List_, ContactList *PW_Cont_List_,
                PhysicalProperty *m_PhysProp_);

    /**
     * @brief 析构函数
     * 
     */
    ~LinearForce();

    /**
     * @brief 计算颗粒与颗粒接触力及力矩
     * 
     * @param ind 碰撞索引
     * @param lnew 是否为首次接触标识
     */
    void ContactForcePP( int &ind , bool &lnew );
    
    /**
     * @brief 计算颗粒与壁面接触力及力矩
     * 
     * @param ind 碰撞索引
     * @param lnew 是否为首次接触标识
     */
    void ContactForcePW( int &ind , bool &lnew );
    
    /**
     * @brief 计算颗粒与颗粒接触信息
     * 
     * @param i 颗粒索引
     * @param j 颗粒索引
     * @param Vrij 相对速度
     * @param wi 颗粒角速度
     * @param wj 颗粒角速度
     * @param norm_v 颗粒体心连线单位矢量
     * @param ovrlp 两个颗粒的重叠量
     * @param Ri 颗粒半径
     * @param Rj 颗粒半径
     */
    void CalculateContactVelocity(const int &i, const int &j, Vector &Vrij, Vector &wi, Vector &wj,
                                  Vector &norm_v, Scalar &ovrlp, Scalar &Ri, Scalar &Rj);
    /**
     * @brief 计算颗粒与壁面接触信息
     * 
     * @param i 颗粒索引
     * @param wallID 壁面索引
     * @param Vrij 相对速度
     * @param wi 颗粒角速度
     * @param norm_v 颗粒与壁面距离单位矢量
     * @param ovrlp 颗粒与壁面重叠量
     * @param Ri 颗粒半径
     * @param w_pt 壁面属性编号
     */
    void CalculateContactVelocity_PW(const int &i, const int &wallID, Vector &Vrij, Vector &wi,
                                     Vector &norm_v, Scalar &ovrlp, Scalar &Ri, int &w_pt );

    /**
     * @brief 计算力矩
     * 
     * @param fn 法向接触力
     * @param ftij 切向接触力
     * @param roll_fric 滚转摩擦因子
     * @param nij 距离单位矢量
     * @param wi 旋转速度
     * @param wj 旋转速度
     * @param Ri 颗粒半径
     * @param Rj 颗粒半径
     * @param Mij 切向力矩
     * @param Mji 切向力矩
     * @param Mri 转动力矩
     * @param Mrj 转动力矩
     */
    void CalculateTorque(const Scalar &fn, const Vector &ftij,  const Scalar &roll_fric, const Vector &nij,
                    const Vector &wi , const Vector &wj , const Scalar &Ri, const Scalar &Rj,
                    Vector &Mij, Vector &Mji , Vector &Mri, Vector &Mrj);
};

} // namespace Contact

} // namespace Particle

#endif