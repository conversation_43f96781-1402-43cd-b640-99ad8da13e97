﻿#include "feilian-specialmodule/particle/contact/contactForce/LinearForce.h"

namespace Particle
{
namespace Contact
{

LinearForce::LinearForce(Geometry::Geometry *geom,
                         const Configure::Particle::ContactForceType &CF_Type_,
						 const Configure::Particle::ContactTorqueType &CT_Model_,
						 const Configure::Particle::WallContactType &wallContactType_,
                         const int & max_nPrtcl_, const int &numParticles_,
                         std::vector<Particle *> &particles_, const Scalar &dt_,
                         ContactList *PP_Cont_List_, ContactList *PW_Cont_List_,
                         PhysicalProperty *m_PhysProp_ )
						 : BaseForce(geom, CF_Type_, CT_Model_, wallContactType_, max_nPrtcl_, numParticles_, particles_, dt_), Ph_Props(m_PhysProp_)
{
    this->PP_CntctList = dynamic_cast<ContactList*>(PP_Cont_List_);
    this->b_PP_ContList = dynamic_cast<ContactList*>(PP_Cont_List_);
    this->PW_CntctList = dynamic_cast<ContactList*>(PW_Cont_List_);
    this->b_PW_ContList= dynamic_cast<ContactList*>(PW_Cont_List_);
}

LinearForce::~LinearForce()
{
    
}

void LinearForce::ContactForcePP( int &ind , bool &lnew )
{
    // 获得接触信息
    auto &ContPair_i = this->PP_CntctList->GetItem(ind);
    
    // 颗粒索引
    int i = ContPair_i.pari;
    int j = ContPair_i.parj;
    
    // 颗粒物理属性
    const int &pt_i = this->particles[i]->type;
    const int &pt_j = this->particles[j]->type;
    BinaryProperty prop_ij = this->Ph_Props->GetBinaryPropertyPP( pt_i, pt_j);
    
    // 计算接触点速度及法向重叠量
    Scalar ovrlp, Ri, Rj;
    Vector Vrij, rveli, rvelj, norm_v;
    this->CalculateContactVelocity(i, j, Vrij, rveli, rvelj, norm_v, ovrlp , Ri, Rj );
    
    // 接触点相对速度及其法向分量和切向分量
    const Scalar vrn = Vrij & norm_v;
    const Vector Vij_n = vrn * norm_v;
    const Vector Vij_t = Vrij - Vij_n;
    
    // 计算切向重叠量
    if( lnew) ContPair_i.tang_del = Vector0;
    Vector ovlp_t = (Vij_t * this->dt) + ContPair_i.tang_del;
    ovlp_t = ovlp_t - (( ovlp_t & norm_v ) * norm_v);
    
    // 计算法向和切向接触力
    Scalar fn =  -prop_ij.kn * ovrlp - prop_ij.dmp_n* vrn;
    Vector fnij = fn * norm_v;
    Vector ftij = ( (-prop_ij.kt) * ovlp_t) - (prop_ij.dmp_t * Vij_t);
    
    // 库伦摩擦定律
    Scalar ft = ftij.Mag();
    Scalar ft_fric = prop_ij.fric * abs(fn);
    if( abs(ft) > ft_fric )
    {
        if( ovlp_t.Mag() > 0.0 )
        {
            //TODO::检查
            if( this->contactForceType == Configure::Particle::ContactForceType::NONLINEAR_LIMITED )
            {
                // 有限制重叠
                ftij = ftij*(ft_fric/ft);
                ovlp_t = -1.0 * (ftij / prop_ij.kt);
            }
            else
            {
                // 无限制重叠
                ftij = (ftij/ft)*ft_fric;
            }
        }
        else
        {
            ftij = Vector0;
        }
    } 

	// 更新颗粒接触力
	this->AddForce(i, j, fnij + ftij);

	if (contactTorqueFlag)
	{
		// 计算接触力矩
		Vector Mij, Mji, Mri, Mrj;
		this->CalculateTorque(fn, ftij, prop_ij.roll_fric, norm_v, rveli, rvelj, Ri, Rj, Mij, Mji, Mri, Mrj);

		// 更新颗粒接触力矩  
		this->AddTorque(i, j, Mij + Mri, Mji + Mrj);
	}

    // 更新颗粒接触信息
    ContPair_i.tang_del = ovlp_t;
    this->PP_CntctList->SetItem(ind , ContPair_i);
}

void LinearForce::ContactForcePW( int &ind , bool &lnew )
{
    // 接触对信息
    auto &ContPair_i = this->PW_CntctList->GetItem(ind);
    const int &i = ContPair_i.pari;
    const int &wallID = ContPair_i.id_j;
    
    // 颗粒属性
    const int &pt_i = this->particles[i]->type;
    
    int w_pt;
    Scalar Ri, ovrlp;
    Vector rveli, norm_v, Vrij;

    // 接触点相对速度
    this->CalculateContactVelocity_PW( i, wallID, Vrij, rveli, norm_v, ovrlp, Ri, w_pt);  
    
    // 法向和切向接触速度分量
    const Scalar vrn = Vrij & norm_v; 
    const Vector Vij_n = vrn * norm_v;
    const Vector Vij_t = Vrij - Vij_n;
     
    // 颗粒与壁面碰撞属性
    const BinaryProperty &prop_ij = this->Ph_Props->GetBinaryPropertyPW( pt_i , w_pt );

    // 切向重叠矢量
    if( lnew) ContPair_i.tang_del = Vector0;
    Vector ovlp_t = (Vij_t * this->dt) + ContPair_i.tang_del;
    ovlp_t = ovlp_t - (( ovlp_t & norm_v ) * norm_v);
    
    // 计算法向和切向接触力
    Scalar fn =  -prop_ij.kn * ovrlp - prop_ij.dmp_n* vrn;
    Vector fnij = fn * norm_v;
    Vector ftij = ( (-prop_ij.kt) * ovlp_t) - (prop_ij.dmp_t * Vij_t); 
    
    // Coulomb摩擦定律
    Scalar ft = ftij.Mag();
    Scalar ft_fric = prop_ij.fric * abs(fn);
    if( abs(ft) > ft_fric )
    {
        if( ovlp_t.Mag() > 0.0 )
        {
            //TODO::检查
            if( this->contactForceType == Configure::Particle::ContactForceType::NONLINEAR_LIMITED )
            {
                // 重叠量受限制
                ftij = ftij*(ft_fric/ft);
                ovlp_t = (-1.0) * (ftij/prop_ij.kt);
            }
            else
            {
                // 重叠量不受限制
                ftij = (ftij/ft)*ft_fric;
            }
        }
        else
        {
            ftij = Vector0;
        }
    }

	// 更新颗粒的接触力
	this->AddForcePW(i, fnij + ftij);

	if (contactTorqueFlag)
	{
		Vector Mij, Mji, Mri, Mrj;
		Scalar Rj = 100000000000.0;

		// 计算球形颗粒力矩
		this->CalculateTorque(fn, ftij, prop_ij.roll_fric, norm_v, rveli, Vector0, Ri, Rj, Mij, Mji, Mri, Mrj);

		// 更新颗粒的接触力和力矩  
		this->AddTorquePW(i, (Mij + Mri));
	}
    
    // 更新接触列表信息
    ContPair_i.tang_del = ovlp_t;
    this->PW_CntctList->SetItem(ind , ContPair_i);
}

void LinearForce::CalculateContactVelocity(const int &i, const int &j, Vector &Vrij, Vector &wi, Vector &wj,
                                           Vector &norm_v, Scalar &ovrlp, Scalar &Ri, Scalar &Rj)
{
    // 计算两个球形颗粒的重叠量
    ovrlp = Overlap(*this->particles[i], *this->particles[j]);
    
    // 体心连线单位矢量
    norm_v = (this->particles[j]->position - this->particles[i]->position).GetNormal();  // posj-posi
    
    // 球形颗粒半径
    Ri = 0.5 * this->particles[i]->diameter;
    Rj = 0.5 * this->particles[j]->diameter;
    
    // 颗粒角速度
    wi = this->particles[i]->angularVelocity;
    wj = this->particles[j]->angularVelocity;
    
    // 计算相对速度
    Vrij = this->particles[i]->linearVelocity - this->particles[j]->linearVelocity  +  ( (Ri*wi + Rj*wj) ^ norm_v );
}

void LinearForce::CalculateContactVelocity_PW(const int &i, const int &wallID, Vector &Vrij, Vector &wi,
                                         Vector &norm_v, Scalar &ovrlp, Scalar &Ri, int &w_pt )
{
    // 颗粒平移速度
    const Vector &veli = this->particles[i]->linearVelocity;
       
    // 颗粒位置及直径
    const Vector &pos = this->particles[i]->position;
    const Scalar &diam = this->particles[i]->diameter;
    
    Scalar dist = Scalar0;
    Vector velj = Vector0;
    
    // 颗粒角速度
    wi = this->particles[i]->angularVelocity;
    
    // 获得法向矢量、距离、接触点速度和壁面属性编号
    this->geometry->GetPointToWallInfo(pos, diam, wallID, norm_v, dist, velj , w_pt );
    
    // 法向矢量反向
    norm_v *= -1.0;
    
    Ri =  0.5 * diam;
    ovrlp = Ri - dist; 
    
    // 计算相对速度
    Vrij = veli - velj  +  ( (Ri*wi) ^ norm_v );
}

void LinearForce::CalculateTorque(const Scalar &fn, const Vector &ftij,  const Scalar &roll_fric, const Vector &nij,
                                  const Vector &wi , const Vector &wj , const Scalar &Ri, const Scalar &Rj,
                                  Vector &Mij, Vector &Mji , Vector &Mri, Vector &Mrj)
{
    // 切向力矩
    Vector M = nij ^ ftij;
    Mij = Ri*M;
    Mji = Rj*M;
    
    // 转动力矩
    Vector w_hat = wi-wj;
    Scalar w_hat_mag = w_hat.Mag();
    if( w_hat_mag > 0.000001 ) w_hat = w_hat/w_hat_mag;
    else                       w_hat = Vector0;
    
    Scalar Reff = 1.0 /( (1.0/Ri) + (1.0/Rj) );
    
    if( this->contactTorqueType == Configure::Particle::ContactTorqueType::CONSTANT )
        Mri = (-roll_fric * abs(fn) * Reff) * w_hat;
    else
        Mri = (-roll_fric * abs(fn) * Reff * ( (Ri*wi+Rj*wj) ^ nij ).Mag() ) * w_hat;
       
    Mrj = (-1.0)*Mri;
}

} // namespace Contact

} // namespace Particle