﻿#include "feilian-specialmodule/particle/contact/contactForce/NonlinearForce.h"

namespace Particle
{
namespace Contact
{

NonlinearForce::NonlinearForce(Geometry::Geometry *geom,
							   const Configure::Particle::ContactForceType &CF_Type_,
							   const Configure::Particle::ContactTorqueType &CT_Model_,
							   const Configure::Particle::WallContactType &wallContactType_,
                               const int & max_nPrtcl_, const int &numParticles_,
                               std::vector<Particle *> &particles_, const Scalar &dt_,
                               ContactList *PP_Cont_List_, ContactList *PW_Cont_List_,
                               PhysicalProperty *m_PhysProp_)
							   : LinearForce(geom, CF_Type_, CT_Model_, wallContactType_, max_nPrtcl_, numParticles_, particles_, dt_, PP_Cont_List_, PW_Cont_List_, m_PhysProp_)
{
}

NonlinearForce::~NonlinearForce()
{
    
}

void NonlinearForce::ContactForcePP( int &ind , bool &lnew )
{ 
    // 获得接触信息
    auto &ContPair_i = this->PP_CntctList->GetItem(ind);
    
    // 颗粒索引
    const int &i = ContPair_i.pari;
    const int &j = ContPair_i.parj;
    
    // 颗粒物理属性
    const int &pt_i = this->particles[i]->type;
    const int &pt_j = this->particles[j]->type;
    const BinaryProperty &prop_ij = this->Ph_Props->GetBinaryPropertyPP( pt_i , pt_j );
    
    // 颗粒半径
    const Scalar Ri = 0.5 * this->particles[i]->diameter;
    const Scalar Rj = 0.5 * this->particles[j]->diameter;
    
    // 颗粒平移速度
    const Vector &veli = this->particles[i]->linearVelocity;
    const Vector &velj = this->particles[j]->linearVelocity;
    
    // 颗粒旋转速度
    const Vector &rveli = this->particles[i]->angularVelocity;
    const Vector &rvelj = this->particles[j]->angularVelocity;
    
    // 颗粒体心连线单位矢量
    const Vector norm_v = (this->particles[j]->position - this->particles[i]->position).GetNormal();
    
    // 接触点相对速度及其法向分量和切向分量
    const Vector Vrij = veli - velj  +  ( (Ri*rveli + Rj*rvelj) ^ norm_v );
    const Vector Vij_n = (Vrij & norm_v)*norm_v;
    const Vector Vij_t = Vrij - Vij_n;
    
    // 球形颗粒法向重叠量
    const Scalar ovrlp = Overlap(*this->particles[i], *this->particles[j]);
    
    // 计算切向重叠量
    if( lnew) ContPair_i.tang_del = Vector0;
    Vector ovlp_t = (Vij_t * this->dt) + ContPair_i.tang_del;
    ovlp_t = ovlp_t - (( ovlp_t & norm_v ) * norm_v);
    
    // 计算法向和切向接触力 
    Scalar fn;
    Vector fnij, ftij;
    this->CalculateForce(prop_ij, norm_v, Vrij, ovrlp, ovlp_t, fn, fnij, ftij);

	// 更新颗粒的接触力
	this->AddForce(i, j, fnij + ftij);

	if (contactTorqueFlag)
	{
		// 计算接触力矩 
		Vector Mij, Mji, Mri, Mrj;
		this->CalculateTorque(fn, ftij, prop_ij.roll_fric, norm_v, rveli, rvelj, Ri, Rj, Mij, Mji, Mri, Mrj);

		// 更新颗粒的接触力矩
		this->AddTorque(i, j, Mij + Mri, Mji + Mrj);
	}

    // 更新颗粒接触信息 
    ContPair_i.tang_del = ovlp_t;
    this->PP_CntctList->SetItem(ind , ContPair_i);
}

void NonlinearForce::CalculateForce(const BinaryProperty &prop_ij,
                                    const Vector &norm_v, const Vector &Vrij, const Scalar &ovrlp,
                                    Vector &ovlp_t, Scalar &fn, Vector &fnij, Vector &ftij)
{
    // 计算法向和切向接触力 
    fn = - (4.0/3.0 * prop_ij.Yeff * sqrt(prop_ij.Reff) * pow(ovrlp, 1.5)) - (prop_ij.dmp_n*pow(ovrlp, 0.25) * (Vrij & norm_v) );
    fnij = fn * norm_v;
    ftij = (- 16.0/3.0 * prop_ij.Geff * sqrt(prop_ij.Reff*ovrlp) ) * ovlp_t;
    
    // 库伦摩擦定律
    Scalar ft = ftij.Mag();
    Scalar ft_fric = prop_ij.fric * abs(fn);
    if( abs(ft) > ft_fric )
    {
        if( ovlp_t.Mag() > 0.0 )
        {
            //TODO::检查
            if( this->contactForceType == Configure::Particle::ContactForceType::LINEAR_LIMITED )
            {
                // 有限制重叠
                ftij = ftij*(ft_fric/ft);
                Scalar kt = 16.0/3.0 * prop_ij.Geff * sqrt(prop_ij.Reff*ovrlp);
                ovlp_t = (-1.0) * (ftij/kt);
            }
            else
            {
                // 无限制重叠
                ftij = (ftij/ft)*ft_fric;
            }
        }
        else
        {
            ftij = Vector0;
        }
    }
}

void NonlinearForce::ContactForcePW( int &ind , bool &lnew )
{
    //locals 
    int w_pt;
    Scalar Ri, Rj, ft , ft_fric, kt;
    Scalar ovrlp;
    Vector rveli, rvelj, dtij, dftij;
    Vector norm_v, Vrij;
    Vector Mij, Mji, Mri, Mrj , wi;
    
    // 接触对信息
    auto &ContPair_i = this->PW_CntctList->GetItem(ind);
    const int i = ContPair_i.pari;
    const int wallID = ContPair_i.id_j;
    
    // 颗粒属性
    const int &pt_i = this->particles[i]->type;
    
    // 接触点相对速度
    this->CalculateContactVelocity_PW( i, wallID, Vrij, rveli, norm_v, ovrlp, Ri, w_pt);
    
    // 法向和切向接触速度分量
    const Scalar vrn = Vrij & norm_v;
    const Vector Vij_n = vrn * norm_v;
    const Vector Vij_t = Vrij - Vij_n;
    
    // 颗粒与壁面碰撞属性
    const BinaryProperty &prop_ij = this->Ph_Props->GetBinaryPropertyPW( pt_i , w_pt );

    // 切向重叠量
    if( lnew) ContPair_i.tang_del = Vector0;
    Vector ovlp_t = (Vij_t * this->dt) + ContPair_i.tang_del;
    ovlp_t = ovlp_t - (( ovlp_t & norm_v ) * norm_v);
    
    // 计算法向和切向接触力 
    Scalar fn = - (4.0/3.0 * prop_ij.Yeff * sqrt(prop_ij.Reff) * pow(ovrlp,1.5) ) - (prop_ij.dmp_n* pow(ovrlp, 0.25) * vrn);
    Vector fnij = fn * norm_v;
    Vector ftij = (- 16.0/3.0 * prop_ij.Geff * sqrt(prop_ij.Reff*ovrlp) ) * ovlp_t;
    
    // 库伦摩擦定律
    ft = ftij.Mag();
    ft_fric = prop_ij.fric * abs(fn);
    if( abs(ft) > ft_fric )
    {
        if( ovlp_t.Mag() > 0.0 )
        {
            //TODO::检查
            if( this->contactForceType == Configure::Particle::ContactForceType::NONLINEAR_LIMITED )
            {
                // 有限制重叠
                ftij = ftij*(ft_fric/ft);
                kt = 16.0/3.0 * prop_ij.Geff * sqrt(prop_ij.Reff*ovrlp);
                ovlp_t = (-1.0) * (ftij/kt);
            }
            else
            {
                // 无限制重叠
                ftij = (ftij/ft)*ft_fric;
            }
        }
        else
        {
            ftij = Vector0; 
        }
    }

	// 更新颗粒接触力
	this->AddForcePW(i, fnij + ftij);

	if (contactTorqueFlag)
	{
		// 计算接触力矩 
		Rj = 1000000000.0;
		this->CalculateTorque(fn, ftij, prop_ij.roll_fric, norm_v, rveli, Vector0, Ri, Rj, Mij, Mji, Mri, Mrj);

		// 更新颗粒接触力矩   
		this->AddTorquePW(i, (Mij + Mri));
	}

    // 更新颗粒接触信息 
    ContPair_i.tang_del = ovlp_t;
    this->PW_CntctList->SetItem(ind , ContPair_i);
}

} // namespace Contact

} // namespace Particle