﻿#ifndef _particle_contact_contactForce_NonLin_ContactForce_
#define _particle_contact_contactForce_NonLin_ContactForce_

#include "feilian-specialmodule/particle/contact/contactForce/LinearForce.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class NonlinearForce : public LinearForce
{
public:
    /**
     * @brief 非线性接触力构造函数
     * 
     * @param geom 几何
     * @param CF_Type_ 接触力计算方法
     * @param CT_Model_ 接触力矩计算方法
     * @param wallContactType_ 壁面接触模型
     * @param max_nPrtcl_ 最大颗粒数量
     * @param numParticles_ 当前颗粒数量
     * @param particles_ 颗粒对象容器
     * @param dt_ 时间步长
     * @param PP_Cont_List_ 颗粒颗粒碰撞信息列表
     * @param PW_Cont_List_ 颗粒壁面碰撞信息列表
     * @param m_PhysProp_ 物理属性
     */
	NonlinearForce(Geometry::Geometry *geom,
                   const Configure::Particle::ContactForceType &CF_Type_,
                   const Configure::Particle::ContactTorqueType &CT_Model_,
				   const Configure::Particle::WallContactType &wallContactType_,
                   const int & max_nPrtcl_, const int &numParticles_,
                   std::vector<Particle *> &particles_, const Scalar &dt_,
                   ContactList *PP_Cont_List_, ContactList *PW_Cont_List_,
                   PhysicalProperty *m_PhysProp_);
    
    /**
     * @brief 析构函数
     * 
     */
    ~NonlinearForce();

    /**
     * @brief 计算颗粒与颗粒接触力及力矩
     * 
     * @param ind 碰撞索引
     * @param lnew 是否为首次接触标识
     */
    void ContactForcePP( int &ind , bool &lnew );
    
    /**
     * @brief 计算颗粒与壁面接触力及力矩
     * 
     * @param ind 碰撞索引
     * @param lnew 是否为首次接触标识
     */
    void ContactForcePW( int &ind , bool &lnew );

private:
    void CalculateForce(const BinaryProperty &prop_ij,
                   const Vector &norm_v, const Vector &Vrij, const Scalar &ovrlp,
                   Vector &ovlp_t, Scalar &fn, Vector &fnij, Vector &ftij);
};

} // namespace Contact

} // namespace Particle

#endif