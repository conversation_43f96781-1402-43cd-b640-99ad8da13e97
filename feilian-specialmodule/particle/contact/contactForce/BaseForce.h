﻿#ifndef _particle_contact_contactForce_ContactForce_
#define _particle_contact_contactForce_ContactForce_

#include "basic/geometry/Geometry.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"
#include "feilian-specialmodule/particle/property/PhysicalProperty.h"
#include "feilian-specialmodule/particle/contact/ContactList.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class BaseForce
{
public:
    // 接触力模型
    const Configure::Particle::ContactForceType &contactForceType;
    
    // 接触力矩模型
    const Configure::Particle::ContactTorqueType &contactTorqueType;

	// 壁面接触类型
	const Configure::Particle::WallContactType &wallContactType;

    // 当前颗粒数量
    int numParticles;
    
    // 最大颗粒数量
    const int &numParticlesMax;
    
    // 颗粒积分时间步长
    const Scalar &dt;
    
    // 颗粒
    std::vector<Particle *> &particles;
    
    // 颗粒与颗粒接触信息
    ContactList *b_PP_ContList;
    
    // 颗粒与壁面接触信息
    ContactList *b_PW_ContList;
    
    // 几何
    Geometry::Geometry *geometry;

	// 力矩计算标识
	bool contactTorqueFlag;

public:

    /**
     * @brief 接触力计算基类
     * 
     * @param geom 几何
     * @param CF_Type_ 接触力模型
     * @param CT_Model_ 接触力矩模型
     * @param wallContactType_ 壁面接触模型
     * @param max_nPrtcl_ 最大颗粒数量
     * @param numParticles_ 当前颗粒数量
     * @param particles_ 颗粒
     * @param dt_ 颗粒积分时间步长
     */
	BaseForce(Geometry::Geometry *geom,
              const Configure::Particle::ContactForceType &CF_Type_,
			  const Configure::Particle::ContactTorqueType &CT_Model_,
			  const Configure::Particle::WallContactType &wallContactType_,
              const int & max_nPrtcl_, const int &numParticles_,
              std::vector<Particle *> &particles_, const Scalar &dt_ = 1.0E-5 );
    
    /**
     * @brief 析构函数
     * 
     */
    virtual ~BaseForce();

    /**
     * @brief 更新颗粒数量
     * 
     * @param numParticleNew 颗粒数量
     */
    void SetParticleNumber(const int &numParticleNew);

    /**
     * @brief 累加颗粒与颗粒接触力
     * 
     * @param pari 颗粒i
     * @param parj 颗粒j
     * @param Fc 接触力
     */
    void AddForce( const int &pari , const int &parj , const Vector &Fc );
    
    /**
     * @brief 累加颗粒与颗粒接触力矩
     * 
     * @param pari 颗粒i
     * @param parj 颗粒j
     * @param Mij j对i的力矩
     * @param Mji i对j的力矩
     */
    void AddTorque( const int &pari , const int &parj , const Vector &Mij, const Vector &Mji );
    
    /**
     * @brief 累加颗粒受到壁面的作用力
     * 
     * @param pari 颗粒i
     * @param Fc 接触力
     */
    void AddForcePW( const int &pari , const Vector &Fc );

    /**
     * @brief 累加颗粒受到壁面的力矩
     * 
     * @param pari 颗粒i
     * @param Mij 壁面对颗粒的力矩
     */
    void AddTorquePW( const int &pari , const Vector &Mij );
    
    /**
     * @brief 计算所有颗粒与颗粒接触力及力矩
     * 
     */
    void CalculateForceAndTorquePP();
    
    /**
     * @brief 计算所有颗粒与壁面接触力及力矩
     * 
     */
    void CalculateForceAndTorquePW();
    
    /**
     * @brief 计算颗粒与颗粒接触力
     * 
     * @param ind 接触索引
     * @param lnew 首次接触标识
     */
    virtual void ContactForcePP( int &ind , bool &lnew ) = 0;
    
    /**
     * @brief 计算颗粒与壁面接触力
     * 
     * @param ind 接触索引
     * @param lnew 首次接触标识
     */
    virtual void ContactForcePW( int &ind , bool &lnew ) = 0;

};

} // namespace Contact

} // namespace Particle

#endif