﻿#ifndef _particle_ContactList_
#define _particle_ContactList_

#include "basic/common/ConfigUtility.h"
#include "feilian-specialmodule/particle/contact/ContactInfo.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

enum ContactStatus
{
    NO_CONTACT = -1,
    PROCESSED_CONTACT = 0,
    OLD_CONTACT = 1,
    NEW_CONTACT = 2
};

class ContactList
{
public:
    int numParticles; ///< 颗粒数量
    int numContacts; ///< 当前接触数量
    int numContactsMax; ///< 最大接触数量
    std::vector<std::pair<int, int>> contactList; ///< 接触列表
    std::vector<int> contactIndex; ///< 接触索引列表
    std::vector<int> blankIndex; ///< 空索引列表
    std::vector<ContactStatus> contactStatus; ///< 接触状态
    std::vector<ContactInfo> contactPair; ///< 接触信息容器
    
public:
    /**
     * @brief 接触列表构造函数
     * 
     * @param numContactsMax_ 最大接触数量
     * @param numParticles_ 颗粒数量
     */
    ContactList(const int &numContactsMax_, const int &numParticles_);
    
    /**
     * @brief 获得接触数量
     * 
     * @return const int& 接触数量
     */
    const int &GetContactsNumber()const {return this->numContacts;}

    /**
     * @brief 获得最大接触数量
     * 
     * @return const int& 最大接触数量
     */
    const int &GetContactsNumberMax()const {return this->numContactsMax;}
    
    /**
     * @brief 更新接触数量
     * 
     * @param val 接触数量
     */
    void SetContactsNumber(const int &numContacts_){ this->numContacts = numContacts_;}

    /**
     * @brief 获取接触信息
     * 
     * @param ind 接触索引
     * @param item 接触信息
     */
    ContactInfo &GetItem(int &ind) { return this->contactPair[ind];}
    
    /**
     * @brief 更新接触信息
     * 
     * @param ind 接触索引
     * @param item 接触信息
     */
    void SetItem(int &ind, const ContactInfo &item){this->contactPair[ind] = item;}
    
    /**
     * @brief 增加新接触
     * 
     * @param Mem_i 接触对象i的属性类型
     * @param Mem_j 接触对象j的属性类型
     * @param id_i 接触对象i的编号
     * @param id_j 接触对象j的编号
	 *
     * @return int 接触编号
     */
    int AddContact(const int &Mem_i, const int &Mem_j, const int &id_i, const int &id_j );

    /**
     * @brief 清空之前接触现在不接触的已有接触信息
     * 
     */
    void RemvReleased();
};

} // namespace Contact

} // namespace Particle

#endif