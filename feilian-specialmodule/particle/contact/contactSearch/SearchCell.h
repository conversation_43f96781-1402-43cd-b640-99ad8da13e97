﻿#ifndef _particle_contact_contactSearch_SearchCell_
#define _particle_contact_contactSearch_SearchCell_

#include "feilian-specialmodule/particle/basic/IndexVector.h"
#include "feilian-specialmodule/particle/basic/Particle.h"
#include "feilian-specialmodule/particle/contact/contactSearch/SimulationWorld.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class SearchCell : public SimulationWorld
{
private:
    Scalar dx;    // 搜索单元边长
    const Scalar &ratio; /// 搜索单元边长与最大直径比值
    IndexVector num; // 搜索单元三个方向的数量
    std::vector<IndexVector> box_index; // 搜索单元索引容器
    const bool clc_minmax; // 计算颗粒最大最小直径标识
	Vector minDomainCurrent; // 当前计算域最小范围
	Vector maxDomainCurrent; // 当前计算域最大范围
	bool dim2; // 二维标识

public:
    /**
     * @brief 搜索单元构造函数
     * 
     * @param minDomain 最小位置点
     * @param maxDomain 最大位置点
     * @param max_nPrtcl_ 最大颗粒数量
     * @param numParticles_ 当前颗粒数量
     * @param particles_ 颗粒容器
     * @param clc_minmax_ 计算颗粒最大最小直径标识
     * @param ratio_ 搜索单元边长与最大直径比值
     */
	SearchCell(const Vector &minDomain, const Vector &maxDomain,
              const Scalar &minDiameter, const Scalar &maxDiameter,
              const int &max_nPrtcl_, const int &numParticles_,
			  std::vector<Particle *> &particles_,
			  ElementField<Scalar> *volumeFraction,
			  const bool &clc_minmax_ = false, const Scalar &ratio_ = 1.0);

    /**
     * @brief 计算所有搜索单元的索引
     * 
     */
    void BoxIndex();

    /**
     * @brief 初始化函数
     * 
     */
    void Initialize();

    /**
     * @brief 获得搜索单元数量
     * 
     * @return const int& 搜索单元数量
     */
    const int &GetBoxNumber()const {return this->GetBoxTotalNumber();}

    /**
     * @brief 获得三个方向搜索单元数量
     * 
     * @return const IndexVector& 搜索单元数量
     */
    const IndexVector &GetCellNumber()const {return this->num;}

    /**
     * @brief 获得单元边长
     * 
     * @return Scalar 单元边长
     */
    Scalar GetCellSize(){return this->dx;}

    /**
     * @brief 获得第n个单元的索引坐标
     * 
     * @param n 单元编号
     * @return IndexVector 索引坐标
     */
    IndexVector GetIndex(const int &n){return this->box_index[n];}

	/**
	* @brief 更新索引坐标
	*
	*/
	void UpdateBoxIndex();

	/**
	* @brief 计算三个方向搜索单元数量
	*
	*/
	void CalculateSearchCellNumber();
};

} // namespace Contact

} // namespace Particle

#endif