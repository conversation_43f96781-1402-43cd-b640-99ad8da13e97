﻿#ifndef _particle_contact_contactSearch_NBS_Munjiza_
#define _particle_contact_contactSearch_NBS_Munjiza_

#include "feilian-specialmodule/particle/contact/contactSearch/SearchCell.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

/// Munjiza提出的No binary search (NBS) 接触搜索算法
class NBS_Munjiza : public SearchCell
{
public:
	std::vector<std::vector<int>> indexPrtcl;
	std::vector<std::vector<int>> sortIDY;
	std::vector<std::vector<int>> sortIDX_Y1;
	std::vector<std::vector<int>> sortIDX_Y0;
	std::vector<std::vector<std::vector<int>>> sortIDZ_Y1_X;
	std::vector<std::vector<std::vector<int>>> sortIDZ_Y0_X;

public:
	/**
	 * @brief 搜索算法构造函数
	 * 
	 * @param minDomain 域最小坐标
	 * @param maxDomain 域最大坐标
	 * @param numParticlesMax 最大颗粒数量
	 * @param numParticles 当前颗粒数量
	 * @param particles_ 颗粒容器
	 * @param ratio 搜索单元边长与最大直径比值
	 */
	NBS_Munjiza(const Vector &minDomain, const Vector &maxDomain,
				const Scalar &minDiameter_, const Scalar &maxDiameter_,
				const int &numParticlesMax, const int &numParticles,
				std::vector<Particle *> &particles_,
				ElementField<Scalar> *volumeFraction, const Scalar &ratio = 1.0);

	// 遍历所有搜索单元并采用NBS标记
	void LoopNBSMask(const int &iy, const int &ix, const int &iz);

	// 初始化
	void Initialize();

	// 全局搜索
	void BroadSearch();

	// 接触搜索
	void SearchParticleParticle();

	// 构造YList
	void YList();

	// 构造XList
	void XList(const int &iy, const bool &lcheck = false);

	// 构造ZLists1
	void ZList1(const int &ix, const int &k, const bool &lcheck = false);

	// 构造ZLists0
	void ZList0(const int &ix, const int &k, const bool &lcheck = false);

};

} // namespace Contact

} // namespace Particle

#endif