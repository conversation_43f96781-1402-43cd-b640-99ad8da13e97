﻿#include "feilian-specialmodule/particle/contact/contactSearch/SearchParticleParticle.h"

namespace Particle
{
namespace Contact
{

SearchParticleParticle::SearchParticleParticle(const Configure::Particle::ParticleConfigure &DEMConfigure_, const int &max_nPrtcl_, int &numParticles_,
							 std::vector<Particle *> &particels_, ContactList *contactListPP_, ElementField<Scalar> *volumeFraction)
                             : contactSearchMethod(DEMConfigure_.contact.contactSearchMethod), numParticles(numParticles_), numParticlesMax(max_nPrtcl_),
                             particles(particels_), contactListPP(contactListPP_)
{
    const Vector &minDomain_ = DEMConfigure_.reference.minDomain;
    const Vector &maxDomain_ = DEMConfigure_.reference.maxDomain;
    const Scalar &sizeRatio = DEMConfigure_.contact.sizeRatio;
    const Scalar &minDiameter_ = DEMConfigure_.distribution.minDiameter;
    const Scalar &maxDiameter_ = DEMConfigure_.distribution.maxDiameter;
    
    m_NBS_Munjiza = nullptr;
    
    switch(contactSearchMethod)
    {
    case Configure::Particle::ContactSearchMethod::NBS_Munjiza:
		this->m_NBS_Munjiza = new NBS_Munjiza(minDomain_, maxDomain_, minDiameter_, maxDiameter_, max_nPrtcl_, numParticles_, particels_, volumeFraction, sizeRatio);
        this->m_NBS_Munjiza->SetContactLists(contactListPP);
		break;
    case Configure::Particle::ContactSearchMethod::NBS:
    case Configure::Particle::ContactSearchMethod::NBS_Hrchl:
    default:
        FatalError("暂不支持该格式！");
		break;
    }
}

void SearchParticleParticle::Initialize()
{
    if(m_NBS_Munjiza) m_NBS_Munjiza->Initialize();
    switch(contactSearchMethod)
    {
    case Configure::Particle::ContactSearchMethod::NBS_Munjiza:
        Print("采用NBS Munjiza相邻搜索算法", 2);
        Print("搜索单元大小为：" + ToString(this->m_NBS_Munjiza->GetCellSize()), 3);
        Print("各方向搜索单元数量为：" + ToString(this->m_NBS_Munjiza->GetCellNumber().ToDoubleVector()), 3);
		break;
    case Configure::Particle::ContactSearchMethod::NBS:
    case Configure::Particle::ContactSearchMethod::NBS_Hrchl:
    default:
        FatalError("暂不支持该格式！");
		break;
    }
}

void SearchParticleParticle::SetParticleNumber(const int &numParticleNew)
{
    this->numParticles = numParticleNew;
    
    switch(this->contactSearchMethod)
	{
	case Configure::Particle::ContactSearchMethod::NBS_Munjiza:
		this->m_NBS_Munjiza->SetParticleNumber(numParticleNew);
		break;
    case Configure::Particle::ContactSearchMethod::NBS:
    case Configure::Particle::ContactSearchMethod::NBS_Hrchl:
    default:
        FatalError("暂不支持该格式！");
		break;
    }
}

void SearchParticleParticle::FindContacts()
{
    switch(this->contactSearchMethod)
	{
	case Configure::Particle::ContactSearchMethod::NBS_Munjiza:
		this->m_NBS_Munjiza->SearchParticleParticle();
		break;
    case Configure::Particle::ContactSearchMethod::NBS:
    case Configure::Particle::ContactSearchMethod::NBS_Hrchl:
    default:
        FatalError("暂不支持该格式！");
		break;
    }

    // 清空之前接触现在不接触的已有接触信息
    this->contactListPP->RemvReleased();
}

std::vector<int> SearchParticleParticle::GetContactNumber()
{
    std::vector<int> res(2, 0);
    switch(this->contactSearchMethod)
	{
	case Configure::Particle::ContactSearchMethod::NBS_Munjiza:
		res[0] = this->m_NBS_Munjiza->GetContactNumber();
		break;

    case Configure::Particle::ContactSearchMethod::NBS:
    case Configure::Particle::ContactSearchMethod::NBS_Hrchl:
    default:
        FatalError("暂不支持该格式！");
		break;
    }
    
    return res;
}

} // namespace Contact

} // namespace Particle