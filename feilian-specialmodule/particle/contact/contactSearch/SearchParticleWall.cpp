﻿#include "feilian-specialmodule/particle/contact/contactSearch/SearchParticleWall.h"

namespace Particle
{
namespace Contact
{

SearchParticleWall::SearchParticleWall(const int &numParticles_, std::vector<Particle *> &particels_,
                                 const Scalar &dt_, const int &iterNumber_, ContactList *contactListPW_, Geometry::Geometry *geometry_)
                                 : numParticles(numParticles_), particles(particels_), dt(dt_), iterNumber(iterNumber_), 
                                 contactListPW(contactListPW_), geometry(geometry_)
{
    lastIterStep = -1;
}

void SearchParticleWall::Initialize()
{
	this->nearWallParticleRange.clear();
    this->nearWallParticleRange.resize(geometry->GetPlaneWallNumber());
	this->nearWallParticleID.reserve(1000);
}

bool SearchParticleWall::FindNearWallParticle()
{
    // // 检查当前迭代中壁面相邻颗粒是否需要更新
    // if( iterNumber < this->lastIterStep ) return false;

    // 计算最大颗粒直径
    Scalar max_d = Scalar0;
	for (int i = 0; i < this->numParticles; ++i)
	{
		if (this->particles[i] != nullptr)
			max_d = Max(max_d, this->particles[i]->diameter);
	}

    // 最大颗粒直径的两倍作为近壁面颗粒判据
    // const Scalar dx = max_d * 2.0;
	Scalar distanceMax = 0;
    const Scalar dx = Max(max_d * 2.0, distanceMax);
    
    // 清空近壁面颗粒编号容器
	this->nearWallParticleID.clear();

    // 遍历所有壁面，形成每个壁面临近颗粒编号容器
    const int &wallNumber = this->geometry->GetPlaneWallNumber();
    for(int i = 0; i < wallNumber; ++i) this->FindNearWallParticle( i , dx );
    
    // // 计算所有颗粒的最大速度和加速度
    // Scalar velocityMax = 0, accelerationMax = 0, distanceMax = 0;
    // for(int i = 0; i < numParticles; ++i)
    // {
	// 	if (this->particles[i] != nullptr)
    //     {
    //         const Scalar vel = this->particles[i]->linearVelocity.Mag();
    //         const Scalar acc = this->particles[i]->linearAcceleration.Mag();
    //         velocityMax = Max( velocityMax , vel );
    //         accelerationMax = Max( accelerationMax , acc );
    //         // distanceMax = Max(distanceMax, vel * dt + 0.5 * acc * dt * dt);
    //     }
    // }
    // const Scalar vMax = Max(velocityMax, 0.3);
    // const Scalar aMax = Max(accelerationMax, 9.8);
	// 
    // // 计算颗粒以最大速度和最大加速度运动dx需要多少时间步
    // // 位移公式：S = v * t + 0.5 * a * t * t
    // // 计算t: t = ( -v + sqrt( v * v - (4 * (0.5 * a) * (-S)) ) ) / a
    // const Scalar dist = dx - 0.5 * max_d;
    // const Scalar t = ( sqrt(vMax * vMax + 2.0 * aMax * dist) - vMax ) / aMax;
    // this->lastIterStep = this->iterNumber + Min(Max((int)(t/dt), 1), 10);

    return true;
}

void SearchParticleWall::FindContacts()
{
	for (int i = 0; i < numParticles; ++i)
	{
		if (this->particles[i] != nullptr)
		{
			this->particles[i]->SetContactWallIndex(-1);
		}
	}

    const int &wallNumber = this->geometry->GetPlaneWallNumber();
    for(int i = 0; i < wallNumber; ++i)
    {
        const std::vector<int> &rng = this->nearWallParticleRange[i];
        for(int n = rng[0]; n <= rng[1]; ++n)
        {
             // 获得近壁面的颗粒编号
             const int &particleID = this->nearWallParticleID.at(n);
             const auto &particle = this->particles[particleID];
			 if (particles[particleID] == nullptr) continue;
			 const int flag = this->geometry->pWall[i].IsInContact(particle->position, particle->diameter);
			 if (flag == 1)
			 {
				 if (this->particles[particleID]->GetContactWallIndex() < 0)
				 {
					 const int pos = this->contactListPW->AddContact(particleID, n, particle->ID, this->geometry->pWall[i].wallID);
					 this->particles[particleID]->SetContactWallIndex(pos);
				 }
			 }
        }
    }
    
    // 更新接触信息
    this->contactListPW->RemvReleased();
}

void SearchParticleWall::ResetSearch()
{
    this->lastIterStep = -1;
}

void SearchParticleWall::FindNearWallParticle(const int &wallID, const Scalar &dx)
{
    // 获取壁面
    const auto &wall = this->geometry->GetWall(wallID);

    // 获得各方向最大坐标
    const auto &nodes = wall.nodes;
    const int &nodeSize = nodes.size();
    Vector min_point = nodes[0], max_point = nodes[0];
    for (int i = 1; i < nodeSize; i++)
    {
        min_point = Min(min_point, nodes[i]);
        max_point = Max(max_point, nodes[i]);
    }

    // 各方向最大最小值外延dx
    bool dim2 = geometry->GetDimension() == 2;
    if (dim2)
    {
        min_point = min_point - (dx * Vector(1.0,1.0,0.0));
        max_point = max_point + (dx * Vector(1.0,1.0,0.0));
    }
    else
    {
        min_point = min_point - (dx * Vector(1.0,1.0,1.0));
        max_point = max_point + (dx * Vector(1.0,1.0,1.0));
    }
    
    // 计算各方向最大距离的倒数
    Vector ddx = max_point - min_point;
    if (dim2) ddx = Vector(1.0/ddx.X(), 1.0/ddx.Y(), 0.0);
    else      ddx = Vector(1.0/ddx.X(), 1.0/ddx.Y(), 1.0/ddx.Z());
    
    // 当前壁面最近颗粒在所有近壁面颗粒中的起始位置索引
    const int firstIndex = this->nearWallParticleID.size();
    
    for (int i = 0; i < this->numParticles; ++i)
    {
        // 遍历计算域的所有颗粒
		if (this->particles[i] != nullptr)
        {
			const Vector distance = this->particles[i]->position - min_point;

			if (distance.X() < 0 || distance.Y() < 0 || (!dim2 && distance.Z() < 0)) continue;

			IndexVector indx(distance.Multiply(ddx));
            
            // 如果颗粒在盒子内，添加颗粒到近壁面颗粒列表
            if(indx.x == 0 && indx.y == 0 && indx.z == 0 )
                this->nearWallParticleID.push_back(i);
        }
    }
    
    // 当前壁面最近颗粒在所有近壁面颗粒中的终止位置索引
    const int lastIndex = this->nearWallParticleID.size() - 1;

    // 更新当前壁面的近壁面颗粒索引范围
    this->nearWallParticleRange[wallID] = std::vector<int>{firstIndex, lastIndex};
}

} // namespace Contact

} // namespace Particle