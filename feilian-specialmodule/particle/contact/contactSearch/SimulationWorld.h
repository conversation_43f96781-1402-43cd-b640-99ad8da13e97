﻿#ifndef _particle_contact_contactSearch_SimulationWorld_
#define _particle_contact_contactSearch_SimulationWorld_

#include "basic/field/ElementField.h"
#include "feilian-specialmodule/particle/contact/ContactList.h"
#include "feilian-specialmodule/particle/basic/Particle.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

/**
 * @brief 接触命名空间
 * 
 */
namespace Contact
{

class SimulationWorld
{
public:
    const Vector &minDomain; // 计算域坐标下限
    const Vector &maxDomain; // 计算域坐标上限
    
    const int &numParticlesMax;   // 最大颗粒数量
    const int &numBoundingBoxesTotal; //
    int numParticles;     // 当前颗粒数量
    std::vector<Particle *> &particles; // 颗粒容器
	ElementField<Scalar> *volumeFraction; // 颗粒体积分数场

    int numConservativeContacts; // 精细搜索次数
    
    Scalar minDiameter; // 最小颗粒直径
	Scalar maxDiameter; // 最大颗粒直径
    
    ContactList *contactListPP; // 颗粒接触信息

	bool contactSearch;  // 颗粒接触搜索标识

public:
    // 构造函数
	SimulationWorld(const Vector &minDomain, const Vector &maxDomain,
                    const Scalar &minDiameter_, const Scalar &maxDiameter_,
                    const int &max_nPrtcl_, const int &numParticles_,
					std::vector<Particle *> &particles_,
					ElementField<Scalar> *volumeFraction_);
    
    // 更新颗粒数量
    void SetParticleNumber(const int &numParticleNew);

    // 更新接触信息
	void SetContactLists(ContactList *PP_CL);
    
    // 更新最小最大直径
    void SetMinMaxDiameter(Scalar &min_, Scalar &max_) { this->minDiameter = min_; this->maxDiameter = max_; }

	// 返回最小最大直径
    std::pair<Scalar, Scalar> GetMinMaxDiameter() { return std::pair<Scalar, Scalar>(this->minDiameter, this->maxDiameter); }
    
	// 获取计算域最小坐标
    const Vector &GetMinDomain()const { return this->minDomain; }

	// 获取计算域最大坐标
    const Vector &GetMaxDomain()const { return this->maxDomain; }

    // 获取总的盒子数量
    const int &GetBoxTotalNumber()const {return this->numBoundingBoxesTotal;}
    
    // 获取当前的颗粒数量
    const int &GetParticleNumber()const {return this->numParticles;}

	// 获取最大颗粒数量
    const int &GetParticleNumberMax()const {return this->numParticlesMax;}
    
    // 颗粒颗粒碰撞精细搜索
    void FineSearch(const int &index1, const int &index2);
    
    // 设置颗粒颗粒接触数量
    void SetContactNumber(const int &num){this->numConservativeContacts = num;}

	// 获取颗粒颗粒接触数量
    const int &GetContactNumber()const {return this->numConservativeContacts;}

	// 增加颗粒颗粒接触数量
    void AddContactNumber(const int &num){this->numConservativeContacts += num;}

	// 判断是否进行接触搜索
	bool IsContactSearch() { return this->contactSearch; }

};

} // namespace Contact

} // namespace Particle

#endif