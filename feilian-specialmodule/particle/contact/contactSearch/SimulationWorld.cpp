﻿#include "feilian-specialmodule/particle/contact/contactSearch/SimulationWorld.h"

namespace Particle
{
namespace Contact
{

SimulationWorld::SimulationWorld(const Vector &minDomain_, const Vector &maxDomain_, 
                                 const Scalar &minDiameter_, const <PERSON>alar &maxDiameter_,
                                 const int &max_nPrtcl_, const int &numParticles_,
								 std::vector<Particle *> &particles_,
								 ElementField<Scalar> *volumeFraction_)
				                 : minDomain(minDomain_), maxDomain(maxDomain_),
				                 minDiameter(minDiameter_), maxDiameter(maxDiameter_),
                                 numParticlesMax(max_nPrtcl_), numBoundingBoxesTotal(max_nPrtcl_),
				                 numParticles(numParticles_), particles(particles_),
								 volumeFraction(volumeFraction_)
{
    numConservativeContacts = 0;
	contactSearch = true;
}

void SimulationWorld::SetParticleNumber(const int &numParticleNew)
{
    this->numParticles = numParticleNew;
}

void SimulationWorld::SetContactLists(ContactList *PP_CL)
{
	this->contactListPP = (ContactList *)PP_CL;
}

void SimulationWorld::FineSearch(const int &index1, const int &index2)
{
	if(Overlap(*this->particles[index1], *this->particles[index2]) >= Scalar0)
		this->contactListPP->AddContact(index1, index2, this->particles[index1]->ID, this->particles[index2]->ID);
}

} // namespace Contact

} // namespace Particle
