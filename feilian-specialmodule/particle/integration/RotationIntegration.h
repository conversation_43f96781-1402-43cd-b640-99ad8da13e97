﻿#ifndef _particle_RotationIntegration_
#define _particle_RotationIntegration_

#include "feilian-specialmodule/particle/integration/AB3AM4.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

class RotationIntegration
{
public:
    const Configure::Particle::MotionIntegrationScheme &Int_Method;
    const int numParticlesMax;
    int numParticles;
    const Scalar &dt;
    std::vector<Particle *> &particles;

	/// 积分方法对象
	AB3AM4 *m_AB3AM4;

public:
    /**
     * @brief 构造函数
     * 
     * @param max_nPrtcl_ 最大颗粒数量
     * @param numParticles_ 当前颗粒数量
     * @param particles_ 颗粒容器
     * @param dt_ 时间步长
     * @param Method_ 积分方法
     */
    RotationIntegration(const int &max_nPrtcl_, const int &numParticles_,
                        std::vector<Particle *> &particles_,
                        const Scalar &dt_ = 1.0E-5,
                        const Configure::Particle::MotionIntegrationScheme &Method_ = Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4);
    
    /**
     * @brief 预测函数
     * 
     */
    void Predict();
    
    /**
     * @brief 修正函数
     * 
     */
    void Correct();
    
    /**
     * @brief 更新颗粒数量
     * 
     * @param numParticleNew 颗粒数量
     */
    void SetParticleNumber(const int &numParticleNew);

};

} // namespace Particle

#endif