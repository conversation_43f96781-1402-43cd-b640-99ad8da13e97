﻿#include "feilian-specialmodule/particle/integration/RotationIntegration.h"

namespace Particle
{

RotationIntegration::RotationIntegration(const int &max_nPrtcl_, const int &numParticles_,
                                         std::vector<Particle *> &particles_,
                                         const Scalar &dt_,
                                         const Configure::Particle::MotionIntegrationScheme &Method_)
    :numParticles(numParticles_), numParticlesMax(max_nPrtcl_), Int_Method(Method_), dt(dt_), particles(particles_)
{
	switch (this->Int_Method)
	{
	case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
		this->m_AB3AM4 = new AB3AM4();
		break;

	default:
		FatalError("旋转运动不支持该积分方法！");
	}
}

void RotationIntegration::Predict()
{
    switch (this->Int_Method)
    {
    case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
        for (int i = 0; i < this->numParticles; ++i)
        {
			if (this->particles[i] != nullptr)
			{
				auto &particle = this->particles[i];
				this->m_AB3AM4->Predict(this->dt, particle->angularPosition0, particle->angularVelocity0, particle->angularAcceleration0,
					particle->angularPosition, particle->angularVelocity);
            }
        }
		break;

    default:
        FatalError("旋转运动不支持该积分方法！");
    }
}

void RotationIntegration::Correct()
{
    switch (this->Int_Method)
    {
    case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
        for (int i = 0; i < this->numParticles; ++i)
        {
			if (this->particles[i] != nullptr)
			{
				auto &p = this->particles[i];
				this->m_AB3AM4->Correct(p->angularAcceleration, this->dt, p->angularPosition, p->angularVelocity,
					p->angularPosition0, p->angularVelocity0, p->angularAcceleration0, false);
            }
        }
		break;

    default:
        FatalError("旋转运动不支持该积分方法！");
    }
}

void RotationIntegration::SetParticleNumber(const int &numParticleNew)
{
	switch (this->Int_Method)
	{
	case Configure::Particle::MotionIntegrationScheme::PIM_AB3AM4:
		for (int i = numParticles; i < numParticleNew; ++i)
		{
			if (this->particles[i] != nullptr)
			{
				this->particles[i]->angularVelocity0.resize(3, Vector0);
				this->particles[i]->angularAcceleration0.resize(3, Vector0);
				this->particles[i]->angularPosition0 = this->particles[i]->angularPosition;
				this->particles[i]->angularVelocity0[0] = this->particles[i]->angularVelocity;
			}
		}
		break;

	default:
		FatalError("旋转运动不支持该积分方法！");
	}

    this->numParticles = numParticleNew;
}

} // namespace Particle