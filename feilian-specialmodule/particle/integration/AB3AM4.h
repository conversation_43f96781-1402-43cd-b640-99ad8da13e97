﻿#ifndef _particle_AB3AM4_
#define _particle_AB3AM4_

#include "feilian-specialmodule/particle/configure/ParticleConfigure.h"
#include "feilian-specialmodule/particle/basic/Particle.h"

/**
 * @brief 颗粒命名空间
 * 
 */
namespace Particle
{

class AB3AM4
{
private:
    std::vector<Scalar> AB3_coeff;
    std::vector<Scalar> AM4_coeff;

public:
	/**
	* @brief 构造函数
	*
	* @param[in] pos 初始位置
	* @param[in] vel 初始速度
	*/
	AB3AM4();

    /**
     * @brief 构造函数
     * 
     * @param[in] pos 初始位置
     * @param[in] vel 初始速度
     */
	AB3AM4(const Vector &pos, const Vector &vel, Vector &x0, std::vector<Vector> &v0, std::vector<Vector> &a0);

    /**
     * @brief 预测函数
     * 
     * @param dt 时间步长
     * @param[out] pos 预测位置
     * @param[out] vel 预测速度 
     */
	void Predict(const Scalar &dt, const Vector &x0, const std::vector<Vector> &v0, const std::vector<Vector> &a0, Vector &pos, Vector &vel);

    /**
     * @brief 修正函数
     * 
     * @param[in] acc 加速度
     * @param[in] dt 时间步长
     * @param[out] pos 修正后位置
     * @param[in, out] vel 输入修正前速度，输出修正后速度
     * @param[in] 壁面反弹标识
     */
	void Correct(Vector &acc, const Scalar &dt, Vector &pos, Vector &vel, Vector &x0, std::vector<Vector> &v0, std::vector<Vector> &a0, const bool &flag);
    
};

} // namespace Particle

#endif