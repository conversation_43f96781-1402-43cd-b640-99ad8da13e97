﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file ChemkinFile.h
//! <AUTHOR>
//! @brief 多组分化学反应输入文件处理类，用于读取文件信息。
//! @date 2024-11-05
//
//------------------------------修改日志----------------------------------------
// 2024-11-05 李艳亮、钱琛庚
//    说明：建立并规范化。
//------------------------------------------------------------------------------

#ifndef _sourceFlow_reaction_ChemkinFile_
#define _sourceFlow_reaction_ChemkinFile_

#include "sourceFlow/configure/FlowConfigure.h"

/**
 * @brief 多组分化学反应命名空间
 * 
 */
namespace MultiSpecies
{
/**
 * @brief  多组分化学反应输入文件处理类
 * 
 */
class  ChemkinFile
{
	struct ReactionStruct // 存储化学反应信息的结构体
	{
		bool reverse; ///< 反应是否可逆（ture为可逆）
		std::vector<std::pair<int, Scalar>> left;  ///< 左端反应物序号(first)及系数(second)
		std::vector<std::pair<int, Scalar>> right; ///< 右端生成物序号及系数

		std::vector<std::pair<int, Scalar>> Order;  ///< 左端反应物序号(first)及系数(second)

		std::vector<Scalar> arrhenius;
		std::vector<std::pair<int, Scalar>> thirdBody;
		std::vector<Scalar> low;
		std::vector<Scalar> troe;
	};

	struct PureSpeciesStruct //储存单个组分信息
	{
		std::string speciesName;
		Scalar molarMass;                       ///<  摩尔质量
		Scalar gasConstant;
		std::vector<Scalar> nasaPolynomialLow;  ///<  T<1000K, NASA多项式系数用于计算Cp、熵、焓
		std::vector<Scalar> nasaPolynomialHigh; ///<  T>1000K, NASA多项式系数用于计算Cp、熵、焓
		Scalar wellDepth;
		Scalar diameter;
		Scalar polarizability;
		Scalar rotationalRelaxation;
	};

public:
    /**
     * @brief 构造函数
     * 
     * @param[in] fileName 文件路径及名称
     */
	ChemkinFile(const std::string &fileName);

    /**
     * @brief 析构函数
     * 
     */
	~ChemkinFile(){};

	void Initialize(const std::string &fileName);

	/**
	* @brief 得到组分数量
	*
	*/
	const int &GetSpeciesSize(){ return speciesSize; };
	
	/**
	* @brief 得到化学反应数量
	*
	*/
	const int &GetReactionSize(){ return reactionSize; }

	/**
	* @brief 获取原子名称
	*
	* @param[in] index 序号
	*/
	const std::string &GetElementName(const int &index){ return elementList[index]; }

	/**
	* @brief 获取组分名称
	*
	* @param[in] index 序号
	*/
	const std::string &GetSpeciesName(const int &index){ return speciesList[index]; }

	/**
	* @brief 获取化学反应信息结构体
	*
	* @param[in] index 序号
	*/
	const ReactionStruct &GetReaction(const int &index){ return reactionList[index]; }

	const PureSpeciesStruct &GetPureSpeciesData(const int &index){ return speciesDataList[index]; }

	const Scalar GetMolarMass(const int &index){ return speciesDataList[index].molarMass; }

	const Scalar GetGasConstant(const int &index){ return speciesDataList[index].gasConstant; }

	const Scalar GetWellDepth(const int &index){ return speciesDataList[index].wellDepth; }

	const Scalar GetDiameter(const int &index){ return speciesDataList[index].diameter; }

	const Scalar GetPolarizability(const int &index){ return speciesDataList[index].polarizability; }

	const Scalar GetRotationalRelaxation(const int &index){ return speciesDataList[index].rotationalRelaxation; }
	
	std::vector<Scalar> GetThermoDatLow(const int &index){ return speciesDataList[index].nasaPolynomialLow; }

	std::vector<Scalar> GetThermoDatHigh(const int &index){ return speciesDataList[index].nasaPolynomialHigh; }
	
private:
	/**
	* @brief 读取文件中的参数信息
	*
	* @param[in] fileName 文件名称
	*/
	void ReadFile(const std::string &fileName);

	/**
	* @brief 读取化学反应信息
	*
	* @param[in] info 化学反应字符串容器
	* @param[out] r 化学反应信息结构体
	*/
	void GetReaction(const std::vector<std::string> &info, ReactionStruct &r);

	/**
	* @brief 读取NASA多项式信息
	*
	* @param[in] info NASA多项式字符串容器
	* @param[out] pureSpecies 单个组分信息结构体
	*/
	void ReadThermoDat(const std::vector<std::string> &info, std::vector<PureSpeciesStruct> &pureSpecies);

	/**
	* @brief 读取计算黏性、热传导和扩散系数所需参数的信息
	*
	* @param[in] info 参数字符串容器
	* @param[out] pureSpecies 单个组分信息结构体
	*/
	void ReadTransDat(const std::vector<std::string> &info, std::vector<PureSpeciesStruct> &pureSpecies);

	/**
	* @brief 计算组分摩尔质量
	*
	* @param[in] info NASA多项式字符串容器
	* @param[out] pureSpecies 单个组分信息结构体
	*/
	void GetMolarMass(const std::vector<std::string> &info, std::vector<PureSpeciesStruct> &pureSpecies);

	/**
	* @brief 读取NASA多项式信息
	*
	* @param[in] elementName 元素名称
	* @param[out]  元素摩尔质量
	*/
	const int GetElementMolarMass(const char &elementName);

	/**
	* @brief 读取化学反应方程的左边或者右边信息
	*
	* @param[in] reaction 化学反应字符串
	* @param[out] r 化学反应信息结构体
	* @param[in] left ture为读取左边，false为右边
	*/
	void GetSpeciesandCoeff(std::string reaction, ReactionStruct &r, const bool &left);

	/**
	* @brief 读取化学反应方程（带(+M)）的附加信息
	*
	* @param[in] sLow 字符串sLow
	* @param[in] sTROE 字符串sTROE
	* @param[out] r 化学反应信息结构体
	*/
	void GetMAddCoeff(const std::string &sLow, const std::string &sTROE, ReactionStruct &r);

	/**
	* @brief 读取化学反应方程（带+M）的附加信息
	*
	* @param[in] ss 字符串
	* @param[out] r 化学反应信息结构体
	*/
	void GetMCoeff(std::string ss, ReactionStruct &r);

	/**
	* @brief 按照关键字读取文件的字符串
	*
	* @param[in] file 文件对象
	* @param[out] info 字符串信息容器
	* @param[in] start 起始字符串标记
	* @param[in] end 结束字符串标记
	*/
	void GetStringFromKeyWords(std::ifstream &file, std::vector<std::string> &info, const std::string &start, const std::string &end);

	/**
	* @brief 按照关键字读取文件的字符串，针对thermo.dat文件
	*
	* @param[in] file 文件对象
	* @param[out] info 字符串信息容器
	* @param[in] start 起始字符串标记
	* @param[in] end 结束字符串标记
	*/
	void GetStringFromKeyWordsThermo(std::ifstream &file, std::vector<std::string> &info, const std::string &start, const std::string &end);

	/**
	* @brief 按照关键字读取文件的字符串，针对trans.dat文件
	*
	* @param[in] file 文件对象
	* @param[out] info 字符串信息容器
	* @param[in] start 起始字符串标记
	* @param[in] end 结束字符串标记
	*/
	void GetStringFromKeyWordsTrans(std::ifstream &file, std::vector<std::string> &info, const std::string &start, const std::string &end);

	/**
	* @brief 计算文件中化学反应方程的数量
	*
	* @param[in] file 文件对象
	*/
	int GetReactionsNumber(std::ifstream &file);

	/**
	* @brief 从文件中读取给定序号的化学反应字符串
	*
	* @param[in] file 文件对象
	* @param[in] index 序号
	* @param[out] info 字符串信息容器
	*/
	void GetReactionString(std::ifstream &file, const int &index, std::vector<std::string> &info);

	/**
	* @brief 从给定字符串中分离出组分及其系数（如将“3H2O”分解为3与“H2O”）
	*
	* @param[in] s 给定字符串
	* @param[out] name 组分名称
	* @param[out] n 组分系数
	*/
    void SplitString(const std::string s, std::string &name, Scalar &n);


	void GetOrder(const std::string &sOrder, ReactionStruct &r);

private:    
	int speciesSize;  ///< 组分数量
	int reactionSize; ///< 反应数量
	Scalar R_;
	std::vector<std::string> elementList; ///< 原子名称列表
	std::vector<std::string> speciesList; ///< 组分名称列表
	std::vector<std::string> thermoDat; ///< NASA多项式系数
	std::vector<std::string> transDat; ///< 输运参数

	std::vector<ReactionStruct> reactionList; ///< 反应方程信息列表
	std::vector<PureSpeciesStruct> speciesDataList; ///< 单个组分信息列表
};

} // namespace Reaction
#endif 