////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Acceptor.h
//! <AUTHOR>
//! @brief 重叠网格插值单元（受体单元）类
//! @date 2024-03-20
//------------------------------------------------------------------------------

#pragma once

#include "basic/common/ConfigUtility.h"

class Acceptor
{
public:
    /**
     * @brief default constructor
     */
    Acceptor()
        : acceptorID(-1),
          acceptorProcID(-1),
          acceptorZoneID(-1),
          acceptorCenter(Vector0),
          centralDonorID(-1),
          centralDonorProcID(-1),
          centralDonorType(-1),
          centralDonorVolume(0){};

    /**
     * @brief 构造仅已知插值单元编号的对象
     *
     * @param acceptorID_
     */
    explicit Acceptor(const int &acceptorID_)
        : acceptorID(acceptorID_),
          acceptorProcID(-1),
          acceptorZoneID(-1),
          acceptorCenter(Vector0),
          centralDonorID(-1),
          centralDonorProcID(-1),
          centralDonorType(-1),
          centralDonorVolume(0){};

    /**
     * @brief 构造已知插值单元各项信息，但无贡献单元信息的对象
     *
     * @param acceptorID_
     */
    Acceptor(const int &acceptorID_,
             const int &acceptorProcID_,
             const int &acceptorZoneID_,
             const Vector &acceptorCenter_)
        : acceptorID(acceptorID_),
          acceptorProcID(acceptorProcID_),
          acceptorZoneID(acceptorZoneID_),
          acceptorCenter(acceptorCenter_),
          centralDonorID(-1),
          centralDonorProcID(-1),
          centralDonorType(-1), // 贡献单元的初始类型设为Hole
          centralDonorVolume(0){};

    /**
     * @brief Destroy the Acceptor Donor Stencil object
     *
     */
    ~Acceptor(){};

    const int &GetAcceptorID() const { return this->acceptorID; }
    const int &GetAcceptorProcID() const { return this->acceptorProcID; }
    const int &GetAcceptorZoneID() const { return this->acceptorZoneID; }
    const Vector &GetAcceptorCenter() const { return this->acceptorCenter; }

    const int &GetCentralDonorID() const { return this->centralDonorID; }
    const int &GetCentralDonorProcID() const { return this->centralDonorProcID; }
    const int &GetCentralDonorType() const { return this->centralDonorType; }
    const Scalar &GetCentralDonorVolume() const { return this->centralDonorVolume; }

    void SetAcceptorProcID(const int &pID) { this->acceptorProcID = pID; }
    void SetAcceptorID(const int &elemID) { this->acceptorID = elemID; }

    void SetCentralDonor(const int &newID,
                         const int &newProcID,
                         const Scalar &newVolume,
                         const int &newType)
    {
        // 1. 未找到贡献单元
        if (newID < 0)
        {
            return;
        }

        // 2. 找到了贡献单元
        //  a. 旧信息无贡献单元，替换旧信息
        if (this->centralDonorID < 0)
        {
            this->centralDonorID = newID;
            this->centralDonorProcID = newProcID;
            this->centralDonorVolume = newVolume;
            this->centralDonorType = newType;
        }
        else // b. 旧信息已经有贡献单元
        {
            if (this->centralDonorType < newType )//取贡献单元类型更好的
            {
                this->centralDonorID = newID;
                this->centralDonorProcID = newProcID;
                this->centralDonorVolume = newVolume;
                this->centralDonorType = newType;
            }
            else if (this->centralDonorType == newType && newVolume < this->centralDonorVolume)//贡献单元类型相同，取体积更小的
            {
                this->centralDonorID = newID;
                this->centralDonorProcID = newProcID;
                this->centralDonorVolume = newVolume;
                this->centralDonorType = newType;
            }
        }
    }

    /**
     * @brief Merges donor information from another Acceptor object.
     * This should be called for objects considered equivalent (same acceptorID and acceptorProcID).
     * @param other The other Acceptor object to merge from.
     */
    void Merge(const Acceptor &other)
    {
        this->SetCentralDonor(other.centralDonorID,
                              other.centralDonorProcID,
                              other.centralDonorVolume,
                              other.centralDonorType);
    }

    bool operator==(const Acceptor &acpt) const
    {
        return acceptorID == acpt.acceptorID && acceptorProcID == acpt.acceptorProcID;
    }

    bool operator<(const Acceptor &acpt) const
    {
        if (this->acceptorID != acpt.acceptorID) // 按受体单元编号比大小
        { 
            return this->acceptorID < acpt.acceptorID;
        }
        
        // 插值单元编号相同时，按进程编号比大小
        return this->acceptorProcID < acpt.acceptorProcID;
    }

    // 按照插值单元编号从小到大排序的方法
    static bool cmp(const Acceptor& a, const Acceptor& b)
    {
        return a.GetAcceptorID() < b.GetAcceptorID();
    }

private:
    // acceptor info
    int acceptorID;
    int acceptorProcID;
    int acceptorZoneID;
    Vector acceptorCenter;

    // Donor info，Acceptor类中仅存储centralDonor
    int centralDonorID; // 中央贡献单元的当地编号，该单元为acceptor中心所在的单元
    int centralDonorProcID;
    int centralDonorType;
    Scalar centralDonorVolume;

#if defined(_BaseParallelMPI_)
public:
    // 并行序列化
    template <class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        ar & acceptorID;
        ar & acceptorProcID;
        ar & acceptorZoneID;
        ar & acceptorCenter;
        ar & centralDonorID;
        ar & centralDonorProcID;
        ar & centralDonorType;
        ar & centralDonorVolume;
    }
#endif
};