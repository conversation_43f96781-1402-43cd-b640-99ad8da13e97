﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OverDefines.h
//! <AUTHOR>
//! @brief 重叠网格数据类型定义
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_overDefines_
#define _specialModule_oversetMesh_overDefines_

#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "feilian-specialmodule/oversetMesh/Donor.h"
#include "meshProcess/wallDistance/KDT_utilities.h"
#include "meshProcess/wallDistance/KDT_utilities.hxx"

template <class T>
using List = std::vector<T>;
template <class T1, class T2>
using Pair = std::pair<T1, T2>;
template <class T1, class T2>
using Map = std::map<T1, T2>;
template <class T>
using Set = std::set<T>;

/**
 * @brief 重叠网格单元类型枚举
 *
 */
enum OversetElemType
{
    HOLE = -1,      // 洞单元或节点
    ACCEPTOR = 0,   // 插值单元或节点
    CALCULATED = 1, // 计算单元或节点
};

/**
 * @brief 网格单元重叠类型判断方法
 *
 */
enum ElemTypeMethod
{
    ElemBased = 1, // 直接计算网格单元壁面距离，然后判断单元重叠类型
    NodeBased = 2  // 先计算网格点壁面距离，然后判断点重叠类型，再判断单元重叠类型
};

//------------------KDT相关变量-------------------
typedef DataStruct_KdtNode<List<Scalar>, int> KdtNode;
typedef DataStruct_KdtTree<List<Scalar>, int> KdtTree;

// KDT树的基本信息
struct TreeInfo
{
    int zoneID;            // tree所属子域
    int procID;            // tree所在进程
    List<Scalar> spaceMax; // tree的上界
    List<Scalar> spaceMin; // tree的下界

#if defined(_BaseParallelMPI_)
public:
    template <class Archive>
    void serialize(Archive &ar, const unsigned int version)
    {
        ar & zoneID;
        ar & procID;
        ar & spaceMax;
        ar & spaceMin;
    }
#endif
};
#endif