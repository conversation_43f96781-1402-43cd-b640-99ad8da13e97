#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

void OversetMesh::CreateElemKdtTrees()
{
	elemKdtTrees.resize(n_Zones);

	for (int zoneID = 0; zoneID < n_Zones; zoneID++)
	{
		int zoneElemStartID = zoneManager->GetZoneStartElemID(zoneID);
		int zoneElemNum = zoneManager->GetZoneElemNum(zoneID);

		if (zoneElemNum > 0)
		{
			// 将当前子域网格单元的AABB放入电云，将单元编号放入数据云
			List<List<Scalar>> pointCloud;
			List<int> dataCloud;
			for (int elemID = zoneElemStartID; elemID < zoneElemStartID + zoneElemNum; elemID++)
			{
				pointCloud.push_back(elemBound[elemID]);
				dataCloud.push_back(elemID);
			}
			elemKdtTrees[zoneID] = new KdtTree(2 * dim, pointCloud, dataCloud);
			
			pointCloud.clear();
			dataCloud.clear();
		}
		else // 子域在当前进程无网格，为空
		{
			elemKdtTrees[zoneID] = NULL;
		}
	}
}

void OversetMesh::ComputeElemBoundBox()
{
	// TODO:修改KDT树，使其能够传入点编号List，修改elemBound使其能够按域存储网格单元Box

	const int elemNum = localMesh->GetElementNumberReal();

	elemBound.clear();
	elemBound.resize(elemNum);

	// 获取所有单元的上下界，成为2*dim空间中的点
	for (int elemID = 0; elemID < elemNum; elemID++)
	{
		elemBound[elemID].resize(2 * dim);
		for (int i = 0; i < dim; i++)
		{
			elemBound[elemID][i] = INF;
			elemBound[elemID][i + dim] = -1 * INF;
		}

		// 循环elem的点，找到其上下界
		for (int nodeI = 0; nodeI < localMesh->GetElement(elemID).GetNodeSize(); nodeI++)
		{
			const int &nodeID = localMesh->GetElement(elemID).GetNodeID(nodeI);
			const Node &tempNode = localMesh->v_node[nodeID];
			List<Scalar> temp = { tempNode.X(), tempNode.Y(), tempNode.Z() };

			for (int i = 0; i < dim; i++)
			{
				elemBound[elemID][i] = std::min(elemBound[elemID][i], temp[i]);
				elemBound[elemID][i + dim] = std::max(elemBound[elemID][i + dim], temp[i]);
			}
		}
	}
}

void OversetMesh::AllGatherTreeInfo()
{
	for (int zoneID = 0; zoneID < n_Zones; zoneID++)
	{
		if (elemKdtTrees[zoneID])
		{
			TreeInfo temp = TreeInfo();
			temp.procID = processorID;
			temp.zoneID = zoneID;
			temp.spaceMax = elemKdtTrees[zoneID]->GetMax();
			temp.spaceMin = elemKdtTrees[zoneID]->GetMin();

			globalTreeInfo.push_back(temp);
		}
	}

	// 并行时合并全局信息
	if (nProcessor > 1)
	{
		this->AllGatherAndMergeList(globalTreeInfo);
	}
}

void OversetMesh::ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts, Set<Acceptor> &searchResults)
{
	// 处理需要发送到其他进程进行搜索的数据
	List<boost::mpi::request> sendRequest1;
	List<boost::mpi::request> recvRequest1;
	List<int> sendProcs;						 // 当前进程需要发送数据的目标进程
	List<List<int>> globalSendProcs(nProcessor); // 所有进程需要发送数据的目标进程
	for (int i = 0; i < nProcessor; i++)
	{
		if (i != processorID && groupedAcpts[i].size() > 0) // 将需要在其他进程搜索的Acceptor发送至目标进程，并非阻塞等待接收结果
		{
			sendRequest1.push_back(mpi_world.isend(i, 0, groupedAcpts[i]));
			recvRequest1.push_back(mpi_world.irecv(i, 1, groupedAcpts[i]));
			sendProcs.push_back(i);
		}
	}
	boost::mpi::all_gather(mpi_world, sendProcs, globalSendProcs);
								
	int emptyCount = 0; // 用于统计globalSendProcs中不需要发送数据的进程数
	for (int i = 0; i < nProcessor; i++)
	{
		if (globalSendProcs[i].size() > 0)
		{
			for (int j = 0; j < globalSendProcs[i].size(); j++)
			{
				if (globalSendProcs[i][j] == processorID) // 有进程需要向本进程发送数据，接收、搜索、返回
				{
					List<boost::mpi::request> sendRequest2;
					List<boost::mpi::request> recvRequest2;
					List<Acceptor> recvAcceptors;
					recvRequest2.push_back(mpi_world.irecv(i, 0, recvAcceptors));
					MPIWaitAll(recvRequest2);
					this->ChunkDonorSearch(recvAcceptors);
					sendRequest2.push_back(mpi_world.isend(i, 1, recvAcceptors));
					MPIWaitAll(sendRequest2);
				}
			}
		}
		else
		{
			++emptyCount;
		}
	}
	MPIWaitAll(sendRequest1);
	MPIWaitAll(recvRequest1);

	// 处理在本进程内的贡献单元搜索
	this->ChunkDonorSearch(groupedAcpts[processorID]);

	// 合并各进程搜索结果，自动选择最优贡献单元，形成最终搜索结果
	for (int i = 0; i < groupedAcpts.size(); i++)
	{
		searchResults.insert(groupedAcpts[i].begin(),groupedAcpts[i].end());
	}

	MPIBarrier();
}

void OversetMesh::NonBlockingDonorSearch()
{
	// if (processorID == 0 && verboseInfo)
	// {
	// 	Print("贡献单元搜索非阻塞监听--启动...........");
	// }
	// while (listenFlag)
	// {
	// 	// probe监听是否有消息到达
	// 	boost::optional<boost::mpi::status> msgStatus = mpi_world.probe(boost::mpi::any_source, boost::mpi::any_tag);
	// 	if (msgStatus) // 有消息到达
	// 	{
	// 		List<boost::mpi::request> recvRequest;
	// 		List<Acceptor> recvAcceptors;
	// 		recvRequest.push_back(mpi_world.irecv(msgStatus->source(), msgStatus->tag(), recvAcceptors));
	// 		MPIWaitAll(recvRequest);

	// 		if (msgStatus->tag() == 0) // tag=0表示要进行搜索
	// 		{
	// 			this->ChunkDonorSearch(recvAcceptors);
	// 			mpi_world.send(msgStatus->source(), 1, recvAcceptors);
	// 		}
	// 		else if (msgStatus->tag() == 1) // tag=1表示是返回的搜索结果
	// 		{
	// 			donorSearchResults[msgStatus->source()] = recvAcceptors;
	// 			searchCount -= 1; // 接收一个进程返回的结果，count减一
	// 		}
	// 		// else
	// 		// {
	// 		// 	FatalError("OversetMesh: 贡献单元搜索子线程接收到未知的标签");
	// 		// }
	// 	}
	// }

	// if (processorID == 0 && verboseInfo)
	// {
	// 	Print("贡献单元搜索非阻塞监听--关闭...........");
	// }
}

void OversetMesh::GroupingAcceptors(Set<int> &searchElemID, List<List<Acceptor>> &groupedAcpts)
{
	groupedAcpts.clear();
	groupedAcpts.resize(GetMPISize());
	for (auto it = searchElemID.begin(); it != searchElemID.end(); it++)
	{
		int elemID = *it;
		int elemZoneID = zoneManager->GetElemZoneID(elemID);

		bool grouped = false;
		for (int treeI = 0; treeI < globalTreeInfo.size(); treeI++)
		{
			TreeInfo &treeInfo = globalTreeInfo[treeI];

			if (elemZoneID != treeInfo.zoneID) // 只搜索其他子域
			{								   // 判断单元是否与该树空间相交
				if (ElemCenterInTree(elemID, treeInfo))
				{
					const Node &center = this->localMesh->GetElement(elemID).GetCenter();
					Acceptor temp(elemID, processorID, elemZoneID, center);
					groupedAcpts[treeInfo.procID].push_back(temp);
					grouped = true;
				}
			}
		}
		if (grouped == false) // 无法分配到任何树（无贡献单元），加入到当前进程，后续搜索时也将保持AcceptorID=-1的信息
		{
			const Node &center = this->localMesh->GetElement(elemID).GetCenter();
			Acceptor temp(elemID, processorID, elemZoneID, center);
			groupedAcpts[processorID].push_back(temp);
		}
	}
	searchElemID.clear();
}

void OversetMesh::GroupingAcceptors(const Set<Acceptor> &srcAcpts, List<List<Acceptor>> &groupedAcpts)
{
	groupedAcpts.clear();
	groupedAcpts.resize(nProcessor);

	for (auto it = srcAcpts.begin(); it != srcAcpts.end(); it++)
	{
		const int &donorProcID = it->GetCentralDonorProcID();
		const int &donorID = it->GetCentralDonorID();
		if (donorID >= 0)
		{
			groupedAcpts[donorProcID].push_back(*it);
		}
		else
		{
			int elemID = it->GetAcceptorID();
			int elemZoneID = zoneManager->GetElemZoneID(elemID);

			bool grouped = false;
			for (int treeI = 0; treeI < globalTreeInfo.size(); treeI++)
			{
				TreeInfo &treeInfo = globalTreeInfo[treeI];

				if (elemZoneID != treeInfo.zoneID) // 只搜索其他子域
				{								   // 判断单元是否与该树空间相交
					if (ElemCenterInTree(elemID, treeInfo))
					{
						groupedAcpts[treeInfo.procID].push_back(*it);
						grouped = true;
					}
				}
			}
			if (grouped == false) // 无法分配到任何树（无贡献单元），加入到当前进程，后续搜索时也将保持AcceptorID=-1的信息
			{
				groupedAcpts[processorID].push_back(*it);
			}
		}
	}
}

bool OversetMesh::ElemCenterInTree(const int &elemID, const TreeInfo &treeInfo)
{
	const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
	List<Scalar> center = { elemCenter.X(), elemCenter.Y(), elemCenter.Z() };
	for (int i = 0; i < dim; i++)
	{
		if (center[i] < treeInfo.spaceMin[i] || center[i] > treeInfo.spaceMax[i+dim])
        {
            return false;
        }
	}
	return true;
}

void OversetMesh::ChunkDonorSearch(List<Acceptor> &acptList)
{
	if (acptList.size()==0){return;}
	
	for (int i = 0; i < acptList.size(); i++)
	{
		Acceptor &acpt = acptList[i];
		const Node &elemCenter = acpt.GetAcceptorCenter();
		const int &donorID = acpt.GetCentralDonorID();
		const int &donorProcID = acpt.GetCentralDonorProcID();
		const Scalar &donorVolume = acpt.GetCentralDonorVolume();
		if (donorID >= 0 && donorProcID == processorID) // 如果已经有贡献单元且就在本进程
		{
			const Element &elem = this->localMesh->GetElement(donorID);
			if (this->NodeInElem(elemCenter, elem)) // 且插值单元中心仍然在原来的贡献单元里面（网格没有动，或者动的很小），仅更新贡献单元的状态
			{
				const int &newDonorType = elemTypeField->GetValue(donorID);
				const Scalar &newDonorVolume = elem.GetVolume();
				acpt.SetCentralDonor(donorID, donorProcID, newDonorVolume, newDonorType);
				continue;
			}
		}

		for (int zoneI = 0; zoneI < elemKdtTrees.size(); zoneI++)
		{
			if (elemKdtTrees[zoneI] && acpt.GetAcceptorZoneID() != zoneI) // 仅搜索其他子域的树
			{
				int newDonorID = this->DonorSearch(elemCenter, elemKdtTrees[zoneI]);
				if (newDonorID >= 0)
				{
					// Set<int> nei;
					// this->GetNodeNeighbour(donorID, nei);
					// nei.insert(donorID);
					// Scalar elemVolume = 0;
					// for (auto it = nei.begin(); it != nei.end(); it++)
					// {
						// elemVolume += localMesh->GetElement(*it).GetVolume();
					// }
					// elemVolume = elemVolume / nei.size();
					Scalar elemVolume = localMesh->GetElement(newDonorID).GetVolume();
					acpt.SetCentralDonor(newDonorID,
										 processorID,
										 elemVolume, 
										 elemTypeField->GetValue(newDonorID));
				}
			}
		}
	}
}

int OversetMesh::DonorSearch(const Node &srcNode, KdtTree *tgtTree)
{
	int donorID = -1;

	//确定2dim空间搜索范围
	List<Scalar> searchRangeMin;
	List<Scalar> searchRangeMax;
	this->NodeTo2dimRange(srcNode, searchRangeMin, searchRangeMax);

	//初筛所有可能的相交单元
	List<KdtNode *> kdtNodeList;
	tgtTree->FindNodesInRegion(searchRangeMin, searchRangeMax, kdtNodeList);

	//二次筛选，找到srcNode所在的贡献单元，(TODO：另一种策略是仅保留距离最近的单元，暂不考虑，未来看情况)
	if (kdtNodeList.size() > 0)
	{
		for (int i = 0; i < kdtNodeList.size(); i++)
		{
			int &elemID = kdtNodeList[i]->data; // 获取单元编号
			const Element &elem = localMesh->GetElement(elemID);
			// 不对贡献单元的重叠类型进行判断，只要落在单元内、即使是洞单元也可先作为贡献单元，后续再进一步处理
			if (this->NodeInElem(srcNode, elem))
			{
				donorID = elemID;
				const int &elemType = elemTypeField->GetValue(elemID);
				if (elemType == OversetElemType::ACCEPTOR) // 贡献单元作为了插值单元，标记出来
				{
					acceptorDonorFlag[elemID] = 1;
				}
			}
		}
	}

	return donorID;
}



void OversetMesh::SelectBestDonor(const List<List<Acceptor>> &groupedSearchResults, List<Acceptor> &mergedResults)
{
	// 合并
	mergedResults.clear();
	for (int i = 0; i < groupedSearchResults.size(); i++)
	{
		mergedResults.insert(mergedResults.end(), groupedSearchResults[i].begin(), groupedSearchResults[i].end());
	}
	if (mergedResults.size() == 0)
	{
		return;
	}

	// 排序
	std::sort(mergedResults.begin(), mergedResults.end(), Acceptor::cmp);

	// 筛选
	for (auto it = mergedResults.begin() + 1; it != mergedResults.end();)
	{
		// 当前元素与前一元素重复，更新前一元素信息，删除当前元素，erase会将迭代器自动指向后一元素
		if (it->GetAcceptorID() == (it - 1)->GetAcceptorID())
		{
			(it - 1)->SetCentralDonor(it->GetCentralDonorID(),
									  it->GetCentralDonorProcID(),
									  it->GetCentralDonorVolume(),
									  it->GetCentralDonorType());
			mergedResults.erase(it);
		}
		else // 当前元素与前一元素不重复，迭代器+1指向后一元素
		{
			it++;
		}
	}
}

void OversetMesh::NodeTo2dimRange(const Node &node, List<Scalar> &rangeMin2dim, List<Scalar> &rangeMax2dim)
{
	rangeMin2dim.clear();
	rangeMax2dim.clear();
	rangeMin2dim.resize(2 * dim);
	rangeMax2dim.resize(2 * dim);
	List<Scalar> point = { node.X(), node.Y(), node.Z() };

	// 将单元中心转换为2*dim空间中的范围
	for (int i = 0; i < dim; i++)
	{
		rangeMin2dim[i] = -1 * INF;
		rangeMin2dim[i + dim] = point[i];

		rangeMax2dim[i] = point[i];
		rangeMax2dim[i + dim] = INF;
	}
}

void OversetMesh::CreateCommitedDonors()
{
	List<List<Acceptor>> collectedAcceptors;
	this->CollectAcceptors(this->commitedAcceptors, collectedAcceptors);

	this->commitedDonors.clear();
	this->commitedDonors.resize(nProcessor);
	for (int i = 0; i < collectedAcceptors.size(); i++)
	{
		for (int j = 0; j < collectedAcceptors[i].size(); j++)
		{
			
			Donor temp(collectedAcceptors[i][j].GetAcceptorID(),
					   collectedAcceptors[i][j].GetAcceptorProcID(),
					   collectedAcceptors[i][j].GetAcceptorCenter(),
					   {collectedAcceptors[i][j].GetCentralDonorID()},
					   {1.0}); // 中央贡献单元的初始权重设为1
			this->commitedDonors[i].push_back(temp);
		}
	}

	this->FillDonorAndWeights();
}

void OversetMesh::FillDonorAndWeights()
{
	switch (this->interpolationType)
	{
	case OversetType::InterpolationType::InverseDistance:
		this->InverseDistance();	
		break;

	case OversetType::InterpolationType::Linear:
		FatalError("OversetMesh: 线性插值方法有待开发！");
		break;

	case OversetType::InterpolationType::LeastSquare:
		FatalError("OversetMesh: 最小二乘插值方法有待开发！");
		break;
	default:
		FatalError("OversetMesh: 未知的插值方法！");
		break;
	}
}

const Acceptor &OversetMesh::GetDonorSearchResults(const int &elemID, const Set<Acceptor> &tgtResults)
{
	auto it = std::find_if(tgtResults.begin(), tgtResults.end(),
						   [elemID](const Acceptor &acpt)
						   { return acpt.GetAcceptorID() == elemID; }); // 在搜索结果中检测
	if (it != tgtResults.end())
	{
		return *it;
	}
	else
	{
		// 搜索结果中没有该单元的Acceptor信息，这种情况不应当存在，直接报错
		FatalError("OversetMesh: pID= " + ToString(processorID) + ", elemID=" + ToString(elemID) + "未找到搜索结果！");
	}
}


/**
 * @ 废弃
 * 
 */
void OversetMesh::SelectBestDonor(Acceptor &srcAcpt, const List<List<Acceptor>> &tgtResults)
{
	bool flag = false;
	const int &elemID = srcAcpt.GetAcceptorID();
	
	for (int i = 0; i < tgtResults.size(); i++) // 在各个进程的搜索结果中检测
	{
		if (tgtResults[i].size() > 0)
		{
			auto it = std::find_if(tgtResults[i].begin(), tgtResults[i].end(),
								   [elemID](const Acceptor &acpt)
								   { return acpt.GetAcceptorID() == elemID; });
			if (it != tgtResults[i].end())
			{
				if (flag == false) // 第一个搜索结果
				{
					srcAcpt = *it;
					flag = true;
				}
				else // 已经发现了搜索结果，选择最优贡献单元
				{
					srcAcpt.SetCentralDonor(it->GetCentralDonorID(),
											it->GetCentralDonorProcID(),
											it->GetCentralDonorVolume(),
											it->GetCentralDonorType());
				}
			}
		}
	}
	if (flag == false) // 搜索结果中没有该单元的Acceptor信息，这种情况不应当存在，直接报错
	{
		FatalError("OversetMesh: pID= " + ToString(processorID) + ", elemID=" + ToString(elemID) + "未找到搜索结果！");
	}
}