﻿#include "feilian-specialmodule/meshDeform/MeshDeform.h"

#if defined(_BaseParallelMPI_)
namespace mpi = boost::mpi;
#endif
#if defined(_EnableMKL_)
#if defined(small)
#undef small
#endif

#include "mkl.h"
#include "mkl_pblas.h"
#include "mkl_scalapack.h"
#include "mkl_blacs.h"
#endif
MeshDeform::MeshDeform(std::vector<Node> &v_wallNode_,std::vector<Node> &v_wallNode_deform_,Scalar &R_ )
    :v_wallNode(v_wallNode_), v_wallNode_deform(v_wallNode_deform_),R(R_)
{
    
}

MeshDeform::~MeshDeform()
{
    v_weight.clear();
    //v_wallNode.clear();
    //v_wallNode_deform.clear();
}

void MeshDeform::Process()
{

    int nPart = GetMPISize();
    int processorID = GetMPIRank();

    if (nPart == 1)  //串行
    {
        RBFGreedyAlg rbfGreedyAlg(v_wallNode,v_wallNode_deform,R);
        rbfGreedyAlg.rbfGreedy();
        
        Print("\n v_wallNode.size = "+ std::to_string((long long int)v_wallNode.size()));
        
        if (v_wallNode.size() > 5000)  //判断条件
        {
            Print("\t使用基于空间子集逼近的贪心算法求RBF权重系数..\n");             
            v_weight = rbfGreedyAlg.GreedyAlgImp();
        }
        else if (v_wallNode.size() > 3000) 
        {
            Print("\t使用贪心算法求RBF权重系数 ....\n");
            //v_weight = rbfGreedyAlg.DirectGreedyAlg();
            v_weight = rbfGreedyAlg.GreedyAlgImp();
            //v_weight = rbfGreedyAlg.DirectRBFAlg();
        }
        else
        {
            Print("\t直接求解RBF权重系数..\n");
            v_weight = rbfGreedyAlg.DirectRBFAlg();
            // v_weight = rbfGreedyAlg.GreedyAlgImp();
            Print("\tRBF权重系数求解完成..\n");
        }    
    }
    else
    {
#if defined(_BaseParallelMPI_)
        mpi_world.barrier();            
        this->ScalapackDeform();
        mpi_world.barrier();    
        if (processorID == 0)  
        Print("\n 进程数 =   "+std::to_string((long  long int)processorID)+ "  权重计算完成");     
#endif             
     }
}

void MeshDeform::ScalapackDeform()
{
#if defined(_EnableMKL_)
    int nprocs,ictxt,info;
    int nprow,npcol,nrhs,nb;
    int work[5],myrow,mycol;
    int desca[9],descb[9];
    int zero = 0, one = 1,five = 5;
    int negone = -1;
    
    std::vector<int> work1;
    AllocateDim(work1, 5);

    int nPart = GetMPISize();
    int processorID = GetMPIRank();
    
    char symbol_r = 'r';
    char symbol_all = 'all';
    char symbol_ = ' ';
  
    int n = v_wallNode.size();
    nprocs = nPart;
    //主进程中划分进程块 
    if(processorID == 0)
    {
        int temp = (int)sqrt((Scalar)nprocs);
        for(int i = 1; i< temp+1; i++)
        {
            if( nprocs%i == 0) nprow=i;
        }
        npcol = nprocs/nprow;
        nrhs = 3;                                          //方程右边项列数
        nb = 400;        
            
    }
    
 
    //初始化work数组
    if(processorID == 0) 
    {
        work1[0]=n;                                           //矩阵A阶数
        work1[1]=nrhs;                                        //方程右边项列数
        work1[2]=nb;                                          //全局矩阵分块大小
        work1[3]=nprow;                                       //进程矩阵的行数
        work1[4]=npcol;                                       //进程矩阵的列数
    
    for(int i = 1; i < nPart; i++)
        {
            mpi_world.send(i, 55, work1);
        }              
    }
    else
    {
        mpi_world.recv(0, 55, work1);  
        n    =work1[0];
        nrhs =work1[1];
        nb   =work1[2];
        nprow=work1[3];
        npcol=work1[4];
    }
 
 
    //初始化当地矩阵a和b的数组描述器，desca descb
    blacs_get_(&negone, &zero, &ictxt);               
    blacs_gridinit_(&ictxt, &symbol_r, &nprow, &npcol);
    blacs_gridinfo_(&ictxt, &nprow, &npcol, &myrow, &mycol);  

    int np   =numroc_(&n, &nb, &myrow, &zero, &nprow);                         //np = LOCr()   当地矩阵a的总行数
    int nq   =numroc_(&n, &nb, &mycol, &zero, &npcol);                         //nq = LOCc()   当地矩阵a的总列数
    int nqrhs =numroc_(&nrhs, &nb, &mycol, &zero, &npcol);                      //当地右侧矩阵b的总列数
 
    int maxnp;
    if(np>1)
        {maxnp = np;}
    else
        {maxnp = 1;}
    descinit_(desca,&n,&n,&nb,&nb,&zero,&zero,&ictxt,&maxnp,&info);       //初始化desca数组
    descinit_(descb,&n,&nrhs,&nb,&nb,&zero,&zero,&ictxt,&maxnp,&info);    //max(1,np)为LLD，The leading dimension of the local array，number of rows 

 

    //矩阵声明  (a*x=b) 
    double *a = new double[np*nq];
    double *b = new double[np*nqrhs];    

    std::vector<Vector> b_temp;                  
    AllocateDim(b_temp, np);
    
   
    double *a_temp = new double[np*nq];
      
  /*  for( int i = 0; i < np*nq; i++)
      {
         a_temp[i] = 0.0;                                   
      }    */
              
    mpi_world.barrier();
    
    //当地矩阵a初始化
    int nblock_row_rmfs=np/nb;                      //计算出该处理器行上来自总体矩阵上多少个小block
    int nblock_col_rmfs=nq/nb;                      //计算出该处理器列上来自总体矩阵上多少个小block
    //        
    int reste_row_rmfs=n % nb;                   //行剩余的大小(全局矩阵A分块后)
    int reste_col_rmfs=n % nb;                   //列剩余的大小(全局矩阵A分块后)
    //    
    int extra_t_row_rmfs=(n/nb) % nprow;            //行剩余所在处理器行号    
    int extra_t_col_rmfs=(n/nb) % npcol;            //列剩余所在处理器列号      
    //    
    if(myrow==extra_t_row_rmfs && reste_row_rmfs!=0) nblock_row_rmfs=nblock_row_rmfs+1;
    if(mycol==extra_t_col_rmfs && reste_col_rmfs!=0) nblock_col_rmfs=nblock_col_rmfs+1;
  
    Scalar yita, fai;
    mpi_world.barrier(); 

    //openmp并行计算 
    int j_block_rmfs, i_v_rmfs, j_v_rmfs, rowmax_rmfs, colmax_rmfs,i_rmfs, j_rmfs, i_ov_rmfs, j_ov_rmfs, i_sp_rmfs,j_sp_rmfs;
    ARI_OMP(parallel for schedule(static) private(j_block_rmfs, i_v_rmfs, j_v_rmfs, rowmax_rmfs, 
            colmax_rmfs,i_rmfs, j_rmfs, i_ov_rmfs, j_ov_rmfs, i_sp_rmfs,j_sp_rmfs,yita, fai) )
    for( int i_block_rmfs=1; i_block_rmfs < nblock_row_rmfs+1; i_block_rmfs++ )                         //循环该处理器行上来自总体矩阵上的小block的总数
    {
        for(  j_block_rmfs=1; j_block_rmfs < nblock_col_rmfs+1; j_block_rmfs++ )
                            //循环该处理器列上来自总体矩阵上的小block的总数 
        {
                    
            i_v_rmfs=myrow*nb+(i_block_rmfs-1)*nprow*nb;                    //对应全局矩阵的角点位置
            j_v_rmfs=mycol*nb+(j_block_rmfs-1)*npcol*nb;
            
            rowmax_rmfs=nb;
            colmax_rmfs=nb;
            
            if(myrow==extra_t_row_rmfs && reste_row_rmfs!=0 && i_block_rmfs==nblock_row_rmfs) rowmax_rmfs=reste_row_rmfs;
            if(mycol==extra_t_col_rmfs && reste_col_rmfs!=0 && j_block_rmfs==nblock_col_rmfs) colmax_rmfs=reste_col_rmfs;
                            
            for(  i_rmfs = 0; i_rmfs < rowmax_rmfs; i_rmfs++)
            {
                    for(  j_rmfs = 0; j_rmfs < colmax_rmfs; j_rmfs++)
                    {
                        i_ov_rmfs=i_v_rmfs+i_rmfs;
                        j_ov_rmfs=j_v_rmfs+j_rmfs;
                                    
                        yita = sqrt(pow((v_wallNode[j_ov_rmfs].X() - v_wallNode[i_ov_rmfs].X()), 2)
                                + pow((v_wallNode[j_ov_rmfs].Y() - v_wallNode[i_ov_rmfs].Y()), 2)
                                + pow((v_wallNode[j_ov_rmfs].Z() - v_wallNode[i_ov_rmfs].Z()), 2)) / R;
                        fai=pow((1 - yita), 4)*(4 * yita + 1.0);
                        if(yita > 1.0) fai=0.0;
                        i_sp_rmfs=(i_block_rmfs-1)*nb+i_rmfs;
                        j_sp_rmfs=(j_block_rmfs-1)*nb+j_rmfs;
                        a[i_sp_rmfs + j_sp_rmfs*np] = fai;
                    }                              
            }         
        }
    }
    //当地矩阵b初始化     
    nblock_col_rmfs=nqrhs/nb;    
    reste_col_rmfs=nrhs % nb;               //列剩余的大小(全局矩阵A分块后)
    extra_t_col_rmfs=(nrhs/nb) % npcol;        //列剩余所在处理器列号          

    if(mycol==extra_t_col_rmfs && reste_col_rmfs!=0) nblock_col_rmfs=nblock_col_rmfs+1;
    ARI_OMP(parallel for schedule(static) private(j_block_rmfs, i_v_rmfs, rowmax_rmfs, i_rmfs, i_ov_rmfs,  i_sp_rmfs) )    
    for( int i_block_rmfs=1; i_block_rmfs < nblock_row_rmfs+1; i_block_rmfs++ )       
    {
        for( j_block_rmfs=1; j_block_rmfs < nblock_col_rmfs+1; j_block_rmfs++ )
        {
            i_v_rmfs=myrow*nb+(i_block_rmfs-1)*nprow*nb;                              
            rowmax_rmfs=nb;
                    
            if(myrow==extra_t_row_rmfs && reste_row_rmfs!=0 && i_block_rmfs==nblock_row_rmfs) rowmax_rmfs=reste_row_rmfs;
            
            for(  i_rmfs = 0; i_rmfs < rowmax_rmfs; i_rmfs++)
            {               
                    i_ov_rmfs=i_v_rmfs+i_rmfs;                                      
                    i_sp_rmfs=(i_block_rmfs-1)*nb+i_rmfs;
                    
                    b_temp[i_sp_rmfs].SetX(v_wallNode_deform[i_ov_rmfs].X() - v_wallNode[i_ov_rmfs].X());     
                    b_temp[i_sp_rmfs].SetY(v_wallNode_deform[i_ov_rmfs].Y() - v_wallNode[i_ov_rmfs].Y());    
                    b_temp[i_sp_rmfs].SetZ(v_wallNode_deform[i_ov_rmfs].Z() - v_wallNode[i_ov_rmfs].Z());                                                                  
            } 
        }
    }
        
    mpi_world.barrier();
         
    if(nqrhs != 0)
    {
        for( int i = 0; i < np; i++)
        {
            b[i] = b_temp[i].X();    
            b[i + np] = b_temp[i].Y();
            b[i + np*2] = b_temp[i].Z();                                         
        }                
    } 
                  
    delete[] a_temp;
    b_temp.clear();
    AllocateDim(v_weight, n);     
            
    int *ipiv = new int[np + desca[4]];
    char symbol_N = 'N';
     
    MPIBarrier();
    if( processorID == 0)
    Print("\n 矩阵准备完成...........................");     
    //调用矩阵LU分解函数    
    pdgetrf(&n, &n, a, &one, &one, desca, ipiv, &info);
    
    pdgetrs(&symbol_N, &n, &nrhs, a, &one, &one, desca, ipiv, b, &one, &one, descb, &info);
    if( processorID == 0)
    Print("\n LU分解完成...........................");     
    std::fstream file;
    std::string caseName1 = "matrix_b_";

    std::vector<Vector> b_send;
    std::vector<Vector> b_recv;    
        
//主进程从其他进程中接收解矩阵b，将其存放在m_b中 
    if(processorID == 0) 
    {
        for( int i_block_rmfs=1; i_block_rmfs < nblock_row_rmfs+1; i_block_rmfs++ )       
        {
            for( int j_block_rmfs=1; j_block_rmfs < nblock_col_rmfs+1; j_block_rmfs++ )
            {
                int i_v_rmfs=myrow*nb+(i_block_rmfs-1)*nprow*nb;                    
                int rowmax_rmfs=nb;
                    
                if(myrow==extra_t_row_rmfs && reste_row_rmfs!=0 && i_block_rmfs==nblock_row_rmfs) rowmax_rmfs=reste_row_rmfs;         
          
                for( int i_rmfs = 0; i_rmfs < rowmax_rmfs; i_rmfs++)
                {               
                    int i_ov_rmfs=i_v_rmfs+i_rmfs;                                      
                    int i_sp_rmfs=(i_block_rmfs-1)*nb+i_rmfs;
                                              
                    v_weight[i_ov_rmfs].SetX(b[i_sp_rmfs]);     
                    v_weight[i_ov_rmfs].SetY(b[i_sp_rmfs + np]);    
                    v_weight[i_ov_rmfs].SetZ(b[i_sp_rmfs + 2*np]);    
               }         
            }
        }
              
        for(int i_th_rmfs = 0; i_th_rmfs < nprow; i_th_rmfs++)
        {
            for(int j_th_rmfs = 0; j_th_rmfs < npcol; j_th_rmfs++)
            {
                int id_th_rmfs = i_th_rmfs*npcol+j_th_rmfs;
                if(id_th_rmfs == 0) continue;                        
                mpi_world.recv(id_th_rmfs, 0, nblock_row_rmfs);
                mpi_world.recv(id_th_rmfs, 1, nblock_col_rmfs);                
                for( int i_block_rmfs=1; i_block_rmfs < nblock_row_rmfs+1; i_block_rmfs++ )       
                {
                    for( int j_block_rmfs=1; j_block_rmfs < nblock_col_rmfs+1; j_block_rmfs++ )
                    {
                        int i_v_rmfs, rowmax_rmfs; 
                        mpi_world.recv(id_th_rmfs, 2, i_v_rmfs);
                        mpi_world.recv(id_th_rmfs, 4, rowmax_rmfs);               
                        AllocateDim(b_recv, rowmax_rmfs);
                        mpi_world.recv(id_th_rmfs, 5, b_recv);
                        for( int i_rmfs = 0; i_rmfs < rowmax_rmfs; i_rmfs++)
                        {
                            int i_ov_rmfs=i_v_rmfs+i_rmfs;
                            v_weight[i_ov_rmfs].SetX( b_recv[i_rmfs].X());     
                            v_weight[i_ov_rmfs].SetY( b_recv[i_rmfs].Y());    
                            v_weight[i_ov_rmfs].SetZ( b_recv[i_rmfs].Z());    
                        }
                        b_recv.clear();  
                    }
                }                   
            }
        }
    }
    else
    {
        mpi_world.send(0, 0, nblock_row_rmfs);
        mpi_world.send(0, 1, nblock_col_rmfs);
          
        for( int i_block_rmfs=1; i_block_rmfs < nblock_row_rmfs+1; i_block_rmfs++ )       
        {
            for( int j_block_rmfs=1; j_block_rmfs < nblock_col_rmfs+1; j_block_rmfs++ )
            {
                int i_v_rmfs=myrow*nb+(i_block_rmfs-1)*nprow*nb;                    
                int rowmax_rmfs=nb;
                    
                if(myrow==extra_t_row_rmfs && reste_row_rmfs!=0 && i_block_rmfs==nblock_row_rmfs) rowmax_rmfs=reste_row_rmfs;
              
                AllocateDim(b_send, rowmax_rmfs);
              
                for( int i_rmfs = 0; i_rmfs < rowmax_rmfs; i_rmfs++)
                {                                                        
                    int i_sp_rmfs=(i_block_rmfs-1)*nb+i_rmfs;
                                              
                    b_send[i_rmfs].SetX( b[i_sp_rmfs]);     
                    b_send[i_rmfs].SetY( b[i_sp_rmfs + np]);    
                    b_send[i_rmfs].SetZ( b[i_sp_rmfs + 2*np]);    
                }
                mpi_world.send(0, 2, i_v_rmfs);
                mpi_world.send(0, 4, rowmax_rmfs);
                mpi_world.send(0, 5, b_send);
                b_send.clear();                        
            }
        }          
    }
      
    delete[] a;
    delete[] b;
      

    delete[] ipiv;
    
    blacs_gridexit_(&ictxt);
    if( processorID == 0)
        Print("\n矩阵合并完成...........................");
        
    //各进程从主进程中接收解矩阵v_weight
    if(processorID == 0) 
    {
        for (int i = 1; i < nPart; i++)
        {
            mpi_world.send(i, 6, v_weight);
        }           
    }
    else
    {
        mpi_world.recv(0, 6, v_weight);             
    } 

#endif
}


void MeshDeform::RebuildNode(std::vector<Node> &v_globalNode)
{
    std::vector<Node> Delta;

    Node Delta_xyz;
    Scalar yita;
    // Scalar temp;
    std::fstream file;
    int n = v_globalNode.size();
    int n1 = v_wallNode.size();
    Delta.resize(n1);

    std::vector<Scalar> fai;
    int j, k;

    ARI_OMP(parallel for schedule(static) private(Delta_xyz, yita, fai, j, k))
    for (int i = 0; i < n; i++)
    {
        Delta_xyz = Vector0;
        yita = 0.0;
        const Node nodeTemp1 = v_globalNode[i];
        Node node_deform = Vector0;
        fai.resize(n1);
        for (j = 0; j < v_wallNode.size(); j++)
        {
            const Node nodeTemp2 = v_wallNode[j];

            yita = sqrt(pow((nodeTemp2.X() - nodeTemp1.X()), 2) + pow((nodeTemp2.Y() - nodeTemp1.Y()), 2) + pow((nodeTemp2.Z() - nodeTemp1.Z()), 2)) / R;

            if (yita > 1.0)
            {
                fai[j] = 0.0;
            }
            else
            {
                fai[j] = pow((1 - yita), 4) * (4 * yita + 1);
            }
        }

        for (k = 0; k < v_wallNode.size(); k++)
        {
            Delta_xyz.SetX(Delta_xyz.X() + v_weight[k].X() * fai[k]);
            Delta_xyz.SetY(Delta_xyz.Y() + v_weight[k].Y() * fai[k]);
            Delta_xyz.SetZ(Delta_xyz.Z() + v_weight[k].Z() * fai[k]);
        }

        node_deform.SetX(nodeTemp1.X() + Delta_xyz.X());
        node_deform.SetY(nodeTemp1.Y() + Delta_xyz.Y());
        node_deform.SetZ(nodeTemp1.Z() + Delta_xyz.Z());

        fai.clear();
        v_globalNode[i] = node_deform;
    }
}
