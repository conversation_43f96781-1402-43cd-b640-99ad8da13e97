#include "mlTurbModel.h"

mlTurbModel::mlTurbModel(const std::string& model_path, int IntraOpNumThreads, int InterOpNumThreads, int numFeatures_) 
    : env_(ORT_LOGGING_LEVEL_WARNING, "mlTurbModel"),
      session_(nullptr),
      numFeatures(numFeatures_)
{
    // 模型优化选项
    Ort::SessionOptions session_options;
    // 算子内部并行线程
    if( IntraOpNumThreads > 0 ) session_options.SetIntraOpNumThreads(IntraOpNumThreads);   
    // 算子间并行线程
    if( InterOpNumThreads > 0 ) session_options.SetInterOpNumThreads(InterOpNumThreads);
    // 使用全部默认优化
    session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);

    // 初始化模型
    session_ = Ort::Session(env_, model_path.c_str(), session_options);
}

mlTurbModel::~mlTurbModel()
{
}

std::vector<float> mlTurbModel::predictor(std::vector<float>& input_data) 
{
    // 获取输入数据维度
    //constexpr size_t num_features = 6;
    const size_t num_samples = input_data.size() / numFeatures;
    // 准备输入Tensor
    std::vector<int64_t> input_shape = {static_cast<int64_t>(num_samples), numFeatures};
    
    auto memory_info = Ort::MemoryInfo::CreateCpu(
        OrtDeviceAllocator, 
        OrtMemTypeCPU
    );
    
    Ort::Value input_tensor = Ort::Value::CreateTensor<float>(
        memory_info,
        input_data.data(),
        input_data.size(),
        input_shape.data(),
        input_shape.size()
    );

    // 定义输入输出名称
    const char* input_names[] = {"input"};
    const char* output_names[] = {"variable"};

    // 执行推理
    Ort::RunOptions run_options;

    auto output_tensors = session_.Run(
        run_options,
        input_names,
        &input_tensor,
        1,
        output_names,
        1
    );

    // 解析输出结果
    float* output = output_tensors[0].GetTensorMutableData<float>();
    return std::vector<float>(output, output + num_samples);
}