import joblib
import numpy as np
import onnx
#from onnxoptimizer import optimize
import skl2onnx
from skl2onnx import convert_sklearn
from skl2onnx.common.data_types import FloatTensorType
#from skl2onnx.common.data_types import DoubleTensorType
import time

print("-" * 50)
print("skl2onnx 版本:", skl2onnx.__version__)
print("onnx 版本:", onnx.__version__)
print("默认 Opset:", skl2onnx.get_latest_tested_opset_version())
print("-" * 50)

# 加载模型
#modelInput = 'RF_weights_d_log_mut.pkl'
modelInput = 'RF_weights.pkl'
modelOutput = "rf_model.onnx"

model = joblib.load(modelInput)

print(model.n_features_in_)
print("模型加载完成")

#        0.857681       -0.859153        0.995006        0.727003     1.29219e-06     5.87694e-08
#        0.836809       -0.839771        0.994227        0.482299     1.16133e-08     5.79344e-13
#X_train = np.array([[0.857681, -0.859153, 0.995006, 0.727003, 1.29219e-06,5.87694e-08],[0.836809, -0.839771, 0.994227, 0.482299, 1.16133e-08, 5.79344e-13]])
#Y_predict = model.predict(X_train).reshape((-1,1))
#
#print(Y_predict)

# 提取单棵决策树，查看其节点阈值的数据类型
tree = model.estimators_[0].tree_

# 检查阈值数组的数据类型（一般为 float64）
print("决策树节点阈值数据类型:", tree.threshold.dtype)

print("n_estimators = ", model.n_estimators)
print("max_depth = ", model.max_depth)


# 扩展：检查所有树的最大深度（可选）
all_depths = [tree.tree_.max_depth for tree in model.estimators_]
print("\n所有树的最大深度列表:", all_depths)
print("最大深度:", max(all_depths))
print("最小深度:", min(all_depths))


initial_type = [('input', FloatTensorType([None, 6]))]
#initial_type = [('input', DoubleTensorType([None, 6]))]

# 转换模型时指定opset
onnx_model = convert_sklearn(
    model,
    initial_types=initial_type
)
#onnx_model = convert_sklearn(
#    model,
#    initial_types=initial_type,
#    target_opset={"ai.onnx.ml": 1, "": 17}
#)

print("onnx info")
for opset in onnx_model.opset_import:
    print(f"Domain: {opset.domain}, Version: {opset.version}")
print(f"IR版本: {onnx_model.ir_version}")
print("-" * 50)
 
with open(modelOutput, "wb") as f:
    f.write(onnx_model.SerializeToString())

