#-----------------------------------------------------------------------------
# 定义相关变量
#-----------------------------------------------------------------------------
# 获取当前文件夹名称CURRENT_FOLDER
get_filename_component(CURRENT_FOLDER ${CURRENT_SOURCE_DIR} NAME ABSOLUTE)

# 获取上层和上上文件夹名称SECOND_FOLDER、THIRD_FOLDER
get_filename_component(SECOND_FOLDER_ABSOLUTE ${CURRENT_SOURCE_DIR} DIRECTORY ABSOLUTE)
get_filename_component(THIRD_FOLDER_ABSOLUTE ${SECOND_FOLDER_ABSOLUTE} DIRECTORY ABSOLUTE)
if("${SECOND_FOLDER_ABSOLUTE}" STREQUAL "${ARI_SRCDIR}")
    set(SECOND_FOLDER ${CURRENT_FOLDER})
	set(THIRD_FOLDER "")
else()
    string(REGEX REPLACE ".*/(.*)" "\\1" SECOND_FOLDER ${SECOND_FOLDER_ABSOLUTE})
	if("${THIRD_FOLDER_ABSOLUTE}" STREQUAL "${ARI_SRCDIR}")
		set(THIRD_FOLDER "")
	else ()
		string(REGEX REPLACE ".*/(.*)" "\\1" THIRD_FOLDER ${THIRD_FOLDER_ABSOLUTE})
	endif()
endif()

# 获取子文件夹名称列表SUBDIRS
file(GLOB FILES ${CURRENT_SOURCE_DIR}/*)
set(SUBDIRS "")
foreach(FILE ${FILES})
    file(RELATIVE_PATH FILE_NAME ${CURRENT_SOURCE_DIR} ${FILE})
    if(IS_DIRECTORY ${CURRENT_SOURCE_DIR}/${FILE_NAME})
		set(FILE_LOCAL ${CURRENT_SOURCE_DIR}/${FILE_NAME})
		file(GLOB_RECURSE SOURCE_FILES ${FILE_LOCAL}/*.cpp)
		file(GLOB_RECURSE HEADER_FILES ${FILE_LOCAL}/*.h*)
		if((NOT ("${SOURCE_FILES}" STREQUAL "")) AND (NOT ("${HEADER_FILES}" STREQUAL "")))
			list(APPEND SUBDIRS ${FILE_NAME})
		endif()
    endif()
endforeach()

# 定义库名称变量
set(LIB_NAME "${SECOND_FOLDER}_${CURRENT_FOLDER}")

# 定义工程中库所在文件夹名称
set(LIB_FOLDER ${SECOND_FOLDER})

## 在所有库目标文件名称变量中添加库名称
#list (APPEND ARI_LIB_TARGET ${LIB_NAME})

## 在所有库安装子目录变量中添加库源码文件夹
#list (APPEND ARI_INSTALL_SUBDIR ${CURRENT_FOLDER})

#-----------------------------------------------------------------------------
# 设置库的编译
#-----------------------------------------------------------------------------
# 设置库所包含的头文件路径
if("${INCLUDE_DIR}" STREQUAL "")
    set(INCLUDE_DIR ${ARI_SRCDIR})
endif()
include_directories(${INCLUDE_DIR})

# 设置库文件的输出路径
# set(LIBRARY_OUTPUT_PATH ${CMAKE_BINARY_DIR}/lib)

# 设置库的源码（为绝对路径）
file(GLOB_RECURSE LIB_SOURCE ./*.cpp)
file(GLOB_RECURSE LIB_HEADER ./*.h*)

# 添加库
add_library(${LIB_NAME} ${BUILD_TYPE} ${LIB_SOURCE} ${LIB_HEADER})

# 设置库的依赖库
if("${LIB_LINK}" STREQUAL "feilian-specialmodule_staticAeroelastic")
    target_link_libraries(${LIB_NAME}
    PRIVATE
    ${LIB_LINK}
    #-L${INTEL_MKL_ROOT}/lib/intel64_lin -lmkl_intel_lp64 -lmkl_intel_thread -lmkl_scalapack_lp64 -lmkl_blacs_intelmpi_lp64 -lmkl_core -liomp5 -lpthread -lm -ldl
    -lpthread -lm -ldl
	)
endif()

# 为target_link_libraries目标提供头文件
target_include_directories(${LIB_NAME}
INTERFACE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 设置库在工程中所属的文件夹
if(NOT ("${LIB_FOLDER}" STREQUAL ""))
    set_target_properties(${LIB_NAME} PROPERTIES FOLDER ${LIB_FOLDER})
endif()

#-----------------------------------------------------------------------------
# 设置库的安装
#-----------------------------------------------------------------------------
# 设置库的安装路径
install(TARGETS ${LIB_NAME}
        #CONFIGURATIONS Debug
        ARCHIVE DESTINATION ${ARI_INSTALL_PREFIX}/lib)

# 定义头文件安装路径变量
if(NOT ("${THIRD_FOLDER}" STREQUAL ""))
	set(INCLUDE_INSTALL_DIR ${ARI_INSTALL_PREFIX}/include/${THIRD_FOLDER}/${SECOND_FOLDER}/${CURRENT_FOLDER})
else()
	set(INCLUDE_INSTALL_DIR ${ARI_INSTALL_PREFIX}/include/${SECOND_FOLDER}/${CURRENT_FOLDER})
endif()

# 设置头文件的安装路径（获取当前文件夹的.h文件列表，并指定安装路径）
file(GLOB HEADER_FILES ./*.h*)
install(FILES ${HEADER_FILES} DESTINATION ${INCLUDE_INSTALL_DIR})

# 循环实现子文件夹头文件的安装路径设置
foreach(SUBDIR ${SUBDIRS})
    file(GLOB SUB_HEADER_FILES ./${SUBDIR}/*.h)
    install(FILES ${SUB_HEADER_FILES} DESTINATION ${INCLUDE_INSTALL_DIR}/${SUBDIR})
endforeach(SUBDIR ${SUBDIRS})